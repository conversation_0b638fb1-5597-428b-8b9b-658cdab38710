ext {
    buildToolsVersion = "30.0.3"
    minSdkVersion = 26
    targetSdkVersion = 30
    compileSdkVersion = 33
    versionCode = 85
    versionName = "2.8.5"
}

def getProjectEnvironment() {
    Properties properties = new Properties()
    InputStream inputStream = project.rootProject.file('local.properties').newDataInputStream()
    properties.load(inputStream)
    return properties.getProperty('projectEnvironment')
}
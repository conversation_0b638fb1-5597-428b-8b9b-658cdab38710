************* Log Head ****************
Date of Log        : 2025_08_05
Rom Info           : RomInfo{name=samsung, version=ap3a.240905.015.a2.s9260zcs4byf1}
Device Manufacturer: samsung
Device Model       : SM-S9260
Android Version    : 15
Android SDK        : 35
App VersionName    : 2.8.4
App VersionCode    : 84
************* Log Head ****************

09:22:56.716 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.onCreate(MainActivity.java:11)]: MainActivity OnCreate()
09:23:04.439 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /*************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ax, RSSI: -47, Link speed: 1152Mbps, Tx Link speed: 1152Mbps, Max Supported Tx Link speed: 2401Mbps, Rx Link speed: 1921Mbps, Max Supported Rx Link speed: 2401Mbps, Frequency: 5240MHz, Net ID: -1, Metered hint: false, score: 60, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:23:04.444 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=1754356984438
09:23:31.264 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -47, Link speed: 390Mbps, Tx Link speed: 390Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 325Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: false, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:23:31.270 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=1754357011264
09:23:32.489 D/DefaultTaskExecutor [main, com.icatch.mobilecam.utils.executor.DefaultTaskExecutor.<init>(DefaultTaskExecutor.java:8)]: 线程池最大线程数：16
09:23:32.500  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:23:32.508  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:23:36.541  [main, PreviewPresenter.startOrStopCapture(:1)]: 拍摄：点击拍摄按钮
09:23:36.569  [main, PreviewPresenter.startPhotoCapture(:1)]: 拍摄：开始拍摄
09:23:36.684  [main, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:23:36.688  [main, PreviewPresenter.startA6CaptureTimer(:6)]: 拍摄：A6 开启计时器
09:23:36.692  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 0
09:23:36.724  [Thread-33, PreviewPresenter.lambda$startPhotoCapture$13(:3)]: 拍摄：循环询问相机是否可以拍照开始
09:23:36.942  [Thread-33, PreviewPresenter.lambda$startPhotoCapture$13(:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
09:23:37.006 D/PhotoCapture [Thread-33, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:23:39.307  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:23:39.712  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:23:42.457  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:23:42.721  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:23:44.486  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:23:44.498  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
09:23:44.504  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：0
09:23:44.548  [Thread-39, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:23:45.545  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:23:45.722  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:23:48.707  [Thread-39, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
09:23:48.722  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:23:48.912 D/PhotoCapture [Thread-39, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:23:51.140  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:23:51.735  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:23:54.184  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:23:54.759  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:23:56.149  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:23:56.160  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
09:23:56.168  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
09:23:56.200  [Thread-46, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:23:57.206  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:23:57.759  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:24:01.141  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:24:01.160  [Thread-46, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
09:24:01.374 D/PhotoCapture [Thread-46, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:24:03.543  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:24:04.155  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:24:06.599  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:24:07.168  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:24:08.635  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:24:08.644  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
09:24:08.661  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
09:24:08.690  [Thread-51, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:24:09.693  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:24:10.168  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:24:12.956  [Thread-51, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
09:24:13.128 D/PhotoCapture [Thread-51, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:24:13.197  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:24:15.428  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:24:16.208  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:24:18.619  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:24:19.215  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:24:20.661  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:24:20.669  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
09:24:20.675  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
09:24:20.689  [main, PreviewPresenter$PreviewHandler.handleMessage(:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
09:24:20.697  [Thread-54, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:2)]: 拍摄：循环询问相机是否已复位开始
09:24:21.713  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:24:22.216  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:24:22.650 D/SPKey [arch_disk_io_5, com.icatch.mobilecam.utils.SPKey.isPhoneQualifiedPerformance(SPKey.java:3)]: CPU 数量：8
09:24:22.657 D/SPKey [arch_disk_io_5, com.icatch.mobilecam.utils.SPKey.isPhoneQualifiedPerformance(SPKey.java:9)]: 总内存大小：11628584960
09:24:22.664 D/SPKey [arch_disk_io_5, com.icatch.mobilecam.utils.SPKey.isPhoneQualifiedPerformance(SPKey.java:10)]: 可用内存大小：5857398784
09:24:22.669 D/PbDownloadManager [arch_disk_io_5, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
09:24:22.672 D/PbDownloadManager [arch_disk_io_5, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：3
09:24:25.216  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:24:28.228  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:24:29.023  [Thread-54, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:24:29.026  [Thread-54, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
09:24:34.907 D/PbDownloadManager [arch_disk_io_5, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
09:24:35.048 D/StitchUtils [arch_disk_io_5, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
09:24:35.052 D/StitchUtils [arch_disk_io_5, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
09:24:35.060 D/StitchUtils [arch_disk_io_5, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"Gv93ZluzegyHGHI+J+SWoMCChNdYR4O2UrlSPxhqA71EE3dgGCgJ9zzoXp6Izrl\/xTZOubL0lFcfij7YMhBCmx+7uD5RN48P14cPbO+JFXX2TjDAjnlX\/652WQR66Mf\/iMic+5hLWPJvNu8mJa9U1nTV8jSfSShodJ4kNC0\/QJV7sw3ovEXENqBtcdaZ801O5m5qEMlvS6gj5GvncnOy9HhU0Ll1AdibtkFQkLTyMXtGcKxsfLyiRCcU766vjrO4cValVhuY8nRNJ\/F2dcXHJPDylDckcDEIx5j1C2lpW0R3Bsttj4tgZWcSNWhpfO6Gnc\/2EK9QM20wBt13e4OOiFEEVYl8F\/HoGRqtU9izt+KXHip0KWz8mjm6e7wwjk6sXimnjpinFCsFj9xaBaOHFBWSsWMT1J9BfkReISW+dvUUkA25AdfZTqnrSQFQt7t0KukLQQazsJnJHseVF7i5uVjnQ\/9nlcauZqBIQ+SXiVas\/91bSCaQKg3E5WWNWHQpRe8D0ezlE5lbCaAEvTnLZJJEaZVGYgwwHKRnuy8VCaQQ90TpzIYnI+f65Nh7gBFg309ZPluaOcs7CFH7W7QpyGBoUqMIn6RCnN9ZdAM4aj3LSTKevoyPZdFeB5DXqrTLUnpcsSVuy8NMFsRRo2eInLLBLb5UTSasH2kVUgwxZLYvpuRwq++BQJDh4tQXIM5nkLiMFdV9JV\/Alj8gxRFoSGW\/R0fX\/27fwVH4VtsOlk9q86r4pF\/H3KVWa1lCDT57LjiVONqm9FozCEcQxzZZCYCFOuW4uxecRx37AxbL8pqKnehfH1gYHqeceOFBdHUcekzKxaGNtU6YgE5P5zDxEzMsVDXFkhdamfPH4mzwfZJjxxM9Lz1foDHVHacZm2tGNz7Mr1ryP34sY69bLICq2Eh5hT1f82C\/la+J4DEQrqM8XwEc\/v0QsxolqrNjew\/hd6QslkXgvGM4p9Fcj2ehcCifYKotm6Cp97y3w0mTFj2v0fqw3IG5qmZY5kjor2YtZTiPbQxuar\/+N6WUxwlas03heyqmJyJHcqDIbJWSTY9j\/4n2B7AajllT+YPOTQyHKhXChBQw\/5p7PF\/HOJ70339fy+LvAdXkNzVifF8mgtgC27ckMNdWc+xw8yaOtustTlL8ihrSeoL0BTMndBanxzhM7WvBWZbOJx7gT3CwpV8SHOXq3MT0NhZPGeozfTGgQXFYEX9kk9sfcjlGMksdw0kitfcEfQr+vRZ3OxRDrk8hIMtDKJ2s3kByheu2qBZHIdw8OArZLYVCp7YgE72qg5R0Ohn0i1Y1CNkNu6Krd\/1uODNB0F6Ivqo8Qj\/F31ukKq5ZW+bT3TvSNS1gdqnnmQc2LQ2poaWKFSyj6UsigJXmGrOlpPnP3iPpK9\/FWrggYc+mnY+peCJ3UihMQZaHHvNAssBr\/kwIW3Jmk2svP\/QIMYeN6R9q1hR0C7TMZ2jBhNsi8c\/ktkynuCWNcVNV9QodlXSP0WMWmo5O\/5hs1h1o4JmVi+LKpnTg5evYlFYQJGAR7D3ldHqrKEQPjV5ooBTYuizEqZgvUzkQ2mzjjvcmQp2zHlMedpg5oJLhidZ6TCXLS2RYK+bGcfR1+f1eOHfPEjLJsXZ4KbzHdzi9iIuq++Qf\/K6mZGdYKtBZL0axXvV4yYaqsb0CfsfG0EnmlgH4459LfJlhVxFoByFWg70=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092337_34160854303582_172.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092337_34160462535561_173.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092337_34160849354832_174.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092337_34165090378372_173.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
09:24:40.326 D/StitchUtils [arch_disk_io_5, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
09:25:54.500  [main, PreviewPresenter.startOrStopCapture(:1)]: 拍摄：点击拍摄按钮
09:25:54.539  [main, PreviewPresenter.startPhotoCapture(:1)]: 拍摄：开始拍摄
09:25:54.658  [main, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:25:54.663  [main, PreviewPresenter.startA6CaptureTimer(:6)]: 拍摄：A6 开启计时器
09:25:54.668  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 0
09:25:54.698  [Thread-57, PreviewPresenter.lambda$startPhotoCapture$13(:3)]: 拍摄：循环询问相机是否可以拍照开始
09:25:54.907  [Thread-57, PreviewPresenter.lambda$startPhotoCapture$13(:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
09:25:54.956 D/PhotoCapture [Thread-57, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:25:57.248  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:25:57.680  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:00.371  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:00.694  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:02.402  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:26:02.410  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
09:26:02.418  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：0
09:26:02.450  [Thread-61, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:26:03.455  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:03.695  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:26:06.660  [Thread-61, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
09:26:06.696  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:06.832 D/PhotoCapture [Thread-61, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:26:08.989  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:09.710  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:12.017  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:12.719  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:14.046  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:26:14.052  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
09:26:14.058  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
09:26:14.097  [Thread-65, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:26:15.113  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:15.721  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:26:18.484  [Thread-65, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
09:26:18.683 D/PhotoCapture [Thread-65, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:26:18.757  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:20.978  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:21.765  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:24.151  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:24.778  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:26.180  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:26:26.187  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
09:26:26.193  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
09:26:26.231  [Thread-69, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:26:27.243  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:27.779  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:26:30.372  [Thread-69, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
09:26:30.583 D/PhotoCapture [Thread-69, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:26:30.795  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:32.853  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:33.804  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:36.005  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:36.813  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:26:38.046  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:26:38.051  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
09:26:38.056  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
09:26:38.069  [main, PreviewPresenter$PreviewHandler.handleMessage(:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
09:26:38.074  [Thread-72, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:2)]: 拍摄：循环询问相机是否已复位开始
09:26:39.100  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:26:39.814  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:26:39.950 D/PbDownloadManager [arch_disk_io_2, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
09:26:39.961 D/PbDownloadManager [arch_disk_io_2, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：3
09:26:42.815  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:26:45.815  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:26:46.347  [Thread-72, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:26:46.350  [Thread-72, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
09:26:51.479 D/PbDownloadManager [arch_disk_io_2, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
09:26:51.616 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
09:26:51.620 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
09:26:51.624 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"Gv93ZluzegyHGHI+J+SWoMCChNdYR4O2UrlSPxhqA71EE3dgGCgJ9zzoXp6Izrl\/xTZOubL0lFcfij7YMhBCmx+7uD5RN48P14cPbO+JFXX2TjDAjnlX\/652WQR66Mf\/iMic+5hLWPJvNu8mJa9U1nTV8jSfSShodJ4kNC0\/QJV7sw3ovEXENqBtcdaZ801O5m5qEMlvS6gj5GvncnOy9HhU0Ll1AdibtkFQkLTyMXtGcKxsfLyiRCcU766vjrO4cValVhuY8nRNJ\/F2dcXHJPDylDckcDEIx5j1C2lpW0R3Bsttj4tgZWcSNWhpfO6Gnc\/2EK9QM20wBt13e4OOiFEEVYl8F\/HoGRqtU9izt+KXHip0KWz8mjm6e7wwjk6sXimnjpinFCsFj9xaBaOHFBWSsWMT1J9BfkReISW+dvUUkA25AdfZTqnrSQFQt7t0KukLQQazsJnJHseVF7i5uVjnQ\/9nlcauZqBIQ+SXiVas\/91bSCaQKg3E5WWNWHQpRe8D0ezlE5lbCaAEvTnLZJJEaZVGYgwwHKRnuy8VCaQQ90TpzIYnI+f65Nh7gBFg309ZPluaOcs7CFH7W7QpyGBoUqMIn6RCnN9ZdAM4aj3LSTKevoyPZdFeB5DXqrTLUnpcsSVuy8NMFsRRo2eInLLBLb5UTSasH2kVUgwxZLYvpuRwq++BQJDh4tQXIM5nkLiMFdV9JV\/Alj8gxRFoSGW\/R0fX\/27fwVH4VtsOlk9q86r4pF\/H3KVWa1lCDT57LjiVONqm9FozCEcQxzZZCYCFOuW4uxecRx37AxbL8pqKnehfH1gYHqeceOFBdHUcekzKxaGNtU6YgE5P5zDxEzMsVDXFkhdamfPH4mzwfZJjxxM9Lz1foDHVHacZm2tGNz7Mr1ryP34sY69bLICq2Eh5hT1f82C\/la+J4DEQrqM8XwEc\/v0QsxolqrNjew\/hd6QslkXgvGM4p9Fcj2ehcCifYKotm6Cp97y3w0mTFj2v0fqw3IG5qmZY5kjor2YtZTiPbQxuar\/+N6WUxwlas03heyqmJyJHcqDIbJWSTY9j\/4n2B7AajllT+YPOTQyHKhXChBQw\/5p7PF\/HOJ70339fy+LvAdXkNzVifF8mgtgC27ckMNdWc+xw8yaOtustTlL8ihrSeoL0BTMndBanxzhM7WvBWZbOJx7gT3CwpV8SHOXq3MT0NhZPGeozfTGgQXFYEX9kk9sfcjlGMksdw0kitfcEfQr+vRZ3OxRDrk8hIMtDKJ2s3kByheu2qBZHIdw8OArZLYVCp7YgE72qg5R0Ohn0i1Y1CNkNu6Krd\/1uODNB0F6Ivqo8Qj\/F31ukKq5ZW+bT3TvSNS1gdqnnmQc2LQ2poaWKFSyj6UsigJXmGrOlpPnP3iPpK9\/FWrggYc+mnY+peCJ3UihMQZaHHvNAssBr\/kwIW3Jmk2svP\/QIMYeN6R9q1hR0C7TMZ2jBhNsi8c\/ktkynuCWNcVNV9QodlXSP0WMWmo5O\/5hs1h1o4JmVi+LKpnTg5evYlFYQJGAR7D3ldHqrKEQPjV5ooBTYuizEqZgvUzkQ2mzjjvcmQp2zHlMedpg5oJLhidZ6TCXLS2RYK+bGcfR1+f1eOHfPEjLJsXZ4KbzHdzi9iIuq++Qf\/K6mZGdYKtBZL0axXvV4yYaqsb0CfsfG0EnmlgH4459LfJlhVxFoByFWg70=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092555_34297722462227_206.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092555_34297625786238_207.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092555_34297020537176_208.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_092555_34301658613372_208.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
09:26:56.817 D/StitchUtils [arch_disk_io_2, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
09:27:12.896 D/PanoramaPlayerActivity [main, com.ijoyer.camera.activity.PanoramaPlayerActivity$7.onSuccess(PanoramaPlayerActivity.java:4)]: PanoramaPlayerActivity HMS Image Vision API 初始化成功
09:27:12.898 D/PanoramaPlayerActivity [main, com.ijoyer.camera.activity.PanoramaPlayerActivity$7.onSuccess(PanoramaPlayerActivity.java:4)]: PanoramaPlayerActivity HMS Image Vision API 初始化成功
09:27:55.918  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:27:55.927  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:27:59.447  [main, PreviewPresenter.startOrStopCapture(:1)]: 拍摄：点击拍摄按钮
09:27:59.472  [main, PreviewPresenter.startPhotoCapture(:1)]: 拍摄：开始拍摄
09:27:59.578  [main, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:27:59.582  [main, PreviewPresenter.startA6CaptureTimer(:6)]: 拍摄：A6 开启计时器
09:27:59.585  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 0
09:27:59.610  [Thread-93, PreviewPresenter.lambda$startPhotoCapture$13(:3)]: 拍摄：循环询问相机是否可以拍照开始
09:27:59.829  [Thread-93, PreviewPresenter.lambda$startPhotoCapture$13(:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
09:27:59.869 D/PhotoCapture [Thread-93, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:28:02.157  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:28:02.594  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:28:05.330  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:28:05.604  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:28:07.361  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:28:07.371  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
09:28:07.378  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：0
09:28:07.423  [Thread-97, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:28:08.410  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:28:08.606  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:28:17.971  [Thread-32, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = -1
09:28:18.971  [Thread-97, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:28:19.020  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：8
09:28:19.587 D/PreviewActivity [main, com.icatch.mobilecam.ui.activity.PreviewActivity.onDestroy(PreviewActivity.java:4)]: PreviewActivity:正常销毁
09:28:25.019 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -52, Link speed: 390Mbps, Tx Link speed: 390Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 117Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: false, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:28:25.021 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=5965
09:28:36.623 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -52, Link speed: 390Mbps, Tx Link speed: 390Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 117Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: false, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:28:36.627 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=17569
09:28:50.477 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -51, Link speed: 390Mbps, Tx Link speed: 390Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 175Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: false, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:28:50.481 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=31423
09:28:57.831 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -51, Link speed: 390Mbps, Tx Link speed: 390Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 175Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: false, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:28:57.835 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=38777
09:28:59.026  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:28:59.037  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:29:05.305  [main, PreviewPresenter.startOrStopCapture(:1)]: 拍摄：点击拍摄按钮
09:29:05.328  [main, PreviewPresenter.startPhotoCapture(:1)]: 拍摄：开始拍摄
09:29:05.452  [main, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:29:05.460  [main, PreviewPresenter.startA6CaptureTimer(:6)]: 拍摄：A6 开启计时器
09:29:05.462  [Thread-113, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 0
09:29:05.484  [Thread-114, PreviewPresenter.lambda$startPhotoCapture$13(:3)]: 拍摄：循环询问相机是否可以拍照开始
09:29:05.692  [Thread-114, PreviewPresenter.lambda$startPhotoCapture$13(:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
09:29:05.725 D/PhotoCapture [Thread-114, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:29:08.036  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:29:08.491  [Thread-113, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:29:11.288  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:29:11.502  [Thread-113, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:29:13.232  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:29:13.240  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
09:29:13.246  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：0
09:29:13.293  [Thread-118, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:29:14.433  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:29:14.504  [Thread-113, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:29:28.626  [Thread-113, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = -1
09:29:29.630  [Thread-118, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:29:29.656  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：8
09:29:30.214 D/PreviewActivity [main, com.icatch.mobilecam.ui.activity.PreviewActivity.onDestroy(PreviewActivity.java:4)]: PreviewActivity:正常销毁
09:29:36.305 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -51, Link speed: 263Mbps, Tx Link speed: 263Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 117Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: false, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:29:36.309 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=6618
09:29:45.675 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -51, Link speed: 263Mbps, Tx Link speed: 263Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 117Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: false, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:29:45.680 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=15988
09:29:57.937 D/MWifiManager [main, com.icatch.mobilecam.data.SystemInfo.MWifiManager.isWifiConnected(MWifiManager.java:8)]: MWifiManager：当前的NetworkCapabilities + SSID: <unknown ssid>, BSSID: 02:00:00:00:00:00, MAC: 02:00:00:00:00:00, IP: /************, Security type: 2, Supplicant state: COMPLETED, Wi-Fi standard: 11ac, RSSI: -45, Link speed: 263Mbps, Tx Link speed: 263Mbps, Max Supported Tx Link speed: 433Mbps, Rx Link speed: 175Mbps, Max Supported Rx Link speed: 433Mbps, Frequency: 5200MHz, Net ID: -1, Metered hint: false, score: 30, isUsable: true, CarrierMerged: false, SubscriptionId: -1, IsPrimary: -1, Trusted: true, Restricted: false, Ephemeral: false, OEM paid: false, OEM private: false, OSU AP: false, FQDN: <none>, Provider friendly name: <none>, Requesting package name: <none><none>MLO Information: , Is TID-To-Link negotiation supported by the AP: false, AP MLD Address: <none>, AP MLO Link Id: <none>, AP MLO Affiliated links: <none>, Vendor Data: <none>
09:29:57.944 D/MainActivity [main, com.ijoyer.camera.activity.MainActivity.goToNext(MainActivity.java:3)]: 进入：lastExitPreviewActivityTime=28250
09:30:00.235  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:30:00.244  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:30:03.652  [main, PreviewPresenter.startOrStopCapture(:1)]: 拍摄：点击拍摄按钮
09:30:03.677  [main, PreviewPresenter.startPhotoCapture(:1)]: 拍摄：开始拍摄
09:30:03.779  [main, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:30:03.787  [main, PreviewPresenter.startA6CaptureTimer(:6)]: 拍摄：A6 开启计时器
09:30:03.796  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 0
09:30:03.819  [Thread-133, PreviewPresenter.lambda$startPhotoCapture$13(:3)]: 拍摄：循环询问相机是否可以拍照开始
09:30:04.027  [Thread-133, PreviewPresenter.lambda$startPhotoCapture$13(:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
09:30:04.070 D/PhotoCapture [Thread-133, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:30:06.318  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:06.808  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:09.566  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:09.822  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:11.521  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:30:11.529  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
09:30:11.540  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：0
09:30:11.591  [Thread-137, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:30:12.575  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:12.822  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:30:15.784  [Thread-137, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
09:30:15.823  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:15.926 D/PhotoCapture [Thread-137, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:30:18.096  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:18.833  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:21.152  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:21.842  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:23.177  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:30:23.185  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
09:30:23.193  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
09:30:23.220  [Thread-141, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:30:25.129  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:30:25.161  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:27.615  [Thread-141, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
09:30:27.802 D/PhotoCapture [Thread-141, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:30:28.139  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:30.102  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:31.151  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:33.256  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:34.160  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:35.285  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:30:35.295  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
09:30:35.301  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
09:30:35.335  [Thread-145, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:30:36.336  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:37.161  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:30:41.476  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:41.481  [Thread-145, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
09:30:41.680 D/PhotoCapture [Thread-145, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:30:43.944  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:44.577  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:47.168  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:47.623  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:30:49.202  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:30:49.208  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
09:30:49.219  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
09:30:49.232  [main, PreviewPresenter$PreviewHandler.handleMessage(:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
09:30:49.237  [Thread-148, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:2)]: 拍摄：循环询问相机是否已复位开始
09:30:50.265  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:30:50.624  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:30:51.117 D/PbDownloadManager [arch_disk_io_4, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
09:30:51.127 D/PbDownloadManager [arch_disk_io_4, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：3
09:30:53.868  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:30:56.869  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:30:57.633  [Thread-148, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:30:57.635  [Thread-148, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
09:31:02.731 D/PbDownloadManager [arch_disk_io_4, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
09:31:02.876 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
09:31:02.880 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
09:31:02.900 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"Gv93ZluzegyHGHI+J+SWoMCChNdYR4O2UrlSPxhqA71EE3dgGCgJ9zzoXp6Izrl\/xTZOubL0lFcfij7YMhBCmx+7uD5RN48P14cPbO+JFXX2TjDAjnlX\/652WQR66Mf\/iMic+5hLWPJvNu8mJa9U1nTV8jSfSShodJ4kNC0\/QJV7sw3ovEXENqBtcdaZ801O5m5qEMlvS6gj5GvncnOy9HhU0Ll1AdibtkFQkLTyMXtGcKxsfLyiRCcU766vjrO4cValVhuY8nRNJ\/F2dcXHJPDylDckcDEIx5j1C2lpW0R3Bsttj4tgZWcSNWhpfO6Gnc\/2EK9QM20wBt13e4OOiFEEVYl8F\/HoGRqtU9izt+KXHip0KWz8mjm6e7wwjk6sXimnjpinFCsFj9xaBaOHFBWSsWMT1J9BfkReISW+dvUUkA25AdfZTqnrSQFQt7t0KukLQQazsJnJHseVF7i5uVjnQ\/9nlcauZqBIQ+SXiVas\/91bSCaQKg3E5WWNWHQpRe8D0ezlE5lbCaAEvTnLZJJEaZVGYgwwHKRnuy8VCaQQ90TpzIYnI+f65Nh7gBFg309ZPluaOcs7CFH7W7QpyGBoUqMIn6RCnN9ZdAM4aj3LSTKevoyPZdFeB5DXqrTLUnpcsSVuy8NMFsRRo2eInLLBLb5UTSasH2kVUgwxZLYvpuRwq++BQJDh4tQXIM5nkLiMFdV9JV\/Alj8gxRFoSGW\/R0fX\/27fwVH4VtsOlk9q86r4pF\/H3KVWa1lCDT57LjiVONqm9FozCEcQxzZZCYCFOuW4uxecRx37AxbL8pqKnehfH1gYHqeceOFBdHUcekzKxaGNtU6YgE5P5zDxEzMsVDXFkhdamfPH4mzwfZJjxxM9Lz1foDHVHacZm2tGNz7Mr1ryP34sY69bLICq2Eh5hT1f82C\/la+J4DEQrqM8XwEc\/v0QsxolqrNjew\/hd6QslkXgvGM4p9Fcj2ehcCifYKotm6Cp97y3w0mTFj2v0fqw3IG5qmZY5kjor2YtZTiPbQxuar\/+N6WUxwlas03heyqmJyJHcqDIbJWSTY9j\/4n2B7AajllT+YPOTQyHKhXChBQw\/5p7PF\/HOJ70339fy+LvAdXkNzVifF8mgtgC27ckMNdWc+xw8yaOtustTlL8ihrSeoL0BTMndBanxzhM7WvBWZbOJx7gT3CwpV8SHOXq3MT0NhZPGeozfTGgQXFYEX9kk9sfcjlGMksdw0kitfcEfQr+vRZ3OxRDrk8hIMtDKJ2s3kByheu2qBZHIdw8OArZLYVCp7YgE72qg5R0Ohn0i1Y1CNkNu6Krd\/1uODNB0F6Ivqo8Qj\/F31ukKq5ZW+bT3TvSNS1gdqnnmQc2LQ2poaWKFSyj6UsigJXmGrOlpPnP3iPpK9\/FWrggYc+mnY+peCJ3UihMQZaHHvNAssBr\/kwIW3Jmk2svP\/QIMYeN6R9q1hR0C7TMZ2jBhNsi8c\/ktkynuCWNcVNV9QodlXSP0WMWmo5O\/5hs1h1o4JmVi+LKpnTg5evYlFYQJGAR7D3ldHqrKEQPjV5ooBTYuizEqZgvUzkQ2mzjjvcmQp2zHlMedpg5oJLhidZ6TCXLS2RYK+bGcfR1+f1eOHfPEjLJsXZ4KbzHdzi9iIuq++Qf\/K6mZGdYKtBZL0axXvV4yYaqsb0CfsfG0EnmlgH4459LfJlhVxFoByFWg70=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093004_34548394032392_348.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093004_34548745273434_349.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093004_34548771594944_350.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093004_34552914272911_348.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
09:31:08.477 D/StitchUtils [arch_disk_io_4, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
09:31:19.009  [main, PreviewPresenter.startOrStopCapture(:1)]: 拍摄：点击拍摄按钮
09:31:19.040  [main, PreviewPresenter.startPhotoCapture(:1)]: 拍摄：开始拍摄
09:31:19.133  [main, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:31:19.148  [main, PreviewPresenter.startA6CaptureTimer(:6)]: 拍摄：A6 开启计时器
09:31:19.150  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 0
09:31:19.160  [Thread-151, PreviewPresenter.lambda$startPhotoCapture$13(:3)]: 拍摄：循环询问相机是否可以拍照开始
09:31:19.369  [Thread-151, PreviewPresenter.lambda$startPhotoCapture$13(:13)]: 拍摄：循环询问相机是否可以拍照结束,相机准备好了：motorState=2
09:31:19.410 D/PhotoCapture [Thread-151, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:31:21.705  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:22.151  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:24.849  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:25.159  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:26.881  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:31:26.890  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=0
09:31:26.895  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：0
09:31:26.931  [Thread-155, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:31:27.941  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:28.160  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:31:31.148  [Thread-155, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=1
09:31:31.161  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:31.323 D/PhotoCapture [Thread-155, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:31:33.526  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:34.174  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:36.564  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:37.200  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:38.572  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:31:38.579  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=1
09:31:38.585  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：1
09:31:38.629  [Thread-159, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:31:39.632  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:40.233  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:31:42.991  [Thread-159, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=2
09:31:43.171 D/PhotoCapture [Thread-159, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:31:43.282  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:45.488  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:46.292  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:48.673  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:49.302  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:50.634  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:31:50.643  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=2
09:31:50.650  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：2
09:31:50.686  [Thread-163, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:1)]: 拍摄：循环询问相机是否可以进行下一次拍照开始
09:31:51.688  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:52.303  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:31:54.920  [Thread-163, PreviewPresenter$PreviewHandler.lambda$handleMessage$3(:8)]: 拍摄：循环询问相机是否可以进行下一次拍照结束：开始拍照：motorState1=2,shotTimes1=3
09:31:55.097 D/PhotoCapture [Thread-163, CameraAction.PhotoCapture$CaptureThread.run(PhotoCapture.java:34)]: 拍摄：触发拍摄动作（triggerCapturePhoto）
09:31:55.312  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:31:57.352  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:31:58.323  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:32:00.475  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:32:01.335  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 2
09:32:02.518  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：1
09:32:02.526  [main, PreviewPresenter$PreviewHandler.handleMessage(:63)]: 拍摄：单次拍照完成（EVENT_CAPTURE_COMPLETED），countTimes=3
09:32:02.532  [main, PreviewPresenter$PreviewHandler.handleMessage(:72)]: 拍摄：获取当前拍摄进度（0xD761）：3
09:32:02.544  [main, PreviewPresenter$PreviewHandler.handleMessage(:82)]: 拍摄：理论上上拍摄完毕（countTimes == 4）
09:32:02.550  [Thread-166, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:2)]: 拍摄：循环询问相机是否已复位开始
09:32:03.574  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：7
09:32:04.341  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:32:04.387 D/PbDownloadManager [arch_disk_io_1, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:5)]: 开始HDR，当前模式：多线程模式
09:32:04.396 D/PbDownloadManager [arch_disk_io_1, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:7)]: 开始HDR，多线程模式，线程池数量：3
09:32:07.342  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:32:10.345  [Thread-132, PreviewPresenter.lambda$startA6CaptureTimer$19(:6)]: 拍摄：motorState = 1
09:32:10.889  [Thread-166, PreviewPresenter.cancelA6CaptureTimer(:2)]: 拍摄：中断计时器
09:32:10.892  [Thread-166, PreviewPresenter$PreviewHandler.lambda$handleMessage$0(:12)]: 拍摄：循环询问相机是否已复位结束：相机已复位
09:32:15.770 D/PbDownloadManager [arch_disk_io_1, CameraAction.PbDownloadManager.startHdr(PbDownloadManager.java:27)]: HDR 结束：hdrList.size=4
09:32:15.901 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:10)]: 从标定文件中获取到的标定参数：
09:32:15.906 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.getNewestZdStr(StitchUtils.java:15)]: 获取临时标定参数失败：length=0; index=5
09:32:15.909 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:48)]: 开始拼接，拼接参数={"calistr":"Gv93ZluzegyHGHI+J+SWoMCChNdYR4O2UrlSPxhqA71EE3dgGCgJ9zzoXp6Izrl\/xTZOubL0lFcfij7YMhBCmx+7uD5RN48P14cPbO+JFXX2TjDAjnlX\/652WQR66Mf\/iMic+5hLWPJvNu8mJa9U1nTV8jSfSShodJ4kNC0\/QJV7sw3ovEXENqBtcdaZ801O5m5qEMlvS6gj5GvncnOy9HhU0Ll1AdibtkFQkLTyMXtGcKxsfLyiRCcU766vjrO4cValVhuY8nRNJ\/F2dcXHJPDylDckcDEIx5j1C2lpW0R3Bsttj4tgZWcSNWhpfO6Gnc\/2EK9QM20wBt13e4OOiFEEVYl8F\/HoGRqtU9izt+KXHip0KWz8mjm6e7wwjk6sXimnjpinFCsFj9xaBaOHFBWSsWMT1J9BfkReISW+dvUUkA25AdfZTqnrSQFQt7t0KukLQQazsJnJHseVF7i5uVjnQ\/9nlcauZqBIQ+SXiVas\/91bSCaQKg3E5WWNWHQpRe8D0ezlE5lbCaAEvTnLZJJEaZVGYgwwHKRnuy8VCaQQ90TpzIYnI+f65Nh7gBFg309ZPluaOcs7CFH7W7QpyGBoUqMIn6RCnN9ZdAM4aj3LSTKevoyPZdFeB5DXqrTLUnpcsSVuy8NMFsRRo2eInLLBLb5UTSasH2kVUgwxZLYvpuRwq++BQJDh4tQXIM5nkLiMFdV9JV\/Alj8gxRFoSGW\/R0fX\/27fwVH4VtsOlk9q86r4pF\/H3KVWa1lCDT57LjiVONqm9FozCEcQxzZZCYCFOuW4uxecRx37AxbL8pqKnehfH1gYHqeceOFBdHUcekzKxaGNtU6YgE5P5zDxEzMsVDXFkhdamfPH4mzwfZJjxxM9Lz1foDHVHacZm2tGNz7Mr1ryP34sY69bLICq2Eh5hT1f82C\/la+J4DEQrqM8XwEc\/v0QsxolqrNjew\/hd6QslkXgvGM4p9Fcj2ehcCifYKotm6Cp97y3w0mTFj2v0fqw3IG5qmZY5kjor2YtZTiPbQxuar\/+N6WUxwlas03heyqmJyJHcqDIbJWSTY9j\/4n2B7AajllT+YPOTQyHKhXChBQw\/5p7PF\/HOJ70339fy+LvAdXkNzVifF8mgtgC27ckMNdWc+xw8yaOtustTlL8ihrSeoL0BTMndBanxzhM7WvBWZbOJx7gT3CwpV8SHOXq3MT0NhZPGeozfTGgQXFYEX9kk9sfcjlGMksdw0kitfcEfQr+vRZ3OxRDrk8hIMtDKJ2s3kByheu2qBZHIdw8OArZLYVCp7YgE72qg5R0Ohn0i1Y1CNkNu6Krd\/1uODNB0F6Ivqo8Qj\/F31ukKq5ZW+bT3TvSNS1gdqnnmQc2LQ2poaWKFSyj6UsigJXmGrOlpPnP3iPpK9\/FWrggYc+mnY+peCJ3UihMQZaHHvNAssBr\/kwIW3Jmk2svP\/QIMYeN6R9q1hR0C7TMZ2jBhNsi8c\/ktkynuCWNcVNV9QodlXSP0WMWmo5O\/5hs1h1o4JmVi+LKpnTg5evYlFYQJGAR7D3ldHqrKEQPjV5ooBTYuizEqZgvUzkQ2mzjjvcmQp2zHlMedpg5oJLhidZ6TCXLS2RYK+bGcfR1+f1eOHfPEjLJsXZ4KbzHdzi9iIuq++Qf\/K6mZGdYKtBZL0axXvV4yYaqsb0CfsfG0EnmlgH4459LfJlhVxFoByFWg70=","accesskey":"ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO\/GJcaKw==","cachefile":"\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/","img":["\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093120_34621238593927_381.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093120_34622169742156_382.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093120_34622128440593_383.jpg","\/data\/user\/0\/com.ijoyer.mobilecam\/cache\/fusion20250805_093120_34625943529706_381.jpg"],"dstwidth":8000,"dstheight":4000,"isopt":0,"ismulti":1,"mutiluv":1}
09:32:21.175 D/StitchUtils [arch_disk_io_1, com.detu.szStitch.StitchUtils.stitchExecHdr(StitchUtils.java:50)]: 拼接结束，拼接结果=0
09:32:32.169 D/PanoramaPlayerActivity [main, com.ijoyer.camera.activity.PanoramaPlayerActivity$7.onSuccess(PanoramaPlayerActivity.java:4)]: PanoramaPlayerActivity HMS Image Vision API 初始化成功
09:33:26.862  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:33:26.869  [main, PreviewPresenter.refreshBatteryLevel(:4)]: 刷新电池电量：75
09:33:50.212  [main, PreviewPresenter$PreviewHandler.handleMessage(:2)]: 收到相机发过来的事件：8
09:33:50.781 D/PreviewActivity [main, com.icatch.mobilecam.ui.activity.PreviewActivity.onDestroy(PreviewActivity.java:4)]: PreviewActivity:正常销毁

// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.3.50'
//    Gemini 建议升级到这个版本
//    ext.kotlin_version = '1.8.20'
    repositories {
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://www.jitpack.io" }
        maven { url  'https://repo1.maven.org/maven2/'}
        google()
        maven { url 'https://developer.huawei.com/repo/' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
//        classpath 'com.android.tools.build:gradle:3.3.2'
        classpath 'com.google.gms:google-services:4.3.4'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files

//        classpath 'com.bugtags.library:bugtags-gradle:2.1.5'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.0'
        classpath 'com.huawei.agconnect:agcp:1.6.0.300'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'http://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url "https://www.jitpack.io" }
        maven { url  'https://repo1.maven.org/maven2/'}
        google()
        maven { url 'https://developer.huawei.com/repo/' }

        maven { url "https://chaquo.com/maven" }
    }
}

apply from: "configs.gradle"

task clean(type: Delete) {
    delete rootProject.buildDir
}
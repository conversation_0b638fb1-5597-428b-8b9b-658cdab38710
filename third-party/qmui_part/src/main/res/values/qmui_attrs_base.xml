<?xml version="1.0" encoding="utf-8"?><!--
 <PERSON><PERSON> is pleased to support the open source community by making QMUI_Android available.

 Copyright (C) 2017-2018 THL A29 Limited, a Tencent company. All rights reserved.

 Licensed under the MIT License (the "License"); you may not use this file except in
 compliance with the License. You may obtain a copy of the License at

 http://opensource.org/licenses/MIT

 Unless required by applicable law or agreed to in writing, software distributed under the License is
 distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 either express or implied. See the License for the specific language governing permissions and
 limitations under the License.
-->

<resources>

    <!--**********************************************
    *                qmui common color               *
    **********************************************-->
    <attr name="qmui_config_color_blue" format="color" />
    <attr name="qmui_config_color_red" format="color" />
    <attr name="qmui_config_color_black" format="color" />
    <attr name="qmui_config_color_link" format="color" />
    <attr name="qmui_config_color_pressed" format="color" />

    <attr name="qmui_config_color_gray_1" format="color" />
    <attr name="qmui_config_color_gray_2" format="color" />
    <attr name="qmui_config_color_gray_3" format="color" />
    <attr name="qmui_config_color_gray_4" format="color" />
    <attr name="qmui_config_color_gray_5" format="color" />
    <attr name="qmui_config_color_gray_6" format="color" />
    <attr name="qmui_config_color_gray_7" format="color" />
    <attr name="qmui_config_color_gray_8" format="color" />
    <attr name="qmui_config_color_gray_9" format="color" />

    <attr name="qmui_alpha_pressed" format="float" />
    <attr name="qmui_alpha_disabled" format="float" />

    <attr name="qmui_general_shadow_elevation" format="dimension" />
    <attr name="qmui_general_shadow_alpha" format="float" />


</resources>
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="QMUILayout">
        <attr name="android:maxWidth" />
        <attr name="android:maxHeight" />
        <attr name="android:minWidth" />
        <attr name="android:minHeight" />
        <attr name="qmui_bottomDividerHeight" format="dimension" />
        <attr name="qmui_bottomDividerColor" format="color|reference" />
        <attr name="qmui_bottomDividerInsetLeft" format="dimension" />
        <attr name="qmui_bottomDividerInsetRight" format="dimension" />
        <attr name="qmui_topDividerHeight" format="dimension" />
        <attr name="qmui_topDividerColor" format="color|reference" />
        <attr name="qmui_topDividerInsetLeft" format="dimension" />
        <attr name="qmui_topDividerInsetRight" format="dimension" />
        <attr name="qmui_leftDividerWidth" format="dimension" />
        <attr name="qmui_leftDividerColor" format="color|reference" />
        <attr name="qmui_leftDividerInsetTop" format="dimension" />
        <attr name="qmui_leftDividerInsetBottom" format="dimension" />
        <attr name="qmui_rightDividerWidth" format="dimension" />
        <attr name="qmui_rightDividerColor" format="color|reference" />
        <attr name="qmui_rightDividerInsetTop" format="dimension" />
        <attr name="qmui_rightDividerInsetBottom" format="dimension" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" format="color|reference" />
        <attr name="qmui_hideRadiusSide" format="enum">
            <enum name="none" value="0" />
            <enum name="top" value="1" />
            <enum name="right" value="2" />
            <enum name="bottom" value="3" />
            <enum name="left" value="4" />
        </attr>
        <attr name="qmui_showBorderOnlyBeforeL" format="boolean" />
        <attr name="qmui_shadowElevation" format="dimension" />
        <attr name="qmui_useThemeGeneralShadowElevation" format="boolean" />
        <attr name="qmui_shadowAlpha" format="float" />
        <attr name="qmui_outlineInsetTop" format="dimension" />
        <attr name="qmui_outlineInsetLeft" format="dimension" />
        <attr name="qmui_outlineInsetRight" format="dimension" />
        <attr name="qmui_outlineInsetBottom" format="dimension" />
        <attr name="qmui_outlineExcludePadding" format="boolean" />
    </declare-styleable>

    <declare-styleable name="QMUIButton">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
    </declare-styleable>

    <declare-styleable name="QMUIConstraintLayout">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
    </declare-styleable>

    <declare-styleable name="QMUIFrameLayout">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
    </declare-styleable>

    <declare-styleable name="QMUILinearLayout">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
    </declare-styleable>

    <declare-styleable name="QMUIPriorityLinearLayout">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
    </declare-styleable>

    <declare-styleable name="QMUIRelativeLayout">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
    </declare-styleable>

    <declare-styleable name="QMUITextView">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
    </declare-styleable>
    <declare-styleable name="QMUIMediumTextView">
        <attr name="qmui_bottomDividerHeight" />
        <attr name="qmui_bottomDividerColor" />
        <attr name="qmui_bottomDividerInsetLeft" />
        <attr name="qmui_bottomDividerInsetRight" />
        <attr name="qmui_topDividerHeight" />
        <attr name="qmui_topDividerColor" />
        <attr name="qmui_topDividerInsetLeft" />
        <attr name="qmui_topDividerInsetRight" />
        <attr name="qmui_leftDividerWidth" />
        <attr name="qmui_leftDividerColor" />
        <attr name="qmui_leftDividerInsetTop" />
        <attr name="qmui_leftDividerInsetBottom" />
        <attr name="qmui_rightDividerWidth" />
        <attr name="qmui_rightDividerColor" />
        <attr name="qmui_rightDividerInsetTop" />
        <attr name="qmui_rightDividerInsetBottom" />
        <attr name="qmui_radius" />
        <attr name="qmui_borderColor" />
        <attr name="qmui_borderWidth" />
        <attr name="qmui_outerNormalColor" />
        <attr name="qmui_hideRadiusSide" />
        <attr name="qmui_showBorderOnlyBeforeL" />
        <attr name="qmui_shadowElevation" />
        <attr name="qmui_useThemeGeneralShadowElevation" />
        <attr name="qmui_shadowAlpha" />
        <attr name="qmui_outlineInsetTop" />
        <attr name="qmui_outlineInsetLeft" />
        <attr name="qmui_outlineInsetRight" />
        <attr name="qmui_outlineInsetBottom" />
        <attr name="qmui_outlineExcludePadding" />
        <attr name="isMedium" format="boolean" />
    </declare-styleable>
</resources>
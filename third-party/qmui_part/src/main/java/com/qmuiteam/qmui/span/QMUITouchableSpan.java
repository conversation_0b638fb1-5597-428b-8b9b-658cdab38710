/*
 * Tencent is pleased to support the open source community by making QMUI_Android available.
 *
 * Copyright (C) 2017-2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the MIT License (the "License"); you may not use this file except in
 * compliance with the License. You may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is
 * distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.qmuiteam.qmui.span;

import android.text.TextPaint;
import android.text.style.ClickableSpan;
import android.view.View;

import androidx.annotation.ColorInt;
import androidx.core.view.ViewCompat;

import com.qmuiteam.qmui.link.ITouchableSpan;
import com.qmuiteam.qmui.util.QMUIResHelper;

/**
 * 可 Touch 的 Span，在 {@link #setPressed(boolean)} 后根据是否 pressed 来触发不同的UI状态
 * <p>
 * 提供设置 span 的文字颜色和背景颜色的功能, 在构造时传入
 * </p>
 */
public abstract class QMUITouchableSpan extends ClickableSpan implements ITouchableSpan {
    private static final String TAG = "QMUITouchableSpan";
    private boolean mIsPressed;
    @ColorInt
    private int mNormalBackgroundColor;
    @ColorInt
    private int mPressedBackgroundColor;
    @ColorInt
    private int mNormalTextColor;
    @ColorInt
    private int mPressedTextColor;

    private int mNormalBgAttr;
    private int mPressedBgAttr;
    private int mNormalTextColorAttr;
    private int mPressedTextColorAttr;

    private boolean mIsNeedUnderline = false;

    public QMUITouchableSpan(@ColorInt int normalTextColor,
                             @ColorInt int pressedTextColor,
                             @ColorInt int normalBackgroundColor,
                             @ColorInt int pressedBackgroundColor) {
        mNormalTextColor = normalTextColor;
        mPressedTextColor = pressedTextColor;
        mNormalBackgroundColor = normalBackgroundColor;
        mPressedBackgroundColor = pressedBackgroundColor;
    }

    public QMUITouchableSpan(View initFollowSkinView,
                             int normalTextColorAttr, int pressedTextColorAttr,
                             int normalBgAttr, int pressedBgAttr) {
        mNormalBgAttr = normalBgAttr;
        mPressedBgAttr = pressedBgAttr;
        mNormalTextColorAttr = normalTextColorAttr;
        mPressedTextColorAttr = pressedTextColorAttr;
        if (normalTextColorAttr != 0) {
            mNormalTextColor = QMUIResHelper.getAttrColor(initFollowSkinView.getContext(), normalTextColorAttr);
        }
        if (pressedTextColorAttr != 0) {
            mPressedTextColor = QMUIResHelper.getAttrColor(initFollowSkinView.getContext(), pressedTextColorAttr);
        }
        if (normalBgAttr != 0) {
            mNormalBackgroundColor = QMUIResHelper.getAttrColor(initFollowSkinView.getContext(), normalBgAttr);
        }
        if (pressedBgAttr != 0) {
            mPressedBackgroundColor = QMUIResHelper.getAttrColor(initFollowSkinView.getContext(), pressedBgAttr);
        }
    }

    public abstract void onSpanClick(View widget);

    @Override
    public final void onClick(View widget) {
        if (ViewCompat.isAttachedToWindow(widget)) {
            onSpanClick(widget);
        }
    }

    public int getNormalBackgroundColor() {
        return mNormalBackgroundColor;
    }

    public int getNormalTextColor() {
        return mNormalTextColor;
    }

    public void setNormalTextColor(int normalTextColor) {
        mNormalTextColor = normalTextColor;
    }

    public int getPressedBackgroundColor() {
        return mPressedBackgroundColor;
    }

    public int getPressedTextColor() {
        return mPressedTextColor;
    }

    public void setPressedTextColor(int pressedTextColor) {
        mPressedTextColor = pressedTextColor;
    }

    public boolean isPressed() {
        return mIsPressed;
    }

    public void setPressed(boolean isSelected) {
        mIsPressed = isSelected;
    }

    public void setIsNeedUnderline(boolean isNeedUnderline) {
        mIsNeedUnderline = isNeedUnderline;
    }

    public boolean isNeedUnderline() {
        return mIsNeedUnderline;
    }

    @Override
    public void updateDrawState(TextPaint ds) {
        ds.setColor(mIsPressed ? mPressedTextColor : mNormalTextColor);
        ds.bgColor = mIsPressed ? mPressedBackgroundColor
                : mNormalBackgroundColor;
        ds.setUnderlineText(mIsNeedUnderline);
    }

}

/*
 * <PERSON><PERSON> is pleased to support the open source community by making QMUI_Android available.
 *
 * Copyright (C) 2017-2018 THL A29 Limited, a Tencent company. All rights reserved.
 *
 * Licensed under the MIT License (the "License"); you may not use this file except in
 * compliance with the License. You may obtain a copy of the License at
 *
 * http://opensource.org/licenses/MIT
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is
 * distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.qmuiteam.qmui.layout;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.text.TextPaint;
import android.util.AttributeSet;

import com.qmuiteam.qmui.R;

/**
 * 字体修改
 */

public class QMUIMediumTextView extends QMUITextView {
    public final static boolean USE_FONT_MEDIUM = true;
    /**
     * 当前是否加粗
     */
    private boolean isMedium = true;

    public static final float STROKE_WIDTH = 0.618f;

    private boolean hasMediumFontFamily;

    public QMUIMediumTextView(Context context) {
        super(context);
        init(context, null, 0);
    }

    public QMUIMediumTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs, 0);
    }

    public QMUIMediumTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs, defStyleAttr);
    }

    private void init(Context context, AttributeSet attrs, int defStyleAttr) {
        String familyName = "sans-serif-condensed-medium";
        Typeface mediumFont = Typeface.create(familyName, Typeface.NORMAL);
        hasMediumFontFamily = USE_FONT_MEDIUM && mediumFont != null;

        TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.QMUIMediumTextView, defStyleAttr, 0);
        boolean isMedium = a.getBoolean(R.styleable.QMUIMediumTextView_isMedium, true);
        setMedium(isMedium);
        a.recycle();
    }

    @Override
    protected void onDraw(Canvas canvas) {
        if (!hasMediumFontFamily) {
            //获取当前控件的画笔
            TextPaint paint = getPaint();
            paint.setStyle(Paint.Style.FILL_AND_STROKE);
            //设置画笔的描边宽度值
            paint.setStrokeWidth(isMedium ? STROKE_WIDTH : 0);
        }

        super.onDraw(canvas);
    }

    public void setMedium(boolean isMedium) {
        this.isMedium = isMedium;
        if (hasMediumFontFamily) {
            String familyName = "sans-serif-condensed-medium";
            Typeface mediumFont = Typeface.create(familyName, Typeface.NORMAL);
            setTypeface(isMedium ? mediumFont : null);
        } else {
            this.invalidate();//刷新一下布局
        }

    }

    public boolean isMedium() {
        return isMedium;
    }
}

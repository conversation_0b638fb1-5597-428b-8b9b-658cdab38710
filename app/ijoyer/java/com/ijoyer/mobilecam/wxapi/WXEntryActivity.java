package com.ijoyer.mobilecam.wxapi;

import android.content.Intent;

import androidx.annotation.Nullable;

import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.umeng.socialize.UMShareAPI;
import com.umeng.socialize.weixin.view.WXCallbackActivity;

///微信回调WXCallbackActivity
public class WXEntryActivity extends WXCallbackActivity {
    private static OnWXResp onWXResp;

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (onWXResp == null){//防止微信回调的code 冲突（被友盟消费了）
            UMShareAPI.get(this).onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onResp(BaseResp resp) {
        if (resp != null && onWXResp != null) {
            onWXResp.onResp(resp);
            unregisterOnWXResp();
            finish();
        }else{
            super.onResp(resp);
        }
    }

    ///注册回调，单次有效
    public static void registerOnWXResp(OnWXResp resp) {
        onWXResp = resp;
    }

    ///释放掉回调，正常不需要手动释放
    public static void  unregisterOnWXResp() {
        onWXResp = null;
    }
    public interface OnWXResp {
        void onResp(BaseResp resp);
    }
}



package com.ijoyer.camera.http.net;

import java.util.HashMap;
import java.util.Map;


public class ReqParams {

    public static final String LOGIN_DATA = "login";
    public static final String TOKEN = "token";
    public static final String TOKEN_VOICE = "token_voice";
    public static final String PHONE = "phone";
    private static ReqParams ourInstance = null;
    private Map<String, Object> parasMap = new HashMap<>();


    public static ReqParams getInstance() {
        if (ourInstance == null) {
            synchronized (ReqParams.class) {
                if (ourInstance == null) {
                    ourInstance = new ReqParams();
                }
            }
        }
        return ourInstance;
    }

    private ReqParams() {
    }

    public Map<String, Object> refreshParamsMap() {
        return parasMap;
    }

}

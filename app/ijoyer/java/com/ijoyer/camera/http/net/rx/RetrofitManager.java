package com.ijoyer.camera.http.net.rx;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.ijoyer.camera.http.net.interceptor.CacheInterceptor;
import com.ijoyer.camera.http.net.interceptor.HeaderInterceptor;
import com.ijoyer.camera.http.net.interceptor.LoggingInterceptor;
import com.ijoyer.camera.http.net.interceptor.ParamsInterceptor;

import java.io.File;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import io.reactivex.schedulers.Schedulers;
import okhttp3.Cache;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Created by Administrator_LZH on 2017/5/22.
 */
public class RetrofitManager {
    private Retrofit.Builder retrofitBuilder;
    private OkHttpClient.Builder builder;
    private Gson mGson;

    public RetrofitManager() {
        mGson = new GsonBuilder().serializeNulls().create();
        retrofitBuilder = RetrofitBuilder.getInstance().getBuilder();
        builder = OkHttpClientManager.getInstance().getBuilder();
    }

    public RetrofitManager(int timeOut) {
        mGson = new GsonBuilder().serializeNulls().create();
        retrofitBuilder = RetrofitBuilder.getInstance().getBuilder();
        builder = new OkHttpClient.Builder()
                .connectTimeout(timeOut, TimeUnit.SECONDS)
                .readTimeout(timeOut, TimeUnit.SECONDS)
                .writeTimeout(timeOut, TimeUnit.SECONDS);
    }

    public RetrofitManager(String url) {
        if (!TextUtils.isEmpty(url)) {
            mGson = new GsonBuilder().serializeNulls().create();
            retrofitBuilder = new Retrofit.Builder();
            retrofitBuilder.baseUrl(url);
            retrofitBuilder.addConverterFactory(GsonConverterFactory.create(mGson))
                    .addCallAdapterFactory(RxJava2CallAdapterFactory.createWithScheduler(Schedulers.io()));
            builder = new OkHttpClient.Builder()
                    .connectTimeout(30, TimeUnit.SECONDS)
                    .readTimeout(30, TimeUnit.SECONDS)
                    .writeTimeout(30, TimeUnit.SECONDS)
                    .addInterceptor(ParamsInterceptor.getInstance())
                    .addInterceptor(LoggingInterceptor.getInstance());
        }
    }


    public RetrofitManager setHeader(HashMap params) {
        builder.addInterceptor(new HeaderInterceptor(params));
        builder.addInterceptor(LoggingInterceptor.getInstance());
        return this;
    }

    public <T> T getDefaultClient(Class<T> serviceClass) {
        OkHttpClient client = builder.build();
        return retrofitBuilder
                .client(client)
                .addConverterFactory(GsonConverterFactory.create(mGson))
                .addCallAdapterFactory(RxJava2CallAdapterFactory.createWithScheduler(Schedulers.io()))
                .build().create(serviceClass);
    }

    public <T> T getLongTimeClient(Class<T> serviceClass) {
        builder.connectTimeout(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS);

        OkHttpClient client = builder.build();
        return retrofitBuilder
                .client(client)
                .addConverterFactory(GsonConverterFactory.create(mGson))
                .addCallAdapterFactory(RxJava2CallAdapterFactory.createWithScheduler(Schedulers.io()))
                .build().create(serviceClass);
    }



    public <T> T getCacheClient(Class<T> serviceClass, File cacheFile) {
        OkHttpClient client = builder
                .cache(new Cache(cacheFile, 10 * 1024 * 1024))
                .addNetworkInterceptor(new CacheInterceptor())
                .build();
        return retrofitBuilder.client(client)
                .addConverterFactory(GsonConverterFactory.create(mGson))
                .addCallAdapterFactory(RxJava2CallAdapterFactory.createWithScheduler(Schedulers.io()))
                .build().create(serviceClass);
    }
}

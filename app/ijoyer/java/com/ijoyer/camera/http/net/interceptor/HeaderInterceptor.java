package com.ijoyer.camera.http.net.interceptor;


import com.ijoyer.camera.http.net.ReqParams;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * Created by Administrator_LZH on 2016/6/23.
 */
public class HeaderInterceptor implements Interceptor {
    private HashMap<String,String> params;

    public HeaderInterceptor(HashMap params){
        this.params=params;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        ReqParams reqParams = ReqParams.getInstance();
        Request original = chain.request();
        Request.Builder builder = original.newBuilder();
        Map<String, Object> refreshParamsMap = reqParams.refreshParamsMap();
        if(params!=null && params.size()>0){
            refreshParamsMap.putAll(params);
        }
        for (Map.Entry<String, Object> entry : refreshParamsMap.entrySet()) {
            builder.addHeader(entry.getKey(), String.valueOf(entry.getValue()));
        }
        builder.method(original.method(), original.body());
        return chain.proceed(builder.build());
    }

}

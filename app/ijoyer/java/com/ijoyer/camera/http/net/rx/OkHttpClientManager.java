package com.ijoyer.camera.http.net.rx;


import com.ijoyer.camera.http.net.interceptor.LoggingInterceptor;
import com.ijoyer.camera.http.net.interceptor.ParamsInterceptor;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;

/**
 * Created by Administrator_LZH on 2016/6/23.
 */
public class OkHttpClientManager {
    private static final String TAG = "OkHttpClientManager";
    private OkHttpClient.Builder builder;
    private static OkHttpClientManager instance = null;

    private OkHttpClientManager() {
        builder = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
//                .proxy(Proxy.NO_PROXY)//一定程度防止抓包
                .addInterceptor(ParamsInterceptor.getInstance())
                .addInterceptor(LoggingInterceptor.getInstance());
    }

    public static OkHttpClientManager getInstance() {
        if (instance == null) {
            synchronized (OkHttpClientManager.class) {
                if (instance == null) {
                    instance = new OkHttpClientManager();
                }
            }
        }
        return instance;
    }

    public OkHttpClient.Builder getBuilder() {
        return builder;
    }

}

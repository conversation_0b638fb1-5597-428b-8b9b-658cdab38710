package com.ijoyer.camera.http.net;

import android.content.Context;

import io.reactivex.subscribers.DisposableSubscriber;

public class CustomDisposableForJava<T> extends DisposableSubscriber<T> {

    private boolean isShowToast = true;
    private String TAG = "CustomDisposableForJava";


    public CustomDisposableForJava(Context mContext) {
    }


    @Override
    public void onNext(T t) {
    }

    @Override
    public void onError(Throwable e) {
    }

    @Override
    public void onComplete() {
        this.dispose();
    }

}

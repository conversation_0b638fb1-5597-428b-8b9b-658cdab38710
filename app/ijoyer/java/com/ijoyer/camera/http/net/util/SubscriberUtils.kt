package com.nickname.mm.http.net.util

import io.reactivex.subscribers.DisposableSubscriber
import java.util.*

class SubscriberUtils {

    companion object {
        val instance = SubscriberUtils()
        val subscribers = ArrayList<DisposableSubscriber<*>>()
    }

    fun addSubscriber(disposable: DisposableSubscriber<*>) {
        subscribers.add(disposable)
    }

    fun cancelAll() {
        val it = subscribers.iterator()
        while (it.hasNext()) {
            var item: DisposableSubscriber<*>? = it.next()
            item!!.dispose()
            it.remove()
            item = null
        }
    }
}
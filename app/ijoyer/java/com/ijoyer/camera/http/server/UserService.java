package com.ijoyer.camera.http.server;


import com.ijoyer.camera.http.net.bean.TokenBean;
import com.ijoyer.camera.http.net.bean.WxPayBean;

import java.util.HashMap;

import io.reactivex.Flowable;
import retrofit2.http.Body;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.PartMap;
import retrofit2.http.QueryMap;
import retrofit2.http.Url;

public interface UserService {

    @FormUrlEncoded
    @POST("/oauth2/v3/token")
    Flowable<TokenBean> getToken(@Field("grant_type") String grant_type, @Field("client_id") String client_id, @Field("client_secret") String client_secret);


    @POST("/api/pay/wxpay_test")
    Flowable<WxPayBean> getWxPay();
}

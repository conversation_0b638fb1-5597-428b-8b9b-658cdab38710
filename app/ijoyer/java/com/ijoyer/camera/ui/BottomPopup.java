package com.ijoyer.camera.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.icatch.mobilecam.utils.GlideUtils;
import com.ijoyer.camera.bean.FilterBean;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

import razerdp.basepopup.BasePopupWindow;

public class BottomPopup extends BasePopupWindow {
    private Context mContext;
    public BaseQuickAdapter<FilterBean, BaseViewHolder> adapter;
    private RecyclerView rv;
    public IndicatorSeekBar isbProgress;
    public ImageView ivSure;
    public TextView tvProgress;

    public BottomPopup(Context context) {
        super(context);
        this.setWidth(ScreenUtils.getScreenWidth());
        setPopupGravity(Gravity.BOTTOM);
        mContext = context;

        tvProgress = findViewById(R.id.tv_progress);
        rv = findViewById(R.id.rv_filter);
        isbProgress = findViewById(R.id.isb_progress);
        isbProgress.setProgress(0f);
        ivSure = findViewById(R.id.iv_sure);

        isbProgress.setOnSeekChangeListener(new OnSeekChangeListener() {
            @Override
            public void onSeeking(SeekParams seekParams) {
                if (seekParams.fromUser) {
                    tvProgress.setVisibility(View.VISIBLE);
                    tvProgress.setText("" + seekParams.progress);
                }
            }

            @Override
            public void onStartTrackingTouch(IndicatorSeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                tvProgress.setVisibility(View.GONE);
            }
        });

        adapter = new BaseQuickAdapter<FilterBean, BaseViewHolder>(R.layout.recycler_filter) {
            @SuppressLint("CheckResult")
            protected void convert(BaseViewHolder helper, FilterBean bean) {

                if ("0".equals(bean.type)){
                    CardView.LayoutParams layoutParams = (CardView.LayoutParams) helper.getView(R.id.iv_pic).getLayoutParams();
                    layoutParams.bottomMargin = ConvertUtils.dp2px(10);
                    layoutParams.leftMargin = ConvertUtils.dp2px(10);
                    layoutParams.rightMargin = ConvertUtils.dp2px(10);
                    layoutParams.topMargin = ConvertUtils.dp2px(10);
                } else {
                    CardView.LayoutParams layoutParams = (CardView.LayoutParams) helper.getView(R.id.iv_pic).getLayoutParams();
                    layoutParams.bottomMargin = ConvertUtils.dp2px(0);
                    layoutParams.leftMargin = ConvertUtils.dp2px(0);
                    layoutParams.rightMargin = ConvertUtils.dp2px(0);
                    layoutParams.topMargin = ConvertUtils.dp2px(0);
                }

                helper.setText(R.id.tv_name, bean.name);
                ImageView ivPic = helper.getView(R.id.iv_pic);
                ivPic.setImageResource(bean.icon);
                ImageView ivSelect = helper.getView(R.id.iv_select);



                if (bean.isSelect){
                    helper.getView(R.id.ll_no_filter).setBackgroundResource(R.drawable.shape_blue_line_5dp);
                } else {
                    helper.getView(R.id.ll_no_filter).setBackgroundResource(0);
                }

                helper.itemView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        for (int i = 0;i<adapter.getData().size();i++){
                            adapter.getData().get(i).isSelect = false;
                        }
                        bean.isSelect = true;
                        notifyDataSetChanged();

                        if ("0".equals(bean.type)){
                            isbProgress.setVisibility(View.INVISIBLE);
                        } else {
                            isbProgress.setVisibility(View.VISIBLE);
                            isbProgress.setProgress(80f);
                        }
                    }
               });
            }
        };
        rv.setLayoutManager(new GridLayoutManager(mContext,6));
        rv.setAdapter(adapter);
    }

    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_bottom);
    }

}

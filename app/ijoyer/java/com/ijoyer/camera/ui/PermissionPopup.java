package com.ijoyer.camera.ui;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import com.blankj.utilcode.util.ScreenUtils;
import com.ijoyer.mobilecam.R;

import razerdp.basepopup.BasePopupWindow;

public class PermissionPopup extends BasePopupWindow {
    private Context mContext;

    public TextView tvRight;

    public PermissionPopup(Context context) {
        super(context);
        this.setWidth((int) (ScreenUtils.getScreenWidth() * 0.8));
        setPopupGravity(Gravity.CENTER);
        mContext = context;
        tvRight = findViewById(R.id.tv_right);
        findViewById(R.id.tv_right).setOnClickListener(v -> dismiss());
        findViewById(R.id.tv_left).setOnClickListener(v -> dismiss());

    }

    public void setReqDesc(String reqDescStr) {
        if (reqDescStr != null) {
            ((TextView) (findViewById(R.id.tv_content))).setText(reqDescStr);
        }
    }

    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_permission);
    }

}

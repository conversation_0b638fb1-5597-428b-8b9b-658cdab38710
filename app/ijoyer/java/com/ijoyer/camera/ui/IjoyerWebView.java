package com.ijoyer.camera.ui;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Build;
import android.util.AttributeSet;

import com.ijoyer.camera.webview.BridgeWebView;


public class IjoyerWebView extends BridgeWebView {

    public IjoyerWebView(Context context) {
        super(getFixedContext(context));
    }

    public IjoyerWebView(Context context, AttributeSet attrs) {
        super(getFixedContext(context), attrs);
    }

    public IjoyerWebView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(getFixedContext(context), attrs, defStyleAttr);
    }

//    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
//    public IjoyerWebView(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
//        super(getFixedContext(context), attrs, defStyleAttr, defStyleRes);
//    }
//
//    public IjoyerWebView(Context context, AttributeSet attrs, int defStyleAttr, boolean privateBrowsing) {
//        super(getFixedContext(context), attrs, defStyleAttr, privateBrowsing);
//    }

    public static Context getFixedContext(Context context) {
        if (Build.VERSION.SDK_INT >= 21 && Build.VERSION.SDK_INT < 23) // Android Lollipop 5.0 & 5.1
            return context.createConfigurationContext(new Configuration());
        return context;
    }
}

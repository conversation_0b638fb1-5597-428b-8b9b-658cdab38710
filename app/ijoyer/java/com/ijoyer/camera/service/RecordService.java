package com.ijoyer.camera.service;
import android.annotation.TargetApi;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.Environment;
import android.os.IBinder;
import android.widget.Toast;
import com.blankj.utilcode.util.ScreenUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.ijoyer.mobilecam.R;

import java.io.File;
import java.nio.ByteBuffer;
import java.text.SimpleDateFormat;
import java.util.Date;
@TargetApi(Build.VERSION_CODES.LOLLIPOP)
public class RecordService extends Service {
    private static final String TAG = "RecordService";
    private MediaProjectionManager mediaProjectionManager; 
    private MediaProjection mediaProjection; 
    private VirtualDisplay virtualDisplay; 
    private String videoPath, videoName; 
    private int screenWidth, screenHeight, screenDensity; 
    private MediaCodec mediaCodec; 
    private MediaMuxer mediaMuxer; 
    private boolean isRecording = false; 
    private boolean isMuxerStarted = false; 
    private MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo(); 
    private int videoTrackIndex = -1; 
    private Context context;
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    @Override
    public void onCreate() {
        super.onCreate();
        context = this;
        videoPath = Environment.getExternalStorageDirectory().toString() + AppInfo.DOWNLOAD_PATH;
        mediaProjectionManager = PanoramaApp.getInstance().getMpMgr();
        screenWidth = ScreenUtils.getScreenWidth();
        screenHeight = ScreenUtils.getScreenHeight();
        screenDensity = ScreenUtils.getScreenDensityDpi();
        AppLog.d(TAG, "mScreenWidth=" + screenWidth + ", mScreenHeight=" + screenHeight);
        if (screenWidth >= 1000) { 
            screenWidth = screenWidth / 2;
            screenHeight = screenHeight / 2;
        }
        if (screenWidth % 2 != 0) { 
            screenWidth--;
        }
        if (screenHeight % 2 != 0) { 
            screenHeight--;
        }
    }
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        isRecording = intent.getBooleanExtra("isRecording", false);
        if (isRecording) {
            Toast.makeText(RecordService.this, getString(R.string.screen_recording_start), Toast.LENGTH_SHORT).show();
            recordStart(); 
        } else {
            Toast.makeText(RecordService.this, getString(R.string.screen_recording_completed) + videoPath + videoName, Toast.LENGTH_LONG).show();
        }
        return super.onStartCommand(intent, flags, startId);
    }
    private String prepare() {
        MediaFormat format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, screenWidth, screenHeight);
        format.setInteger(MediaFormat.KEY_BIT_RATE, 300 * 1024 * 8);
        format.setInteger(MediaFormat.KEY_FRAME_RATE, 20);
        format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface);
        format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 2);
        try {
            mediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_VIDEO_AVC);
            mediaCodec.configure(format, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }
    private void recordStart() {
        String result = prepare(); 
        if (result != null) {
            Toast.makeText(this, "准备录屏发生异常：" + result, Toast.LENGTH_SHORT).show();
            return;
        }
        if (mediaProjection == null) {
            mediaProjection = mediaProjectionManager.getMediaProjection(
                    PanoramaApp.getInstance().getResultCode(),
                    PanoramaApp.getInstance().getResultIntent()
            );
        }
        virtualDisplay = mediaProjection.createVirtualDisplay(
                "ScreenRecords",
                screenWidth,
                screenHeight,
                screenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                mediaCodec.createInputSurface(),
                null,
                null);
        mediaCodec.start(); 
        new RecordThread().start(); 
    }
    private class RecordThread extends Thread {
        @Override
        public void run() {
            try {
                AppLog.d(TAG, "RecordThread Start");
                createDir(videoPath); 
                videoName = getNowDateTime() + "RECORD" + ".mp4";
                mediaMuxer = new MediaMuxer(videoPath + videoName, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
                while (isRecording) { 
                    int index = mediaCodec.dequeueOutputBuffer(bufferInfo, 10000);
                    AppLog.d(TAG, "缓冲区的索引为" + index);
                    if (index == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) { 
                        if (isMuxerStarted) {
                            throw new IllegalStateException("输出格式已经发生变化");
                        }
                        MediaFormat newFormat = mediaCodec.getOutputFormat();
                        videoTrackIndex = mediaMuxer.addTrack(newFormat);
                        mediaMuxer.start();
                        isMuxerStarted = true;
                        AppLog.d(TAG, "新的输出格式是：" + newFormat.toString() + "，媒体转换器的轨道索引是" + videoTrackIndex);
                    } else if (index == MediaCodec.INFO_TRY_AGAIN_LATER) { 
                        Thread.sleep(50);
                    } else if (index >= 0) { 
                        if (!isMuxerStarted) {
                            throw new IllegalStateException("媒体转换器尚未添加格式轨道");
                        }
                        encodeToVideo(index); 
                        mediaCodec.releaseOutputBuffer(index, false);
                        MediaRefresh.scanFileAsync(context, videoPath + videoName);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                release(); 
            }
        }
    }
    private void encodeToVideo(int index) {
        ByteBuffer encoded = mediaCodec.getOutputBuffer(index);
        if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) { 
            bufferInfo.size = 0;
        }
        if (bufferInfo.size == 0) { 
            encoded = null;
        } else {
            AppLog.d(TAG, "缓冲区大小=" + bufferInfo.size + ", 持续时间=" + bufferInfo.presentationTimeUs + ", 偏移=" + bufferInfo.offset);
        }
        if (encoded != null) { 
            encoded.position(bufferInfo.offset);
            encoded.limit(bufferInfo.offset + bufferInfo.size);
            mediaMuxer.writeSampleData(videoTrackIndex, encoded, bufferInfo);
        }
    }
    private void release() {
        isRecording = false;
        isMuxerStarted = false;
        if (mediaCodec != null) {
            mediaCodec.stop(); 
            mediaCodec.release(); 
            mediaCodec = null;
        }
        if (virtualDisplay != null) {
            virtualDisplay.release(); 
            virtualDisplay = null;
        }
        if (mediaMuxer != null) {
            mediaMuxer.stop(); 
            mediaMuxer.release(); 
            mediaMuxer = null;
        }
    }
    @Override
    public void onDestroy() {
        release();
        if (mediaProjection != null) {
            mediaProjection.stop(); 
        }
        super.onDestroy();
    }
    private static void createDir(String path) {
        File dir = new File(path);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }
    private static String getNowDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss_");
        return sdf.format(new Date());
    }
}

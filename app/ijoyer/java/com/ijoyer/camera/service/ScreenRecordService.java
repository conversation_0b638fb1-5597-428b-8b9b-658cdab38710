package com.ijoyer.camera.service;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.CamcorderProfile;
import android.media.MediaRecorder;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Environment;
import android.os.IBinder;
import android.widget.Toast;
import com.blankj.utilcode.util.SPUtils;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.ijoyer.camera.widget.SettingRecordDialog;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
public class ScreenRecordService extends Service {
    private static final String TAG = "ScreenRecordingService";
    private int mScreenWidth;
    private int mScreenHeight;
    private int mScreenDensity;
    private int mResultCode;
    private Intent mResultData;
    private boolean isVideoSd;
    private boolean isAudio;
    private MediaProjection mMediaProjection;
    private MediaRecorder mMediaRecorder;
    private VirtualDisplay mVirtualDisplay;
    private String videoPath, videoName;
    private SPUtils spUtils;
    private SettingRecordDialog.RecordType type;
    @Override
    public void onCreate() {
        super.onCreate();
        AppLog.i(TAG, "Service onCreate() is called");
        videoPath = Environment.getExternalStorageDirectory().toString() + AppInfo.DOWNLOAD_PATH;
        createDir(videoPath);
        spUtils = SPUtils.getInstance("record_param");
        type = SettingRecordDialog.RecordType.values()[spUtils.getInt("KEY_RECORD", SettingRecordDialog.DEFAULT_RECORD.ordinal())];
    }
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        AppLog.i(TAG, "Service onStartCommand() is called");
        mResultCode = intent.getIntExtra("code", -1);
        mResultData = intent.getParcelableExtra("data");
        mScreenWidth = intent.getIntExtra("width", 720);
        mScreenHeight = intent.getIntExtra("height", 1280);
        mScreenDensity = intent.getIntExtra("density", 1);
        isVideoSd = intent.getBooleanExtra("quality", true);
        isAudio = intent.getBooleanExtra("audio", true);
        mMediaProjection = createMediaProjection();
        mMediaRecorder = createMediaRecorder();
        mVirtualDisplay = createVirtualDisplay();
        mMediaRecorder.start();
        return Service.START_NOT_STICKY;
    }
    private MediaProjection createMediaProjection() {
        AppLog.i(TAG, "Create MediaProjection");
        return ((MediaProjectionManager) getSystemService(Context.MEDIA_PROJECTION_SERVICE)).getMediaProjection(mResultCode, mResultData);
    }
    private CamcorderProfile getBestCamcorderProfile() {
        final int videoType = 3;
        int quality = CamcorderProfile.QUALITY_HIGH;
        if (videoType == 1 && CamcorderProfile.hasProfile(CamcorderProfile.QUALITY_1080P)) {
            quality = CamcorderProfile.QUALITY_1080P;
        } else if (videoType == 2 && CamcorderProfile.hasProfile(CamcorderProfile.QUALITY_720P)) {
            quality = CamcorderProfile.QUALITY_720P;
        } else if (videoType == 3 && CamcorderProfile.hasProfile(CamcorderProfile.QUALITY_480P)) {
            quality = CamcorderProfile.QUALITY_480P;
        } else {
            quality = CamcorderProfile.QUALITY_CIF;
        }
        return CamcorderProfile.get(quality);
    }
    private MediaRecorder createMediaRecorder() {
        AppLog.i(TAG, "Create MediaRecorder");
        videoName = getNowDateTime() + "_RECORD" + ".mp4";
        MediaRecorder mediaRecorder = new MediaRecorder();
        if (isAudio) {
            mediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
        }
        mediaRecorder.setVideoSource(MediaRecorder.VideoSource.SURFACE);
        mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.MPEG_4);
        mediaRecorder.setOutputFile(videoPath + videoName);
        mediaRecorder.setVideoSize(mScreenWidth, mScreenHeight);
        mediaRecorder.setVideoEncoder(MediaRecorder.VideoEncoder.H264);
        if (isAudio) {
            mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
            mediaRecorder.setAudioEncodingBitRate(128000);
            mediaRecorder.setAudioSamplingRate(48000);
            mediaRecorder.setAudioChannels(2);
        }
        int bitRate;
        switch (type) {
            case SD:
                mediaRecorder.setVideoEncodingBitRate((int) (4500000 * 0.7));
                mediaRecorder.setVideoFrameRate(30);
                bitRate = mScreenWidth * mScreenHeight / 1000;
                break;
            case HD:
                mediaRecorder.setVideoEncodingBitRate((int) (9000000 * 1.03));
                mediaRecorder.setVideoFrameRate(30);
                bitRate = mScreenWidth * mScreenHeight / 1000;
                break;
            case FHD:
                mediaRecorder.setVideoEncodingBitRate((int) (17000000 * 1.03));
                mediaRecorder.setVideoFrameRate(30);
                bitRate = 5 * mScreenWidth * mScreenHeight / 1000;
                break;
            default:
                if (isVideoSd) {
                    mediaRecorder.setVideoEncodingBitRate(3 * mScreenWidth * mScreenHeight);
                    mediaRecorder.setVideoFrameRate(30);
                    bitRate = mScreenWidth * mScreenHeight / 1000;
                } else {
                    mediaRecorder.setVideoEncodingBitRate(5 * mScreenWidth * mScreenHeight);
                    mediaRecorder.setVideoFrameRate(60);
                    bitRate = 5 * mScreenWidth * mScreenHeight / 1000;
                }
                break;
        }
        try {
            mediaRecorder.prepare();
        } catch (IllegalStateException | IOException e) {
            e.printStackTrace();
        }
        AppLog.i(TAG, "Audio: " + isAudio + ", SD video: " + isVideoSd + ", BitRate: " + bitRate + "kbps");
        return mediaRecorder;
    }
    private VirtualDisplay createVirtualDisplay() {
        AppLog.i(TAG, "Create VirtualDisplay");
        return mMediaProjection.createVirtualDisplay(TAG, mScreenWidth, mScreenHeight, mScreenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR, mMediaRecorder.getSurface(), null, null);
    }
    @Override
    public void onDestroy() {
        Toast.makeText(this, "结束录屏：" + videoPath + videoName, Toast.LENGTH_LONG).show();
        MediaRefresh.scanFileAsync(this, videoPath + videoName);
        super.onDestroy();
        AppLog.i(TAG, "Service onDestroy");
        if (mVirtualDisplay != null) {
            mVirtualDisplay.release();
            mVirtualDisplay = null;
        }
        if (mMediaRecorder != null) {
            mMediaRecorder.setOnErrorListener(null);
            mMediaProjection.stop();
            mMediaRecorder.reset();
        }
        if (mMediaProjection != null) {
            mMediaProjection.stop();
            mMediaProjection = null;
        }
    }
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    private static void createDir(String path) {
        File dir = new File(path);
        if (!dir.exists()) {
            dir.mkdirs();
        }
    }
    private static String getNowDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return sdf.format(new Date());
    }
}

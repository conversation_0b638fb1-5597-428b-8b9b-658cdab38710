package com.ijoyer.camera.Presenter;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.hardware.usb.UsbDevice;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;

import com.blankj.utilcode.util.CleanUtils;
import com.blankj.utilcode.util.LogUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Function.GlobalEvent;
import com.icatch.mobilecam.Function.SDKEvent;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.CameraType;
import com.icatch.mobilecam.MyCamera.CommandSession;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.RemoteMultiPbPresenter;
import com.icatch.mobilecam.SdkApi.CameraProperties;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.AppInfo.AppSharedPreferences;
import com.icatch.mobilecam.data.AppInfo.ConfigureInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Mode.CameraNetworkMode;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.data.SystemInfo.HotSpot;
import com.icatch.mobilecam.data.SystemInfo.MWifiManager;
import com.icatch.mobilecam.data.entity.CameraSlot;
import com.icatch.mobilecam.data.entity.SearchedCameraInfo;
import com.icatch.mobilecam.data.entity.SelectedCameraInfo;
import com.icatch.mobilecam.db.CameraSlotSQLite;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Fragment.AddNewCamFragment;
import com.icatch.mobilecam.ui.activity.LaunchHelpActivity;
import com.icatch.mobilecam.ui.activity.LocalMultiPbActivity;
import com.icatch.mobilecam.ui.activity.PreviewActivity;
import com.icatch.mobilecam.ui.activity.PreviewActivityV2;
import com.icatch.mobilecam.ui.activity.RemoteMultiPbActivity;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.WifiNetworkSpecifierUtil;
import com.icatch.mobilecam.utils.imageloader.ICatchtekImageDownloader;
import com.icatch.mobilecam.utils.imageloader.ImageLoaderConfig;
import com.icatchtek.control.customer.type.ICatchCamEventID;
import com.icatchtek.control.customer.type.ICatchCamFeatureID;
import com.ijoyer.camera.Listener.WifiListener;
import com.ijoyer.camera.Presenter.Interface.BasePresenter;
import com.ijoyer.camera.data.Message.AppMessage;
import com.ijoyer.camera.ui.Interface.LaunchView;
import com.ijoyer.camera.ui.adapter.CameraSlotAdapter;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.R;
import com.tencent.bugly.crashreport.BuglyLog;

import java.util.ArrayList;
import java.util.LinkedList;

public class LaunchPresenter extends BasePresenter {
    private static final String TAG = LaunchPresenter.class.getSimpleName();
    private LaunchView launchView;
    private CameraSlotAdapter cameraSlotAdapter;
    public ArrayList<CameraSlot> camSlotList;
    private final LaunchHandler launchHandler = new LaunchHandler();
    private Activity activity;
    private GlobalEvent globalEvent;
    private LinkedList<SelectedCameraInfo> searchCameraInfoList;
    private static int cameraSlotPosition;
    private WifiListener wifiListener;

    public LaunchPresenter(Activity activity) {
        super(activity);
        this.activity = activity;
    }

    public void setView(LaunchView launchView) {
        this.launchView = launchView;
        initCfg();
        globalEvent = new GlobalEvent(launchHandler);
    }

    @Override
    public void initCfg() {
        GlobalInfo.getInstance().setCurrentApp(activity);
        activity.getWindow().setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        AppInfo.inputIp = AppSharedPreferences.readDataByName(activity, AppSharedPreferences.OBJECT_NAME_INPUT_IP);
        ConfigureInfo.getInstance().initCfgInfo(activity.getApplicationContext());
        GlobalInfo.getInstance().startScreenListener();
    }

    private String getCameraIp() {
        String ip = "";
        if (HotSpot.isApEnabled(activity)) {
            ip = HotSpot.getFirstConnectedHotIP();
        } else {
            ip = AppInfo.inputIp;
        }
        return ip;
    }

    public synchronized void launchCamera(final int position, final int cameraType) {
        AppLog.i(TAG, "launchCamera  position:" + position + " cameraType:" + cameraType);
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        if (camera != null && camera.isConnected()) {
            AppLog.i(TAG, "launchCamera  camera is connected.");
            return;
        }
        String cameraName = null;
        UsbDevice usbDevice = null;
        AppLog.d(TAG, "launchCamera position=" + position + " cameraType=" + cameraType);
        if (cameraType == CameraType.WIFI_CAMERA) {
            cameraName = MWifiManager.getSsid(activity);
        }
        if (camSlotList.get(position).isReady) {
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            final String finalWifiSsid1 = cameraName;
            final UsbDevice finalUsbDevice = usbDevice;
            new Thread(new Runnable() {
                public void run() {
                    if (cameraType == CameraType.WIFI_CAMERA) {
                        beginConnectCamera(position, getCameraIp(), finalWifiSsid1);
                    }
                }
            }).start();
        } else {
            if (camSlotList.get(position).isOccupied) {
                AppDialog.showDialogWarn(activity, activity.getString(R.string.text_please_connect_camera).replace("$1$", camSlotList.get(position).cameraName));
            } else if (!isRegistered(cameraName)) {
                MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
                final String finalWifiSsid = cameraName;
                final UsbDevice finalUsbDevice1 = usbDevice;
                new Thread(new Runnable() {
                    public void run() {
                        if (cameraType == CameraType.WIFI_CAMERA) {
                            beginConnectCamera(position, getCameraIp(), finalWifiSsid);
                        }
                    }
                }).start();
            } else {
                AppDialog.showDialogWarn(activity, activity.getString(R.string.text_camera_has_been_registered).replace("$1$", cameraName));
            }
        }
    }

    public synchronized void launchCamera(final int position, FragmentManager fm) {
        AppLog.i(TAG, "launchCamera  position = " + position);
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        if (camera != null && camera.isConnected()) {
            AppLog.i(TAG, "launchCamera  camera is connected.");
            return;
        }
        cameraSlotPosition = position;
        String wifiSsid = null;
        wifiSsid = MWifiManager.getSsid(activity);
        final int cameraType = camSlotList.get(position).cameraType;
        if (camSlotList.get(position).isReady) {
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            final String finalWifiSsid = wifiSsid;
            new Thread(new Runnable() {
                public void run() {
                    if (cameraType == CameraType.WIFI_CAMERA) {
                        beginConnectCamera(position, getCameraIp(), finalWifiSsid);
                    }
                }
            }).start();
        } else {
            if (camSlotList.get(position).isOccupied) {
                AppDialog.showDialogWarn(activity, activity.getString(R.string.text_please_connect_camera).replace("$1$", camSlotList.get(position).cameraName));
            } else {
                launchView.setLaunchLayoutVisibility(View.GONE);
                launchView.setLaunchSettingFrameVisibility(View.VISIBLE);
                launchView.setBackBtnVisibility(true);
                AddNewCamFragment addNewCamFragment = new AddNewCamFragment(activity.getApplicationContext(), launchHandler, position);
                FragmentTransaction ft = fm.beginTransaction();
                ft.replace(R.id.launch_setting_frame, addNewCamFragment, "other");
                ft.addToBackStack("tag");
                ft.commit();
            }
        }
    }

    public void removeCamera(int position) {
        AppLog.i(TAG, "remove camera position = " + position);
        CameraSlotSQLite.getInstance().deleteByPosition(position);
        loadListView();
        initCamSlotList();
    }

    public void loadListView() {
        initCamSlotList();
        if (cameraSlotAdapter != null) {
            cameraSlotAdapter.notifyDataSetInvalidated();
        }
        cameraSlotAdapter = new CameraSlotAdapter(GlobalInfo.getInstance().getAppContext(), camSlotList, launchHandler);
        launchView.setListViewAdapter(cameraSlotAdapter);
    }

    public void notifyListView() {
        if (cameraSlotAdapter != null) {
            cameraSlotAdapter.notifyDataSetChanged();
        }
    }

    private void setReadyState(boolean isReady, String cameraName) {
        if (cameraName == null || cameraName.equals("")) {
            return;
        }
        if(camSlotList == null){
            return;
        }
        for (CameraSlot temp : camSlotList) {
            if (temp.cameraName != null && temp.cameraName.equals(cameraName)) {
                temp.isReady = isReady;
                break;
            }
        }
    }

    private void resetWifiState() {
        if(camSlotList == null){
            return;
        }
        for (CameraSlot temp : camSlotList) {
            if (temp.cameraType != CameraType.USB_CAMERA) {
                temp.isReady = false;
            }
        }
    }

    private void showSearchCameraListSingleDialog() {
        if (searchCameraInfoList.isEmpty()) {
            return;
        }
        CharSequence title = "Please selectOrCancelAll camera";
        final CharSequence[] tempsearchCameraInfoList = new CharSequence[searchCameraInfoList.size()];
        for (int ii = 0; ii < tempsearchCameraInfoList.length; ii++) {
            tempsearchCameraInfoList[ii] = searchCameraInfoList.get(ii).cameraName + "\n" + searchCameraInfoList.get(ii).cameraIp + "          " +
                    CameraNetworkMode.getModeConvert(searchCameraInfoList.get(ii).cameraMode);
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        };
        showOptionDialogSingle(title, tempsearchCameraInfoList, 0, listener, true);
    }

    private void showOptionDialogSingle(CharSequence title, CharSequence[] items,
                                        int checkedItem, DialogInterface.OnClickListener listener, boolean
                                                cancelable) {
        AlertDialog optionDialog = new AlertDialog.Builder(activity).setTitle(title).setSingleChoiceItems(items, checkedItem, listener).create();
        optionDialog.setCancelable(cancelable);
        optionDialog.show();
    }

    private class LaunchHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case AppMessage.MESSAGE_CAMERA_CONNECT_FAIL:
                    MyProgressDialog.closeProgressDialog();
                    showHelpDialogWarn(activity, R.string.dialog_timeout_2);
                    break;
                case AppMessage.MESSAGE_CAMERA_CONNECT_SUCCESS:
                    MyProgressDialog.closeProgressDialog();
                    redirectToAnotherActivity(activity);
                    break;
                case AppMessage.MESSAGE_DELETE_CAMERA:
                    removeCamera(msg.arg1);
                    break;
                case AppMessage.MESSAGE_CAMERA_SCAN_TIME_OUT:
                    CommandSession.stopDeviceScan();
                    globalEvent.delGlobalEventListener(ICatchCamEventID.ICATCH_EVENT_DEVICE_SCAN_ADD, false);
                    MyProgressDialog.closeProgressDialog();
                    if (searchCameraInfoList.isEmpty()) {
                        MyToast.show(activity, R.string.alert_no_camera_found);
                        break;
                    }
                    showSearchCameraListSingleDialog();
                    break;
                case SDKEvent.EVENT_SEARCHED_NEW_CAMERA:
                    LogUtil.e(msg.what + "EVENT_SEARCHED_NEW_CAMERA");
                    SearchedCameraInfo temp = (SearchedCameraInfo) msg.obj;
                    searchCameraInfoList.addLast(new SelectedCameraInfo(temp.cameraName, temp.cameraIp, temp.cameraMode, temp.uid));
                    break;
                case AppMessage.MESSAGE_CAMERA_CONNECTING_START:
                    launchView.fragmentPopStackOfAll();
                    int cameraType = msg.arg1;
                    int position = msg.arg2;
                    launchCamera(position, cameraType);
                    break;
                case AppMessage.MESSAGE_DISCONNECTED:
                    resetWifiState();
                    notifyListView();
                    updateMainLinkStatus();
                    //断开连接，清缓存
                    CleanUtils.cleanInternalCache();
                    CleanUtils.cleanExternalCache();
                    break;
                case AppMessage.MESSAGE_CONNECTED:
                    String ssid = MWifiManager.getCacheSsid(activity);
                    try {
                        if (ssid != null) {
                            if (!CameraUtils.isIjoyerCamera(ssid)) {
                                //只要非IJOYER设备清除缓存
                                CleanUtils.cleanInternalCache();
                                CleanUtils.cleanExternalCache();
                            } else {
//                                MyProgressDialog.showProgressDialog(activity);
//                                launchCameraForMain(true,2);
                            }
                            setReadyState(true, ssid);
                            notifyListView();
                            updateMainLinkStatus();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    break;
                case AppMessage.MESSAGE_CAMERA_CONNECTING_START_FOR_MAIN:
                    LogUtil.e(msg.what + "MESSAGE_CAMERA_CONNECTING_START_FOR_MAIN");
                    int type = msg.arg1;
                    int pos = msg.arg2;
                    launchCameraForMain(pos, type);
                    break;
                case AppMessage.MESSAGE_POPUP_WINDOW:
                    LogUtil.e(msg.what + "MESSAGE_POPUP_WINDOW");
                    MyProgressDialog.closeProgressDialog();
                    launchView.startPopupWindow();
                    break;
                case AppMessage.MESSAGE_CAMERA_CONNECTING_PROCESSING:
                    LogUtil.e(msg.what + "MESSAGE_CAMERA_CONNECTING_PROCESSING");
                    break;
                case AppMessage.MESSAGE_REFRESH_TXT_LINK_STATUS:
                    LogUtil.e(msg.what + "MESSAGE_REFRESH_TXT_LINK_STATUS");
                    break;
                case AppMessage.MESSAGE_NAVIGATION_TO_LOCAL_PB:
                    LogUtil.e(msg.what + "MESSAGE_NAVIGATION_TO_LOCAL_PB");
                    MyProgressDialog.closeProgressDialog();
                    Intent intent = new Intent();
                    intent.setClass(activity, LocalMultiPbActivity.class);
                    activity.startActivity(intent);
                    break;
            }
        }
    }

    private void showHelpDialogWarn(final Context context, int messageID) {
        AppLog.i(TAG, "showHelpDialogWarn");
        AlertDialog dialog = null;
        if (dialog != null) {
            dialog.dismiss();
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setIcon(R.drawable.warning).setTitle("Warning").setMessage(messageID);
        builder.setCancelable(false);
        builder.setNegativeButton(R.string.help, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                MyProgressDialog.closeProgressDialog();
                redirectToAnotherActivity(context, LaunchHelpActivity.class);
            }
        });
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        dialog = builder.create();
        dialog.show();
    }

    private synchronized void beginConnectCamera(int position, String ip, String wifiSsid) {
        checkBindNetwork(() -> beginConnectCameraAfterBindNetwork(position, ip, wifiSsid));
    }

    private void checkBindNetwork(@NonNull Runnable runAfterSuccess) {
        if (HotSpot.isApEnabled(activity)) {
            runAfterSuccess.run();
        } else {
            WifiNetworkSpecifierUtil.getInstance().bindToNetwork(activity, new WifiNetworkSpecifierUtil.OnCallback() {
                @Override
                public void onSuccess(String ssid) {
                    AppLog.d(TAG, "bindToNetwork Success");
                    runAfterSuccess.run();
                }

                @Override
                public void onError(int error) {
                    AppLog.d(TAG, "bindToNetwork Error: 网络绑定失败，错误码:" + error);
                    LogUtils.file( "bindToNetwork Error: 网络绑定失败，错误码:" + error);
                    MyProgressDialog.closeProgressDialog();
                }
            });
        }
    }

    private synchronized void beginConnectCameraAfterBindNetwork(int position, String ip, String wifiSsid) {
        AppLog.i(TAG, "beginConnectCamera position:" + position + " wifiSsid:" + wifiSsid);
        MyCamera currentCamera = CameraManager.getInstance().getCurCamera();
        if (currentCamera != null && currentCamera.isConnected()) {
            AppLog.i(TAG, "beginConnectCamera camera is connected.");
            return;
        }
        currentCamera = CameraManager.getInstance().createCamera(CameraType.WIFI_CAMERA, wifiSsid, ip, position, CameraNetworkMode.AP);
        if (!currentCamera.connect(true)) {
            launchHandler.obtainMessage(AppMessage.MESSAGE_CAMERA_CONNECT_FAIL).sendToTarget();
            return;
        }
        if (currentCamera.getCameraProperties().hasFunction(PropertyId.CAMERA_DATE)) {
            currentCamera.getCameraProperties().setCameraDate();
        }
        if (currentCamera.getCameraProperties().hasFunction(PropertyId.CAMERA_DATE_TIMEZONE)) {
            currentCamera.getCameraProperties().setCameraDateTimeZone();
        }
        CameraSlotSQLite.getInstance().update(new CameraSlot(position, true, wifiSsid, CameraType.WIFI_CAMERA, null, true));
        launchHandler.post(new Runnable() {
            @Override
            public void run() {
                MyProgressDialog.closeProgressDialog();
                redirectToAnotherActivity(activity);
            }
        });
    }

    public void redirectToAnotherActivity(Context context) {
        ICatchtekImageDownloader downloader = new ICatchtekImageDownloader(activity);
        ImageLoaderConfig.initImageLoader(activity.getApplicationContext(), downloader);
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        CameraProperties cameraProperties = null;
        if (camera != null) {
            cameraProperties = camera.getCameraProperties();
        }
        if (cameraProperties != null
                && cameraProperties.hasFunction(PropertyId.DEFALUT_TO_PREVIEW)
                && cameraProperties.checkCameraCapabilities(ICatchCamFeatureID.ICH_CAM_APP_DEFAULT_TO_PLAYBACK)
        ) {
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (Boolean.TRUE.equals(isSDCardExist)) {
                Intent intent = new Intent();
                AppLog.i(TAG, "intent:start PbMainActivity.class");
                intent.setClass(context, RemoteMultiPbActivity.class);
                context.startActivity(intent);
            } else if (Boolean.FALSE.equals(isSDCardExist)){
                AppDialog.showDialogWarn(activity, R.string.dialog_card_lose);
            }else {
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
            }
        } else {
            if (CameraUtils.isA3S()) {
                Intent intent = new Intent();
                intent.setClass(context, PreviewActivityV2.class);
                context.startActivity(intent);
            } else {
                Intent intent = new Intent();
                intent.setClass(context, PreviewActivity.class);
                context.startActivity(intent);
            }
        }
    }

    private boolean isRegistered(String ssid) {
        if(camSlotList == null){
            return false;
        }
        for (CameraSlot camSlot : camSlotList) {
            if (camSlot.cameraName != null && camSlot.cameraName.equals(ssid)) {
                return true;
            }
        }
        return false;
    }

    public void registerWifiReceiver() {
        if (wifiListener == null) {
            wifiListener = new WifiListener(activity, launchHandler);
        }
        wifiListener.registerReceiver();
    }

    public void unregisterWifiReceiver() {
        if (wifiListener != null) {
            wifiListener.unregisterReceiver();
            wifiListener = null;
        }
    }

    public void initCamSlotList() {
        camSlotList = CameraSlotSQLite.getInstance().getAllCameraSlotFormDb();
    }

    public String getCamSlotLinkStatus() {
        String status = activity.getApplicationContext().getResources().getString(R.string.connect_cam_wifi);
        if (null != camSlotList) {
            int readyPos = getReadyPosition();
            if (-1 != readyPos) {
                CameraSlot camSlotItem = camSlotList.get(readyPos);
                status = PanoramaApp.getContext().getString(R.string.text_connected) + " " + camSlotItem.cameraName;
            }
        }
        return status;
    }

    public void updateMainLinkStatus() {
        launchView.refreshLinkStatus(getCamSlotLinkStatus());
    }

    private int getReadyPosition() {
        if (camSlotList != null) {
            for (int index = 0; index < camSlotList.size(); ++index) {
                if (camSlotList.get(index).isReady) {
                    return index;
                }
            }
        }
        return -1;
    }

    private int getNotOccupiedPosition() {
        if (camSlotList != null) {
            for (int index = 0; index < camSlotList.size(); ++index) {
                if (!camSlotList.get(index).isOccupied) {
                    return index;
                }
            }
        }
        AppDialog.showDialogWarn(activity, "已配对了" + camSlotList.size() + "台相机，请至少删除1个配对信息才用连接！");
        return -1;
    }

    public synchronized void launchCameraForMain() {
        AppLog.i(TAG, "launchCameraForMain()");
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        if (camera != null && camera.isConnected()) {
            AppLog.i(TAG, "launchCameraForMain() camera is connected.");
            redirectToAnotherActivityByNav(activity);
            return;
        }
        final int notOccupiedPosition = getNotOccupiedPosition();
        if (-1 == notOccupiedPosition) {
            return;
        }
        final int readyPosition = getReadyPosition();
        final int position = (-1 != readyPosition) ? readyPosition : notOccupiedPosition;
        String wifiSSID = MWifiManager.getSsid(activity);
        BuglyLog.d(TAG, "launchCameraForMain： wifiSSID = " + wifiSSID);
        AppLog.d(TAG, "xxxxxxxxxxxxxx, wifiSSID = " + wifiSSID);
        if (R.id.navigation_album == navigationAction && !CameraUtils.isIjoyerCamera(wifiSSID)) {
            Intent intent = new Intent();
            intent.setClass(activity, LocalMultiPbActivity.class);
            activity.startActivity(intent);
            return;
        }
        final int cameraType = camSlotList.get(position).cameraType;
        if (camSlotList.get(position).isReady) {
            AppLog.i(TAG, "launchCameraForMain, camSlotList.get(position).isReady");
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            final String finalWifiSSID = wifiSSID;
            new Thread(new Runnable() {
                public void run() {
                    if (cameraType == CameraType.WIFI_CAMERA) {
                        beginConnectCameraForMain(position, getCameraIp(), finalWifiSSID);
                    }
                }
            }).start();
        } else {
            if (camSlotList.get(position).isOccupied) {
                AppLog.i(TAG, "launchCameraForMain, camSlotList.get(position).isOccupied");
                if (R.id.navigation_album == navigationAction) {
                    launchHandler.obtainMessage(AppMessage.MESSAGE_NAVIGATION_TO_LOCAL_PB).sendToTarget();
                } else {
                    launchHandler.obtainMessage(AppMessage.MESSAGE_POPUP_WINDOW).sendToTarget();
                }
            } else {
                AppLog.i(TAG, "launchCameraForMain, else !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!");
                launchHandler.obtainMessage(AppMessage.MESSAGE_CAMERA_CONNECTING_START_FOR_MAIN, CameraType.WIFI_CAMERA, position).sendToTarget();
            }
        }
    }

    public synchronized void launchCameraForMain(boolean isConnect, int cameraType) {
        AppLog.i(TAG, "launchCameraForMain()");
        String wifiSSID = MWifiManager.getSsid(activity);

        if (CameraUtils.isIjoyerCamera(wifiSSID)) {
            new Thread(() -> {
                if (cameraType == CameraType.WIFI_CAMERA) {
                    beginConnectCameraForDown(0, getCameraIp(), wifiSSID);
                }
            }).start();
        }
    }


    public synchronized void launchCameraForMain(final int position, final int cameraType) {
        AppLog.i(TAG, "launchCameraForMain  position:" + position + " cameraType:" + cameraType);
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        if (camera != null && camera.isConnected()) {
            AppLog.i(TAG, "launchCameraForMain  camera is connected.");
            redirectToAnotherActivityByNav(activity);
            return;
        }
        String cameraName = null;
        cameraSlotPosition = camSlotList.get(position).slotPosition;
        AppLog.d(TAG, "launchCameraForMain position=" + position + " cameraType=" + cameraType);
        if (cameraType == CameraType.WIFI_CAMERA) {
            cameraName = MWifiManager.getSsid(activity);
        }
        if (camSlotList.get(position).isReady) {
            AppLog.d(TAG, "launchCameraForMain position=" + position + " cameraType=" + cameraType + "camSlotList.get(position).isReady");
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            final String finalWifiSsid1 = cameraName;
            new Thread(new Runnable() {
                public void run() {
                    if (cameraType == CameraType.WIFI_CAMERA) {
                        beginConnectCameraForMain(position, getCameraIp(), finalWifiSsid1);
                    }
                }
            }).start();
        } else {
            if (camSlotList.get(position).isOccupied) {//相机当前被占用
                AppLog.d(TAG, "launchCameraForMain position=" + position + " cameraType=" + cameraType + "camSlotList.get(position).isOccupied");
                if (R.id.navigation_album == navigationAction) {
                    launchHandler.obtainMessage(AppMessage.MESSAGE_NAVIGATION_TO_LOCAL_PB).sendToTarget();
                } else {
                    launchHandler.obtainMessage(AppMessage.MESSAGE_POPUP_WINDOW).sendToTarget();
                }
            } else if (true || !isRegistered(cameraName)) {//fixme 测试
                AppLog.d(TAG, "launchCameraForMain position=" + position + " cameraType=" + cameraType + "!isRegistered(cameraName)");
                MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
                final String finalWifiSsid = cameraName;
                new Thread(() -> {
                    if (cameraType == CameraType.WIFI_CAMERA) {
                        beginConnectCameraForMain(cameraSlotPosition - 1, getCameraIp(), finalWifiSsid);
                    }
                }).start();
            } else {
                AppLog.d(TAG, "launchCameraForMain position=" + position + " cameraType=" + cameraType + " else !!!!!!!!!!!!!!!!!!!!!!!!");
                AppDialog.showDialogWarn(activity, activity.getString(R.string.text_camera_has_been_registered).replace("$1$", cameraName));
            }
        }
    }

    //先检查是否绑定网络，解决WiFi 和数据无法同时使用的问题
    public synchronized void beginConnectCameraForMain(int position, String ip, String wifiSSID) {
        checkBindNetwork(() -> beginConnectCameraForMainAfterBindNetwork(position, ip, wifiSSID));
    }

    public synchronized void beginConnectCameraForMainAfterBindNetwork(int position, String ip, String wifiSSID) {
        AppLog.i(TAG, "beginConnectCameraForMain position:" + position + " wifiSSID:" + wifiSSID);
        MyCamera currentCamera = CameraManager.getInstance().getCurCamera();
        if (currentCamera != null && currentCamera.isConnected()) {
            AppLog.i(TAG, "beginConnectCameraForMain camera is connected.");
            redirectToAnotherActivityByNav(activity);
            return;
        }

        // 确保完全清理之前的相机实例
        if (currentCamera != null) {
            AppLog.d(TAG, "Disconnecting previous camera instance before creating new one");
            currentCamera.disconnect();
        }

        currentCamera = CameraManager.getInstance().createCamera(CameraType.WIFI_CAMERA, wifiSSID, ip, position, CameraNetworkMode.AP);
        if (!currentCamera.connect(true)) {
            AppLog.w(TAG, "Camera connection failed, performing force cleanup");
            // 连接失败时强制清理，避免SDK状态卡住
            CameraManager.getInstance().forceCleanup();

            if (R.id.navigation_album == navigationAction) {
                launchHandler.obtainMessage(AppMessage.MESSAGE_NAVIGATION_TO_LOCAL_PB).sendToTarget();
            } else {
                launchHandler.obtainMessage(AppMessage.MESSAGE_POPUP_WINDOW).sendToTarget();
            }
            return;
        }
        if (currentCamera.getCameraProperties().hasFunction(PropertyId.CAMERA_DATE)) {
            currentCamera.getCameraProperties().setCameraDate();
        }
        if (currentCamera.getCameraProperties().hasFunction(PropertyId.CAMERA_DATE_TIMEZONE)) {
            currentCamera.getCameraProperties().setCameraDateTimeZone();
        }
        CameraSlotSQLite.getInstance().update(new CameraSlot(position, true, wifiSSID, CameraType.WIFI_CAMERA, null, true));
        launchHandler.post(() -> {
            MyProgressDialog.closeProgressDialog();
            redirectToAnotherActivityByNav(activity);
        });
    }

    @Deprecated
    private synchronized void beginConnectCameraForDown(int position, String ip, String wifiSSID) {
        AppLog.i(TAG, "beginConnectCameraForMain position:" + position + " wifiSSID:" + wifiSSID);
        MyCamera currentCamera = CameraManager.getInstance().getCurCamera();
        if (currentCamera != null) {
            if (!currentCamera.cameraName.equals(wifiSSID)) {
                currentCamera.disconnect();
                currentCamera = CameraManager.getInstance().createCamera(CameraType.WIFI_CAMERA, wifiSSID, ip, position, CameraNetworkMode.AP);
            }
        } else {
            currentCamera = CameraManager.getInstance().createCamera(CameraType.WIFI_CAMERA, wifiSSID, ip, position, CameraNetworkMode.AP);
        }
        if (!currentCamera.isConnected()) {
            if (!currentCamera.connect(true)) {
                MyProgressDialog.closeProgressDialog();
                return;
            }
        }
        if (currentCamera.getCameraProperties().hasFunction(PropertyId.CAMERA_DATE)) {
            currentCamera.getCameraProperties().setCameraDate();
        }
        if (currentCamera.getCameraProperties().hasFunction(PropertyId.CAMERA_DATE_TIMEZONE)) {
            currentCamera.getCameraProperties().setCameraDateTimeZone();
        }
        CameraSlotSQLite.getInstance().update(new CameraSlot(position, true, wifiSSID, CameraType.WIFI_CAMERA, null, true));
        launchHandler.post(new Runnable() {
            @Override
            public void run() {
                RemoteMultiPbPresenter remoteMultiPbPresenter = new RemoteMultiPbPresenter(activity);
                remoteMultiPbPresenter.downZdInfo(remoteMultiPbPresenter);
            }
        });
    }

    public int navigationAction = 0;

    public void redirectToAnotherActivityByNav(Context context) {
        ICatchtekImageDownloader downloader = new ICatchtekImageDownloader(activity);
        ImageLoaderConfig.initImageLoader(activity.getApplicationContext(), downloader);
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        String wifiSSID = MWifiManager.getSsid(activity);
        switch (navigationAction) {
            case R.id.navigation_album:
            case R.id.btn_remote_album: {
                CameraProperties cameraProperties = null;
                if (camera != null) {
                    cameraProperties = camera.getCameraProperties();
                }
                if (cameraProperties != null && CameraUtils.isIjoyerCamera(wifiSSID)) {
                    Boolean isSDCardExist = cameraProperties.isSDCardExist();
                    if (Boolean.TRUE.equals(isSDCardExist)) {
                        Intent intent = new Intent();
                        AppLog.i(TAG, "intent:start PbMainActivity.class");
                        intent.setClass(context, RemoteMultiPbActivity.class);
                        context.startActivity(intent);
                    } else if (Boolean.FALSE.equals(isSDCardExist)){
                        AppDialog.showDialogWarn(activity, R.string.dialog_card_lose);
                    }else {
                        AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                    }
                } else {
                    Intent intent = new Intent();
                    intent.setClass(context, LocalMultiPbActivity.class);
                    context.startActivity(intent);
                }
            }
            break;
            case R.id.txt_link:
            case R.id.navigation_shot:
            case R.id.cam_slot_listview: {
                if (CameraUtils.isA3S()) {
                    Intent intent = new Intent();
                    intent.setClass(context, PreviewActivityV2.class);
                    context.startActivity(intent);
                } else {
                    Intent intent = new Intent();
                    intent.setClass(context, PreviewActivity.class);
                    context.startActivity(intent);
                }
            }
            break;
        }
    }
}

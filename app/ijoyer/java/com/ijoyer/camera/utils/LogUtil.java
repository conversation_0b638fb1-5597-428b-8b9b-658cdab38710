
package com.ijoyer.camera.utils;

import android.util.Log;

public class LogUtil {
    private static boolean logGate = true;

    private static final String TAG = "LogUtil";

    /**
     * 打开
     * 
     * @param b
     */
    public static void openGate(boolean b) {
        logGate = b;
    }

    public static void i(String text) {
        if (logGate) {
            Log.i(TAG + "info", buildMessage(text));
        }
    }

    public static void d(String text) {
        if (logGate) {
            Log.d(TAG + "debug", buildMessage(text));
        }
    }

    public static void e(String text) {
        if (logGate) {
            Log.e(TAG + "errer", buildMessage(text));
        }
    }



    public static void e2long(String msg) {  //信息太长,分段打印
        //因为String的length是字符数量不是字节数量所以为了防止中文字符过多，
        //  把4*1024的MAX字节打印长度改为2001字符数
        int max_str_length = 1800;
        //大于4000时
        while (msg.length() > max_str_length) {
            Log.e(TAG + "errer", buildMessage(msg.substring(0, max_str_length)));
            msg = msg.substring(max_str_length);
        }
        //剩余部分
        Log.e(TAG + "errer", buildMessage(msg));
    }

    public static void v(String text) {
        if (logGate) {
            Log.v(TAG + "verbose", buildMessage(text));
        }
    }

    public static void w(String string) {
        if (logGate) {

            Log.w(TAG + "warn", buildMessage(string));
        }
    }

    public static void i(String tag, String text) {
        if (logGate) {
            Log.i(TAG+tag, buildMessage(text));
        }
    }

    public static void d(String tag, String text) {
        if (logGate) {
            Log.d(TAG+tag, buildMessage(text));
        }
    }

    public static void e(String tag, String text) {
        if (logGate) {
            Log.e(TAG+tag, buildMessage(text));
        }
    }

    public static void v(String tag, String text) {
        if (logGate) {
            Log.v(TAG+tag, buildMessage(text));
        }
    }

    public static void w(String tag, String text) {
        if (logGate) {
            Log.w(TAG+tag, buildMessage(text));
        }
    }

    /**
     * Building Message
     * 
     * @param msg The message you would like logged.
     * @return Message String
     */
    private static String buildMessage(String msg) {
        StackTraceElement caller = new Throwable().fillInStackTrace().getStackTrace()[2];

        return new StringBuilder().append(" [").append(caller.getFileName()).append(".")
                .append(caller.getMethodName()).append("#").append(caller.getLineNumber() + "]")
                .append(msg).toString();
    }

}

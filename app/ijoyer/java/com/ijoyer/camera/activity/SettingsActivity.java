package com.ijoyer.camera.activity;

import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.alipay.sdk.app.PayTask;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.CleanUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.SPUtils;
import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.ui.activity.LicenseAgreementActivity;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.utils.SPKey;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.ToastUtil;
import com.ijoyer.camera.base.BaseActivity;
import com.ijoyer.camera.http.net.CustomDisposableForJava;
import com.ijoyer.camera.http.net.bean.WxPayBean;
import com.ijoyer.camera.http.net.rx.RetrofitManager;
import com.ijoyer.camera.http.server.UserService;
import com.ijoyer.camera.utils.DataCleanManager;
import com.ijoyer.camera.widget.SettingHdrDialog;
import com.ijoyer.camera.widget.SettingLogoDialog;
import com.ijoyer.camera.widget.SettingRecordDialog;
import com.ijoyer.camera.widget.StitchParamDialog;
import com.ijoyer.mobilecam.R;
import com.kyleduo.switchbutton.SwitchButton;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import butterknife.BindView;
import io.reactivex.android.schedulers.AndroidSchedulers;

public class SettingsActivity extends BaseActivity implements AdapterView.OnItemClickListener {
    private static final String TAG = SettingsActivity.class.getSimpleName();
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.setup_menu_listView)
    ListView listView;
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.help)
    Button help;
    private Context context;
    static String cacheSize;

    //上次上报日志的时间
    private long lastReportLogTime;

    @Override
    protected boolean showHomeAsUp() {
        return true;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        calcTotalCacheSize(context);

        api = WXAPIFactory.createWXAPI(this, PanoramaApp.APP_ID, true);
        api.registerApp(PanoramaApp.APP_ID);
        registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {

                // 将该app注册到微信
                api.registerApp(PanoramaApp.APP_ID);
            }
        }, new IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP));
    }

    private void calcTotalCacheSize(Context context) {
        try {
            cacheSize = DataCleanManager.getTotalCacheSize(context);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        assert toolbar != null;
        toolbar.setTitle(R.string.ijoyer_setting);
        toolbar.setVisibility(View.VISIBLE);
        AppInfo.checkLocationDialog(this);
    }

    public static void start(Context context) {
        Intent intent = new Intent(context, SettingsActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.ijoyer_settings;
    }

    private final static String INFO = "相机信息";
    private final static String LOGO = "补地LOGO";
    private final static String PARAM = "拼接算法参数（调试）";
    private final static String RECORD = "录屏设置";
    private final static String CACHE = "清除缓存";
    private final static String VERSION = "软件版本";
    private final static String LICENSE = "用户协议";
    private final static String PRIVATE = "隐私政策";
    private final static String ABOUT = "关于IJOYER";
    private LinkedList<Menu> settingMenuList;
    private UserService userService;
    private Context mContext;

    @Override
    protected void initView() {
        mContext = this;
        help.setOnClickListener(view -> AboutActivity.start(this));
        listAdapter = new ListAdapter(this, settingMenuList, handler);
        listView.setAdapter(listAdapter);
        listView.setOnItemClickListener(this);
        userService = new RetrofitManager().getDefaultClient(UserService.class);
    }

    private ListAdapter listAdapter;
    public final Handler handler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            switch (msg.what) {
                case WHAT_CLEAN_CACHE:
                    calcTotalCacheSize(context);
                    listAdapter.notifyDataSetChanged();
//                    SPUtils.getInstance(StitchUtils.SP).put(StitchUtils.KEY_LOGO, StitchUtils.LogoType.NONE.ordinal());
                    break;
                case WHAT_REQUEST_ALBUM:
                    startAlbumForResult(SettingsActivity.this);
                    break;
            }
        }
    };

    public static void startAlbumForResult(AppCompatActivity activity) {
        Intent intent = new Intent(Intent.ACTION_PICK, android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");
        activity.startActivityForResult(intent, CODE_REQUEST_ALBUM_FILE);
    }

    @Override
    protected void initCfg() {
        if (context == null) {
            context = this;
        }
        if (settingMenuList == null) {
            settingMenuList = new LinkedList<Menu>();
        } else {
            settingMenuList.clear();
        }
//        settingMenuList.add(new Menu(INFO, String.format("可保存%1$d台配对信息", CameraSlotSQLite.MAX)));
        settingMenuList.add(new Menu(getResources().getString(R.string.auto_download_photos), "拍摄结束，照片将自动下载至手机（距离太远可能会失效）", "11"));
        settingMenuList.add(new Menu(getResources().getString(R.string.use_multi_thread_mode), getString(R.string.use_multi_thread_mode_desc), "12"));
        settingMenuList.add(new Menu(getResources().getString(R.string.hdr_compression_mode), getString(R.string.hdr_compression_mode_desc), "13"));
        settingMenuList.add(new Menu(getResources().getString(R.string.use_image_enhancement), getString(R.string.use_image_enhancement_desc), "14"));
        settingMenuList.add(new Menu(getResources().getString(R.string.camera_info), "", "1"));
        settingMenuList.add(new Menu(getResources().getString(R.string.complementary_ground_logo), getResources().getString(R.string.set_complementary_ground_logo), "2"));
        settingMenuList.add(new Menu(getResources().getString(R.string.params), "", "3"));
//        settingMenuList.add(new Menu(getResources().getString(R.string.stitching_params), "", "10"));
        settingMenuList.add(new Menu(getResources().getString(R.string.screen_recording_setting), "", "4"));
        settingMenuList.add(new Menu(getResources().getString(R.string.clear_cache), "", "5"));
        settingMenuList.add(new Menu(getResources().getString(R.string.software_version), "", "6"));
        settingMenuList.add(new Menu(getResources().getString(R.string.user_agreement), "", "7"));
        settingMenuList.add(new Menu(getResources().getString(R.string.privacy_statement), "", "8"));
        settingMenuList.add(new Menu(getResources().getString(R.string.about_ijoyer), "", "9"));
        if (SPUtils.getInstance().contains("uuid")) {
            settingMenuList.add(new Menu("日志ID", "点击复制", "15"));
        }
        settingMenuList.add(new Menu("上报日志", "", "16"));

    }

    private static final int WHAT_CLEAN_CACHE = 88;

    @SuppressLint("HandlerLeak")
    private Handler mHandler = new Handler() {
        @SuppressWarnings("unused")
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 1: {
                    @SuppressWarnings("unchecked")
                    PayResult payResult = new PayResult((Map<String, String>) msg.obj);
                    /**
                     * 对于支付结果，请商户依赖服务端的异步通知结果。同步通知结果，仅作为支付结束的通知。
                     */
                    String resultInfo = payResult.getResult();// 同步返回需要验证的信息
                    String resultStatus = payResult.getResultStatus();
                    // 判断resultStatus 为9000则代表支付成功
                    if (TextUtils.equals(resultStatus, "9000")) {
                        // 该笔订单是否真实支付成功，需要依赖服务端的异步通知。
                        showAlert(SettingsActivity.this, "支付成功" + payResult);
                    } else {
                        // 该笔订单真实的支付结果，需要依赖服务端的异步通知。
                        showAlert(SettingsActivity.this, "支付失败" + payResult);
                    }
                    break;
                }
                default:
                    break;
            }
        }
    };

    private static void showAlert(Context ctx, String info) {
        showAlert(ctx, info, null);
    }

    private static void showAlert(Context ctx, String info, DialogInterface.OnDismissListener onDismiss) {
        new AlertDialog.Builder(ctx)
                .setMessage(info)
                .setPositiveButton(R.string.confirm, null)
                .setOnDismissListener(onDismiss)
                .show();
    }

    private IWXAPI api;

    private void testWxPay() {
        userService.getWxPay()
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CustomDisposableForJava<WxPayBean>(mContext) {
                    @Override
                    public void onNext(WxPayBean bean) {
                        super.onNext(bean);
                        try {
                            IWXAPI api = WXAPIFactory.createWXAPI(context, PanoramaApp.APP_ID);
                            api.registerApp(PanoramaApp.APP_ID);
                            PayReq payReq = new PayReq();
                            payReq.appId = PanoramaApp.APP_ID;
                            payReq.partnerId = "1460734302";
                            payReq.prepayId = bean.result.prepayid;
                            payReq.packageValue = "Sign=WXPay";
                            payReq.nonceStr = bean.result.noncestr;
                            payReq.timeStamp = bean.result.timestamp;
                            payReq.sign = bean.result.sign;
                            api.sendReq(payReq);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }

                    }

                    @Override
                    public void onError(Throwable e) {
                        super.onError(e);
                    }
                });


    }

    private void testAliPay() {
        final Runnable payRunnable = new Runnable() {

            @Override
            public void run() {
                PayTask alipay = new PayTask(SettingsActivity.this);
                String orderInfo2 = "alipay_sdk=alipay-sdk-PHP-4.11.14.ALL&app_id=2021003181624151&biz_content=%7B%22out_trade_no%22%3A%22pay1681961508%22%2C%22total_amount%22%3A0.01%2C%22subject%22%3A%22test%22%2C%22product_code%22%3A%22QUICK_MSECURITY_PAY%22%7D&charset=GBK&format=json&method=alipay.trade.app.pay&sign_type=RSA2&timestamp=2023-04-20+11%3A31%3A48&version=1.0&sign=aldoyiY5SgWqjhiUi8gKr4NE4KPIkukuNxWPBPB14kJGAS9lmYkG285npmiXAgDsldvKn%2FtPUY4RchqNqIaPoGs5DkWnwD3dsQZme8pNkG1binqAyXv7RYDIOjxQrGGo21mHCOs77d6sSSH4TqBzC%2BWmWPBunKPrLez%2B%2FYZpY9IlDxZXqD05RVD5pxxWNi8G5aZ3YkEQUmArnEPDSF9aK%2FA3LagKqsgA6%2FiuwaKvloN1dcC0uN4Gz82aRXqBbb%2BPxj0DsJuwykYuPpavvTNsEES4g9jgBXYcgML%2B8eDTirfty8qoSf8t16%2B%2FHeLrnmAzhDScl%2BrI2GUkSD7fYgMa2g%3D%3D";
                Map<String, String> result = alipay.payV2(orderInfo2, true);
                Log.i("msp", result.toString());

                Message msg = new Message();
                msg.what = 1;
                msg.obj = result;
                mHandler.sendMessage(msg);
            }
        };

        // 必须异步调用
        Thread payThread = new Thread(payRunnable);
        payThread.start();
    }

    @Override
    public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
        String name = settingMenuList.get(i).type;
        switch (name) {
            case "16":
                long nextCanReportLogTime = lastReportLogTime + 1000 * 60 * 5;
                if (System.currentTimeMillis() < nextCanReportLogTime) {
                    ToastUtil.showShortToast("请不要频繁上报日志，可在（" +
                            (nextCanReportLogTime - System.currentTimeMillis()) / 1000 + "）秒后再次上报");
                } else {
                    lastReportLogTime = System.currentTimeMillis();
                    ToastUtil.showLongToast("上报日志成功，已帮您复制日志ID 到剪切板。\n如需帮助，请将日志ID 发送给客服");
                    copyUuid(false);
                    CrashReport.postCatchedException(new Exception("用户主动上报日志"));
                }
                break;
            case "15":
                copyUuid(true);
                break;
            case "10":
                new StitchParamDialog(this, R.layout.param_layout).show();
                break;
            case "1":
                CamSlotListActivity.start(context);
                break;
            case "2":
                new SettingLogoDialog(this, R.layout.set_logo_layout, handler).show();
                break;
            case "3":
//                HdrWindow hdrWindow = new HdrWindow(context);
//                hdrWindow.showPopupWindow();
//                hdrWindow.llSelect.setVisibility(View.GONE);
//                hdrWindow.tvCancel.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        SPUtils.getInstance().put(Const.SP.HDR_DATA, 1);
//                        hdrWindow.dismiss();
//                    }
//                });
//
//                hdrWindow.tvSure.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        SPUtils.getInstance().put(Const.SP.HDR_DATA, 2);
//                        hdrWindow.dismiss();
//                    }
//                });
//
//                hdrWindow.tvCancelNoTip.setVisibility(View.VISIBLE);
//                hdrWindow.tvCancelNoTip.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View v) {
//                        SPUtils.getInstance().put(Const.SP.HDR_DATA, 0);
//                        hdrWindow.dismiss();
//                    }
//                });
//                new StitchParamDialog(this, R.layout.param_layout).show();


//                HashMap<String, String> stringStringHashMap1 = readXml("string_zn.xml");
//                HashMap<String, String> stringStringHashMap2 = readXml("string_en.xml");
//
//                ArrayList<String> strings = new ArrayList<>();
//                for (Map.Entry<String,String> entry1: stringStringHashMap1.entrySet()) {
//                    for (Map.Entry<String,String> entry2: stringStringHashMap2.entrySet()) {
//                        if (entry1.getKey().equals(entry2.getKey())){
//                            strings.add(entry1.getValue() + "," + entry2.getValue() );
//                        }
//                    }
//                }
//
                new SettingHdrDialog(this, R.layout.set_hdr_layout).show();
//                testAliPay();
//                testWxPay();
                break;
            case "4":
                new SettingRecordDialog(this, R.layout.set_record_layout).show();
                break;
            case "5":
                AppDialog.showDialogWarn(context, getString(R.string.is_claen), true, () -> {
                    boolean ret = true;

                    ret &= CleanUtils.cleanInternalCache();
                    ret &= CleanUtils.cleanExternalCache();
                    AppToast.show(context, ret ? "清除成功" : "清除失败");
                    if (ret) {
                        handler.sendEmptyMessage(WHAT_CLEAN_CACHE);
                    }
                });
                break;
            case "6":
                AppDialog.showAPPVersionDialog(context);
                break;
            case "7":
                Intent mainIntent1 = new Intent(context, LicenseAgreementActivity.class);
                mainIntent1.putExtra("type", "Agreement");
                startActivity(mainIntent1);
                break;
            case "8":
                Intent mainIntent2 = new Intent(context, LicenseAgreementActivity.class);
                mainIntent2.putExtra("type", "Private");
                startActivity(mainIntent2);
                break;
            case "9":
                AboutActivity.start(context);
                break;
            default:
                AppDialog.showDialog(context, "正在开发中...");
                break;
        }
    }

    private void copyUuid(boolean needShowToast) {
        String uuid = SPUtils.getInstance().getString("uuid");
        if (!TextUtils.isEmpty(uuid)) {
            android.content.ClipboardManager clipboard = (android.content.ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
            android.content.ClipData clip = android.content.ClipData.newPlainText("日志ID", uuid);
            clipboard.setPrimaryClip(clip);
            if (needShowToast) {
                ToastUtil.showLongToast("复制日志ID成功");
            }
        }
    }

    private HashMap<String, String> readXml(String xmlName) {
        try {
            HashMap<String, String> results = new HashMap<>();
            // 创建解析器工厂
            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            DocumentBuilder db = factory.newDocumentBuilder();
            // 创建一个Document对象

            Document doc = db.parse(new File(StorageUtil.getRootPath(PanoramaApp.getContext()) + AppInfo.DOWNLOAD_PATH_PHOTO, xmlName));
            NodeList bookList = doc.getElementsByTagName("string");
            // 获取节点个数

            // 遍历每个book节点
            for (int i = 0; i < bookList.getLength(); i++) {
//                LogUtil.e("*******************************");
                // 索引从零开始
                org.w3c.dom.Node book = bookList.item(i);
                // 获取book节点所有属性集合
                org.w3c.dom.NamedNodeMap attrs = book.getAttributes();
                String nodeName = "";
                // 遍历book属性，不知道节点属性和属性名情况
                for (int j = 0; j < attrs.getLength(); j++) {
                    // 获取某一个属性
                    org.w3c.dom.Node attr = attrs.item(j);
                    nodeName = attr.getNodeValue();
//                    LogUtil.e(" --- 属性值:" + attr.getNodeValue());
                }


                // 若已经知道book节点有且只有1个ID属性,可用以下方式
                // org.w3c.dom.Element e = (org.w3c.dom.Element)
                // bookList.item(i);
                // System.out.println("Element属性值:"+e.getAttribute("id"));

                NodeList childNodes = book.getChildNodes();
                for (int k = 0; k < childNodes.getLength(); k++) {
                    // 区分,去掉空格和换行符
                    if (childNodes.item(k).getNodeType() == Node.TEXT_NODE) {
                        // 获取element类型的节点和节点值
//                        LogUtil.e(" --- 节点值：" + childNodes.item(k).getTextContent());
                        String nodeValue = childNodes.item(k).getTextContent();
                        results.put(nodeName, nodeValue);
                    }
                }
            }
            return results;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public static final int WHAT_REQUEST_ALBUM = 456;
    public static final int CODE_REQUEST_ALBUM_FILE = 123;

    public static void handleActivityResult(final Context context, int requestCode, int resultCode, @Nullable Intent data) {
        if (requestCode == CODE_REQUEST_ALBUM_FILE && resultCode == RESULT_OK && null != data) {
            final Uri selectedImage = data.getData();
            if (!TextUtils.isEmpty(selectedImage.getAuthority())) {
                Cursor cursor = context.getContentResolver().query(selectedImage,
                        new String[]{MediaStore.Images.Media.DATA}, null, null, null);
                if (null == cursor) {
                    AppToast.show(context, "图片没有找到！", Toast.LENGTH_LONG);
                    return;
                }
                cursor.moveToFirst();
                String imagePath = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Media.DATA));
                Log.e(TAG, "onActivityResult: " + imagePath);
                cursor.close();
                File srcFile = new File(imagePath).getAbsoluteFile();
//                File dstFile = new File(context.getCacheDir().getAbsolutePath() + "/" + StitchUtils.CUSTOMER_LOGO);
                FileUtils.createOrExistsDir(Environment.getExternalStorageDirectory() + AppInfo.PHOTO_TEMP);
                File dstFile = new File(Environment.getExternalStorageDirectory() + AppInfo.PHOTO_TEMP + StitchUtils.CUSTOMER_LOGO);

                try {
                    StitchUtils.copyFile(dstFile, new FileInputStream(srcFile));
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
                SPUtils.getInstance(StitchUtils.SP).put(StitchUtils.KEY_LOGO, StitchUtils.LogoType.CUSTOMER.ordinal());


            } else {

            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        handleActivityResult(context, requestCode, resultCode, data);

    }

    class ListAdapter extends BaseAdapter {
        private final Context context;
        private final List<Menu> menuList;
        private Handler handler;
        private ExecutorService executorService;

        public ListAdapter(Context context, List<Menu> menuList, Handler handler) {
            this.context = context;
            this.menuList = menuList;
            this.handler = handler;
        }

        @Override
        public int getCount() {
            return menuList.size();
        }

        @Override
        public Object getItem(int i) {
            return null;
        }

        @Override
        public long getItemId(int i) {
            return 0;
        }

        @SuppressLint({"ViewHolder", "SetTextI18n", "InflateParams"})
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = new ViewHolder();
            convertView = LayoutInflater.from(context).inflate(R.layout.setting_menu_item, null);

            holder.rlDefaultItem = (RelativeLayout) convertView.findViewById(R.id.rl_default_item);
            holder.clHaveTipItem = (ConstraintLayout) convertView.findViewById(R.id.cl_have_tip_item);
            if ("11".equals(menuList.get(position).type)
                    || "12".equals(menuList.get(position).type)
                    || "13".equals(menuList.get(position).type)
                    || "14".equals(menuList.get(position).type)) {
                holder.rlDefaultItem.setVisibility(View.GONE);
                holder.clHaveTipItem.setVisibility(View.VISIBLE);
            } else {
                holder.rlDefaultItem.setVisibility(View.VISIBLE);
                holder.clHaveTipItem.setVisibility(View.GONE);
            }
            holder.tvTitle = (TextView) convertView.findViewById(R.id.tv_title);
            holder.tvTip = (TextView) convertView.findViewById(R.id.tv_tip);
            holder.sw = (SwitchButton) convertView.findViewById(R.id.sw);
            holder.title = (TextView) convertView.findViewById(R.id.item_text);
            holder.text = (TextView) convertView.findViewById(R.id.item_value);

            holder.tvTitle.setText(menuList.get(position).name);
            holder.tvTip.setText(menuList.get(position).value);

            if (menuList.get(position).type.equals("11")) {
                holder.sw.setChecked(SPUtils.getInstance().getBoolean("isAutoDown", true));
                holder.sw.setOnCheckedChangeListener((buttonView, isChecked) ->
                        SPUtils.getInstance().put("isAutoDown", isChecked));
            } else if (menuList.get(position).type.equals("12")) {
                holder.sw.setChecked(SPKey.isUseMultiThreadMode());
                holder.sw.setOnCheckedChangeListener((buttonView, isChecked) ->
                        SPUtils.getInstance().put(SPKey.USE_MULTI_THREADED_MODE, isChecked));
            } else if (menuList.get(position).type.equals("13")) {
                holder.sw.setChecked(SPKey.isHdrCompressMode());
                holder.sw.setOnCheckedChangeListener((buttonView, isChecked) ->
                        SPUtils.getInstance().put(SPKey.HDR_COMPRESSION_MODE, isChecked));
            } else if (menuList.get(position).type.equals("14")) {
                holder.sw.setChecked(SPKey.useImageEnhancement());
                holder.sw.setOnCheckedChangeListener((buttonView, isChecked) ->
                        SPUtils.getInstance().put(SPKey.USE_IMAGE_ENHANCEMENT, isChecked));
            }


            convertView.setTag(holder);
            holder.title.setText(menuList.get(position).name);
            if (menuList.get(position).name.equals("6")) {
                holder.text.setText("V" + AppUtils.getAppVersionName());
            } else if (menuList.get(position).type.equals("5")) {
                holder.text.setText(cacheSize);
            } else if (menuList.get(position).value.equals("")) {
                holder.text.setVisibility(View.GONE);
            } else {
                holder.text.setText(menuList.get(position).value);
            }
            return convertView;
        }

        public final class ViewHolder {
            public TextView title;
            public TextView text;
            public RelativeLayout rlDefaultItem;
            public ConstraintLayout clHaveTipItem;
            public TextView tvTitle;
            public TextView tvTip;
            public SwitchButton sw;


        }
    }

    private static class Menu {
        String name;
        String value;
        String type;

        public Menu(String name, String value, String type) {
            this.name = name;
            this.value = value;
            this.type = type;
        }
    }

    private AppCompatTextView titleView;

    protected void initToolBar() {
        if (null == toolbar) {
            throw new NullPointerException("toolbar can not be null");
        } else {
            this.setSupportActionBar(toolbar);
            int childCount = toolbar.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View view = toolbar.getChildAt(i);
                if (view instanceof AppCompatImageButton) {
                    view.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
                        @Override
                        public void onLayoutChange(View view, int i, int i1, int i2, int i3, int i4, int i5, int i6, int i7) {
                            if (null != titleView) {
                                titleView.setPadding(0, 0, view.getWidth(), 0);
                            }
                        }
                    });
                }
                if (view instanceof AppCompatTextView) {
                    titleView = (AppCompatTextView) view;
                    titleView.setGravity(Gravity.CENTER);
                    Toolbar.LayoutParams params = new Toolbar.LayoutParams(Toolbar.LayoutParams.WRAP_CONTENT, Toolbar.LayoutParams.WRAP_CONTENT);
                    params.gravity = Gravity.CENTER;
                    titleView.setLayoutParams(params);
                    break;
                }
            }
            ActionBar actionBar = this.getSupportActionBar();
            assert actionBar != null;
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setElevation(0);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                toolbar.setElevation(0);
            }
        }
    }
}

package com.ijoyer.camera.activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.view.Window;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.ijoyer.camera.widget.FirstLicenseDialog;
import com.ijoyer.mobilecam.R;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import yanzhikai.textpath.SyncTextPathView;
import yanzhikai.textpath.painter.FireworksPainter;
public class SplashActivity extends BugTagsBaseActivity {
    @BindView(R.id.path_view)
    SyncTextPathView syncTextPathView;
    private Unbinder unbinder;
    private Context context;
    private boolean isFirst = false;
    private SPUtils spUtils;
    private void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            View decorView = window.getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            window.setNavigationBarColor(Color.TRANSPARENT);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        actionBar.hide();
    }
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        this.hideActionBar();
        setContentView(R.layout.activity_splash);
        this.context = this;
        this.unbinder = ButterKnife.bind(this);
        syncTextPathView.setPathPainter(new FireworksPainter());
        syncTextPathView.startAnimation(0, 1);
        spUtils = SPUtils.getInstance();
        isFirst = spUtils.getBoolean("isFirst");
        if (!isFirst) {
            new FirstLicenseDialog(this).show();
        } else {
            new Handler().postDelayed(() -> {
                MainActivity.start(context);
                finish();
            }, 3000);
        }
    } 
    @Override
    protected void onDestroy() {
        super.onDestroy();
        this.unbinder.unbind();
    }
}

package com.ijoyer.camera.activity;
import android.animation.Animator;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.provider.Settings;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.TextView;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraType;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.db.CameraSlotSQLite;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.utils.ClickUtils;
import com.icatch.mobilecam.utils.GpsUtil;
import com.ijoyer.camera.Presenter.LaunchPresenter;
import com.ijoyer.camera.base.BaseActivity;
import com.ijoyer.camera.ui.Interface.LaunchView;
import com.ijoyer.camera.ui.adapter.CameraSlotAdapter;
import com.ijoyer.camera.utils.AnimUtil;
import com.ijoyer.mobilecam.R;
import butterknife.BindView;
public class CamSlotListActivity extends BaseActivity implements LaunchView {
    private static final String TAG = CamSlotListActivity.class.getSimpleName();
    private Context mContext;
    private LaunchPresenter launchPresenter;
    public static class NestedListView extends ListView {
        public NestedListView(Context context, AttributeSet attrs) {
            super(context, attrs);
        }
        @Override
        protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
            int heightSpec = MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST);
            super.onMeasure(widthMeasureSpec, heightSpec);
        }
    }
    @BindView(R.id.collapsing_toolbar)
    CollapsingToolbarLayout collapsingToolbar;
    @BindView(R.id.cam_slot_listview)
    NestedListView camSlotListView;
    @Override
    protected int getLayoutId() {
        return R.layout.activity_camslotlist;
    }
    @Override
    protected boolean showHomeAsUp() {
        return true;
    }
    public static void start(Context context) {
        Intent intent = new Intent(context, CamSlotListActivity.class);
        context.startActivity(intent);
    }
    @Override
    protected void onResume() {
        super.onResume();
        AppLog.i(TAG, "onResume()");
        toolbar.setVisibility(View.VISIBLE);
        launchPresenter.registerWifiReceiver();
        launchPresenter.loadListView();
        if (null != popupWindow) {
            popupWindow.dismiss();
        }
        AppInfo.checkLocationDialog(this);
    }
    @Override
    protected void initCfg() {
        mContext = this;
        launchPresenter = new LaunchPresenter(this);
        launchPresenter.setView(this);
    }
    @Override
    protected void onStop() {
        super.onStop();
        launchPresenter.unregisterWifiReceiver();
    }
    @BindView(R.id.count)
    TextView count;
    @Override
    protected void initView() {
        String countStr = (String) count.getText();
        count.setText(String.format(countStr, CameraSlotSQLite.MAX));
        animUtil = new AnimUtil();
        camSlotListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && !GpsUtil.checkGPSIsOpen(mContext)) {
                    AppDialog.showDialogWarn(mContext, R.string.turn_on_location_information_tips, false, new AppDialog.OnDialogSureClickListener() {
                        @Override
                        public void onSure() {
                            GpsUtil.openGpsSettings(mContext);
                        }
                    });
                    return;
                }
                if (!ClickUtils.isFastDoubleClick(camSlotListView) ) {
                    launchPresenter.navigationAction = R.id.cam_slot_listview;
                    launchPresenter.launchCameraForMain(position, CameraType.WIFI_CAMERA);
                }
            }
        });
        collapsingToolbar.setTitle(getString(R.string.paired_camera));
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                CamSlotListActivity.this.onBackPressed();
            }
        });
    }
    @Override
    public void startPopupWindow() {
        bottomWindow(toolbar);
        toggleBright();
    }
    private AnimUtil animUtil;
    private float bgAlpha = 1f;
    private boolean bright = false;
    private PopupWindow popupWindow;
    private void toggleBright() {
        animUtil.setValueAnimator(0.2f, 1.0f, 400);
        animUtil.addUpdateListener(new AnimUtil.UpdateListener() {
            @Override
            public void progress(float progress) {
                bgAlpha = bright ? progress : (1.2f - progress);
                backgroundAlpha(bgAlpha);
            }
        });
        animUtil.addEndListner(new AnimUtil.EndListener() {
            @Override
            public void endUpdate(Animator animator) {
                bright = !bright;
            }
        });
        animUtil.startAnimator();
    }
    private void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha; 
        getWindow().setAttributes(lp);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
    }
    private void bottomWindow(View view) {
        if (popupWindow != null && popupWindow.isShowing()) {
            return;
        }
        LinearLayout layout = (LinearLayout) getLayoutInflater().inflate(R.layout.connect_popup_layout, null);
        popupWindow = new PopupWindow(layout,
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        popupWindow.setFocusable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());
        popupWindow.setAnimationStyle(R.style.popup_window);
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        popupWindow.showAtLocation(view, Gravity.LEFT | Gravity.BOTTOM, 0, -location[1]);
        popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                toggleBright();
            }
        });
        layout.findViewById(R.id.connect).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (android.os.Build.VERSION.SDK_INT > Build.VERSION_CODES.GINGERBREAD_MR1) {
                    startActivity(new Intent(Settings.ACTION_WIFI_SETTINGS)); 
                } else {
                    startActivity(new Intent(android.provider.Settings.ACTION_WIRELESS_SETTINGS));
                }
            }
        });
    }
    @Override
    public void setListViewAdapter(CameraSlotAdapter cameraSlotAdapter) {
        camSlotListView.setAdapter(cameraSlotAdapter);
    }
    @Override
    public void setBackBtnVisibility(boolean visibility) {
    }
    @Override
    public void setLaunchLayoutVisibility(int visibility) {
    }
    @Override
    public void setLaunchSettingFrameVisibility(int visibility) {
    }
    @Override
    public void refreshLinkStatus(String status) {
    }
    @Override
    public void fragmentPopStackOfAll() {
    }
}

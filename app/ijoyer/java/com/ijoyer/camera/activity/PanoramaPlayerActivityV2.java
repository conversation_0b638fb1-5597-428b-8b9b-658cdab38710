package com.ijoyer.camera.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.PointF;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.detu.PanoramaPlayerDemo.utils.DTListDialog;
import com.detu.PanoramaPlayerDemo.utils.SwipeEvent;
import com.detu.android_panoplayer.PanoPlayerImpl;
import com.detu.android_panoplayer.PanoPlayerUrl;
import com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView;
import com.detu.getmaskandweight.FileReadandWrite;
import com.detu.getmaskandweight.GetWeightAndMaskHelper;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.utils.ClickUtils;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.PermissionTools;
import com.icatch.mobilecam.utils.StorageUtil;
import com.ijoyer.camera.service.ScreenRecordService;
import com.ijoyer.camera.widget.SettingRecordDialog;
import com.ijoyer.mobilecam.R;
import com.player.panoplayer.GLGesture;
import com.player.panoplayer.IPanoPlayerListener;
import com.player.panoplayer.IPanoPluginListener;
import com.player.panoplayer.enitity.EaseTypes;
import com.player.panoplayer.enitity.LiteDecor;
import com.player.panoplayer.enitity.PanoDeviceId;
import com.player.panoplayer.enitity.PanoPlayerError;
import com.player.panoplayer.enitity.PanoPlayerStatus;
import com.player.panoplayer.enitity.PanoPluginError;
import com.player.panoplayer.enitity.PanoPluginStatus;
import com.player.panoplayer.enitity.PanoViewMode;
import com.player.panoplayer.plugin.Plugin;
import com.player.panoplayer.plugin.VideoPlugin;
import com.player.panoplayer.view.DisplayType;
import com.player.panoplayer.view.LayoutParams;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

@Deprecated()
public class PanoramaPlayerActivityV2 extends AppCompatActivity implements
        View.OnClickListener,
        IPanoPlayerListener,
        IPanoPluginListener,
        GLGesture.ClickPanoViewListener {
    private static final String TAG = PanoramaPlayerActivity.class.getSimpleName();
    @BindView(R.id.iv_play)
    ImageView ivPlay;
    @BindView(R.id.panorama_type_btn)
    TextView panoramaTypeBtn;
    @BindView(R.id.local_pb_top_layout)
    RelativeLayout localPbTopLayout;
    @BindView(R.id.tv_edit)
    TextView tvEdit;
    private Context context;
    private Boolean isControlNeedInvisible = false;
    float[] curGestureData = {};
    boolean isReplay = false;
    PanoViewMode curViewMode = PanoViewMode.DEF;
    private final int REQUEST_MEDIA_PROJECTION = 1;
    private Intent mResultIntent = null;
    private int mResultCode = 0;
    private static int mCurPhotoIndex = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mResultIntent = PanoramaApp.getInstance().getResultIntent();
        mResultCode = PanoramaApp.getInstance().getResultCode();
        initLayout();
        initPlayer();
        initBtnFuncSwitch();
        initBtnModeChoose();
        hideControlDelay();
        Bundle data = getIntent().getExtras();
        mCurPhotoIndex = data.getInt("curFilePosition", 0);
        getScreenBaseInfo();
    }

    private int mScreenWidth;
    private int mScreenHeight;
    private int mScreenDensity;
    private boolean isStarted = false;
    private boolean isVideoSd = true;
    private boolean isAudio = true;

    private void getScreenBaseInfo() {
        mScreenWidth = ScreenUtils.getScreenWidth();
        mScreenHeight = ScreenUtils.getScreenHeight();
        mScreenDensity = ScreenUtils.getScreenDensityDpi();
        SPUtils spUtils = SPUtils.getInstance("record_param");
        SettingRecordDialog.RecordType type;
        type = SettingRecordDialog.RecordType.values()[spUtils.getInt("KEY_RECORD", SettingRecordDialog.DEFAULT_RECORD.ordinal())];
        switch (type) {
            case SD:
                mScreenWidth = mScreenWidth / 3;
                mScreenHeight = mScreenHeight / 3;
                break;
            case HD:
                mScreenWidth = mScreenWidth / 2;
                mScreenHeight = mScreenHeight / 2;
                break;
            case FHD:
                ;
                break;
            default:
                if (mScreenWidth >= 1000) {
                    mScreenWidth = mScreenWidth / 2;
                    mScreenHeight = mScreenHeight / 2;
                }
                break;
        }
        if (mScreenWidth % 2 != 0) {
            mScreenWidth--;
        }
        if (mScreenHeight % 2 != 0) {
            mScreenHeight--;
        }
        AppLog.d(TAG, "ScreenWidth: " + mScreenWidth);
        AppLog.d(TAG, "ScreenHeight: " + mScreenHeight);
    }

    private void startScreenRecording() {
        MediaProjectionManager mediaProjectionManager = (MediaProjectionManager) getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        Intent permissionIntent = mediaProjectionManager.createScreenCaptureIntent();
        try {
            startActivityForResult(permissionIntent, REQUEST_MEDIA_PROJECTION);
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "当前系统不支持录屏功能", Toast.LENGTH_SHORT).show();
            showControlView();
        }
    }

    private void stopScreenRecording() {
        Intent service = new Intent(this, ScreenRecordService.class);
        stopService(service);
        isStarted = !isStarted;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        AppLog.d(TAG, "onActivityResult requestCode=" + requestCode + ", resultCode=" + resultCode);
        if (requestCode == REQUEST_MEDIA_PROJECTION) {
            if (resultCode == RESULT_OK) {
                Intent service = new Intent(this, ScreenRecordService.class);
                service.putExtra("code", resultCode);
                service.putExtra("data", data);
                service.putExtra("audio", isAudio);
                service.putExtra("width", mScreenWidth);
                service.putExtra("height", mScreenHeight);
                service.putExtra("density", mScreenDensity);
                service.putExtra("quality", isVideoSd);
                startService(service);
                isStarted = !isStarted;
                AppLog.i(TAG, "Started screen recording");
                Toast.makeText(this, getString(R.string.screen_recording_start), Toast.LENGTH_SHORT).show();
            } else {
                AppLog.i(TAG, "User cancelled");
                showControlView();
            }
        }
    }

    @Override
    protected void onStop() {
        if (isStarted) {
            stopScreenRecording();
            AppLog.i(TAG, "Stopped screen recording");
        }
        super.onStop();
        AppLog.d(TAG, "onStop()~~~~~~~~~~~~~~~~");
    }

    private void hideControlDelay() {
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                hideControlView();
            }
        }, 3000);
    }

    private void hideControlView() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (back != null) back.setVisibility(View.INVISIBLE);
                if (txtRecord != null) txtRecord.setVisibility(View.INVISIBLE);
                if (localPbBottomLayout != null) localPbBottomLayout.setVisibility(View.INVISIBLE);
                isControlNeedInvisible = true;
                if (ivPlay != null) ivPlay.setVisibility(View.GONE);
                if (tvEdit != null) tvEdit.setVisibility(View.GONE);
            }
        });
    }

    private void showControlView() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (back != null) back.setVisibility(View.VISIBLE);
                if (txtRecord != null) txtRecord.setVisibility(View.VISIBLE);
                if (localPbBottomLayout != null) localPbBottomLayout.setVisibility(View.VISIBLE);
                isControlNeedInvisible = false;
                if (2 == tag) {
                    if (ivPlay != null) ivPlay.setVisibility(View.VISIBLE);
                    if (tvEdit != null) tvEdit.setVisibility(View.VISIBLE);
                }
            }
        });

    }

    private void initLayout() {
        setContentView(R.layout.pano_player_layout);
        this.context = this;
        unbinder = ButterKnife.bind(this);
    }

    @BindView(R.id.btn_replay)
    Button btnReplay;

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }
    }

    private void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setNavigationBarColor(Color.TRANSPARENT);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        actionBar.hide();
    }

    @BindView(R.id.pp_surfaceView)
    PanoPlayerSurfaceView ppSurfaceView;
    private Unbinder unbinder;
    PanoPlayerImpl panoramaPlayer;

    static {
        System.loadLibrary("MaskAndWeight");
    }

    @BindView(R.id.local_photo_pb_delete)
    ImageButton delete;
    @BindView(R.id.local_pb_bottom_layout)
    LinearLayout localPbBottomLayout;
    @BindView(R.id.local_pb_back)
    ImageButton back;

    private int tag = 1;

    private void initPlayer() {
        ppSurfaceView.setOnClickPanoViewListener(this);
        ppSurfaceView.setGyroEnable(PanoramaApp.getPlayGyro());
        panoramaPlayer = ppSurfaceView.getRender();
        panoramaPlayer.setPanoPlayerListener(this);
        panoramaPlayer.setPanoPluginListener(this);
        parsePlayRequest();
        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,}, 0);
        turnUp.setOnClickListener(this);
        turnDown.setOnClickListener(this);
        prev.setOnClickListener(this);
        next.setOnClickListener(this);
        ivPlay.setOnClickListener(this);
        btnReplay.setOnClickListener(this);
        tvEdit.setOnClickListener(this);
        delete.setOnClickListener(view -> showDeleteEnsureDialog());
        back.setOnClickListener(view -> {
            if (isStarted) {
                stopScreenRecording();
            }
            finish();
        });
        tag = getIntent().getIntExtra("playTag", 1);
        if (2 == tag) {
            ivPlay.setVisibility(View.VISIBLE);
            btnReplay.setVisibility(View.VISIBLE);
            delete.setVisibility(View.GONE);
//            prev.setText("上一部");
//            next.setText("下一部");
        } else {
            ivPlay.setVisibility(View.GONE);
        }
        txtRecord.setOnClickListener(this);
    }

    @Override
    public void onBackPressed() {
        if (isStarted) {
            stopScreenRecording();
        }
        super.onBackPressed();
    }

    @BindView(R.id.txt_record)
    TextView txtRecord;
    private ExecutorService executorService;
    private Handler handler = new Handler() {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == 10) {
                initBtnFuncSwitch();
            } else if (msg.what == 20) {
                initBtnModeChoose();
            }
        }
    };

    private void showDeleteEnsureDialog() {
        if (ClickUtils.isFastDoubleClick(delete)) {
            return;
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setCancelable(false);
        builder.setTitle(R.string.image_delete_des);
        builder.setNegativeButton(R.string.gallery_delete, (dialog, whichButton) -> {
            MyProgressDialog.showProgressDialog(context, R.string.dialog_deleting);
            executorService = Executors.newSingleThreadExecutor();
            executorService.submit(new DeleteThread(), null);
        });
        builder.setPositiveButton(R.string.gallery_cancel, (dialog, whichButton) -> {
            dialog.dismiss();
            showControlView();
        });
        builder.create().show();
    }

    private int curPhotoIdx;
    private List<LocalPbItemInfo> fileList;
    private boolean isPlay = true;

    private class DeleteThread implements Runnable {
        @Override
        public void run() {
            Bundle data = getIntent().getExtras();
            curPhotoIdx = data.getInt("curFilePosition");
            fileList = GlobalInfo.getInstance().getLocalPhotoList();
            File tempFile = fileList.get(curPhotoIdx).file;
            if (tempFile.exists()) {
                tempFile.delete();
                MediaRefresh.notifySystemToScan(tempFile);
            }
            handler.post(() -> {
                fileList.remove(curPhotoIdx);
                finish();
                MyProgressDialog.closeProgressDialog();
            });
        }
    }

    @Override
    public void onClick(View view) {
        if (panoramaPlayer != null && ppSurfaceView != null) {
            if (R.id.btn_viewMode == view.getId()) {
                if (ClickUtils.isFastDoubleClick(btnViewMode)) {
                    return;
                }
                if (menuDialogMode != null) {
                    hideControlView();
                    menuDialogMode.show();
                }
            } else if (R.id.btn_chooseSwitch == view.getId()) {
                if (ClickUtils.isFastDoubleClick(btnChooseSwitch)) {
                    return;
                }
                if (menuDialogFunc != null) {
                    hideControlView();
                    menuDialogFunc.show();
                }
            } else if (R.id.btn_prev == view.getId()) {
                if (ClickUtils.isFastDoubleClick(prev)) {
                    return;
                }
                int tag = getIntent().getIntExtra("playTag", 1);
                String tempPath;
                if (1 == tag) {
                    if (mCurPhotoIndex > 0) {
                        List<LocalPbItemInfo> fileList;
                        fileList = GlobalInfo.getInstance().getLocalPhotoList();
                        tempPath = fileList.get(--mCurPhotoIndex).getFilePath();
                        playLocalPicture(tempPath);
                        AppToast.show(this, new File(tempPath).getName());
                    } else {
                        AppToast.show(this, "已经是第一张图片了！");
                    }
                } else if (2 == tag) {
                    if (mCurPhotoIndex > 0) {
                        List<LocalPbItemInfo> fileList;
                        fileList = GlobalInfo.getInstance().getLocalVideoList();
                        tempPath = fileList.get(--mCurPhotoIndex).getFilePath();
                        playLocalVideo(tempPath);
                        AppToast.show(this, new File(tempPath).getName());
                    } else {
                        AppToast.show(this, "已经是第一部视频了！");
                    }
                }
            } else if (R.id.btn_next == view.getId()) {
                if (ClickUtils.isFastDoubleClick(next)) {
                    return;
                }
                int tag = getIntent().getIntExtra("playTag", 1);
                String tempPath;
                if (1 == tag) {
                    int photoListSize = GlobalInfo.getInstance().getLocalPhotoList().size();
                    if (mCurPhotoIndex < photoListSize - 1) {
                        List<LocalPbItemInfo> fileList;
                        fileList = GlobalInfo.getInstance().getLocalPhotoList();
                        tempPath = fileList.get(++mCurPhotoIndex).getFilePath();
                        playLocalPicture(tempPath);
                        AppToast.show(this, new File(tempPath).getName());
                    } else {
                        AppToast.show(this, "已经是最后一张图片了！");
                    }
                } else if (2 == tag) {
                    int videoListSize = GlobalInfo.getInstance().getLocalVideoList().size();
                    if (mCurPhotoIndex < videoListSize - 1) {
                        List<LocalPbItemInfo> fileList;
                        fileList = GlobalInfo.getInstance().getLocalVideoList();
                        tempPath = fileList.get(++mCurPhotoIndex).getFilePath();
                        playLocalVideo(tempPath);
                        AppToast.show(this, new File(tempPath).getName());
                    } else {
                        AppToast.show(this, "已经是最后一部视频了！");
                    }
                }
            } else if (R.id.btn_turn_up == view.getId()) {
                if (ClickUtils.isFastDoubleClick(turnUp)) {
                    return;
                }
                MyToast.show(context, R.string.turn_up);
                Executors.newSingleThreadExecutor().submit(() -> SwipeEvent.makeSwipeDown(context, 100, 400, 100, 800, 6));
            } else if (R.id.btn_turn_down == view.getId()) {
                if (ClickUtils.isFastDoubleClick(turnDown)) {
                    return;
                }
                MyToast.show(context, R.string.turn_down);
                Executors.newSingleThreadExecutor().submit(() -> SwipeEvent.makeSwipeDown(context, 100, 800, 100, 400, 6));
            } else if (R.id.btn_replay == view.getId()) {
                if (ClickUtils.isFastDoubleClick(btnReplay)) {
                    return;
                }
                Plugin plugin = panoramaPlayer.getPlugin();
                if (plugin instanceof VideoPlugin) {
                    curViewMode = panoramaPlayer.getCurrentPanoViewMode();
                    curGestureData = panoramaPlayer.getCurrentGesturedata();
                    isReplay = true;
                    ((VideoPlugin) plugin).refresh();
                    String paths = getIntent().getStringExtra("path");
                    playLocalVideo(paths);
                }
            } else if (R.id.txt_record == view.getId()) {
                PermissionUtils.permission(PermissionConstants.MICROPHONE)
                        .rationale(new PermissionUtils.OnRationaleListener() {
                            @Override
                            public void rationale(@NonNull UtilsTransActivity activity, @NonNull ShouldRequest shouldRequest) {
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        shouldRequest.again(true);
                                    }
                                }, 100);
                            }
                        })
                        .callback(new PermissionUtils.FullCallback() {
                            @Override
                            public void onGranted(List<String> permissionsGranted) {
                                int count = 0;
                                for (int i = 0; i < permissionsGranted.size(); i++) {
                                    if (Manifest.permission.RECORD_AUDIO.equals(permissionsGranted.get(i))) {
                                        count++;
                                    }
                                }
                                //成功赋予权限
                                if (count >= 1) {
                                    if (ClickUtils.isFastDoubleClick(txtRecord)) {
                                        return;
                                    }
                                    if (isStarted) {
                                        stopScreenRecording();
                                        AppLog.i(TAG, "Stoped screen recording");
                                    } else {
                                        startScreenRecording();
                                    }
                                }
                            }

                            @Override
                            public void onDenied(List<String> permissionsDeniedForever, List<String> permissionsDenied) {
                                if (!permissionsDeniedForever.isEmpty()) {
                                    String[] systemRequestArray = permissionsDenied.toArray(new String[permissionsDenied.size()]);
                                    ActivityCompat.requestPermissions((PanoramaPlayerActivity)context, systemRequestArray, PermissionTools.ALL_REQUEST_CODE);
                                    return;
                                }

                            }
                        }).request();
            } else if (R.id.iv_play == view.getId()) {
                Plugin plugin = panoramaPlayer.getPlugin();
                if (isPlay) {
                    isPlay = false;
                    ((VideoPlugin) plugin).pause();
                    ivPlay.setImageDrawable(getResources().getDrawable(R.drawable.ic_play_arrow_white));
                } else {
                    isPlay = true;
                    ((VideoPlugin) plugin).start();
                    ivPlay.setImageDrawable(getResources().getDrawable(R.drawable.ic_pause_white));
                }
            } else if (R.id.tv_edit == view.getId()) {
                Intent intent = new Intent();
                intent.putExtra("path", getIntent().getStringExtra("path"));
                intent.setClass(this, VideoCutActivity.class);
                startActivity(intent);
            }
        }
    }

    @BindView(R.id.btn_prev)
    ImageButton prev;
    @BindView(R.id.btn_next)
    ImageButton next;
    @BindView(R.id.btn_turn_down)
    Button turnDown;
    @BindView(R.id.btn_turn_up)
    Button turnUp;
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_viewMode)
    Button btnViewMode;
    DTListDialog menuDialogMode;

    @SuppressLint("SetTextI18n")
    private void initBtnModeChoose() {
        btnViewMode.setOnClickListener(this);
        final String ON = "●", OFF = "○";
        if (PanoramaApp.getPlayMode() == 1) {
            curViewMode = PanoViewMode.DEF;
        } else if (PanoramaApp.getPlayMode() == 2) {
            curViewMode = PanoViewMode.FISHEYE;
        } else if (PanoramaApp.getPlayMode() == 3) {
            curViewMode = PanoViewMode.LITTLEPLANET;
        } else if (PanoramaApp.getPlayMode() == 4) {
            curViewMode = PanoViewMode.SPHERE;
        } else if (PanoramaApp.getPlayMode() == 5) {
            curViewMode = PanoViewMode.SPACE3D;
        } else if (PanoramaApp.getPlayMode() == 6) {
            curViewMode = PanoViewMode.VR_HORIZONTAL;
        } else if (PanoramaApp.getPlayMode() == 7) {
            curViewMode = PanoViewMode.VR_VERTICAL;
        } else if (PanoramaApp.getPlayMode() == 8) {
            curViewMode = PanoViewMode.FLAT;
        } else if (PanoramaApp.getPlayMode() == 9) {
            curViewMode = PanoViewMode.ORIGINAL;
        } else {
            curViewMode = PanoViewMode.DEF;
        }


        menuDialogMode = new DTListDialog(this);
        menuDialogMode.setTitle(getString(R.string.mode_switching));
        menuDialogMode.setItems(
                new String[]{
                        getString(R.string.mode_switch1) + (curViewMode == PanoViewMode.DEF ? ON : OFF),
                        getString(R.string.mode_switch2) + (curViewMode == PanoViewMode.FISHEYE ? ON : OFF),
                        getString(R.string.mode_switch3) + (curViewMode == PanoViewMode.LITTLEPLANET ? ON : OFF),
                        getString(R.string.mode_switch4) + (curViewMode == PanoViewMode.SPHERE ? ON : OFF),
                        getString(R.string.mode_switch5) + (curViewMode == PanoViewMode.SPACE3D ? ON : OFF),
                        getString(R.string.mode_switch6) + (curViewMode == PanoViewMode.VR_HORIZONTAL ? ON : OFF),
                        getString(R.string.mode_switch7) + (curViewMode == PanoViewMode.VR_VERTICAL ? ON : OFF),
                        getString(R.string.mode_switch8) + (curViewMode == PanoViewMode.FLAT ? ON : OFF),
                        getString(R.string.mode_switch9) + (curViewMode == PanoViewMode.ORIGINAL ? ON : OFF),
                },
                (dialog, view, position) -> {
                    PanoViewMode viewMode = PanoViewMode.DEF;
                    int mode = 1;
                    switch (position) {
                        case 0:
                            mode = 1;
                            viewMode = PanoViewMode.DEF;
                            btnViewMode.setText(getString(R.string.mode_switch1_data));
                            break;
                        case 1:
                            mode = 2;
                            viewMode = PanoViewMode.FISHEYE;
                            btnViewMode.setText(getString(R.string.mode_switch2_data));
                            break;
                        case 2:
                            mode = 3;
                            viewMode = PanoViewMode.LITTLEPLANET;
                            btnViewMode.setText(getString(R.string.mode_switch3_data));
                            break;
                        case 3:
                            mode = 4;
                            viewMode = PanoViewMode.SPHERE;
                            btnViewMode.setText(getString(R.string.mode_switch4_data));
                            break;
                        case 4:
                            mode = 5;
                            viewMode = PanoViewMode.SPACE3D;
                            btnViewMode.setText(getString(R.string.mode_switch5_data));
                            break;
                        case 5:
                            mode = 6;
                            viewMode = PanoViewMode.VR_HORIZONTAL;
                            btnViewMode.setText(getString(R.string.mode_switch6_data));
                            break;
                        case 6:
                            mode = 7;
                            viewMode = PanoViewMode.VR_VERTICAL;
                            btnViewMode.setText(getString(R.string.mode_switch7_data));
                            break;
                        case 7:
                            mode = 8;
                            viewMode = PanoViewMode.FLAT;
                            btnViewMode.setText(getString(R.string.mode_switch8_data));
                            break;
                        case 8:
                            mode = 9;
                            viewMode = PanoViewMode.ORIGINAL;
                            btnViewMode.setText(getString(R.string.mode_switch9_data));
                            break;
                        default:
                            break;
                    }
                    PanoramaApp.setPlayMode(mode);
                    panoramaPlayer.setAnimationViewMode(viewMode, 1, EaseTypes.LinearEaseOuts);
                    menuDialogMode.dismiss();
                    handler.sendEmptyMessage(20);
                }
        );
        menuDialogMode.setOnDismissListener(dialog -> {
            showControlView();
        });
    }

    boolean isAutoPlay = PanoramaApp.getPlayAutoPlay();
    boolean isReverse = PanoramaApp.getReverse();
    boolean isScenesBackMusic = PanoramaApp.getScenesBackgroundMusic();
    boolean isPointSelecting = PanoramaApp.getPlayPointSelecting();
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_chooseSwitch)
    Button btnChooseSwitch;
    DTListDialog menuDialogFunc;

    @SuppressLint("SetTextI18n")
    private void initBtnFuncSwitch() {
        ppSurfaceView.setGyroEnable(PanoramaApp.getPlayGyro());
        ppSurfaceView.setZoomEnable(PanoramaApp.getPlayZoom());
        ppSurfaceView.setGestureEnable(PanoramaApp.getPlayGesture());
        ppSurfaceView.setGyroModeShouldMove(PanoramaApp.getPlayGyroModeShouldMove());
        panoramaPlayer.setAutoRotate(PanoramaApp.getPlayAutoPlay(), -0.35f);
        panoramaPlayer.setPauseTime(1);
        panoramaPlayer.setReverse(PanoramaApp.getReverse());
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (PanoramaApp.getScenesBackgroundMusic()) {
                    panoramaPlayer.pauseBackgroundMusic();
                } else {
                    panoramaPlayer.startBackgroundMusic();
                }
                panoramaPlayer.setPointSelecting(PanoramaApp.getPlayPointSelecting());
            }
        }, 300);

        btnChooseSwitch.setOnClickListener(this);
        menuDialogFunc = new DTListDialog(this);
        final String ON = getString(R.string.function_on), OFF = getString(R.string.function_off);
        menuDialogFunc.setTitle(getString(R.string.function_switch)).setItems(
                new String[]{
                        getString(R.string.gyro) + (ppSurfaceView.getGyroEnable() ? ON : OFF),
                        getString(R.string.gesture_zoom) + (ppSurfaceView.getZoomEnable() ? ON : OFF),
                        getString(R.string.gesture_movement) + (ppSurfaceView.getGestureEnable() ? ON : OFF),
                        getString(R.string.gyro_gesture_coexistence) + (ppSurfaceView.getGyroModeShouldMove() ? ON : OFF),
                        getString(R.string.auto_play) + (PanoramaApp.getPlayAutoPlay() ? ON : OFF),
                        getString(R.string.flip) + (PanoramaApp.getReverse() ? ON : OFF),
                        getString(R.string.background_music) + (PanoramaApp.getScenesBackgroundMusic() ? ON : OFF),
                        getString(R.string.tap) + (PanoramaApp.getPlayPointSelecting() ? ON : OFF)
                },
                (dialog, view, position) -> {
                    TextView tv = (TextView) view;
                    boolean isEnable;
                    switch (position) {
                        case 0://陀螺仪
                            isEnable = PanoramaApp.getPlayGyro();
                            PanoramaApp.setPlayGyro(!isEnable);
                            ppSurfaceView.setGyroEnable(!isEnable);
                            tv.setText(getString(R.string.gyro) + (!isEnable ? ON : OFF));
                            break;
                        case 1://手势缩放
                            isEnable = PanoramaApp.getPlayZoom();
                            PanoramaApp.setPlayZoom(!isEnable);
                            ppSurfaceView.setZoomEnable(!isEnable);
                            tv.setText(getString(R.string.gesture_zoom) + (!isEnable ? ON : OFF));
                            break;
                        case 2://手势移动
                            isEnable = PanoramaApp.getPlayGesture();
                            PanoramaApp.setPlayGesture(!isEnable);
                            ppSurfaceView.setGestureEnable(!isEnable);
                            tv.setText( getString(R.string.gesture_movement) + (!isEnable ? ON : OFF));
                            break;
                        case 3://陀螺仪手势共存
                            isEnable = PanoramaApp.getPlayGyroModeShouldMove();
                            PanoramaApp.setPlayGyroModeShouldMove(!isEnable);
                            ppSurfaceView.setGyroModeShouldMove(!isEnable);
                            tv.setText(getString(R.string.gyro_gesture_coexistence) + (!isEnable ? ON : OFF));
                            break;
                        case 4://自动播放
                            isAutoPlay = !PanoramaApp.getPlayAutoPlay();
                            PanoramaApp.setPlayAutoPlay(isAutoPlay);
                            panoramaPlayer.setAutoRotate(isAutoPlay, -0.35f);
                            panoramaPlayer.setPauseTime(1);
                            tv.setText(getString(R.string.auto_play) + (isAutoPlay ? ON : OFF));
                            break;
                        case 5://翻转
                            isReverse = !PanoramaApp.getReverse();
                            PanoramaApp.setReverse(isReverse);
                            panoramaPlayer.setReverse(isReverse);
                            tv.setText(getString(R.string.flip) + (isReverse ? ON : OFF));
                            break;
                        case 6://背景音乐
                            isScenesBackMusic = !PanoramaApp.getScenesBackgroundMusic();
                            PanoramaApp.setScenesBackgroundMusic(isScenesBackMusic);
                            if (isScenesBackMusic) {
                                panoramaPlayer.pauseBackgroundMusic();
                            } else {
                                panoramaPlayer.startBackgroundMusic();
                            }
                            tv.setText( getString(R.string.background_music) + (isScenesBackMusic ? ON : OFF));
                            break;
                        case 7://点选
//                            isPointSelecting = !panoramaPlayer.getCurrentPanoData().nodeView.isPointSelecting;
                            isPointSelecting = !PanoramaApp.getPlayPointSelecting();
                            PanoramaApp.setPlayPointSelecting(isPointSelecting);
                            panoramaPlayer.setPointSelecting(isPointSelecting);
                            tv.setText( getString(R.string.tap) + (isPointSelecting ? ON : OFF));
                            break;
                        default:
                            break;
                    }
                    handler.sendEmptyMessage(10);
                    menuDialogFunc.dismiss();
                }
        );
        menuDialogFunc.setOnDismissListener(dialog -> {
            showControlView();
        });
    }

    private void parsePlayRequest() {
        int tag = getIntent().getIntExtra("playTag", 1);
        String paths = getIntent().getStringExtra("path");
        if (TextUtils.isEmpty(paths)) {
        }
        AppLog.e(TAG, "parsePlayRequest, tag:" + tag + ", path:" + paths);
        switch (tag) {
            case 1:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择图片", Toast.LENGTH_LONG).show();
                    return;
                }
                playLocalPicture(paths);
                break;
            case 2:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择视频", Toast.LENGTH_LONG).show();
                    return;
                }
                playLocalVideo(paths);
                break;
            case 3:
                PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请输入xml", Toast.LENGTH_LONG).show();
                    return;
                }
                if (!paths.contains("xml")) {
                    Toast.makeText(getApplicationContext(), "输入有误，请重新输入", Toast.LENGTH_LONG).show();
                    return;
                }
                panoplayerurl.setXmlUrl(paths);
                panoramaPlayer.playByXml(panoplayerurl);
                break;
        }
    }

    private static final String PLAYER_CONFIG = "<DetuVr>\n" +
            "   <settings init='pano1' initmode='default' enablevr='true' title=''/>\n" +
            "   <scenes>\n" +
            "       <scene name='pano1' title='' thumburl=''>\n" +
            "           <preview url='' />\n" +
            "           <image type='%s' url='%s' device='%d' isreplay_f4 = 'true'/>\n" +
            "           <view viewmode='default'  isptselect ='true' />\n" +
            "       </scene>\n" +
            "   </scenes>\n" +
            "</DetuVr>";

    private void playLocalPicture(String paths) {
        String url = String.format(PLAYER_CONFIG, "sphere", paths, -1);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        panoramaPlayer.playByXml(panoplayerurl, null);
    }

    private PanoPlayerUrl panoPlayerUrl;

    private void playLocalVideo(String paths) {
        String url = String.format(PLAYER_CONFIG, "video", paths, -1);
        this.panoPlayerUrl = new PanoPlayerUrl();
        panoPlayerUrl.setXmlContent(url);
        panoramaPlayer.playByXml(panoPlayerUrl, null);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (ppSurfaceView != null) {
            ppSurfaceView.onDestroy();
        }
        unbinder.unbind();
        AppLog.d(TAG, "onDestroy()~~~~~~~~~~~~~~~~");
    }

    @Override
    public void onResume() {
        super.onResume();
        ppSurfaceView.onResume();

        // 延迟调用，确保播放器完全初始化，避免空指针异常
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                startVideoPluginSafely();
            }
        }, 200); // 延迟200ms

        isScenesBackMusic = true;
        panoramaPlayer.resumeAllBackgroundMusic();
        AppLog.e(TAG, "onResume");
        showControlView();
        AppInfo.checkLocationDialog(this);
    }

    /**
     * 安全地启动视频插件，避免空指针异常
     */
    private void startVideoPluginSafely() {
        if (panoramaPlayer != null) {
            Plugin plugin = panoramaPlayer.getPlugin();
            if (plugin instanceof VideoPlugin) {
                try {
                    ((VideoPlugin) plugin).start();
                    AppLog.d(TAG, "Video plugin started successfully");
                } catch (Exception e) {
                    AppLog.e(TAG, "Failed to start video plugin: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        ppSurfaceView.onPause();
        Plugin plugin = panoramaPlayer.getPlugin();
        if (plugin instanceof VideoPlugin) {
            ((VideoPlugin) plugin).pause();
        }
        panoramaPlayer.onGLSurfaceViewPause();
        panoramaPlayer.pauseAllHotMusic();
        panoramaPlayer.pauseAllBackgroundMusic();
        panoramaPlayer.onGLSurfaceViewPause();
        AppLog.e(TAG, "onPause");
    }

    @Override
    public void onPanoPlayerStatusChanged(PanoPlayerStatus status, String tip) {
        switch (status) {
            case LOADING:
                if (panoramaPlayer.getCurrentPanoData().nodeImage.panoDeviceId.deviceId > 4000) {
                    panoramaPlayer.setCurrentImageDataCali(Calibration);
                    panoramaPlayer.setWeight(weightPath);
                }
                if (IS_TEST_LITE_DECOR) {
                    LiteDecor liteDecor = new LiteDecor();
                    liteDecor.tag = "smart";
                    liteDecor.imageUrl = "/mnt/sdcard/3.png";
                    LayoutParams layoutParams = new LayoutParams();
                    layoutParams.displayType = DisplayType.GL_DISTORTED;
                    layoutParams.ath = 0f;
                    layoutParams.atv = 0f;
                    layoutParams.width = 0.17f;
                    layoutParams.height = 0.17f;
                    liteDecor.layoutParams = layoutParams;
                    panoramaPlayer.addLiteDecor(liteDecor);
                }
                AppLog.e(TAG, "onPanoPlayerStatusChanged: loading!");
                break;
            case LOADED:
                if (isReplay) {
                    isReplay = false;
                    panoramaPlayer.setViewMode(curViewMode);
                    panoramaPlayer.setCurrentGestureData(curGestureData);
                }
                panoramaPlayer.setAnimationViewMode(curViewMode, 1, EaseTypes.LinearEaseOuts);
                panoramaPlayer.setPointSelecting(false);
                AppLog.e(TAG, "onPanoPlayerStatusChanged: loaded!");
                break;
            case ERROR:
                PanoPlayerError error = PanoPlayerError.parseByString(tip);
                switch (error) {
                    case LACK_CALIBRATION:
                        AppLog.e(TAG, "onPanoPlayerError: lack calibration!");
                        break;
                    case PLAY_MANAGER_DATA_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: PLAY MANAGER DATA IS EMPTY");
                        break;
                    case SETTING_DATA_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: SETTING DATA IS EMPTY!");
                        break;
                    case PANORAMALIST_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: PANORAMALIST IS EMPTY!");
                        break;
                    case PLAY_URL_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: PLAY URL IS EMPTY!");
                        break;
                    case IMAGE_LOAD_ERROR:
                        AppLog.e(TAG, "onPanoPlayerError: IMAGE LOAD ERROR");
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }

    private static final boolean IS_TEST_LITE_DECOR = false;
    String M1Path = "";
    String F4PlusPath = "";
    String Calibration = "";
    String[] weightPath = new String[4];
    private Handler uiHandler = new Handler();

    @Override
    public void onPanoPlayerConfigLoaded(int deviceId) {
        AppLog.e(TAG, "onPanoPlayerConfigLoaded: loading!");
        if (deviceId > 4000) {
            GetWeightAndMaskHelper getWeightAndMaskHelper = new GetWeightAndMaskHelper();
            M1Path = "/mnt/sdcard/M1WeightPath";
            F4PlusPath = "/mnt/sdcard/F4PlusWeightPath";
            if (deviceId == PanoDeviceId.PanoDeviceId_SPHERE_DETU_M1.deviceId) {
                File file = new File(M1Path);
                if (!file.exists()) {
                    file.mkdir();
                }
                weightPath = new String[]{M1Path + "/wt0.jpg", M1Path + "/wt1.jpg", M1Path + "/wt2.jpg", M1Path + "/wt3.jpg"};
                String biaodingPath = M1Path + "/biaoding.txt";
                File[] fileWeight = new File[4];
                for (int i = 0; i < 4; i++) {
                    fileWeight[i] = new File(weightPath[i]);
                }
                File biaodingFile = new File(biaodingPath);
                if ((fileWeight[0].exists()) && (biaodingFile.exists())) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        Calibration = FileReadandWrite.readtxt(biaodingFile);
                    }
                } else {
                    GetWeightAndMaskHelper helper = new GetWeightAndMaskHelper();
                    Calibration = helper.GetWgetWeightAndMaskInfo(M1Path + "/pat.pts", new int[]{1024, 512}, weightPath);
                    FileReadandWrite.writetxt(biaodingPath, Calibration);
                }
            } else {
                File file = new File(F4PlusPath);
                if (!file.exists()) {
                    file.mkdir();
                }
                weightPath = new String[]{F4PlusPath + "/wt0.jpg", F4PlusPath + "/wt1.jpg", F4PlusPath + "/wt2.jpg", F4PlusPath + "/wt3.jpg"};
                String biaodingPath = F4PlusPath + "/biaoding.txt";
                File[] fileWeight = new File[4];
                for (int i = 0; i < 4; i++) {
                    fileWeight[i] = new File(weightPath[i]);
                }
                File biaodingfile = new File(biaodingPath);
                if ((fileWeight[0].exists()) && (biaodingfile.exists())) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        Calibration = FileReadandWrite.readtxt(biaodingfile);
                    }
                } else {
                    GetWeightAndMaskHelper helper = new GetWeightAndMaskHelper();
                    String path = "A:_4000_3000_-3_-465_3915_3915_2_203.729_55_90_2_0_-0.346621_0.214988_44.621_14.0211_0_0_0_0_B:_4000_3000_16_-474_3919_3919_2_202.197_145.849_91.6581_-2.06139_0_-0.256364_0.110774_66.2789_6.20713_0_0_0_0_C:_4000_3000_5_-427_3864_3864_2_199.69_54.8259_-90.61_-179.144_0_-0.270679_0.142519_57.8891_6.8586_0_0_0_0_D:_4000_3000_15_-467_3942_3942_2_207.991_-33.6985_89.0577_-0.285306_0_-0.330378_0.183777_40.8338_-25.4735_0_0_0_0_&&";
                    Calibration = helper.GetWgetWeightAndMaskInfo(path, new int[]{1024, 512}, weightPath);
                    FileReadandWrite.writetxt(biaodingPath, Calibration);
                }
            }
        }
        AppLog.e(TAG, "onPanoPlayerConfigLoaded: finish!");
    }

    @Override
    public void onPanoPluginStateChanged(PanoPluginStatus status, String tip) {
        switch (status) {
            case PREPARED:
                AppLog.e(TAG, "onPanoPluginStateChanged, PREPARED!");
                break;
            case PLAYING:
                AppLog.e(TAG, "onPanoPluginStateChanged, PLAYING!");
                break;
            case PAUSE:
                AppLog.e(TAG, "onPanoPluginStateChanged, Pause!");
                break;
            case STOP:
                AppLog.e(TAG, "onPanoPluginStateChanged, Stop!");
                break;
            case FINISH:
                AppLog.e(TAG, "onPanoPluginStateChanged, Finish!");
                if (panoramaPlayer.getCurrentPanoData().nodeImage.panoDeviceId.deviceId > 4000) {
                    Plugin plugin = panoramaPlayer.getPlugin();
                    if (plugin != null) {
                        plugin.refresh();
                    }
                }
                break;
            case CODEC_SWITCH:
                AppLog.e(TAG, "onPanoPluginStateChanged, Switch Codec:" + tip);
                break;
            case ERROR:
                PanoPluginError error = PanoPluginError.parseByString(tip);
                switch (error) {
                    case NETWORK:
                        AppLog.e(TAG, "*onPanoPluginStateChanged, Network Error!");
                        uiHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                panoramaPlayer.playByXml(panoPlayerUrl, null);
                            }
                        });
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void onPanoPluginProgressChanged(long currentTime, long bufferTime, long duration) {
        int progress = (int) (currentTime * 1.0 / duration * 100);
        AppLog.e(TAG, "onPanoPluginProgressChanged:" + progress);
    }

    @Override
    public void onClickPanoView(MotionEvent motionEvent) {
        PointF pointF = panoramaPlayer.calDegByWinPoint(motionEvent.getX(), motionEvent.getY());
        double x = Math.toDegrees(pointF.x);
        if (x > 270) {
            x = x - 360 - 90;
        } else {
            x = x - 90;
        }
        double y = 90 - Math.toDegrees(pointF.y);
        AppLog.i(TAG, "position:" + x + "\t" + y);
        AppLog.d(TAG, "motionEvent.getAction = " + motionEvent.getAction());
        AppLog.d(TAG, "isControlNeedInvisible = " + isControlNeedInvisible);
        if (MotionEvent.ACTION_UP == motionEvent.getAction() || MotionEvent.ACTION_DOWN == motionEvent.getAction()) {
            if (isControlNeedInvisible) {
                showControlView();
            } else {
                hideControlView();
            }
        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (isStarted) {
                stopScreenRecording();
                AppLog.i(TAG, "Stopped screen recording");
            }
        }
        if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN || keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN || keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            if (isStarted) {
                stopScreenRecording();
                AppLog.i(TAG, "Stopped screen recording");
            }
            return true;
        }
        return super.onKeyUp(keyCode, event);
    }
}

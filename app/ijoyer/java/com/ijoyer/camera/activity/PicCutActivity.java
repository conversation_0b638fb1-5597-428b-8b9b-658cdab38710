package com.ijoyer.camera.activity;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.PathUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.target.Target;
import com.gyf.immersionbar.BarHide;
import com.gyf.immersionbar.ImmersionBar;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.utils.GlideUtils;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

import butterknife.BindView;
import butterknife.ButterKnife;
import jp.co.cyberagent.android.gpuimage.GPUImage;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageContrastFilter;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageExposureFilter;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageFilter;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageSaturationFilter;

import static com.detu.szStitch.StitchUtils.getBitmapOption;

public class PicCutActivity extends BugTagsBaseActivity {

    @BindView(R.id.iv_pic)
    ImageView ivPic;
    @BindView(R.id.sb_contrast_ratio)
    IndicatorSeekBar sbContrastRatio;
    @BindView(R.id.sb_exposure)
    IndicatorSeekBar sbExposure;
    @BindView(R.id.sb_saturation)
    IndicatorSeekBar sbSaturation;
    @BindView(R.id.ll_edit_photo)
    LinearLayout llEditPhoto;
    private String path;
    private Context mContext;
    private String tempPhotoPath = "";
    private String tempPhotoDir = PathUtils.getInternalAppDataPath() + "/temp";



    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pic_cut);
        ButterKnife.bind(this);
        path = getIntent().getStringExtra("path");
        mContext = this;
        ImmersionBar.with(this)
                .hideBar(BarHide.FLAG_HIDE_BAR)
                .transparentBar()
                .transparentStatusBar()
                .transparentNavigationBar()
                .init();

        GlideUtils.loadImageView(mContext,path,ivPic);



        initSeekBar();
    }

    private void initSeekBar() {
        GPUImageContrastFilter contrastFilter = new GPUImageContrastFilter(2.0f);
        GPUImageExposureFilter exposureFilter = new GPUImageExposureFilter(0.0f);
        GPUImageSaturationFilter saturationFilter = new GPUImageSaturationFilter(1.0f);

        sbContrastRatio.setMin(0f);
        sbContrastRatio.setMax(2f);
        sbContrastRatio.setProgress(2f);
        sbContrastRatio.setOnSeekChangeListener(new OnSeekChangeListener() {
            @Override
            public void onSeeking(SeekParams seekParams) {
            }

            @Override
            public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                sbContrastRatio.post(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.showProgressDialog(mContext);
                    }
                });
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        tempPhotoPath = tempPhotoDir + "/temp.jpg";
                        MyProgressDialog.showProgressDialog(mContext);
//                    contrastFilter.setContrast(range(seekBar.getProgress(), 0.0f, 2.0f));
                        contrastFilter.setContrast(seekBar.getProgressFloat());
                        setGpuImage(getIntent().getStringExtra("path"), contrastFilter);
                        setGpuImage(tempPhotoPath, saturationFilter);
                        setGpuImage(tempPhotoPath, exposureFilter);
                        parsePlayRequest();
                    }
                });
            }
        });

        sbExposure.setMin(-10f);
        sbExposure.setMax(10f);
        sbExposure.setProgress(0f);
        sbExposure.setOnSeekChangeListener(new OnSeekChangeListener() {
            @Override
            public void onSeeking(SeekParams seekParams) {
            }

            @Override
            public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
//                    exposureFilter.setExposure(range(seekBar.getProgress(), -10.0f, 10.0f));
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        tempPhotoPath = tempPhotoDir + "/temp.jpg";
                        MyProgressDialog.showProgressDialog(mContext);
                        exposureFilter.setExposure(seekBar.getProgressFloat());
                        setGpuImage(getIntent().getStringExtra("path"), contrastFilter);
                        setGpuImage(tempPhotoPath, saturationFilter);
                        setGpuImage(tempPhotoPath, exposureFilter);
                        parsePlayRequest();
                    }
                });
            }
        });

        sbSaturation.setMin(0f);
        sbSaturation.setMax(2f);
        sbSaturation.setProgress(1);
        sbSaturation.setOnSeekChangeListener(new OnSeekChangeListener() {
            @Override
            public void onSeeking(SeekParams seekParams) {
            }

            @Override
            public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        tempPhotoPath = tempPhotoDir + "/temp.jpg";
                        MyProgressDialog.showProgressDialog(mContext);
//                    saturationFilter.setSaturation(range(seekBar.getProgress(), 0.0f, 2.0f));
                        saturationFilter.setSaturation(seekBar.getProgressFloat());
                        setGpuImage(getIntent().getStringExtra("path"), contrastFilter);
                        setGpuImage(tempPhotoPath, saturationFilter);
                        setGpuImage(tempPhotoPath, exposureFilter);
                        parsePlayRequest();
                    }
                });
            }
        });
    }

    private void parsePlayRequest() {
        GlideUtils.loadImageView(mContext,path,ivPic);


        Glide.with(mContext)
                .load(path)
                .listener(new RequestListener<Drawable>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                        MyProgressDialog.closeProgressDialog();
                        return false;
                    }

                    @Override
                    public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                        MyProgressDialog.closeProgressDialog();
                        return false;
                    }
                }).into(ivPic);
    }


    private void setGpuImage(String gpuPath, GPUImageFilter filter) {
        if (!new File(tempPhotoDir).exists()) {
            boolean mkdirs = new File(tempPhotoDir).mkdirs();
        }
        if (!new File(tempPhotoPath).exists()) {
            try {
                boolean newFile = new File(tempPhotoPath).createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        Bitmap bitmap = BitmapFactory.decodeFile(gpuPath, getBitmapOption(1)); //将图片的长和宽缩小味原来的1/2
        GPUImage gpuImage = new GPUImage(mContext);
        gpuImage.setImage(bitmap);


        if (sbContrastRatio.getProgressFloat() > 0) {
            gpuImage.setFilter(new GPUImageContrastFilter(sbContrastRatio.getProgressFloat()));
        } else {
            gpuImage.setFilter(new GPUImageContrastFilter(0.1f));
        }

        if (sbContrastRatio.getProgressFloat() > 0) {
            gpuImage.setFilter(new GPUImageContrastFilter(sbContrastRatio.getProgressFloat()));
        } else {
            gpuImage.setFilter(new GPUImageContrastFilter(0.1f));
        }

        gpuImage.setFilter(filter);

        Bitmap newBitmap = gpuImage.getBitmapWithFilterApplied();

        File myCaptureFile = new File(tempPhotoPath);
        try {
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(myCaptureFile));
            newBitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            bos.flush();
            bos.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        path = tempPhotoPath;
    }
}

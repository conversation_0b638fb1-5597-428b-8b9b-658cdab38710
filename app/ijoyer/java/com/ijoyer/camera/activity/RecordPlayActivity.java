package com.ijoyer.camera.activity;

import static com.icatch.mobilecam.Application.PanoramaApp.getContext;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.ExoPlayerFactory;
import com.google.android.exoplayer2.PlaybackParameters;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.Timeline;
import com.google.android.exoplayer2.source.ExtractorMediaSource;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.TrackGroupArray;
import com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.TrackSelection;
import com.google.android.exoplayer2.trackselection.TrackSelectionArray;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.BandwidthMeter;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultBandwidthMeter;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.gyf.immersionbar.BarHide;
import com.gyf.immersionbar.ImmersionBar;
import com.huawei.hms.videoeditor.ui.api.MediaApplication;
import com.huawei.hms.videoeditor.ui.api.MediaExportCallBack;
import com.huawei.hms.videoeditor.ui.api.MediaInfo;
import com.huawei.hms.videoeditor.ui.api.VideoEditorLaunchOption;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.TimeUtil;
import com.ijoyer.camera.http.net.rx.RetrofitManager;
import com.ijoyer.camera.http.server.UserService;
import com.ijoyer.camera.ui.PermissionPopup;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.R;
import com.pili.pldroid.player.common.Util;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class RecordPlayActivity extends BugTagsBaseActivity {

    @BindView(R.id.video_player)
    PlayerView videoPlayer;
    @BindView(R.id.tv_edit)
    TextView tvEdit;

    private Context mContext;
    private SimpleExoPlayer exoPlayer;
    private String path;
    private UserService userService;


    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_record_play);
        mContext = this;
        path = getIntent().getStringExtra("path");
        ButterKnife.bind(this);
        userService = new RetrofitManager().getDefaultClient(UserService.class);
        ImmersionBar.with(this)
                .transparentBar()
                .hideBar(BarHide.FLAG_HIDE_BAR)
                .transparentStatusBar()
                .transparentNavigationBar()
                .init();
    }

    @Override
    protected void onResume() {
        super.onResume();
        initVideo();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (exoPlayer != null) {
            exoPlayer.release();
            exoPlayer = null;
        }
    }

    private void initVideo() {
        // 得到默认合适的带宽
        BandwidthMeter bandwidthMeter = new DefaultBandwidthMeter();
        // 创建跟踪的工厂
        TrackSelection.Factory factory = new AdaptiveTrackSelection.Factory(bandwidthMeter);
        // 创建跟踪器
        DefaultTrackSelector trackSelection = new DefaultTrackSelector(factory);
        // 创建播放器
        exoPlayer = ExoPlayerFactory.newSimpleInstance(mContext, trackSelection);

        // 生成数据媒体实例，通过该实例加载媒体数据
        DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(mContext, Util.getUserAgent(mContext, "PanoramaApp"));
        // 创建资源
        Uri uri = Uri.parse(path);
        MediaSource mediaSource = new ExtractorMediaSource.Factory(dataSourceFactory).createMediaSource(uri);
        // 将播放器附加到view
        videoPlayer.setPlayer(exoPlayer);
        // 准备播放
        exoPlayer.prepare(mediaSource);
        // 准备好了之后自动播放，如果已经准备好了，调用该方法实现暂停、开始功能
        exoPlayer.setRepeatMode(Player.REPEAT_MODE_OFF);
        // 添加事件监听
        exoPlayer.addListener(new Player.EventListener() {
            @Override
            public void onTimelineChanged(Timeline timeline, @Nullable Object manifest, int reason) {

            }

            @Override
            public void onTracksChanged(TrackGroupArray trackGroups, TrackSelectionArray trackSelections) {

            }

            @Override
            public void onLoadingChanged(boolean isLoading) {

            }

            @Override
            public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {

            }

            @Override
            public void onRepeatModeChanged(int repeatMode) {

            }

            @Override
            public void onShuffleModeEnabledChanged(boolean shuffleModeEnabled) {

            }

            @Override
            public void onPlayerError(ExoPlaybackException error) {

            }

            @Override
            public void onPositionDiscontinuity(int reason) {

            }

            @Override
            public void onPlaybackParametersChanged(PlaybackParameters playbackParameters) {

            }

            @Override
            public void onSeekProcessed() {

            }
        });
        videoPlayer.setShowShuffleButton(false);
        exoPlayer.setPlayWhenReady(true);
    }


    @OnClick({R.id.tv_edit, R.id.local_pb_back})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.tv_edit:
                // 检查READ_PHONE_STATE权限，HMS Video Editor需要此权限
                if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE)
                        != PackageManager.PERMISSION_GRANTED) {
                    // 权限未授予，显示权限请求弹窗
                    requestHmsPermissionForVideoEditor();
                    break;
                }

                // 权限已授予，启动HMS视频编辑器
                launchHmsVideoEditor();
                break;
            case R.id.local_pb_back:
                finish();
                break;
        }
    }

    private void getHmsToken() {
//        userService.getToken("client_credentials","104616831","fb0ac89a8ec87bc13ffba1f0968dc334af3a2672b21add885cb831e437288e82")
//                .subscribeOn(AndroidSchedulers.mainThread())
//                .observeOn(AndroidSchedulers.mainThread())
//                .subscribe(new CustomDisposableForJava<TokenBean>(getContext()) {
//                    @Override
//                    public void onNext(TokenBean bean) {
//                        super.onNext(bean);
//                        runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                try {
//                                     MediaApplication.getInstance().setAccessToken(bean.access_token);
//                                } catch (Exception e) {
//                                    e.printStackTrace();
//                                }
//
//
//                            }
//                        });
//
//                    }
//
//                    @Override
//                    public void onError(Throwable e) {
//                        super.onError(e);
//                    }
//                });
    }

    private static MediaExportCallBack callBack = new MediaExportCallBack() {
        @Override
        public void onMediaExportSuccess(MediaInfo mediaInfo) {
            // 导出成功
            String mediaPath = mediaInfo.getMediaPath();

            final MediaMetadataRetriever mediaMetadata = new MediaMetadataRetriever();
            mediaMetadata.setDataSource(getContext(), Uri.parse(mediaPath));
            int width = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
            int height = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
            String outPath = "";

            if (width/height == 2){
                //全景视频
                outPath = StorageUtil.getRootPath(getContext()) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() +  "_" + System.currentTimeMillis() /1000+ ".MP4";
            } else {
                //录屏视频
                outPath = StorageUtil.getRootPath(getContext()) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + "_RECORD.MP4";
            }

            FileUtils.copy(mediaPath, outPath);
            MediaRefresh.scanFileAsync(getContext(), outPath);
            FileUtils.delete(mediaPath);
        }

        @Override
        public void onMediaExportFailed(int errorCode) {
            // 导出失败
        }
    };

    /**
     * 请求HMS功能所需的READ_PHONE_STATE权限
     */
    private void requestHmsPermissionForVideoEditor() {
        PermissionPopup popup = new PermissionPopup(this);
        popup.setReqDesc("为使用视频编辑功能，请授予设备信息权限用于获取网络状态");
        popup.showPopupWindow();
        popup.tvRight.setOnClickListener(v -> {
            popup.dismiss();
            PermissionUtils.permission(PermissionConstants.PHONE)
                    .rationale((activity, shouldRequest) -> new Handler().postDelayed(() ->
                            shouldRequest.again(true), 100))
                    .callback(new PermissionUtils.FullCallback() {
                        @Override
                        public void onGranted(List<String> permissionsGranted) {
                            // 权限授予成功，启动HMS视频编辑器
                            launchHmsVideoEditor();
                        }

                        @Override
                        public void onDenied(List<String> permissionsDeniedForever, List<String> permissionsDenied) {
                            Toast.makeText(RecordPlayActivity.this, "缺少设备信息权限，无法使用视频编辑功能", Toast.LENGTH_LONG).show();
                        }
                    }).request();
        });
    }

    /**
     * 启动HMS视频编辑器
     */
    private void launchHmsVideoEditor() {
        try {
            MediaApplication.getInstance().setApiKey("CwEAAAAAilz6/UCs1xRzXdGzAcuq5FVjoahmIHUruify0vaxv5/uyfXtXrF3v56qMm5R9MPb5FsMf6C8u5lMBiFJCL+k6fP9Rks=");
            MediaApplication.getInstance().setLicenseId(DeviceUtils.getUniqueDeviceId());
            VideoEditorLaunchOption option = new VideoEditorLaunchOption.Builder().setStartMode(MediaApplication.START_MODE_IMPORT_FROM_MEDIA).build();
            MediaApplication.getInstance().launchEditorActivity(mContext, option);
            MediaApplication.getInstance().setOnMediaExportCallBack(callBack);
        } catch (Exception e) {
            LogUtil.e("HMS Video Editor启动失败: " + e.getMessage());
            Toast.makeText(this, "视频编辑器启动失败", Toast.LENGTH_SHORT).show();
        }
    }
}

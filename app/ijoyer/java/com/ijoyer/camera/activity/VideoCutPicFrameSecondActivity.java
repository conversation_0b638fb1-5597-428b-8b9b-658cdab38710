package com.ijoyer.camera.activity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.PersistableBundle;
import android.view.View;

import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.utils.TimeUtil;
import com.ijoyer.camera.base.BaseActivity;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.R;

import VideoHandle.EpEditor;
import VideoHandle.OnEditorListener;

public class VideoCutPicFrameSecondActivity extends BugTagsBaseActivity {

    private boolean isRecord;
    private boolean canSaveFile;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        hideActionBar();
        MyProgressDialog.showProgressDialog(this, R.string.action_processing);
        String path = getIntent().getStringExtra("path");
        isRecord = getIntent().getBooleanExtra("isRecord", false);
        canSaveFile = getIntent().getBooleanExtra("canSaveFile", false);
        cutPicFrameSecond(path);
    }

    private void cutPicFrameSecond(String path) {
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
//                String saveDir = Environment.getExternalStorageDirectory() + "/DCIM/IJTEMP/" + TimeUtil.getNowDateTime() + "/";
                String saveDir = getCacheDir() + "/IJTEMP/" + TimeUtil.getNowDateTime() + "/";

                FileUtils.createOrExistsDir(saveDir);
                String cmd = "-i " + path + " -s 200x100 -vf fps=1 " + saveDir + "image-%3d.jpg";
                EpEditor.execCmd(cmd, 0, new OnEditorListener() {
                    @Override
                    public void onSuccess() {
                        MyProgressDialog.closeProgressDialog();
                        Intent intent = new Intent();
                        intent.putExtra("canSaveFile", canSaveFile);
                        intent.putExtra("isRecord", isRecord);
                        intent.putExtra("path", getIntent().getStringExtra("path"));

                        if ( getIntent().getStringExtra("Activity")!= null && getIntent().getStringExtra("Activity").contains("VideoCutActivity")){
                            intent.setClass(mContext, VideoCutNewActivity.class);
                            ActivityUtils.finishActivity(VideoCutActivity.class);
                        } else {
                            intent.setClass(mContext, VideoCutActivity.class);
                            ActivityUtils.finishActivity(VideoCutNewActivity.class);
                        }

                        intent.putExtra("saveDir", saveDir);
                        startActivity(intent);
                        ActivityUtils.finishActivity(VideoCutPicFrameSecondActivity.class);
                        LogUtil.e("获取帧图成功");
                    }

                    @Override
                    public void onFailure() {
                        MyProgressDialog.closeProgressDialog();
                        Intent intent = new Intent();
                        intent.putExtra("canSaveFile", canSaveFile);
                        intent.putExtra("isRecord", isRecord);
                        intent.putExtra("path", getIntent().getStringExtra("path"));
                        intent.setClass(mContext, VideoCutActivity.class);
                        startActivity(intent);
                        ActivityUtils.finishActivity(VideoCutPicFrameSecondActivity.class);
                        LogUtil.e("获取帧图失败");
                    }

                    @Override
                    public void onProgress(float progress) {
                    }
                });
            }
        },1000);

    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }
    }

    public void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setNavigationBarColor(Color.TRANSPARENT);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
    }
}

package com.ijoyer.camera.activity;

import static com.detu.szStitch.StitchUtils.getBitmapOption;
import static com.icatch.mobilecam.Application.PanoramaApp.getContext;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PathUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.blankj.utilcode.util.UtilsTransActivity;
import com.bumptech.glide.Glide;
import com.detu.PanoramaPlayerDemo.utils.DTListDialog;
import com.detu.PanoramaPlayerDemo.utils.SwipeEvent;
import com.detu.android_panoplayer.PanoPlayerImpl;
import com.detu.android_panoplayer.PanoPlayerUrl;
import com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView;
import com.detu.getmaskandweight.FileReadandWrite;
import com.detu.getmaskandweight.GetWeightAndMaskHelper;
import com.huawei.hms.image.vision.ImageVision;
import com.huawei.hms.image.vision.ImageVisionImpl;
import com.huawei.hms.image.vision.bean.ImageVisionResult;
import com.huawei.hms.videoeditor.ui.api.MediaApplication;
import com.huawei.hms.videoeditor.ui.api.MediaExportCallBack;
import com.huawei.hms.videoeditor.ui.api.MediaInfo;
import com.huawei.hms.videoeditor.ui.api.VideoEditorLaunchOption;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.utils.ClickUtils;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.PermissionTools;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.TimeUtil;
import com.ijoyer.camera.bean.FilterBean;
import com.ijoyer.camera.http.net.CustomDisposableForJava;
import com.ijoyer.camera.http.net.bean.TokenBean;
import com.ijoyer.camera.http.net.rx.RetrofitManager;
import com.ijoyer.camera.http.server.UserService;
import com.ijoyer.camera.ui.BottomPopup;
import com.ijoyer.camera.ui.PermissionPopup;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.camera.widget.SettingRecordDialog;
import com.ijoyer.mobilecam.R;
import com.player.panoplayer.GLGesture;
import com.player.panoplayer.IPanoPlayerListener;
import com.player.panoplayer.IPanoPluginListener;
import com.player.panoplayer.IRecordListener;
import com.player.panoplayer.enitity.EaseTypes;
import com.player.panoplayer.enitity.LiteDecor;
import com.player.panoplayer.enitity.PanoDeviceId;
import com.player.panoplayer.enitity.PanoPlayerError;
import com.player.panoplayer.enitity.PanoPlayerStatus;
import com.player.panoplayer.enitity.PanoPluginError;
import com.player.panoplayer.enitity.PanoPluginStatus;
import com.player.panoplayer.enitity.PanoViewMode;
import com.player.panoplayer.plugin.Plugin;
import com.player.panoplayer.plugin.VideoPlugin;
import com.player.panoplayer.view.DisplayType;
import com.player.panoplayer.view.LayoutParams;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import Jni.VideoUitls;
import VideoHandle.EpEditor;
import VideoHandle.OnEditorListener;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
import io.reactivex.android.schedulers.AndroidSchedulers;
import jp.co.cyberagent.android.gpuimage.GPUImage;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageContrastFilter;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageExposureFilter;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageFilter;
import jp.co.cyberagent.android.gpuimage.filter.GPUImageSaturationFilter;
import razerdp.basepopup.BasePopupWindow;

public class PanoramaPlayerActivity extends AppCompatActivity implements
        View.OnClickListener,
        IPanoPlayerListener,
        IPanoPluginListener,
        GLGesture.ClickPanoViewListener {
    private static final String TAG = PanoramaPlayerActivity.class.getSimpleName();
    @BindView(R.id.iv_play)
    ImageView ivPlay;
    @BindView(R.id.panorama_type_btn)
    TextView panoramaTypeBtn;
    @BindView(R.id.local_pb_top_layout)
    RelativeLayout localPbTopLayout;
    @BindView(R.id.tv_edit)
    TextView tvEdit;
    @BindView(R.id.iv_record)
    ImageView ivRecord;
    @BindView(R.id.ll_edit_photo)
    LinearLayout llEditPhoto;
    @BindView(R.id.tv_save)
    TextView tvSave;
    @BindView(R.id.sb_contrast_ratio)
    IndicatorSeekBar sbContrastRatio;
    @BindView(R.id.sb_exposure)
    IndicatorSeekBar sbExposure;
    @BindView(R.id.sb_saturation)
    IndicatorSeekBar sbSaturation;
    @BindView(R.id.iv_test)
    ImageView ivTest;
    @BindView(R.id.tv_reduction)
    TextView tvReduction;
    private Context context;
    private Boolean isControlNeedInvisible = false;
    float[] curGestureData = {};
    boolean isReplay = false;
    PanoViewMode curViewMode = PanoViewMode.DEF;
    private final int REQUEST_MEDIA_PROJECTION = 1;
    private Intent mResultIntent = null;
    private int mResultCode = 0;
    private static int mCurPhotoIndex = 0;
    private boolean isDownRecord = false;
    private boolean isDownRecordEnd = false;
    private Handler handlerCopyFile = new Handler();
    private Runnable runnableCopyFile = new Runnable() {
        @Override
        public void run() {
            if (ivPlay != null) {
                ivPlay.setImageDrawable(getResources().getDrawable(R.drawable.ic_play_arrow_white));
            }
//          videoForMp4();
            mp3Cut(url);
        }
    };

    private int count = 0;
    private Handler handlerRecord = new Handler();
    private Runnable runnableRecord = new Runnable() {
        @Override
        public void run() {
            ThreadUtils.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (count % 2 == 1) {
                        Glide.with(context).load(R.drawable.icon_record1).into(ivRecord);
                    } else {
                        Glide.with(context).load(R.drawable.icon_record2).into(ivRecord);
                    }
                    handlerRecord.postDelayed(runnableRecord, 600);
                    count++;
                }
            });

        }
    };

    private JSONObject authJson;
    String string
            = "{\"projectId\":\"projectIdIjoyer\",\"appId\":\"appIdIjoyer\",\"authApiKey\":\"authApiKeyIjoyer\",\"clientSecret\":\"clientSecretIjoyer\",\"clientId\":\"clientIdIjoyer\",\"token\":\"tokenIjoyer\"}";

    {
        try {
            authJson = new JSONObject(string);
        } catch (JSONException e) {
            LogUtil.e("filter exp" + e.getMessage());
        }
    }

    ExecutorService mExecutorService = Executors.newFixedThreadPool(1);
    ImageVisionImpl imageVisionFilterAPI;


    private File fileVideoRecord;
    private String allMp3;
    private File fileVideoRecordMp4;
    private String outMp3;
    private String outMp3Extract;
    private boolean isShowPhotoEdit;
    private boolean isEditing = false;
    private String path = "";
    private String tempPhotoDir = PathUtils.getInternalAppDataPath() + "/temp";
    private String tempPhotoPath = "";
    private Timer timer;
    private Context mContext;
    private TimerTask task;
    private File fileVideoRecordModify;
    private BottomPopup bottomPopup;
    private ArrayList<FilterBean> mFilterData;
    private int initCodeState = -2;
    private int stopCodeState = -2;
    private Bitmap bitmap;
    private UserService userService;
    private boolean isModify = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mResultIntent = PanoramaApp.getInstance().getResultIntent();
        mResultCode = PanoramaApp.getInstance().getResultCode();
        path = getIntent().getStringExtra("path");
        if (!FileUtils.isFileExists(path)){
            AppToast.show(this,"文件不存在，请退出页面后重新下载");
            LogUtils.file("预览VR ：路径对应的文件不存在：" + path);
            finish();
            return;
        }
        mContext = this;
        initLayout();
        initPlayer();
        initBtnFuncSwitch();
        initBtnModeChoose();
        hideControlDelay();
        Bundle data = getIntent().getExtras();
        userService = new RetrofitManager().getDefaultClient(UserService.class);
        mCurPhotoIndex = data.getInt("curFilePosition", 0);
        getScreenBaseInfo();
        initFilter(context);
        bottomPopup = new BottomPopup(mContext);
        setFilterData();
        bottomPopup.adapter.setNewInstance(mFilterData);

        isShowPhotoEdit = getIntent().getBooleanExtra("isShowPhotoEdit", false);
        if (isShowPhotoEdit) {
            GPUImageContrastFilter contrastFilter = new GPUImageContrastFilter(2.0f);
            GPUImageExposureFilter exposureFilter = new GPUImageExposureFilter(0.0f);
            GPUImageSaturationFilter saturationFilter = new GPUImageSaturationFilter(1.0f);

            tvEdit.setVisibility(View.GONE);
            sbContrastRatio.setMin(0f);
            sbContrastRatio.setMax(2f);
            sbContrastRatio.setProgress(1f);
            sbContrastRatio.setOnSeekChangeListener(new OnSeekChangeListener() {
                @Override
                public void onSeeking(SeekParams seekParams) {
                }

                @Override
                public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
                }

                @Override
                public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                    sbContrastRatio.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.showProgressDialog(mContext, R.string.action_processing);
                        }
                    });

                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            tempPhotoPath = tempPhotoDir + "/temp.jpg";
                            contrastFilter.setContrast(seekBar.getProgressFloat());
                            setGpuImage(getIntent().getStringExtra("path"), contrastFilter);
                            setGpuImage(tempPhotoPath, saturationFilter);
                            setGpuImage(tempPhotoPath, exposureFilter);
                            parsePlayRequest();
                        }
                    }).start();
                }
            });
            sbExposure.setMin(-3f);
            sbExposure.setMax(3f);
            sbExposure.setProgress(0f);
            sbExposure.setOnSeekChangeListener(new OnSeekChangeListener() {
                @Override
                public void onSeeking(SeekParams seekParams) {
                }

                @Override
                public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
                }

                @Override
                public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                    sbExposure.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.showProgressDialog(mContext, R.string.action_processing);
                        }
                    });

                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            tempPhotoPath = tempPhotoDir + "/temp.jpg";
                            exposureFilter.setExposure(seekBar.getProgressFloat());
                            setGpuImage(getIntent().getStringExtra("path"), contrastFilter);
                            setGpuImage(tempPhotoPath, saturationFilter);
                            setGpuImage(tempPhotoPath, exposureFilter);
                            parsePlayRequest();
                        }
                    }).start();
                }
            });
            sbSaturation.setMin(0f);
            sbSaturation.setMax(2f);
            sbSaturation.setProgress(1);
            sbSaturation.setOnSeekChangeListener(new OnSeekChangeListener() {
                @Override
                public void onSeeking(SeekParams seekParams) {
                }

                @Override
                public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
                }

                @Override
                public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                    sbExposure.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.showProgressDialog(mContext, R.string.action_processing);
                        }
                    });

                    new Thread(new Runnable() {
                        @Override
                        public void run() {
                            tempPhotoPath = tempPhotoDir + "/temp.jpg";
                            saturationFilter.setSaturation(seekBar.getProgressFloat());
                            setGpuImage(getIntent().getStringExtra("path"), contrastFilter);
                            setGpuImage(tempPhotoPath, saturationFilter);
                            setGpuImage(tempPhotoPath, exposureFilter);
                            parsePlayRequest();
                        }
                    }).start();


                }
            });
        }

//        HVERecord.init(this, mHVERecordListener);
//
//        HVERecordConfiguration hveRecordConfiguration = new HVERecordConfiguration.Builder()
//                .setMicStatus(true)
//                .setOrientationMode(HVEOrientationMode.LANDSCAPE)
//                .setResolutionMode(HVEResolutionMode.RES_1080P)
//                .setStorageFile(new File("/sdcard/DCIM/IJOYER"))
//                .build();
//        HVERecord.setConfigurations(hveRecordConfiguration);

    }

    private void setFilterData() {
        mFilterData = new ArrayList<>();
        FilterBean filterBean = new FilterBean("0", "无", R.drawable.icon_filter_no);
        filterBean.isSelect = true;
        mFilterData.add(filterBean);
        mFilterData.add(new FilterBean("1", "黑白", R.drawable.icon_filter1));
        mFilterData.add(new FilterBean("2", "综调", R.drawable.icon_filter3));
        mFilterData.add(new FilterBean("3", "慵懒", R.drawable.icon_filter8));
        mFilterData.add(new FilterBean("4", "小苍兰", R.drawable.icon_filter24));
        mFilterData.add(new FilterBean("5", "富士", R.drawable.icon_filter7));
        mFilterData.add(new FilterBean("6", "桃粉", R.drawable.icon_filter19));
        mFilterData.add(new FilterBean("7", "海盐", R.drawable.icon_filter21));
        mFilterData.add(new FilterBean("8", "薄荷", R.drawable.icon_filter2));
        mFilterData.add(new FilterBean("9", "蒹葭", R.drawable.icon_filter13));
        mFilterData.add(new FilterBean("10", "复古", R.drawable.icon_filter4));
        mFilterData.add(new FilterBean("11", "棉花糖", R.drawable.icon_filter18));
        mFilterData.add(new FilterBean("12", "青苔", R.drawable.icon_filter11));
        mFilterData.add(new FilterBean("13", "日光", R.drawable.icon_filter9));
        mFilterData.add(new FilterBean("14", "时光", R.drawable.icon_filter16));
        mFilterData.add(new FilterBean("15", "雾霾蓝", R.drawable.icon_filter12));
        mFilterData.add(new FilterBean("16", "向日葵", R.drawable.icon_filter22));
        mFilterData.add(new FilterBean("17", "硬朗", R.drawable.icon_filter17));
        mFilterData.add(new FilterBean("18", "古铜黄", R.drawable.icon_filter5));
        mFilterData.add(new FilterBean("19", "黑白调", R.drawable.icon_filter10));
        mFilterData.add(new FilterBean("20", "黄绿调", R.drawable.icon_filter23));
        mFilterData.add(new FilterBean("21", "黄调", R.drawable.icon_filter15));
        mFilterData.add(new FilterBean("22", "绿调", R.drawable.icon_filter20));
        mFilterData.add(new FilterBean("23", "青调", R.drawable.icon_filter14));
        mFilterData.add(new FilterBean("24", "紫调", R.drawable.icon_filter6));
    }


    private void setGpuImage(String gpuPath, GPUImageFilter filter) {
        if (!new File(tempPhotoDir).exists()) {
            boolean mkdirs = new File(tempPhotoDir).mkdirs();
        }
        if (!new File(tempPhotoPath).exists()) {
            try {
                boolean newFile = new File(tempPhotoPath).createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        Bitmap bitmap = BitmapFactory.decodeFile(gpuPath, getBitmapOption(1)); //将图片的长和宽缩小味原来的1/2
        GPUImage gpuImage = new GPUImage(context);
        gpuImage.setImage(bitmap);


        if (sbContrastRatio.getProgressFloat() > 0) {
            gpuImage.setFilter(new GPUImageContrastFilter(sbContrastRatio.getProgressFloat()));
        } else {
            gpuImage.setFilter(new GPUImageContrastFilter(0.1f));
        }

        if (sbContrastRatio.getProgressFloat() > 0) {
            gpuImage.setFilter(new GPUImageContrastFilter(sbContrastRatio.getProgressFloat()));
        } else {
            gpuImage.setFilter(new GPUImageContrastFilter(0.1f));
        }

        gpuImage.setFilter(filter);

        Bitmap newBitmap = gpuImage.getBitmapWithFilterApplied();

        File myCaptureFile = new File(tempPhotoPath);
        try {
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(myCaptureFile));
            newBitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            bos.flush();
            bos.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        path = tempPhotoPath;
    }

    protected float range(int percentage, float start, float end) {
        return (end - start) * percentage / 100.0f + start;
    }

    private void mp3Extract() {
        FileUtils.createOrExistsDir(Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH);
        allMp3 = Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH + TimeUtil.getNowDateTime() + "_RECORD.mp3";
        String cmd = "-i " + url + " " + allMp3;

        EpEditor.execCmd(cmd, 0, new OnEditorListener() {
            @Override
            public void onSuccess() {
                LogUtil.e("提取MP3成功");
            }

            @Override
            public void onFailure() {
                LogUtil.e("提取MP3失败");
            }

            @Override
            public void onProgress(float progress) {
            }
        });
    }

    private void initFilter(final Context context) {
        // 检查READ_PHONE_STATE权限，HMS Image Vision API需要此权限获取网络类型
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE)
                != PackageManager.PERMISSION_GRANTED) {
            LogUtils.file("PanoramaPlayerActivity initFilter: READ_PHONE_STATE权限未授予，HMS滤镜功能将不可用");
            return;
        }

        imageVisionFilterAPI = ImageVision.getInstance(this);
        imageVisionFilterAPI.setVisionCallBack(new ImageVision.VisionCallBack() {
            @Override
            public void onSuccess(int successCode) {
                try {
                    int initCode = imageVisionFilterAPI.init(context, authJson);
                    initCodeState = initCode;
                    stopCodeState = -2;
                    LogUtils.file("PanoramaPlayerActivity HMS Image Vision API 初始化成功");
                } catch (SecurityException e) {
                    LogUtils.file("PanoramaPlayerActivity init 抛出 SecurityException（可能是缺少 READ_PHONE_STATE 权限）：" + e.getMessage());
                }
            }

            @Override
            public void onFailure(int errorCode) {
                LogUtil.e("ImageVisionAPI fail, errorCode: " + errorCode);
            }
        });
    }

    private void mp3ExtractV2() {
        FileUtils.createOrExistsDir(Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH);
        outMp3Extract = Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH + TimeUtil.getNowDateTime() + "_RECORD.aac";
        String cmd = "-i " + this.outMp3 + " -acodec aac -strict experimental -y " + outMp3Extract;

        EpEditor.execCmd(cmd, 0, new OnEditorListener() {
            @Override
            public void onSuccess() {
                LogUtil.e("转码MP3成功");
                mergeMp3AndMp4(outMp3Extract);
            }


            @Override
            public void onFailure() {
                LogUtil.e("转码MP3失败");
            }

            @Override
            public void onProgress(float progress) {
            }
        });
    }

    private int mScreenWidth;
    private int mScreenHeight;
    private int mScreenDensity;
    private boolean isStarted = false;
    private boolean isVideoSd = true;
    private boolean isAudio = true;

    private void getScreenBaseInfo() {
        mScreenWidth = ScreenUtils.getScreenWidth();
        mScreenHeight = ScreenUtils.getScreenHeight();
        mScreenDensity = ScreenUtils.getScreenDensityDpi();
        SPUtils spUtils = SPUtils.getInstance("record_param");
        SettingRecordDialog.RecordType type;
        type = SettingRecordDialog.RecordType.values()[spUtils.getInt("KEY_RECORD", SettingRecordDialog.DEFAULT_RECORD.ordinal())];
        switch (type) {
            case SD:
                mScreenWidth = mScreenWidth / 3;
                mScreenHeight = mScreenHeight / 3;
                break;
            case HD:
                mScreenWidth = mScreenWidth / 2;
                mScreenHeight = mScreenHeight / 2;
                break;
            case FHD:
                ;
                break;
            default:
                if (mScreenWidth >= 1000) {
                    mScreenWidth = mScreenWidth / 2;
                    mScreenHeight = mScreenHeight / 2;
                }
                break;
        }
        if (mScreenWidth % 2 != 0) {
            mScreenWidth--;
        }
        if (mScreenHeight % 2 != 0) {
            mScreenHeight--;
        }
        AppLog.d(TAG, "ScreenWidth: " + mScreenWidth);
        AppLog.d(TAG, "ScreenHeight: " + mScreenHeight);
    }

    private void startScreenRecording() {
//        MediaProjectionManager mediaProjectionManager = (MediaProjectionManager) getSystemService(Context.MEDIA_PROJECTION_SERVICE);
//        Intent permissionIntent = mediaProjectionManager.createScreenCaptureIntent();
//        try {
//            startActivityForResult(permissionIntent, REQUEST_MEDIA_PROJECTION);
//        } catch (Exception e) {
//            e.printStackTrace();
//            Toast.makeText(this, "当前系统不支持录屏功能", Toast.LENGTH_SHORT).show();
//            showControlView();
//        }

        if (tag != 1) {
            Plugin plugin = panoramaPlayer.getPlugin();
            if (plugin instanceof VideoPlugin) {
                ((VideoPlugin) plugin).start();
            }
        }
        ivPlay.setImageDrawable(getResources().getDrawable(R.drawable.ic_pause_white));
        isDownRecord = true;
        isDownRecordEnd = false;
        downTime = System.currentTimeMillis();
        txtRecord.setText(R.string.record_screen_end);
        ivRecord.setVisibility(View.VISIBLE);
        isStarted = !isStarted;
        fileVideoRecord = new File(this.getCacheDir(), TimeUtil.getNowDateTime() + "_RECORD.MP4");
        panoramaPlayer.startRecording(fileVideoRecord.getAbsolutePath());
        handlerRecord.post(runnableRecord);

//        HVERecord.startRecord();
    }


    private void stopScreenRecording() {
//        Intent service = new Intent(this, ScreenRecordService.class);
//        stopService(service);
//        isStarted = !isStarted;

        ivRecord.setVisibility(View.GONE);
        isStarted = !isStarted;
        isDownRecord = false;
        isDownRecordEnd = true;

//        HVERecord.stopRecord();
        panoramaPlayer.stopRecording(new IRecordListener() {
            @Override
            public void onEnd() {
//                Plugin plugin = panoramaPlayer.getPlugin();
//                if (plugin instanceof VideoPlugin) {
//                    ((VideoPlugin) plugin).pause();
//                }

                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (txtRecord != null)
                            txtRecord.setText(R.string.record_screen);
                    }
                });

                MediaRefresh.notifySystemToScan(fileVideoRecord);


                if (tag == 1) {
                    try {
                        File newFile = new File(Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + TimeUtil.getNowDateTime() + "_RECORD.MP4");
                        copyFile(newFile, new FileInputStream(fileVideoRecord));
                        MediaRefresh.scanFileAsync(context, newFile.getAbsolutePath());
                    } catch (FileNotFoundException e) {
                        e.printStackTrace();
                    }
                    finish();
                    return;
                }
                fileVideoRecordModify = new File(mContext.getCacheDir(), TimeUtil.getNowDateTime() + "_RECORD.MP4");
                long duration = VideoUitls.getDuration(fileVideoRecord.getPath()) / 1000;

                long hour = duration / 60 / 60 / 1000;
                long minute = duration / 60 / 1000;
                long second = duration / 1000 % 60;
                double secondDecimal = (double) ((double) duration / 1000 % 60);
                String hourStr = String.format("%02d", hour);
                String minuteStr = String.format("%02d", minute);
                String secondStr = String.format("%02d", second);
                String secondDecimalStr = String.format("%.2f", secondDecimal);
                secondDecimalStr = secondDecimalStr.split("[.]")[1];
                String cmd = "-i " + fileVideoRecord.getPath() + " -ss 00:00:00.2 -to " + hourStr + ":" + minuteStr + ":" + secondStr + "." + secondDecimalStr + " -vcodec copy -acodec copy -q:v 1 " + fileVideoRecordModify.getPath();
                LogUtil.e("cmd:" + cmd);
                EpEditor.execCmd(cmd, 0, new OnEditorListener() {
                    @Override
                    public void onSuccess() {
                        LogUtil.e("裁剪成功");
                        MediaRefresh.scanFileAsync(context, fileVideoRecordModify.getAbsolutePath());

                        handlerCopyFile.postDelayed(runnableCopyFile, 2000);
                        upTime = System.currentTimeMillis();
                    }

                    @Override
                    public void onFailure() {
                        LogUtil.e("裁剪失败");
                        MediaRefresh.scanFileAsync(context, fileVideoRecordModify.getAbsolutePath());

                        handlerCopyFile.postDelayed(runnableCopyFile, 2000);
                        upTime = System.currentTimeMillis();
                    }

                    @Override
                    public void onProgress(float progress) {
                    }
                });
            }
        });
        handlerRecord.removeCallbacks(runnableRecord);
        MyProgressDialog.showProgressDialog(context, R.string.action_processing);
    }


    public static boolean copyFile(File fileCopyDest, InputStream inputStream) {
        long start = System.currentTimeMillis();
        if (inputStream == null) throw new AssertionError();
        if (fileCopyDest == null) throw new AssertionError();
        BufferedOutputStream bos = null;
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        try {
            bos = new BufferedOutputStream(new FileOutputStream(fileCopyDest));
            int length = 0;
            byte[] buffer = new byte[10240];
            while ((length = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, length);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (bos != null) {
                    bos.close();
                }
                if (bis != null) {
                    bis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        long end = System.currentTimeMillis();
        return true;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        AppLog.d(TAG, "onActivityResult requestCode=" + requestCode + ", resultCode=" + resultCode);
//        if (requestCode == REQUEST_MEDIA_PROJECTION) {
//            if (resultCode == RESULT_OK) {
//                Intent service = new Intent(this, ScreenRecordService.class);
//                service.putExtra("code", resultCode);
//                service.putExtra("data", data);
//                service.putExtra("audio", isAudio);
//                service.putExtra("width", mScreenWidth);
//                service.putExtra("height", mScreenHeight);
//                service.putExtra("density", mScreenDensity);
//                service.putExtra("quality", isVideoSd);
//                startService(service);
//                isStarted = !isStarted;
//                AppLog.i(TAG, "Started screen recording");
//                Toast.makeText(this, getString(R.string.screen_recording_start), Toast.LENGTH_SHORT).show();
//            } else {
//                AppLog.i(TAG, "User cancelled");
//                showControlView();
//            }
//        }
    }

    @Override
    protected void onStop() {
        if (isStarted) {
            stopScreenRecording();
            AppLog.i(TAG, "Stopped screen recording");
        }
        super.onStop();
        AppLog.d(TAG, "onStop()~~~~~~~~~~~~~~~~");
    }

    private void hideControlDelay() {
        timer = new Timer();
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                hideControlView();
            }
        }, 3000);
    }

    private void hideControlView() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (back != null) back.setVisibility(View.INVISIBLE);
                if (txtRecord != null) txtRecord.setVisibility(View.INVISIBLE);
                if (localPbBottomLayout != null) localPbBottomLayout.setVisibility(View.INVISIBLE);
                isControlNeedInvisible = true;
                if (ivPlay != null) ivPlay.setVisibility(View.GONE);
                if (tvEdit != null) tvEdit.setVisibility(View.GONE);
                if (tvSave != null) tvSave.setVisibility(View.GONE);
                if (tvReduction != null) tvReduction.setVisibility(View.GONE);
            }
        });
    }

    private void showControlView() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (back != null) back.setVisibility(View.VISIBLE);
                if (txtRecord != null) txtRecord.setVisibility(View.VISIBLE);
                if (!isEditing) {
                    if (localPbBottomLayout != null)
                        localPbBottomLayout.setVisibility(View.VISIBLE);
                }
                isControlNeedInvisible = false;
                if (2 == tag) {
                    if (ivPlay != null) ivPlay.setVisibility(View.VISIBLE);
                }
                if (tvEdit != null) tvEdit.setVisibility(View.VISIBLE);
//                if (isShowPhotoEdit) {
//                    if (tvEdit != null) tvEdit.setVisibility(View.GONE);
//                }

                if (isModify) {
                    if (tvSave != null) tvSave.setVisibility(View.VISIBLE);
                    if (tvReduction != null) tvReduction.setVisibility(View.VISIBLE);
                } else {
                    if (tvSave != null) tvSave.setVisibility(View.GONE);
                    if (tvReduction != null) tvReduction.setVisibility(View.GONE);
                }
            }
        });

    }

    private void initLayout() {
        setContentView(R.layout.pano_player_layout);
        this.context = this;
        unbinder = ButterKnife.bind(this);
    }

    @BindView(R.id.btn_replay)
    Button btnReplay;

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }
    }

    private void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setNavigationBarColor(Color.TRANSPARENT);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        actionBar.hide();
    }

    @BindView(R.id.pp_surfaceView)
    PanoPlayerSurfaceView ppSurfaceView;
    private Unbinder unbinder;
    PanoPlayerImpl panoramaPlayer;

//    static {
//        System.loadLibrary("MaskAndWeight");
//    }

    @BindView(R.id.local_photo_pb_delete)
    ImageButton delete;
    @BindView(R.id.local_pb_bottom_layout)
    LinearLayout localPbBottomLayout;
    @BindView(R.id.local_pb_back)
    ImageButton back;

    private int tag = 1;

    @Override
    public void onBackPressed() {
        if (isStarted) {
            stopScreenRecording();

            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    finish();
                }
            }, 2000);
        } else {
            super.onBackPressed();
        }
    }

    private void initPlayer() {
        ppSurfaceView.setOnClickPanoViewListener(this);
        ppSurfaceView.setGyroEnable(PanoramaApp.getPlayGyro());
        panoramaPlayer = ppSurfaceView.getRender();
        panoramaPlayer.setPanoPlayerListener(this);
        panoramaPlayer.setPanoPluginListener(this);
        parsePlayRequest();
        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,}, 0);
        turnUp.setOnClickListener(this);
        turnDown.setOnClickListener(this);
        prev.setOnClickListener(this);
        next.setOnClickListener(this);
        ivPlay.setOnClickListener(this);
        btnReplay.setOnClickListener(this);
        tvEdit.setOnClickListener(this);
        tvSave.setOnClickListener(this);
        tvReduction.setOnClickListener(this);
        delete.setOnClickListener(view -> showDeleteEnsureDialog());
        back.setOnClickListener(view -> {
            if (isStarted) {
                stopScreenRecording();
            }
            finish();
        });
        tag = getIntent().getIntExtra("playTag", 1);
        if (2 == tag) {
            ivPlay.setVisibility(View.VISIBLE);
            btnReplay.setVisibility(View.VISIBLE);
            delete.setVisibility(View.GONE);
//            prev.setText("上一部");
//            next.setText("下一部");
        } else {
            ivPlay.setVisibility(View.GONE);
        }
        txtRecord.setOnClickListener(this);
    }

    @BindView(R.id.txt_record)
    TextView txtRecord;
    private ExecutorService executorService;
    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            if (msg.what == 10) {
                initBtnFuncSwitch();
            } else if (msg.what == 20) {
                initBtnModeChoose();
            }
        }
    };

    private void showDeleteEnsureDialog() {
        if (ClickUtils.isFastDoubleClick(delete)) {
            return;
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setCancelable(false);
        builder.setTitle(R.string.image_delete_des);
        builder.setNegativeButton(R.string.gallery_delete, (dialog, whichButton) -> {
            MyProgressDialog.showProgressDialog(context, R.string.dialog_deleting);
            executorService = Executors.newSingleThreadExecutor();
            executorService.submit(new DeleteThread(), null);
        });
        builder.setPositiveButton(R.string.gallery_cancel, (dialog, whichButton) -> {
            dialog.dismiss();
            showControlView();
        });
        builder.create().show();
    }

    private int curPhotoIdx;
    private List<LocalPbItemInfo> fileList;
    private boolean isPlay = true;

    private class DeleteThread implements Runnable {
        @Override
        public void run() {
            Bundle data = getIntent().getExtras();
            curPhotoIdx = data.getInt("curFilePosition");
            fileList = GlobalInfo.getInstance().getLocalPhotoList();
            File tempFile = fileList.get(curPhotoIdx).file;
            if (tempFile.exists()) {
                tempFile.delete();
                MediaRefresh.notifySystemToScan(tempFile);
            }
            handler.post(() -> {
                fileList.remove(curPhotoIdx);
                finish();
                MyProgressDialog.closeProgressDialog();
            });
        }
    }

    private String getOutTempPath(String str) {
        FileUtils.createOrExistsDir(StorageUtil.getRootPath(mContext) + AppInfo.TEMP_DOWNLOAD_PATH);
        String dir = StorageUtil.getRootPath(mContext) + AppInfo.TEMP_DOWNLOAD_PATH;
        File file = new File(dir);
        if (!file.exists()) {
            file.mkdir();
        }
        return dir + str;
    }

    public File saveFile(Bitmap bitmap) {
        try {
            String path = getOutTempPath(TimeUtil.getNowDateTime() + "cut.jpg");
            File myCaptureFile = new File(path);
            BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(myCaptureFile));
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos);
            bos.flush();
            bos.close();
            return myCaptureFile;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public void onClick(View view) {
        if (panoramaPlayer != null && ppSurfaceView != null) {
            if (R.id.btn_viewMode == view.getId()) {
                if (ClickUtils.isFastDoubleClick(btnViewMode)) {
                    return;
                }
                if (menuDialogMode != null) {
                    hideControlView();
                    menuDialogMode.show();
                }
            } else if (R.id.btn_chooseSwitch == view.getId()) {
                if (ClickUtils.isFastDoubleClick(btnChooseSwitch)) {
                    return;
                }
                if (menuDialogFunc != null) {
                    hideControlView();
                    menuDialogFunc.show();
                }
            } else if (R.id.btn_prev == view.getId()) {
                if (ClickUtils.isFastDoubleClick(prev)) {
                    return;
                }
                tag = getIntent().getIntExtra("playTag", 1);
                String tempPath;
                if (1 == tag) {
                    if (mCurPhotoIndex > 0) {
                        List<LocalPbItemInfo> fileList;
                        fileList = GlobalInfo.getInstance().getLocalPhotoList();
                        tempPath = fileList.get(--mCurPhotoIndex).getFilePath();
                        playLocalPicture(tempPath);
                        AppToast.show(this, new File(tempPath).getName());
                    } else {
                        AppToast.show(this, "已经是第一张图片了！");
                    }
                } else if (2 == tag) {
//                    if (mCurPhotoIndex > 0) {
//                        List<LocalPbItemInfo> fileList;
//                        fileList = GlobalInfo.getInstance().getLocalVideoList();
//                        tempPath = fileList.get(--mCurPhotoIndex).getFilePath();
//                        playLocalVideo(tempPath);
//                        AppToast.show(this, new File(tempPath).getName());
//                    } else {
//                        AppToast.show(this, "已经是第一部视频了！");
//                    }

                    //20220322修改不播放普通视频
                    List<LocalPbItemInfo> localVideoList = GlobalInfo.getInstance().getLocalVideoList();
                    List<LocalPbItemInfo> localVideoListNoRecord = new ArrayList<>();
                    for (int i = 0; i < localVideoList.size(); i++) {
                        if (!localVideoList.get(i).file.getName().contains("RECORD")) {
                            localVideoListNoRecord.add(localVideoList.get(i));
                        }
                    }
                    if (mCurPhotoIndex > 0) {
                        tempPath = localVideoListNoRecord.get(--mCurPhotoIndex).getFilePath();
                        MyProgressDialog.showProgressDialog(this, R.string.action_processing);
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                AppToast.show(getApplicationContext(), new File(tempPath).getName());
                                playLocalVideo(tempPath);
                                MyProgressDialog.closeProgressDialog();
                            }
                        }, 2000);
                    } else {
                        AppToast.show(this, "已经是第一部视频了！");
                    }
                }
            } else if (R.id.btn_next == view.getId()) {
                if (ClickUtils.isFastDoubleClick(next)) {
                    return;
                }
                tag = getIntent().getIntExtra("playTag", 1);
                String tempPath;
                if (1 == tag) {
                    int photoListSize = GlobalInfo.getInstance().getLocalPhotoList().size();
                    if (mCurPhotoIndex < photoListSize - 1) {
                        List<LocalPbItemInfo> fileList;
                        fileList = GlobalInfo.getInstance().getLocalPhotoList();
                        tempPath = fileList.get(++mCurPhotoIndex).getFilePath();
                        playLocalPicture(tempPath);
                        AppToast.show(this, new File(tempPath).getName());
                    } else {
                        AppToast.show(this, "已经是最后一张图片了！");
                    }
                } else if (2 == tag) {
//                    int videoListSize = GlobalInfo.getInstance().getLocalVideoList().size();
//                    if (mCurPhotoIndex < videoListSize - 1) {
//                        List<LocalPbItemInfo> fileList;
//                        fileList = GlobalInfo.getInstance().getLocalVideoList();
//                        tempPath = fileList.get(++mCurPhotoIndex).getFilePath();
//                        playLocalVideo(tempPath);
//                        AppToast.show(this, new File(tempPath).getName());
//                    } else {
//                        AppToast.show(this, "已经是最后一部视频了！");
//                    }

                    //20220322修改不播放普通视频
                    List<LocalPbItemInfo> localVideoList = GlobalInfo.getInstance().getLocalVideoList();
                    List<LocalPbItemInfo> localVideoListNoRecord = new ArrayList<>();

                    for (int i = 0; i < localVideoList.size(); i++) {
                        if (!localVideoList.get(i).file.getName().contains("RECORD")) {
                            localVideoListNoRecord.add(localVideoList.get(i));
                        }
                    }
                    int videoListSize = localVideoListNoRecord.size();
                    if (mCurPhotoIndex < videoListSize - 1) {
                        tempPath = localVideoListNoRecord.get(++mCurPhotoIndex).getFilePath();
                        MyProgressDialog.showProgressDialog(this, R.string.action_processing);
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                playLocalVideo(tempPath);
                                AppToast.show(getApplicationContext(), new File(tempPath).getName());
                                MyProgressDialog.closeProgressDialog();
                            }
                        }, 2000);
                    } else {
                        AppToast.show(this, "已经是最后一部视频了！");
                    }
                }
            } else if (R.id.btn_turn_up == view.getId()) {
                if (ClickUtils.isFastDoubleClick(turnUp)) {
                    return;
                }
                MyToast.show(context, R.string.turn_up);
                Executors.newSingleThreadExecutor().submit(() -> SwipeEvent.makeSwipeDown(context, 100, 400, 100, 800, 6));
            } else if (R.id.btn_turn_down == view.getId()) {
                if (ClickUtils.isFastDoubleClick(turnDown)) {
                    return;
                }
                MyToast.show(context, R.string.turn_down);
                Executors.newSingleThreadExecutor().submit(() -> SwipeEvent.makeSwipeDown(context, 100, 800, 100, 400, 6));
            } else if (R.id.btn_replay == view.getId()) {
                if (ClickUtils.isFastDoubleClick(btnReplay)) {
                    return;
                }
                Plugin plugin = panoramaPlayer.getPlugin();
                if (plugin instanceof VideoPlugin) {
                    curViewMode = panoramaPlayer.getCurrentPanoViewMode();
                    curGestureData = panoramaPlayer.getCurrentGesturedata();
                    isReplay = true;
                    ((VideoPlugin) plugin).refresh();
                    String paths = getIntent().getStringExtra("path");
                    playLocalVideo(paths);
                }
            } else if (R.id.txt_record == view.getId()) {
                PermissionUtils.permission(PermissionConstants.MICROPHONE)
                        .rationale(new PermissionUtils.OnRationaleListener() {
                            @Override
                            public void rationale(@NonNull UtilsTransActivity activity, @NonNull ShouldRequest shouldRequest) {
                                new Handler().postDelayed(new Runnable() {
                                    @Override
                                    public void run() {
                                        shouldRequest.again(true);
                                    }
                                }, 100);
                            }
                        })
                        .callback(new PermissionUtils.FullCallback() {
                            @Override
                            public void onGranted(List<String> permissionsGranted) {
                                int count = 0;
                                for (int i = 0; i < permissionsGranted.size(); i++) {
                                    if (Manifest.permission.RECORD_AUDIO.equals(permissionsGranted.get(i))) {
                                        count++;
                                    }
                                }
                                //成功赋予权限
                                if (count >= 1) {
                                    if (ClickUtils.isFastDoubleClick(txtRecord)) {
                                        return;
                                    }
                                    if (isStarted) {
                                        stopScreenRecording();
                                        AppLog.i(TAG, "Stoped screen recording");
                                    } else {
                                        startScreenRecording();
                                    }
                                }
                            }

                            @Override
                            public void onDenied(List<String> permissionsDeniedForever, List<String> permissionsDenied) {
                                if (!permissionsDeniedForever.isEmpty()) {
                                    String[] systemRequestArray = permissionsDenied.toArray(new String[permissionsDenied.size()]);
                                    ActivityCompat.requestPermissions((PanoramaPlayerActivity) context, systemRequestArray, PermissionTools.ALL_REQUEST_CODE);
                                    return;
                                }

                            }
                        }).request();
            } else if (R.id.iv_play == view.getId()) {
                Plugin plugin = panoramaPlayer.getPlugin();
                if (isPlay) {
                    isPlay = false;
                    ((VideoPlugin) plugin).pause();
                    ivPlay.setImageDrawable(getResources().getDrawable(R.drawable.ic_play_arrow_white));
                } else {
                    isPlay = true;
                    ((VideoPlugin) plugin).start();
                    ivPlay.setImageDrawable(getResources().getDrawable(R.drawable.ic_pause_white));
                }
            } else if (R.id.tv_save == view.getId()) {
//                if (TextUtils.isEmpty(tempPhotoPath)) {
//                    ToastUtils.showShort("没有任何修改数据，无法保存");
//                    return;
//                }
//                String newImageName = TimeUtil.getNowDateTime() + ".jpg";
//                String newImagePath = Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + newImageName;
//                File newImage = new File(newImagePath);
//                try {
//                    newImage.createNewFile();
//                    FileInputStream fileInputStream = new FileInputStream(new File(tempPhotoPath));
//                    copyFile(newImage, fileInputStream);
//                    ToastUtils.showShort("保存成功");
//                    finish();
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }

                if (!isModify) {
                    ToastUtils.showShort("没有任何修改数据，无法保存");
                    return;
                }
                String newImageName = TimeUtil.getNowDateTime() + "_" + System.currentTimeMillis() / 1000 + ".jpg";
                String newImagePath = Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + newImageName;
                File newImage = new File(newImagePath);
                try {
                    newImage.createNewFile();
                    FileInputStream fileInputStream = new FileInputStream(new File(tempPhotoPath));
                    copyFile(newImage, fileInputStream);
                    ToastUtils.showShort("保存成功");
                    finish();
                } catch (IOException e) {
                    e.printStackTrace();
                }

            } else if (R.id.tv_edit == view.getId()) {
                if (isShowPhotoEdit) {
//                    if (isEditing) {
//                        llEditPhoto.setVisibility(View.GONE);
//                        tvSave.setVisibility(View.GONE);
//                        localPbBottomLayout.setVisibility(View.VISIBLE);
//                        tvEdit.setText("编辑");
//                        isEditing = false;
//                        path = getIntent().getStringExtra("path");
//                        sbContrastRatio.setMin(0f);
//                        sbContrastRatio.setMax(2f);
//                        sbContrastRatio.setProgress(1f);
//                        sbExposure.setMin(-3f);
//                        sbExposure.setMax(3f);
//                        sbExposure.setProgress(0f);
//                        sbSaturation.setMin(0f);
//                        sbSaturation.setMax(2f);
//                        sbSaturation.setProgress(1);
//                        parsePlayRequest();
//                    } else {
//                        llEditPhoto.setVisibility(View.VISIBLE);
//                        tvSave.setVisibility(View.VISIBLE);
//                        localPbBottomLayout.setVisibility(View.GONE);
//                        tvEdit.setText("取消");
//                        isEditing = true;
//                        if (timer != null) {
//                            timer.cancel();
//                        }
//                    }

                    if (isEditing) {
                        tvEdit.setText("编辑");
                        isEditing = false;
                        path = getIntent().getStringExtra("path");
                        localPbBottomLayout.setVisibility(View.VISIBLE);
                    } else {
                        localPbBottomLayout.setVisibility(View.GONE);
                        tvEdit.setText("取消");
                        isEditing = true;
                        if (timer != null) {
                            timer.cancel();
                        }
                        bottomPopup.showPopupWindow();
                        bottomPopup.ivSure.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {

                                int isSelectPosition = -1;
                                for (int i = 0; i < mFilterData.size(); i++) {
                                    if (mFilterData.get(i).isSelect) {
                                        isSelectPosition = i;
                                        break;
                                    }
                                }

                                if (isSelectPosition == 0) {
                                    bottomPopup.dismiss();
                                    if (isModify) {
                                        isModify = false;
                                        tvSave.setVisibility(View.GONE);
                                        tvReduction.setVisibility(View.GONE);
                                        parsePlayRequest();
                                    }

                                    return;
                                }

                                if (isSelectPosition == -1) {
                                    ToastUtils.showLong("您还没有选择需要的滤镜效果");
                                    return;
                                }

                                int finalIsSelectPosition = isSelectPosition;
                                Runnable runnable = new Runnable() {
                                    @Override
                                    public void run() {
                                        // 检查HMS Image Vision API是否可用
                                        if (imageVisionFilterAPI == null || initCodeState != 0) {
                                            // HMS API不可用，显示提示信息
                                            ivTest.post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    bottomPopup.dismiss();
                                                    ToastUtils.showLong("滤镜功能需要设备信息权限，请在设置中授予权限后重启应用");
                                                }
                                            });
                                            return;
                                        }

                                        JSONObject jsonObject = new JSONObject();
                                        JSONObject taskJson = new JSONObject();
                                        try {
                                            taskJson.put("intensity", (bottomPopup.isbProgress.getProgressFloat() / 100));
                                            taskJson.put("filterType", mFilterData.get(finalIsSelectPosition).type);
                                            jsonObject.put("taskJson", taskJson);
                                            jsonObject.put("authJson", authJson);

                                            bitmap = ImageUtils.getBitmap(path);

                                            Bitmap resizeBmp;
                                            if (bitmap.getHeight() * bitmap.getWidth() * 4f / 1000f / 1000f > 20) {
//                                                float multiple = 15 /(bitmap.getHeight() * bitmap.getWidth() * 4f / 1000f / 1000f);
//                                                double sqrt = Math.sqrt(multiple);
                                                Matrix matrix = new Matrix();
                                                matrix.postScale(0.5f, 0.5f);
                                                resizeBmp = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
                                            } else {
                                                resizeBmp = bitmap;
                                            }

                                            final ImageVisionResult visionResult = imageVisionFilterAPI.getColorFilter(jsonObject, resizeBmp);

                                            ivTest.post(new Runnable() {
                                                @Override
                                                public void run() {
                                                    bottomPopup.dismiss();
                                                    if (visionResult.getImage() != null) {
                                                        File file = saveFile(visionResult.getImage());
                                                        if (file != null) {
                                                            isModify = true;
                                                            tempPhotoPath = file.getPath();
                                                            parsePlayRequest(file.getPath());
                                                        }

                                                        tvSave.setVisibility(View.VISIBLE);
                                                        tvReduction.setVisibility(View.VISIBLE);
                                                    } else {
                                                        ToastUtils.showLong("网络异常");
                                                    }
                                                    resizeBmp.recycle();
                                                }
                                            });
                                        } catch (JSONException e) {
                                            LogUtil.e("JSONException: " + e.getMessage());
                                        }
                                    }
                                };

                                mExecutorService.execute(runnable);
                            }
                        });
                        bottomPopup.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
                            @Override
                            public void onDismiss() {
                                tvEdit.setText("编辑");
                                isEditing = false;
                                path = getIntent().getStringExtra("path");
                                localPbBottomLayout.setVisibility(View.VISIBLE);
                            }
                        });
                    }
                } else {
                    // 检查READ_PHONE_STATE权限，HMS Video Editor需要此权限
                    if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE)
                            != PackageManager.PERMISSION_GRANTED) {
                        // 权限未授予，显示权限请求弹窗
                        requestHmsPermissionForVideoEditor();
                        return;
                    }

                    // 权限已授予，启动HMS视频编辑器
                    launchHmsVideoEditor();
                }
            } else if (R.id.tv_reduction == view.getId()) {
                isModify = false;
                tvSave.setVisibility(View.GONE);
                tvReduction.setVisibility(View.GONE);
                parsePlayRequest();
            }
        }
    }

    private void getHmsToken() {
        userService.getToken("client_credentials", "104616831", "fb0ac89a8ec87bc13ffba1f0968dc334af3a2672b21add885cb831e437288e82")
                .subscribeOn(AndroidSchedulers.mainThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new CustomDisposableForJava<TokenBean>(getContext()) {
                    @Override
                    public void onNext(TokenBean bean) {
                        super.onNext(bean);
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                try {

                                } catch (Exception e) {
                                    e.printStackTrace();
                                }


                            }
                        });

                    }

                    @Override
                    public void onError(Throwable e) {
                        super.onError(e);
                    }
                });
    }

    @BindView(R.id.btn_prev)
    ImageButton prev;
    @BindView(R.id.btn_next)
    ImageButton next;
    @BindView(R.id.btn_turn_down)
    Button turnDown;
    @BindView(R.id.btn_turn_up)
    Button turnUp;
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_viewMode)
    Button btnViewMode;
    DTListDialog menuDialogMode;

    @SuppressLint("SetTextI18n")
    private void initBtnModeChoose() {
        btnViewMode.setOnClickListener(this);
        final String ON = "●", OFF = "○";
        if (PanoramaApp.getPlayMode() == 1) {
            curViewMode = PanoViewMode.DEF;
        } else if (PanoramaApp.getPlayMode() == 2) {
            curViewMode = PanoViewMode.FISHEYE;
        } else if (PanoramaApp.getPlayMode() == 3) {
            curViewMode = PanoViewMode.LITTLEPLANET;
        } else if (PanoramaApp.getPlayMode() == 4) {
            curViewMode = PanoViewMode.SPHERE;
        } else if (PanoramaApp.getPlayMode() == 5) {
            curViewMode = PanoViewMode.SPACE3D;
        } else if (PanoramaApp.getPlayMode() == 6) {
            curViewMode = PanoViewMode.VR_HORIZONTAL;
        } else if (PanoramaApp.getPlayMode() == 7) {
            curViewMode = PanoViewMode.VR_VERTICAL;
        } else if (PanoramaApp.getPlayMode() == 8) {
            curViewMode = PanoViewMode.FLAT;
        } else if (PanoramaApp.getPlayMode() == 9) {
            curViewMode = PanoViewMode.ORIGINAL;
        } else {
            curViewMode = PanoViewMode.DEF;
        }


        menuDialogMode = new DTListDialog(this);
        menuDialogMode.setTitle(getString(R.string.mode_switching));
        menuDialogMode.setItems(
                new String[]{
                        getString(R.string.mode_switch1) + (curViewMode == PanoViewMode.DEF ? ON : OFF),
                        getString(R.string.mode_switch2) + (curViewMode == PanoViewMode.FISHEYE ? ON : OFF),
                        getString(R.string.mode_switch3) + (curViewMode == PanoViewMode.LITTLEPLANET ? ON : OFF),
                        getString(R.string.mode_switch4) + (curViewMode == PanoViewMode.SPHERE ? ON : OFF),
                        getString(R.string.mode_switch5) + (curViewMode == PanoViewMode.SPACE3D ? ON : OFF),
                        getString(R.string.mode_switch6) + (curViewMode == PanoViewMode.VR_HORIZONTAL ? ON : OFF),
                        getString(R.string.mode_switch7) + (curViewMode == PanoViewMode.VR_VERTICAL ? ON : OFF),
                        getString(R.string.mode_switch8) + (curViewMode == PanoViewMode.FLAT ? ON : OFF),
                        getString(R.string.mode_switch9) + (curViewMode == PanoViewMode.ORIGINAL ? ON : OFF),
                },
                (dialog, view, position) -> {
                    PanoViewMode viewMode = PanoViewMode.DEF;
                    int mode = 1;
                    switch (position) {
                        case 0:
                            mode = 1;
                            viewMode = PanoViewMode.DEF;
                            btnViewMode.setText(getString(R.string.mode_switch1_data));
                            break;
                        case 1:
                            mode = 2;
                            viewMode = PanoViewMode.FISHEYE;
                            btnViewMode.setText(getString(R.string.mode_switch2_data));
                            break;
                        case 2:
                            mode = 3;
                            viewMode = PanoViewMode.LITTLEPLANET;
                            btnViewMode.setText(getString(R.string.mode_switch3_data));
                            break;
                        case 3:
                            mode = 4;
                            viewMode = PanoViewMode.SPHERE;
                            btnViewMode.setText(getString(R.string.mode_switch4_data));
                            break;
                        case 4:
                            mode = 5;
                            viewMode = PanoViewMode.SPACE3D;
                            btnViewMode.setText(getString(R.string.mode_switch5_data));
                            break;
                        case 5:
                            mode = 6;
                            viewMode = PanoViewMode.VR_HORIZONTAL;
                            btnViewMode.setText(getString(R.string.mode_switch6_data));
                            break;
                        case 6:
                            mode = 7;
                            viewMode = PanoViewMode.VR_VERTICAL;
                            btnViewMode.setText(getString(R.string.mode_switch7_data));
                            break;
                        case 7:
                            mode = 8;
                            viewMode = PanoViewMode.FLAT;
                            btnViewMode.setText(getString(R.string.mode_switch8_data));
                            break;
                        case 8:
                            mode = 9;
                            viewMode = PanoViewMode.ORIGINAL;
                            btnViewMode.setText(getString(R.string.mode_switch9_data));
                            break;
                        default:
                            break;
                    }
                    PanoramaApp.setPlayMode(mode);
                    panoramaPlayer.setAnimationViewMode(viewMode, 1, EaseTypes.LinearEaseOuts);
                    menuDialogMode.dismiss();
                    handler.sendEmptyMessage(20);
                }
        );
        menuDialogMode.setOnDismissListener(dialog -> {
            showControlView();
        });
    }

    boolean isAutoPlay = PanoramaApp.getPlayAutoPlay();
    boolean isReverse = PanoramaApp.getReverse();
    boolean isScenesBackMusic = PanoramaApp.getScenesBackgroundMusic();
    boolean isPointSelecting = PanoramaApp.getPlayPointSelecting();
    @SuppressLint("NonConstantResourceId")
    @BindView(R.id.btn_chooseSwitch)
    Button btnChooseSwitch;
    DTListDialog menuDialogFunc;

    @SuppressLint("SetTextI18n")
    private void initBtnFuncSwitch() {
        if (ppSurfaceView == null){
            return;
        }
        ppSurfaceView.setGyroEnable(PanoramaApp.getPlayGyro());
        ppSurfaceView.setZoomEnable(PanoramaApp.getPlayZoom());
        ppSurfaceView.setGestureEnable(PanoramaApp.getPlayGesture());
        ppSurfaceView.setGyroModeShouldMove(PanoramaApp.getPlayGyroModeShouldMove());
        panoramaPlayer.setPauseTime(1);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                panoramaPlayer.setAutoRotate(PanoramaApp.getPlayAutoPlay(), -0.35f);
                panoramaPlayer.setReverse(PanoramaApp.getReverse());
                if (PanoramaApp.getScenesBackgroundMusic()) {
                    panoramaPlayer.pauseBackgroundMusic();
                } else {
                    panoramaPlayer.startBackgroundMusic();
                }
                panoramaPlayer.setPointSelecting(PanoramaApp.getPlayPointSelecting());
            }
        }, 300);

        btnChooseSwitch.setOnClickListener(this);
        menuDialogFunc = new DTListDialog(this);
        final String ON = getString(R.string.function_on), OFF = getString(R.string.function_off);
        menuDialogFunc.setTitle(getString(R.string.function_switch)).setItems(
                new String[]{
                        getString(R.string.gyro) + (ppSurfaceView.getGyroEnable() ? ON : OFF),
                        getString(R.string.gesture_zoom) + (ppSurfaceView.getZoomEnable() ? ON : OFF),
                        getString(R.string.gesture_movement) + (ppSurfaceView.getGestureEnable() ? ON : OFF),
                        getString(R.string.gyro_gesture_coexistence) + (ppSurfaceView.getGyroModeShouldMove() ? ON : OFF),
                        getString(R.string.auto_play) + (PanoramaApp.getPlayAutoPlay() ? ON : OFF),
                        getString(R.string.flip) + (PanoramaApp.getReverse() ? ON : OFF),
                        getString(R.string.background_music) + (PanoramaApp.getScenesBackgroundMusic() ? ON : OFF),
                        getString(R.string.tap) + (PanoramaApp.getPlayPointSelecting() ? ON : OFF)
                },
                (dialog, view, position) -> {
                    TextView tv = (TextView) view;
                    boolean isEnable;
                    switch (position) {
                        case 0://陀螺仪
                            isEnable = PanoramaApp.getPlayGyro();
                            PanoramaApp.setPlayGyro(!isEnable);
                            ppSurfaceView.setGyroEnable(!isEnable);
                            tv.setText(getString(R.string.gyro) + (!isEnable ? ON : OFF));
                            break;
                        case 1://手势缩放
                            isEnable = PanoramaApp.getPlayZoom();
                            PanoramaApp.setPlayZoom(!isEnable);
                            ppSurfaceView.setZoomEnable(!isEnable);
                            tv.setText(getString(R.string.gesture_zoom) + (!isEnable ? ON : OFF));
                            break;
                        case 2://手势移动
                            isEnable = PanoramaApp.getPlayGesture();
                            PanoramaApp.setPlayGesture(!isEnable);
                            ppSurfaceView.setGestureEnable(!isEnable);
                            tv.setText(getString(R.string.gesture_movement) + (!isEnable ? ON : OFF));
                            break;
                        case 3://陀螺仪手势共存
                            isEnable = PanoramaApp.getPlayGyroModeShouldMove();
                            PanoramaApp.setPlayGyroModeShouldMove(!isEnable);
                            ppSurfaceView.setGyroModeShouldMove(!isEnable);
                            tv.setText(getString(R.string.gyro_gesture_coexistence) + (!isEnable ? ON : OFF));
                            break;
                        case 4://自动播放
                            isAutoPlay = !PanoramaApp.getPlayAutoPlay();
                            PanoramaApp.setPlayAutoPlay(isAutoPlay);
                            panoramaPlayer.setAutoRotate(isAutoPlay, -0.35f);
                            panoramaPlayer.setPauseTime(1);
                            tv.setText(getString(R.string.auto_play) + (isAutoPlay ? ON : OFF));
                            break;
                        case 5://翻转
                            isReverse = !PanoramaApp.getReverse();
                            PanoramaApp.setReverse(isReverse);
                            panoramaPlayer.setReverse(isReverse);
                            tv.setText(getString(R.string.flip) + (isReverse ? ON : OFF));
                            break;
                        case 6://背景音乐
                            isScenesBackMusic = !PanoramaApp.getScenesBackgroundMusic();
                            PanoramaApp.setScenesBackgroundMusic(isScenesBackMusic);
                            if (isScenesBackMusic) {
                                panoramaPlayer.pauseBackgroundMusic();
                            } else {
                                panoramaPlayer.startBackgroundMusic();
                            }
                            tv.setText(getString(R.string.background_music) + (isScenesBackMusic ? ON : OFF));
                            break;
                        case 7://点选
//                            isPointSelecting = !panoramaPlayer.getCurrentPanoData().nodeView.isPointSelecting;
                            isPointSelecting = !PanoramaApp.getPlayPointSelecting();
                            PanoramaApp.setPlayPointSelecting(isPointSelecting);
                            panoramaPlayer.setPointSelecting(isPointSelecting);
                            tv.setText(getString(R.string.tap) + (isPointSelecting ? ON : OFF));
                            break;
                        default:
                            break;
                    }
                    handler.sendEmptyMessage(10);
                    menuDialogFunc.dismiss();
                }
        );
        menuDialogFunc.setOnDismissListener(dialog -> {
            showControlView();
        });
    }

    private void parsePlayRequest() {
        int tag = getIntent().getIntExtra("playTag", 1);
        String paths = path;
        if (TextUtils.isEmpty(paths)) {
        }
        AppLog.e(TAG, "parsePlayRequest, tag:" + tag + ", path:" + paths);
        switch (tag) {
            case 1:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择图片", Toast.LENGTH_LONG).show();
                    return;
                }
                playLocalPicture(paths);
                break;
            case 2:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择视频", Toast.LENGTH_LONG).show();
                    return;
                }
                playLocalVideo(paths);
                break;
            case 3:
                PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请输入xml", Toast.LENGTH_LONG).show();
                    return;
                }
                if (!paths.contains("xml")) {
                    Toast.makeText(getApplicationContext(), "输入有误，请重新输入", Toast.LENGTH_LONG).show();
                    return;
                }
                panoplayerurl.setXmlUrl(paths);
                panoramaPlayer.playByXml(panoplayerurl);
                break;
        }
    }

    private void parsePlayRequest(String path) {
        int tag = getIntent().getIntExtra("playTag", 1);
        String paths = path;
        if (TextUtils.isEmpty(paths)) {
        }
        AppLog.e(TAG, "parsePlayRequest, tag:" + tag + ", path:" + paths);
        switch (tag) {
            case 1:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择图片", Toast.LENGTH_LONG).show();
                    return;
                }
                playLocalPicture(paths);
                break;
            case 2:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择视频", Toast.LENGTH_LONG).show();
                    return;
                }
                playLocalVideo(paths);
                break;
            case 3:
                PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请输入xml", Toast.LENGTH_LONG).show();
                    return;
                }
                if (!paths.contains("xml")) {
                    Toast.makeText(getApplicationContext(), "输入有误，请重新输入", Toast.LENGTH_LONG).show();
                    return;
                }
                panoplayerurl.setXmlUrl(paths);
                panoramaPlayer.playByXml(panoplayerurl);
                break;
        }
    }

    private static final String PLAYER_CONFIG = "<DetuVr>\n" +
            "   <settings init='pano1' initmode='default' enablevr='true' title=''/>\n" +
            "   <scenes>\n" +
            "       <scene name='pano1' title='' thumburl=''>\n" +
            "           <preview url='' />\n" +
            "           <image type='%s' url='%s' device='%d' isreplay_f4 = 'true'/>\n" +
            "           <view hlookat ='180' viewmode='default'  isptselect ='true'  defovmax='%d'  vrfov='%d'  fov='%d' fovmax='%d'/>\n" +
            "       </scene>\n" +
            "   </scenes>\n" +
            "</DetuVr>";

    private void playLocalPicture(String paths) {
        this.url = paths;
        String url = String.format(PLAYER_CONFIG, "sphere", paths, -1, 100, 20, 20, 100);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        panoramaPlayer.playByXml(panoplayerurl, null);
    }

    private PanoPlayerUrl panoPlayerUrl;
    private String url;

    private void playLocalVideo(String paths) {
        this.url = paths;
        String url = String.format(PLAYER_CONFIG, "video", paths, -1, 60, 20, 20, 60);
        this.panoPlayerUrl = new PanoPlayerUrl();
        panoPlayerUrl.setXmlContent(url);
        panoramaPlayer.playByXml(panoPlayerUrl, null);

        mp3Extract();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (ppSurfaceView != null) {
            ppSurfaceView.onDestroy();
        }
        if (unbinder != null){
            unbinder.unbind();
        }
        AppLog.d(TAG, "onDestroy()~~~~~~~~~~~~~~~~");

//        VideoBean videoBean = new VideoBean(outMp3, fileVideoRecord.getAbsolutePath());
//        EventBus.getDefault().post(new EventBusEntity<VideoBean>("MEGRA", videoBean));

//        try {
//            EventBus.getDefault().unregister(this);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void onResume() {
        super.onResume();
        ppSurfaceView.onResume();

        // 延迟调用，确保播放器完全初始化，避免空指针异常
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                startVideoPluginSafely();
            }
        }, 200); // 延迟200ms

        isScenesBackMusic = true;
        panoramaPlayer.resumeAllBackgroundMusic();
        AppLog.e(TAG, "onResume");
        showControlView();
        AppInfo.checkLocationDialog(this);

    }

    /**
     * 安全地启动视频插件，避免空指针异常
     */
    private void startVideoPluginSafely() {
        if (panoramaPlayer != null) {
            Plugin plugin = panoramaPlayer.getPlugin();
            if (plugin instanceof VideoPlugin) {
                try {
                    ((VideoPlugin) plugin).start();
                    AppLog.d(TAG, "Video plugin started successfully");
                } catch (Exception e) {
                    AppLog.e(TAG, "Failed to start video plugin: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        ppSurfaceView.onPause();
        Plugin plugin = panoramaPlayer.getPlugin();
        if (plugin instanceof VideoPlugin) {
            ((VideoPlugin) plugin).pause();
        }
        panoramaPlayer.onGLSurfaceViewPause();
        panoramaPlayer.pauseAllHotMusic();
        panoramaPlayer.pauseAllBackgroundMusic();
        panoramaPlayer.onGLSurfaceViewPause();
        AppLog.e(TAG, "onPause");
    }

    @Override
    public void onPanoPlayerStatusChanged(PanoPlayerStatus status, String tip) {
        switch (status) {
            case LOADING:
                if (panoramaPlayer.getCurrentPanoData().nodeImage.panoDeviceId.deviceId > 4000) {
                    panoramaPlayer.setCurrentImageDataCali(Calibration);
                    panoramaPlayer.setWeight(weightPath);
                }
                if (IS_TEST_LITE_DECOR) {
                    LiteDecor liteDecor = new LiteDecor();
                    liteDecor.tag = "smart";
                    liteDecor.imageUrl = "/mnt/sdcard/3.png";
                    LayoutParams layoutParams = new LayoutParams();
                    layoutParams.displayType = DisplayType.GL_DISTORTED;
                    layoutParams.ath = 0f;
                    layoutParams.atv = 0f;
                    layoutParams.width = 0.17f;
                    layoutParams.height = 0.17f;
                    liteDecor.layoutParams = layoutParams;
                    panoramaPlayer.addLiteDecor(liteDecor);
                }
                AppLog.e(TAG, "onPanoPlayerStatusChanged: loading!");
                break;
            case LOADED:
                if (isReplay) {
                    isReplay = false;
                    panoramaPlayer.setViewMode(curViewMode);
                    panoramaPlayer.setCurrentGestureData(curGestureData);
                }
                panoramaPlayer.setAnimationViewMode(curViewMode, 1, EaseTypes.LinearEaseOuts);
                panoramaPlayer.setPointSelecting(false);
                //20220322新增
                handler.sendEmptyMessage(10);
                AppLog.e(TAG, "onPanoPlayerStatusChanged: loaded!");
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                    }
                });
                break;
            case ERROR:
                PanoPlayerError error = PanoPlayerError.parseByString(tip);
                switch (error) {
                    case LACK_CALIBRATION:
                        AppLog.e(TAG, "onPanoPlayerError: lack calibration!");
                        break;
                    case PLAY_MANAGER_DATA_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: PLAY MANAGER DATA IS EMPTY");
                        break;
                    case SETTING_DATA_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: SETTING DATA IS EMPTY!");
                        break;
                    case PANORAMALIST_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: PANORAMALIST IS EMPTY!");
                        break;
                    case PLAY_URL_IS_EMPTY:
                        AppLog.e(TAG, "onPanoPlayerError: PLAY URL IS EMPTY!");
                        break;
                    case IMAGE_LOAD_ERROR:
                        AppLog.e(TAG, "onPanoPlayerError: IMAGE LOAD ERROR");
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }

    private static final boolean IS_TEST_LITE_DECOR = false;
    String M1Path = "";
    String F4PlusPath = "";
    String Calibration = "";
    String[] weightPath = new String[4];
    private Handler uiHandler = new Handler();

    @Override
    public void onPanoPlayerConfigLoaded(int deviceId) {
        AppLog.e(TAG, "onPanoPlayerConfigLoaded: loading!");
        if (deviceId > 4000) {
            GetWeightAndMaskHelper getWeightAndMaskHelper = new GetWeightAndMaskHelper();
            M1Path = "/mnt/sdcard/M1WeightPath";
            F4PlusPath = "/mnt/sdcard/F4PlusWeightPath";
            if (deviceId == PanoDeviceId.PanoDeviceId_SPHERE_DETU_M1.deviceId) {
                File file = new File(M1Path);
                if (!file.exists()) {
                    file.mkdir();
                }
                weightPath = new String[]{M1Path + "/wt0.jpg", M1Path + "/wt1.jpg", M1Path + "/wt2.jpg", M1Path + "/wt3.jpg"};
                String biaodingPath = M1Path + "/biaoding.txt";
                File[] fileWeight = new File[4];
                for (int i = 0; i < 4; i++) {
                    fileWeight[i] = new File(weightPath[i]);
                }
                File biaodingFile = new File(biaodingPath);
                if ((fileWeight[0].exists()) && (biaodingFile.exists())) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        Calibration = FileReadandWrite.readtxt(biaodingFile);
                    }
                } else {
                    GetWeightAndMaskHelper helper = new GetWeightAndMaskHelper();
                    Calibration = helper.GetWgetWeightAndMaskInfo(M1Path + "/pat.pts", new int[]{1024, 512}, weightPath);
                    FileReadandWrite.writetxt(biaodingPath, Calibration);
                }
            } else {
                File file = new File(F4PlusPath);
                if (!file.exists()) {
                    file.mkdir();
                }
                weightPath = new String[]{F4PlusPath + "/wt0.jpg", F4PlusPath + "/wt1.jpg", F4PlusPath + "/wt2.jpg", F4PlusPath + "/wt3.jpg"};
                String biaodingPath = F4PlusPath + "/biaoding.txt";
                File[] fileWeight = new File[4];
                for (int i = 0; i < 4; i++) {
                    fileWeight[i] = new File(weightPath[i]);
                }
                File biaodingfile = new File(biaodingPath);
                if ((fileWeight[0].exists()) && (biaodingfile.exists())) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        Calibration = FileReadandWrite.readtxt(biaodingfile);
                    }
                } else {
                    GetWeightAndMaskHelper helper = new GetWeightAndMaskHelper();
                    String path = "A:_4000_3000_-3_-465_3915_3915_2_203.729_55_90_2_0_-0.346621_0.214988_44.621_14.0211_0_0_0_0_B:_4000_3000_16_-474_3919_3919_2_202.197_145.849_91.6581_-2.06139_0_-0.256364_0.110774_66.2789_6.20713_0_0_0_0_C:_4000_3000_5_-427_3864_3864_2_199.69_54.8259_-90.61_-179.144_0_-0.270679_0.142519_57.8891_6.8586_0_0_0_0_D:_4000_3000_15_-467_3942_3942_2_207.991_-33.6985_89.0577_-0.285306_0_-0.330378_0.183777_40.8338_-25.4735_0_0_0_0_&&";
                    Calibration = helper.GetWgetWeightAndMaskInfo(path, new int[]{1024, 512}, weightPath);
                    FileReadandWrite.writetxt(biaodingPath, Calibration);
                }
            }
        }
        AppLog.e(TAG, "onPanoPlayerConfigLoaded: finish!");
    }

    @Override
    public void onPanoPluginStateChanged(PanoPluginStatus status, String tip) {
        switch (status) {
            case PREPARED:
                inTime = System.currentTimeMillis();
//                new Thread() {
//                    @Override
//                    public void run() {
//                        super.run();
//                        timer.schedule(task, 1000, 1000 / 30);
//                    }
//                }.start();
                break;
            case PLAYING:
                LogUtil.e(TAG, "onPanoPluginStateChanged, PLAYING!");
                break;
            case PAUSE:
                LogUtil.e(TAG, "onPanoPluginStateChanged, Pause!");
                break;
            case STOP:
                LogUtil.e(TAG, "onPanoPluginStateChanged, Stop!");
                break;
            case FINISH:
                LogUtil.e(TAG, "onPanoPluginStateChanged, Finish!");

                if (panoramaPlayer.getCurrentPanoData().nodeImage.panoDeviceId.deviceId > 4000) {
                    Plugin plugin = panoramaPlayer.getPlugin();
                    if (plugin != null) {
                        plugin.refresh();
                    }
                }

                break;
            case CODEC_SWITCH:
                LogUtil.e(TAG, "onPanoPluginStateChanged, Switch Codec:" + tip);
                break;
            case ERROR:
                PanoPluginError error = PanoPluginError.parseByString(tip);
                switch (error) {
                    case NETWORK:
                        LogUtil.e(TAG, "*onPanoPluginStateChanged, Network Error!");
                        uiHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                panoramaPlayer.playByXml(panoPlayerUrl, null);
                            }
                        });
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }

    private long currentTimeStart = 0;
    private long currentTimeEnd = 0;
    private long currentTimeEndForEnd = 0;


    @Override
    public void onPanoPluginProgressChanged(long currentTime, long bufferTime, long duration) {
        int progress = (int) (currentTime * 1.0 / duration * 100);


//        if (duration / 1000 == 1) {
//            if (progress >= 0) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 == 2 || duration / 1000 == 3) {
//            if (progress >= 20) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 == 4 || duration / 1000 == 5) {
//            if (progress >= 30) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 == 6 || duration / 1000 == 7 || duration / 1000 == 8) {
//            if (progress >= 65) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 > 8 && duration / 1000 <= 12) {
//            if (progress >= 80) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 > 12 && duration / 1000 <= 15) {
//            if (progress >= 85) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 > 15 && duration / 1000 <= 30) {
//            if (progress >= 90) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 > 30 && duration / 1000 <= 60) {
//            if (progress >= 92) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 > 60 && duration / 1000 <= 120) {
//            if (progress >= 94) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        } else if (duration / 1000 > 120) {
//            if (progress >= 98) {
//                if (isStarted) {
//                    stopScreenRecording();
//                    return;
//                }
//            }
//        }


        if (isDownRecord) {
            isDownRecord = false;
            currentTimeStart = currentTime;
        }

        if (isDownRecordEnd) {
            currentTimeEnd = currentTime;
        }
        currentTimeEndForEnd = currentTime;

//        LogUtil.e("currentTime:" + currentTime + "bufferTime:" + bufferTime +"duration:" + duration);
//        LogUtil.e("progress:" + progress);
    }

    @Override
    public void onClickPanoView(MotionEvent motionEvent) {
        PointF pointF = panoramaPlayer.calDegByWinPoint(motionEvent.getX(), motionEvent.getY());
        double x = Math.toDegrees(pointF.x);
        if (x > 270) {
            x = x - 360 - 90;
        } else {
            x = x - 90;
        }
        double y = 90 - Math.toDegrees(pointF.y);
        AppLog.i(TAG, "position:" + x + "\t" + y);
        AppLog.d(TAG, "motionEvent.getAction = " + motionEvent.getAction());
        AppLog.d(TAG, "isControlNeedInvisible = " + isControlNeedInvisible);
        if (MotionEvent.ACTION_UP == motionEvent.getAction() || MotionEvent.ACTION_DOWN == motionEvent.getAction()) {
            if (isShowPhotoEdit && isEditing) {
                return;
            }
            if (isControlNeedInvisible) {
                showControlView();
            } else {
                hideControlView();
            }
        }
    }

    @Override
    public boolean dispatchKeyEvent(KeyEvent event) {
        return super.dispatchKeyEvent(event);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
//        if (keyCode == KeyEvent.KEYCODE_BACK) {
//            if (isStarted) {
//                stopScreenRecording();
//                AppLog.i(TAG, "Stopped screen recording");
//            }
//        }
        if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN || keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public boolean onKeyUp(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_VOLUME_DOWN || keyCode == KeyEvent.KEYCODE_VOLUME_UP) {
            if (isStarted) {
                stopScreenRecording();
                AppLog.i(TAG, "Stopped screen recording");
            }
            return true;
        }
        return super.onKeyUp(keyCode, event);
    }

    private long inTime = 0;
    private long downTime = 0;
    private long upTime = 0;

    private void videoForMp4() {
        fileVideoRecordMp4 = new File(StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".mp4");
        String cmd = "-i " + fileVideoRecord + " -y -qscale 0 -vcodec libx264 " + fileVideoRecordMp4;
        EpEditor.execCmd(cmd, 0, new OnEditorListener() {
            @Override
            public void onSuccess() {
                LogUtil.e("MP4转换成功");
                MediaRefresh.scanFileAsync(context, fileVideoRecordMp4.getAbsolutePath());
                new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        mp3Cut(url);
                    }
                }, 2000);

            }

            @Override
            public void onFailure() {
                LogUtil.e("MP4转换失败");
                MediaRefresh.scanFileAsync(context, fileVideoRecord.getAbsolutePath());
                MyProgressDialog.closeProgressDialog();
                finish();
            }

            @Override
            public void onProgress(float progress) {
            }
        });
    }


    private void mp3Cut(String path) {
        outMp3 = StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".mp3";
        long startTime = currentTimeStart;
        LogUtil.e("startTime:" + startTime);
        if (currentTimeEnd < currentTimeStart) {
            currentTimeEnd = currentTimeEndForEnd - 1500;
        }
        int endTime = (int) (currentTimeEnd / 1000 - currentTimeStart / 1000);
        if (endTime < 0) endTime = 0;
        LogUtil.e("endTime:" + endTime);
        long hour = startTime / 60 / 60 / 1000;
        long minute = startTime / 60 / 1000;
        long second = startTime / 1000 % 60;
        double secondDecimal = (double) ((double) startTime / 1000 % 60);
        String hourStr = String.format("%02d", hour);
        String minuteStr = String.format("%02d", minute);
        String secondStr = String.format("%02d", second);
        String secondDecimalStr = String.format("%.2f", secondDecimal);
        secondDecimalStr = secondDecimalStr.split("[.]")[1];
//        String entTimeStr = String.format("%.2f", entTime);

        int hourTime = endTime / 60 / 60;
        int minuteTime = endTime / 60;
        int secondTime = endTime % 60;

        String hourTimeStr = String.format("%02d", hourTime);
        String minuteTimeStr = String.format("%02d", minuteTime);
        String secondTimeStr = String.format("%02d", secondTime);
        String endTimeStr = hourTimeStr + ":" + minuteTimeStr + ":" + secondTimeStr;

        LogUtil.e("entTimeStr:" + endTime);

        String cmd = "-i " + allMp3 + " -ss " + hourStr + ":" + minuteStr + ":" + secondStr + "." + secondDecimalStr + " -t " + endTimeStr + " -q:a 0 -map a " + outMp3;

        LogUtil.e("cmd:" + cmd);

        if (0 == endTime) {
            MediaRefresh.scanFileAsync(context, fileVideoRecord.getAbsolutePath());
            MyProgressDialog.closeProgressDialog();
            finish();
        } else {
            EpEditor.execCmd(cmd, 0, new OnEditorListener() {
                @Override
                public void onSuccess() {
                    new File(allMp3).delete();
                    LogUtil.e("MP3提取成功");
                    MediaRefresh.scanFileAsync(context, outMp3);
                    MyProgressDialog.closeProgressDialog();
                    new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
                        @Override
                        public void run() {
//                            removeMp3(allMp3,mp4);
//                            mergeMp3AndMp4(outMp3);
//                            mp3ExtractV2();
                            mergeMp3AndMp4(outMp3);
                        }
                    }, 500);
                }

                @Override
                public void onFailure() {
                    new File(allMp3).delete();
                    LogUtil.e("MP3提取失败");
                    MediaRefresh.scanFileAsync(context, fileVideoRecord.getAbsolutePath());
                    MyProgressDialog.closeProgressDialog();
                    finish();
                }

                @Override
                public void onProgress(float progress) {
                }
            });
        }
    }

    private void mp4To420(String mp3, String mp4) {
        String outMp3AndMp4 = Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + TimeUtil.getNowDateTime() + "RECORD.MP4";
        String cmd = "-i " + mp4 + " -pix_fmt yuv420p -acodec copy -vcodec copy -f mp4 " + outMp3AndMp4;
        EpEditor.execCmd(cmd, 500, new OnEditorListener() {
            @Override
            public void onSuccess() {
                LogUtil.e("转码成功");
//                mergeMp3AndMp4(mp3);
            }

            @Override
            public void onFailure() {
                LogUtil.e("转码失败");
            }

            @Override
            public void onProgress(float progress) {
            }
        });


    }

    private void removeMp3(String mp3, String mp4) {
        String outMp3AndMp4 = Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + TimeUtil.getNowDateTime() + "RECORD.MP4";
        String cmd = "-i " + mp4 + " -c:v copy -an " + outMp3AndMp4;
        EpEditor.execCmd(cmd, 500, new OnEditorListener() {

            @Override
            public void onSuccess() {
                try {
                    Thread.sleep(1500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                LogUtil.e("去除MP3成功");
                mergeMp3AndMp4(mp3);
            }

            @Override
            public void onFailure() {
                LogUtil.e("去除MP3失败");
                MediaRefresh.scanFileAsync(context, outMp3AndMp4);
                MyProgressDialog.closeProgressDialog();
                finish();
            }

            @Override
            public void onProgress(float progress) {

            }
        });

    }

    private void mergeMp3AndMp4(String dataMp3) {
        String outMp3AndMp4 = Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + TimeUtil.getNowDateTime() + "_RECORD.MP4";
        String cmd = "-i " + fileVideoRecordModify.getAbsolutePath() + " -i " + dataMp3 + " -c:v copy -c:a aac -shortest " + outMp3AndMp4;
        EpEditor.execCmd(cmd, 0, new OnEditorListener() {
            @Override
            public void onSuccess() {
                new File(fileVideoRecord.getAbsolutePath()).delete();
                new File(fileVideoRecordModify.getAbsolutePath()).delete();
                new File(dataMp3).delete();
                LogUtil.e("MP3合并MP4成功");
                MediaRefresh.scanFileAsync(context, outMp3AndMp4);
                MyProgressDialog.closeProgressDialog();
                finish();
            }

            @Override
            public void onFailure() {
                new File(dataMp3).delete();
                new File(fileVideoRecordModify.getAbsolutePath()).delete();
                LogUtil.e("MP3合并MP4失败");
                MediaRefresh.scanFileAsync(context, outMp3AndMp4);
                MyProgressDialog.closeProgressDialog();
                finish();
            }

            @Override
            public void onProgress(float progress) {
            }
        });

//        EpEditor.music(fileVideoRecord.getAbsolutePath(), dataMp3, outMp3AndMp4, 0f, 1f, new OnEditorListener() {
//            @Override
//            public void onSuccess() {
//                LogUtil.e("MP3合并MP4成功");
//                MediaRefresh.scanFileAsync(context, outMp3AndMp4);
//                MyProgressDialog.closeProgressDialog();
//            }
//
//            @Override
//            public void onFailure() {
//                LogUtil.e("MP3合并MP4失败");
//                MediaRefresh.scanFileAsync(context, outMp3AndMp4);
//                MyProgressDialog.closeProgressDialog();
//            }
//
//            @Override
//            public void onProgress(float progress) {
//            }
//        });


//        try {
//            Movie movie = MovieCreator.build(fileVideoRecord.getAbsolutePath());
//            AACTrackImpl aacTrack = new AACTrackImpl(new FileDataSourceImpl(outMp3Extract));
//            CroppedTrack aacCroppedTrack = new CroppedTrack(aacTrack, 1, aacTrack.getSamples().size());
//            movie.addTrack(aacCroppedTrack);
//
//            Container mp4file = new DefaultMp4Builder().build(movie);
//
//            FileOutputStream fileOutputStream = new FileOutputStream(outMp3AndMp4);
//            FileChannel fc = fileOutputStream.getChannel();
//            mp4file.writeContainer(fc);
//            fileOutputStream.close();
//            finish();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }


    }

//    public static class MyRxFFmpegSubscriber extends RxFFmpegSubscriber {
//
//
//        public MyRxFFmpegSubscriber() {
//
//        }
//
//        @Override
//        public void onFinish() {
//            LogUtil.e("MP3合并MP4成功");
//        }
//
//        @Override
//        public void onProgress(int progress, long progressTime) {
//            LogUtil.e("progress：" + progress);
//        }
//
//        @Override
//        public void onCancel() {
//        }
//
//        @Override
//        public void onError(String message) {
//            LogUtil.e("MP3合并MP4失败");
//        }
//    }

    private static MediaExportCallBack callBack = new MediaExportCallBack() {
        @Override
        public void onMediaExportSuccess(MediaInfo mediaInfo) {
            // 导出成功
            String mediaPath = mediaInfo.getMediaPath();

            final MediaMetadataRetriever mediaMetadata = new MediaMetadataRetriever();
            mediaMetadata.setDataSource(getContext(), Uri.parse(mediaPath));
            int width = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
            int height = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
            String outPath = "";

            if (width / height == 2) {
                //全景视频
                outPath = StorageUtil.getRootPath(getContext()) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + "_" + System.currentTimeMillis() / 1000 + ".MP4";
            } else {
                //录屏视频
                outPath = StorageUtil.getRootPath(getContext()) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + "_RECORD.MP4";
            }

            FileUtils.copy(mediaPath, outPath);
            MediaRefresh.scanFileAsync(getContext(), outPath);
            FileUtils.delete(mediaPath);

        }

        @Override
        public void onMediaExportFailed(int errorCode) {
            // 导出失败
        }
    };


//    HVERecordListener mHVERecordListener = new HVERecordListener(){
//        @Override
//        public void onRecordStateChange(HVERecordState recordingStateHve) {
//            // 录屏状态变化
//        }
//
//        @Override
//        public void onRecordProgress(int duration) {
//            // 录屏进度
//        }
//
//        @Override
//        public void onRecordError(HVEErrorCode err, String msg) {
//            // 录屏错误
//        }
//
//        @Override
//        public void onRecordComplete(HVERecordFile fileHve) {
//            // 录屏完成
//        }
//    };

    /**
     * 请求HMS功能所需的READ_PHONE_STATE权限
     */
    private void requestHmsPermissionForVideoEditor() {
        PermissionPopup popup = new PermissionPopup(this);
        popup.setReqDesc("为使用视频编辑功能，请授予设备信息权限用于获取网络状态");
        popup.showPopupWindow();
        popup.tvRight.setOnClickListener(v -> {
            popup.dismiss();
            PermissionUtils.permission(PermissionConstants.PHONE)
                    .rationale((activity, shouldRequest) -> new Handler().postDelayed(() ->
                            shouldRequest.again(true), 100))
                    .callback(new PermissionUtils.FullCallback() {
                        @Override
                        public void onGranted(List<String> permissionsGranted) {
                            // 权限授予成功，启动HMS视频编辑器
                            launchHmsVideoEditor();
                        }

                        @Override
                        public void onDenied(List<String> permissionsDeniedForever, List<String> permissionsDenied) {
                            ToastUtils.showLong("缺少设备信息权限，无法使用视频编辑功能");
                        }
                    }).request();
        });
    }

    /**
     * 启动HMS视频编辑器
     */
    private void launchHmsVideoEditor() {
        try {
            MediaApplication.getInstance().setApiKey("CwEAAAAAilz6/UCs1xRzXdGzAcuq5FVjoahmIHUruify0vaxv5/uyfXtXrF3v56qMm5R9MPb5FsMf6C8u5lMBiFJCL+k6fP9Rks=");
            MediaApplication.getInstance().setLicenseId(DeviceUtils.getUniqueDeviceId());
            VideoEditorLaunchOption option = new VideoEditorLaunchOption.Builder().setStartMode(MediaApplication.START_MODE_IMPORT_FROM_MEDIA).build();
            MediaApplication.getInstance().launchEditorActivity(mContext, option);
            MediaApplication.getInstance().setOnMediaExportCallBack(callBack);
        } catch (Exception e) {
            LogUtil.e("HMS Video Editor启动失败: " + e.getMessage());
            ToastUtils.showShort("视频编辑器启动失败");
        }
    }

}

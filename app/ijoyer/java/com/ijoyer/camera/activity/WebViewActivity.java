package com.ijoyer.camera.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.os.Parcelable;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceError;
import android.webkit.WebResourceRequest;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toolbar;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityCompat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.sdk.app.PayTask;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PathUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.bumptech.glide.Glide;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.ui.popupwindow.SharePopUp;
import com.icatch.mobilecam.utils.ImageUtils;
import com.icatch.mobilecam.utils.PermissionTools;
import com.icatch.mobilecam.utils.SPKey;
import com.icatch.mobilecam.utils.ToastUtil;
import com.ijoyer.camera.bean.QrCodeBean;
import com.ijoyer.camera.http.net.bean.AliPayBean;
import com.ijoyer.camera.http.net.bean.WxPayBean;
import com.ijoyer.camera.ui.IjoyerWebView;
import com.ijoyer.camera.ui.PermissionPopup;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.camera.webview.BridgeHandler;
import com.ijoyer.camera.webview.CallBackFunction;
import com.ijoyer.mobilecam.R;
import com.ijoyer.mobilecam.wxapi.WXEntryActivity;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.mm.opensdk.constants.ConstantsAPI;
import com.tencent.mm.opensdk.modelbase.BaseResp;
import com.tencent.mm.opensdk.modelmsg.SendAuth;
import com.tencent.mm.opensdk.modelpay.PayReq;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;
import com.tencent.mmkv.MMKV;
import com.tencent.tauth.Tencent;
import com.umeng.socialize.ShareAction;
import com.umeng.socialize.UMShareListener;
import com.umeng.socialize.bean.SHARE_MEDIA;
import com.umeng.socialize.media.UMImage;
import com.umeng.socialize.media.UMWeb;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

public class WebViewActivity extends AppCompatActivity {
    @BindView(R.id.webView)
    IjoyerWebView mWebView;
    @BindView(R.id.tv_title)
    TextView tvTitle;
    @BindView(R.id.cl_top)
    ConstraintLayout clTop;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    @BindView(R.id.iv_share)
    ImageView ivShare;
    @BindView(R.id.loadingView)
    View loadingView;

    //loading 是否已经初始化了，处理页面偶发白屏的问题
    private boolean hasLoadedInitialPage = false;

    private String mUrl;
    private Context mContext;
    private Bitmap bitmap;
    private String mPath;
    private File file;
    private ValueCallback<Uri> mUploadMessage;// 表单的数据信息
    private ValueCallback<Uri[]> mUploadCallbackAboveL;
    private final static int FILECHOOSER_RESULTCODE = 1;// 表单的结果回调
    private Uri imageUri;
    private String mUrlFirst;
    private String mShareContent;
    private MyHandlerCallBack.OnSendDataListener mOnSendDataListener;
    private IWXAPI api;

    private void wxPay(WxPayBean.ResultBean bean) {
        IWXAPI api = WXAPIFactory.createWXAPI(mContext, PanoramaApp.APP_ID);
        api.registerApp(PanoramaApp.APP_ID);
        PayReq payReq = new PayReq();
        payReq.appId = PanoramaApp.APP_ID;
        payReq.partnerId = "1460734302";
        payReq.prepayId = bean.prepayid;
        payReq.packageValue = "Sign=WXPay";
        payReq.nonceStr = bean.noncestr;
        payReq.timeStamp = bean.timestamp;
        payReq.sign = bean.sign;
        api.sendReq(payReq);
    }

    private void aliPay(String orderIndo) {
        final Runnable payRunnable = new Runnable() {

            @Override
            public void run() {
                PayTask alipay = new PayTask(WebViewActivity.this);
                Map<String, String> result = alipay.payV2(orderIndo, true);
                Log.i("msp", result.toString());

                Message msg = new Message();
                msg.what = 1;
                msg.obj = result;
                mHandler.sendMessage(msg);
            }
        };

        // 必须异步调用
        Thread payThread = new Thread(payRunnable);
        payThread.start();
    }

    @SuppressLint("HandlerLeak")
    private Handler mHandler = new Handler() {
        @SuppressWarnings("unused")
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case 1: {
                    @SuppressWarnings("unchecked")
                    PayResult payResult = new PayResult((Map<String, String>) msg.obj);
                    /**
                     * 对于支付结果，请商户依赖服务端的异步通知结果。同步通知结果，仅作为支付结束的通知。
                     */
                    String resultInfo = payResult.getResult();// 同步返回需要验证的信息
                    String resultStatus = payResult.getResultStatus();
                    // 判断resultStatus 为9000则代表支付成功
                    if (TextUtils.equals(resultStatus, "9000")) {
                        // 该笔订单是否真实支付成功，需要依赖服务端的异步通知。
                        ToastUtils.showLong("支付成功");
                        Log.e("ijoyer", "支付成功:" + GsonUtils.toJson(payResult));
                    } else {
                        // 该笔订单真实的支付结果，需要依赖服务端的异步通知。
                        ToastUtils.showLong("支付失败");
                        Log.e("ijoyer", "支付失败:" + GsonUtils.toJson(payResult));
                    }
                    break;
                }
                default:
                    break;
            }
        }
    };

    final class InJavaScriptLocalObj {
        @JavascriptInterface
        public void getShareContent(String content) {
            mShareContent = content;
            Log.e("ijoyer", "mShareContent:" + mShareContent);
        }

        @JavascriptInterface
        public void getShareImg(String content) {
            mPath = content;
            Log.e("ijoyer", "mPath:" + mPath);
        }
    }


    @SuppressLint("JavascriptInterface")
    protected void initView() {
        initPay();

        mUrl = getIntent().getStringExtra("url");
        mUrlFirst = getIntent().getStringExtra("url");
        LogUtil.d("Webview loading:" + mUrl);
        initWebView();
        initBridgeWebView();
        mWebView.loadUrl(mUrl);
//        mWebView.loadUrl("file:///android_asset/web.html");
    }

    private void initPay() {
        api = WXAPIFactory.createWXAPI(this, PanoramaApp.APP_ID, true);
        api.registerApp(PanoramaApp.APP_ID);
        registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {

                // 将该app注册到微信
                api.registerApp(PanoramaApp.APP_ID);
            }
        }, new IntentFilter(ConstantsAPI.ACTION_REFRESH_WXAPP));
    }

    private boolean isRegister = false;

    private void initWebView() {
//        mWebView.setLayerType(View.LAYER_TYPE_SOFTWARE, null);
        mWebView.getSettings().setJavaScriptEnabled(true);
        mWebView.getSettings().setDomStorageEnabled(true);
        mWebView.getSettings().setJavaScriptCanOpenWindowsAutomatically(true);
        mWebView.getSettings().setAllowFileAccess(true);
        mWebView.getSettings().setAllowFileAccessFromFileURLs(true);
        mWebView.getSettings().setAllowUniversalAccessFromFileURLs(true);
        mWebView.getSettings().setUseWideViewPort(true);

        mWebView.getSettings().setUseWideViewPort(true);
        mWebView.getSettings().setLoadWithOverviewMode(true);
        mWebView.getSettings().setSupportZoom(true);
        mWebView.getSettings().setBuiltInZoomControls(false);
        mWebView.getSettings().setBlockNetworkImage(false);
        mWebView.enableSlowWholeDocumentDraw();
        String userAgent = mWebView.getSettings().getUserAgentString();
        mWebView.getSettings().setUserAgentString(userAgent + "ijoyerapp");


        mWebView.setWebChromeClient(new WebChromeClient() {

            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                LogUtil.e("ijoyer-clog:" + consoleMessage.message());
                return super.onConsoleMessage(consoleMessage);
            }

            @Override
            public boolean onShowFileChooser(WebView webView,
                                             ValueCallback<Uri[]> filePathCallback,
                                             FileChooserParams fileChooserParams) {
                mUploadCallbackAboveL = filePathCallback;

                int chooseMode = getChooseMode(fileChooserParams);
                LogUtil.d("选择文件，类型：" + chooseMode + "，参数：" + JSON.toJSONString(fileChooserParams));
                ImageUtils.select(WebViewActivity.this, chooseMode, 20, new ImageUtils.OnResult() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        if (mUploadCallbackAboveL != null) {
                            mUploadCallbackAboveL.onReceiveValue(getImgWithRemark(result));
                        }
                    }

                    @Override
                    public void onCancel() {
                        super.onCancel();
                        if (mUploadCallbackAboveL != null) {
                            mUploadCallbackAboveL.onReceiveValue(null);
                        }
                    }
                });
                return true;
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                // 增加Javascript异常监控
                CrashReport.setJavascriptMonitor(view, true);
                super.onProgressChanged(view, newProgress);
            }
        });

        mWebView.setWebViewClient(new WebViewClient() {

            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                if (!hasLoadedInitialPage) {
                    //超过时间后自动隐藏loading，兜底
                    view.postDelayed(() -> {
                        if (isFinishing() || isDestroyed()) {
                            return;
                        }
                        hideLoading(view);
                    }, 10 * 1000);
                }
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                String url = request.getUrl().toString();
                LogUtil.d("Webview loading:" + url);
                if (url.startsWith("tel:")) {
                    startActivity(new Intent(Intent.ACTION_DIAL, Uri.parse(url)));
                    return true; //返回false表示此url默认由系统处理,url未加载完成，会继续往下走
                }
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public void onLoadResource(WebView view, String url) {
                super.onLoadResource(view, url);
                mWebView.loadUrl("javascript:"
                        + "if(document.querySelector('meta[itemprop=\"image\"]')){\n" +
                        "          window.local_obj.getShareImg(document.querySelector('meta[itemprop=\"image\"]').getAttribute('content'))\n" +
                        "        };"
                );
                mWebView.loadUrl("javascript:"
                        + "if(document.querySelector('meta[name=\"description\"]')){\n" +
                        "          window.local_obj.getShareContent(document.querySelector('meta[name=\"description\"]').getAttribute('content'))\n" +
                        "        };"
                );
                if (getIntent().getStringExtra("url").equals(url)) {
                    mPath = "";
                    file = null;
                }
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                hideLoading(view);
                mUrl = url;
                bitmap = captureWebView(mWebView);
                new Handler().postDelayed(() -> {
                    if (!isRegister) {
                        //有方法名的都需要注册Handler后使用
                        isRegister = true;
//                            LogUtil.e("ijoyer-app-注册完成");
                    }
                }, 2000);
            }

            @Override
            public void onReceivedError(WebView view, WebResourceRequest request, WebResourceError error) {
                hideLoading(view);
                super.onReceivedError(view, request, error);
            }

            //             添加图片点击方法
            private void setWebImageClick(WebView view) {
                String jsCode = "javascript:(function(){" +
                        "var imgs=document.getElementsByTagName(\"image\");" +
                        "for(var i=0;i<imgs.length;i++){" +
                        "imgs[i].onclick=function(){" +
                        "window.jsCallJavaObj.showBigImg(this.src);" +
                        "}}})()";
                mWebView.loadUrl(jsCode);
            }
        });

        mWebView.addJavascriptInterface(new JsCallJavaObj() {
            @JavascriptInterface
            @Override
            public void showBigImg(String path) {
                mPath = path;
                try {
                    file = Glide.with(mContext)
                            .load(mPath).downloadOnly(400, 400)
                            .get();
                } catch (ExecutionException e) {
                    e.printStackTrace();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }, "jsCallJavaObj");

        mWebView.addJavascriptInterface(new InJavaScriptLocalObj(), "local_obj");

        mWebView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mWebView.performLongClick();
            }
        });

        mWebView.addJavascriptInterface(new Object() {
            @JavascriptInterface
            public void setNFCData(String action, String data) {
                LogUtil.e("action:" + action + ",data:" + data);
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if ("alipay".equals(action)) {
                            String strDecode = new String(Base64.decode(data, Base64.DEFAULT));
                            AliPayBean aliPayBean = GsonUtils.fromJson(strDecode, AliPayBean.class);
                            LogUtils.d("支付宝支付参数：" + aliPayBean.str);
                            aliPay(aliPayBean.str);
                        } else if ("wxpay".equals(action)) {
                            String strDecode = new String(Base64.decode(data, Base64.DEFAULT));
                            WxPayBean.ResultBean wxPayBean = GsonUtils.fromJson(strDecode, WxPayBean.ResultBean.class);
                            LogUtils.d("微信支付参数：" + GsonUtils.toJson(wxPayBean));
                            wxPay(wxPayBean);
                        } else if ("share_img".equals(action)) {
                            String strDecode = new String(Base64.decode(data, Base64.DEFAULT));
                            QrCodeBean qrCodeBean = GsonUtils.fromJson(strDecode, QrCodeBean.class);
                            Bitmap bitmap = base64ToBitmap(qrCodeBean.QRCodeUrl);
                            SharePopUp popUp = new SharePopUp(mContext);
                            popUp.showPopupWindow();
                            popUp.llShareForQQ.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Tencent.setIsPermissionGranted(true);
                                    UMImage umImage = new UMImage(mContext, bitmap);
                                    umImage.setTitle(mWebView.getTitle());
                                    umImage.setThumb(umImage);
                                    ShareAction shareAction = new ShareAction(WebViewActivity.this)
                                            .setPlatform(SHARE_MEDIA.QQ).withMedia(umImage).setCallback(new UMShareListener() {
                                                @Override
                                                public void onStart(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onResult(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                                    ToastUtils.showShort(throwable.getMessage());
                                                }

                                                @Override
                                                public void onCancel(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }
                                            });
                                    shareAction.share();
                                }
                            });
                            popUp.llShareForWX.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    UMImage umImage = new UMImage(mContext, bitmap);
                                    umImage.setTitle(mWebView.getTitle());
                                    umImage.setThumb(umImage);
                                    ShareAction shareAction = new ShareAction(WebViewActivity.this)
                                            .setPlatform(SHARE_MEDIA.WEIXIN).withMedia(umImage).setCallback(new UMShareListener() {
                                                @Override
                                                public void onStart(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onResult(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                                    ToastUtils.showShort(throwable.getMessage());
                                                }

                                                @Override
                                                public void onCancel(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }
                                            });
                                    shareAction.share();
                                }
                            });

                            popUp.llShareForPyq.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Tencent.setIsPermissionGranted(true);
                                    UMImage umImage = new UMImage(mContext, bitmap);
                                    umImage.setTitle(mWebView.getTitle());
                                    umImage.setThumb(umImage);
                                    ShareAction shareAction = new ShareAction(WebViewActivity.this)
                                            .setPlatform(SHARE_MEDIA.WEIXIN_CIRCLE).withMedia(umImage).setCallback(new UMShareListener() {
                                                @Override
                                                public void onStart(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onResult(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                                    ToastUtils.showShort(throwable.getMessage());
                                                }

                                                @Override
                                                public void onCancel(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }
                                            });
                                    shareAction.share();
                                }
                            });
                            popUp.llShareForWeibo.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Tencent.setIsPermissionGranted(true);
                                    UMImage umImage = new UMImage(mContext, bitmap);
                                    umImage.setTitle(mWebView.getTitle());
                                    umImage.setThumb(umImage);
                                    ShareAction shareAction = new ShareAction(WebViewActivity.this)
                                            .setPlatform(SHARE_MEDIA.SINA).withMedia(umImage).setCallback(new UMShareListener() {
                                                @Override
                                                public void onStart(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onResult(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                                    ToastUtils.showShort(throwable.getMessage());
                                                }

                                                @Override
                                                public void onCancel(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }
                                            });
                                    shareAction.share();
                                }
                            });

                            popUp.llShareForQqZone.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    Tencent.setIsPermissionGranted(true);
                                    UMImage umImage = new UMImage(mContext, bitmap);
                                    umImage.setTitle(mWebView.getTitle());
                                    umImage.setThumb(umImage);
                                    ShareAction shareAction = new ShareAction(WebViewActivity.this)
                                            .setPlatform(SHARE_MEDIA.QZONE).withMedia(umImage).setCallback(new UMShareListener() {
                                                @Override
                                                public void onStart(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onResult(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }

                                                @Override
                                                public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                                    ToastUtils.showShort(throwable.getMessage());
                                                }

                                                @Override
                                                public void onCancel(SHARE_MEDIA share_media) {
                                                    LogUtil.e(GsonUtils.toJson(share_media));
                                                }
                                            });
                                    shareAction.share();
                                }
                            });

                            popUp.llShareLink.setOnClickListener(new View.OnClickListener() {
                                @Override
                                public void onClick(View v) {
                                    mWebView.getUrl();
                                    try {
                                        ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                                        ClipData mClipData = ClipData.newPlainText("Label", mWebView.getUrl().toString());
                                        cm.setPrimaryClip(mClipData);
                                        runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                ToastUtils.showLong("复制成功");
                                            }
                                        });
                                        popUp.dismiss();
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                    }
                                }
                            });
                        } else if ("wechat_login".equals(action)) {//唤起微信登录
                            String strDecode = new String(Base64.decode(data, Base64.DEFAULT));
                            JSONObject loginParam = JSONObject.parseObject(strDecode);
                            startLoginWechat(loginParam.getString("callback"));
                        }
                    }
                });
            }
        }, "android");


        mWebView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                WebView.HitTestResult result = mWebView.getHitTestResult();
                if (result != null) {
                    int type = result.getType();
                    if (type == WebView.HitTestResult.IMAGE_TYPE || type == WebView.HitTestResult.SRC_IMAGE_ANCHOR_TYPE) {
                        String imgurl = result.getExtra();
                        LogUtil.e("imgurl:" + imgurl);
                    }
                }
                return true;
            }
        });
        setActionBar(toolbar);
        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().setDisplayShowTitleEnabled(false);
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (checkCanFinish() || System.currentTimeMillis() - lastClickTime < 200) {
                    finish();
                } else {
                    mWebView.goBack();
                    mPath = "";
                }
                lastClickTime = System.currentTimeMillis();
            }
        });
    }

    //{"a":{"a":1,"b":false,"c":"audio/mpeg","f":false},"acceptTypes":["audio/mpeg"],"captureEnabled":false,"mode":1,"permissionMode":0}
    //{"a":{"a":1,"b":false,"c":"image/gif,image/jpeg,image/png","f":false},"acceptTypes":["image/gif","image/jpeg","image/png"],"captureEnabled":false,"mode":1,"permissionMode":0}
    //{"a":{"a":1,"b":false,"c":"audio/mp4,video/mp4","f":false},"acceptTypes":["audio/mp4","video/mp4"],"captureEnabled":false,"mode":1,"permissionMode":0}
    private static int getChooseMode(WebChromeClient.FileChooserParams fileChooserParams) {
        int chooseMode = SelectMimeType.ofAll();//视频和图片
        if (fileChooserParams != null
                && fileChooserParams.getAcceptTypes() != null
                && fileChooserParams.getAcceptTypes().length > 0) {
            boolean hasImg = false, hasVideo = false,hasAudio = false;
            for (String acceptType : fileChooserParams.getAcceptTypes()) {
                if (TextUtils.isEmpty(acceptType)){
                    continue;
                }
                if (acceptType.startsWith("image")) {
                    hasImg = true;
                } else if (acceptType.startsWith("video")) {
                    hasVideo = true;
                }else if (acceptType.startsWith("audio")){
                    hasAudio = true;
                }
            }
            if (hasImg && hasVideo) {
                chooseMode = SelectMimeType.ofAll();
            } else if (hasImg) {
                chooseMode = SelectMimeType.ofImage();
            } else if (hasVideo) {
                chooseMode = SelectMimeType.ofVideo();
            }else if (hasAudio){
                chooseMode = SelectMimeType.ofAudio();
            }

        }
        return chooseMode;
    }

    //隐藏掉loading view
    private void hideLoading(WebView view) {
        if (!hasLoadedInitialPage) { // Only for the first intended page load
            hasLoadedInitialPage = true;
            view.setVisibility(View.VISIBLE);
            view.post(view::invalidate);
            loadingView.setVisibility(View.GONE);
        }
    }

    ///登录微信
    private void startLoginWechat(String callback) {
        final String wechatLoginState = "wechatLogin" + System.currentTimeMillis();
        WXEntryActivity.registerOnWXResp(resp -> {
            if (!(resp instanceof SendAuth.Resp)) {
                return;
            }
            switch (resp.errCode) {
                case BaseResp.ErrCode.ERR_OK:
                    //拉起微信成功
                    SendAuth.Resp authResp = (SendAuth.Resp) resp;
                    if (!Objects.equals(authResp.state, wechatLoginState)) {
                        ToastUtil.showShortToast("未知错误，请重新尝试");
                        LogUtils.e("wechatLoginState 对不上，考虑被攻击");
                        return;
                    }
                    if (!TextUtils.isEmpty(authResp.code)) {
                        //回调回去
                        String s = "javascript:" + callback + "({'code':'" + authResp.code + "'})";
                        mWebView.loadUrl(s);
                    }
                    break;
                case BaseResp.ErrCode.ERR_AUTH_DENIED:
                    ToastUtil.showShortToast("授权错误，请重新尝试");
                    break;
                case BaseResp.ErrCode.ERR_USER_CANCEL:
                    ToastUtil.showShortToast("您已取消登录");
                    break;
                default:
                    break;
            }
        });
        final SendAuth.Req req = new SendAuth.Req();
        req.scope = "snsapi_userinfo"; // 只能填 snsapi_userinfo
        req.state = wechatLoginState;
        api.sendReq(req);
    }

    private long lastClickTime;

    //将有备注名的图片转存到缓存中并重命名为备注名称
    Uri[] getImgWithRemark(ArrayList<LocalMedia> result) {
        if (result == null || result.isEmpty()) {
            return null;
        }
        String cacheDir = PathUtils.getInternalAppCachePath() + "/remarkName/";
        FileUtils.deleteAllInDir(cacheDir);
        List<Uri> uris = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            LocalMedia media = result.get(i);
            Uri uri = null;
            String group = SPKey.getRemarkGroup(media.getFileName());
            if (!TextUtils.isEmpty(group)) {
                //备注名
                String remarkName = MMKV.defaultMMKV().getString(SPKey.REMARK_NAME_PREFIX + group, null);
                if (!TextUtils.isEmpty(remarkName)) {
                    //将文件copy 到缓存中
                    boolean existsDir = FileUtils.createOrExistsDir(cacheDir);
                    if (existsDir) {
                        String cacheFile = cacheDir + remarkName + "_" + media.getFileName();
                        LogUtil.d("WebView：选择文件：找到备注名，转存：" + remarkName + "：" + cacheFile);
                        boolean copy = FileUtils.copy(ImageUtils.getRealPath(this, media), cacheFile);
                        if (copy) {
                            uri = Uri.fromFile(new File(cacheFile));
                        }
                    }
                }
            }
            if (uri == null) {
                uri = Uri.parse(media.getPath());
            }
            uris.add(uri);
        }
        return uris.toArray(new Uri[0]);
    }

    private boolean checkAndShowRequestPermissionDialog(String reqDescStr, String[] permissions, Runnable runnable) {
        if (PermissionUtils.isGranted(permissions)) {
            if (runnable != null) {
                runnable.run();
            }
            return true;
        }
        PermissionPopup popup = new PermissionPopup(mContext);
        popup.setReqDesc(reqDescStr);
        popup.showPopupWindow();
        popup.tvRight.setOnClickListener(v -> {
            popup.dismiss();
            PermissionUtils.permission(permissions)
                    .rationale((activity, shouldRequest) -> new Handler().postDelayed(() ->
                            shouldRequest.again(true), 100))
                    .callback(new PermissionUtils.FullCallback() {
                        @Override
                        public void onGranted(List<String> permissionsGranted) {
                            int count = 0;
                            for (int i = 0; i < permissionsGranted.size(); i++) {
                                String granted = permissionsGranted.get(i);
                                for (String permission : permissions) {
                                    if (Objects.equals(granted, permission)) {
                                        count++;
                                        break;
                                    }
                                }
                            }

                            //成功赋予权限
                            if (count >= permissions.length) {
                                if (runnable != null) {
                                    runnable.run();
                                }
                            }
                        }

                        @Override
                        public void onDenied(List<String> permissionsDeniedForever, List<String> permissionsDenied) {
                            if (!permissionsDeniedForever.isEmpty()) {
                                String[] systemRequestArray = permissionsDenied.toArray(new String[permissionsDenied.size()]);
                                ActivityCompat.requestPermissions((MainActivity) mContext, systemRequestArray, PermissionTools.ALL_REQUEST_CODE);
                            }
                        }
                    }).request();
        });
        return false;
    }

    public void take() {
        File imageStorageDir = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "MyApp");
        // Create the storage directory if it does not exist
        if (!imageStorageDir.exists()) {
            imageStorageDir.mkdirs();
        }
        File file = new File(imageStorageDir + File.separator + String.valueOf(System.currentTimeMillis()) + ".JPG");
        imageUri = Uri.fromFile(file);

        final List<Intent> cameraIntents = new ArrayList<>();
        final Intent captureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        final PackageManager packageManager = getPackageManager();
        final List<ResolveInfo> listCam = packageManager.queryIntentActivities(captureIntent, 0);
        for (ResolveInfo res : listCam) {
            final String packageName = res.activityInfo.packageName;
            final Intent i = new Intent(captureIntent);
            i.setComponent(new ComponentName(res.activityInfo.packageName, res.activityInfo.name));
            i.setPackage(packageName);
            i.putExtra(MediaStore.EXTRA_OUTPUT, imageUri);
            cameraIntents.add(i);

        }
        Intent i = new Intent(Intent.ACTION_GET_CONTENT);
        i.addCategory(Intent.CATEGORY_OPENABLE);
//        i.setType("image*//*");
        i.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, "image/*");

        Intent chooserIntent = Intent.createChooser(i, "Image Chooser");
        chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, cameraIntents.toArray(new Parcelable[]{}));
        startActivityForResult(chooserIntent, FILECHOOSER_RESULTCODE);
    }

    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == FILECHOOSER_RESULTCODE) {
            if (null == mUploadMessage && null == mUploadCallbackAboveL) return;
            Uri result = data == null || resultCode != RESULT_OK ? null : data.getData();
            if (mUploadCallbackAboveL != null) {
                onActivityResultAboveL(requestCode, resultCode, data);
            } else if (mUploadMessage != null) {
                if (result == null) {
//                   mUploadMessage.onReceiveValue(imageUri);
                    mUploadMessage.onReceiveValue(imageUri);
                    mUploadMessage = null;

                } else {
                    mUploadMessage.onReceiveValue(result);
                    mUploadMessage = null;
                }
            }
        }
    }


    @SuppressWarnings("null")
    private void onActivityResultAboveL(int requestCode, int resultCode, Intent data) {
        if (requestCode == FILECHOOSER_RESULTCODE) {
            if (mUploadCallbackAboveL == null) {
                return;
            }

            Uri[] results = null;
            if (resultCode == Activity.RESULT_OK) {
                if (data == null) {
                    results = new Uri[]{imageUri};
                } else {
                    String dataString = data.getDataString();
                    ClipData clipData = data.getClipData();

                    if (clipData != null) {
                        results = new Uri[clipData.getItemCount()];
                        for (int i = 0; i < clipData.getItemCount(); i++) {
                            ClipData.Item item = clipData.getItemAt(i);
                            results[i] = item.getUri();
                        }
                    }

                    if (dataString != null)
                        results = new Uri[]{Uri.parse(dataString)};
                }
            }
            if (results != null) {
                mUploadCallbackAboveL.onReceiveValue(results);
                mUploadCallbackAboveL = null;
            } else {
                results = new Uri[]{imageUri};
                mUploadCallbackAboveL.onReceiveValue(results);
                mUploadCallbackAboveL = null;
            }
        }
    }


    private interface JsCallJavaObj {
        void showBigImg(String path);
    }

    /**
     * 截取webView可视区域的截图
     *
     * @param webView 前提：WebView要设置webView.setDrawingCacheEnabled(true);
     * @return
     */
    private Bitmap captureWebViewVisibleSize(WebView webView) {
        Bitmap bmp = webView.getDrawingCache();
        return bmp;
    }

    public static Bitmap base64ToBitmap(String imagestr) {
        byte[] decode = Base64.decode(imagestr.split(",")[1], Base64.DEFAULT);
        Bitmap bitmap = BitmapFactory.decodeByteArray(decode, 0, decode.length);
        return bitmap;
    }

    /**
     * 截取webView快照(webView加载的整个内容的大小)
     *
     * @param webView
     * @return
     */
    private Bitmap captureWebView(WebView webView) {
        int height = (int) (webView.getContentHeight() * webView.getScale());
        int width = webView.getWidth();
        int pH = webView.getHeight();
        Bitmap bitmap;
        if (width == 0 || height == 0) {
            bitmap = Bitmap.createBitmap(100, 100, Bitmap.Config.RGB_565);
        } else {
            bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
        }
        Canvas canvas = new Canvas(bitmap);
        int top = height;
        while (top > 0) {
            if (top < pH) {
                top = 0;
            } else {
                top -= pH;
            }
            canvas.save();
            canvas.clipRect(0, top, width, top + pH);
            webView.scrollTo(0, top);
            webView.draw(canvas);
            canvas.restore();
        }

        return bitmap;
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_web_viwe);
        ButterKnife.bind(this);
        mContext = this;

        //如果之前绑定过网络，在这里解绑
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        connectivityManager.bindProcessToNetwork(null); // 解绑当前进程绑定的网络
        initView();
    }


    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (checkCanFinish()) {
            finish();
        }

        if ((keyCode == KeyEvent.KEYCODE_BACK) && mWebView.canGoBack()) {
            mWebView.goBack();
            mPath = "";
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onBackPressed() {
        if (checkCanFinish()) {
            finish();
        }

        if (mWebView.canGoBack()) {
            mPath = "";
            mWebView.goBack();
        } else {
            super.onBackPressed();
        }
    }

    private boolean checkCanFinish() {
        if (mWebView.getUrl() == null || mUrlFirst.equals(mWebView.getUrl())
                || "http://www.ijoyer.com/mobile/index".equals(mWebView.getUrl())
                || "https://www.ijoyer.com/mobile/index".equals(mWebView.getUrl())
                || "http://www.ijoyer.com/mobile/home/<USER>".equals(mWebView.getUrl())
                || "https://www.ijoyer.com/mobile/home/<USER>".equals(mWebView.getUrl())
                || "http://www.ijoyer.com/mobile/home/<USER>/faq".equals(mWebView.getUrl())
                || "https://www.ijoyer.com/mobile/home/<USER>/faq".equals(mWebView.getUrl())
                || mWebView.getUrl().contains("www.ijoyer.com/passport/login")
                || mWebView.getUrl().contains("www.ijoyer.com/mobile/home/<USER>")
                || mWebView.getUrl().contains("www.ijoyer.com/mobile/index")
                || mWebView.getUrl().contains("www.ijoyer.com/mobile/home/<USER>")
                || mWebView.getUrl().contains("www.ijoyer.com/mobile/home/<USER>/faq")

        ) {
            return true;
        } else {
            return false;
        }
    }

    @OnClick({R.id.iv_share})
    public void onViewClicked(View view) {
        bitmap = captureWebView(mWebView);
        switch (view.getId()) {
            case R.id.iv_share:
                SharePopUp popUp = new SharePopUp(this);
                popUp.showPopupWindow();
                popUp.llShareForQQ.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Tencent.setIsPermissionGranted(true);
                        UMWeb web = new UMWeb(mUrl);
                        web.setTitle(mWebView.getTitle());
                        UMImage image;
                        if (!TextUtils.isEmpty(mPath)) {
                            image = new UMImage(mContext, mPath);
                        } else {
                            image = new UMImage(mContext, bitmap);
                        }
                        web.setThumb(image);
                        web.setDescription(TextUtils.isEmpty(mShareContent) ? "爱生活|爱旅行|艾卓悦" : mShareContent);
                        new ShareAction(WebViewActivity.this)
                                .setPlatform(SHARE_MEDIA.QQ).withMedia(web).setCallback(new UMShareListener() {
                                    @Override
                                    public void onStart(SHARE_MEDIA share_media) {
                                    }

                                    @Override
                                    public void onResult(SHARE_MEDIA share_media) {
                                    }

                                    @Override
                                    public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                        ToastUtils.showShort(throwable.getMessage());
                                    }

                                    @Override
                                    public void onCancel(SHARE_MEDIA share_media) {
                                    }
                                }).share();
                    }
                });
                popUp.llShareForWX.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        UMWeb web = new UMWeb(mUrl);
                        web.setTitle(mWebView.getTitle());
                        UMImage image;
                        if (!TextUtils.isEmpty(mPath)) {
                            image = new UMImage(mContext, mPath);
                        } else {
                            image = new UMImage(mContext, bitmap);
                        }
                        web.setThumb(image);
                        web.setDescription(TextUtils.isEmpty(mShareContent) ? "爱生活|爱旅行|艾卓悦" : mShareContent);
                        ShareAction shareAction = new ShareAction(WebViewActivity.this)
                                .setPlatform(SHARE_MEDIA.WEIXIN).withMedia(web).setCallback(new UMShareListener() {
                                    @Override
                                    public void onStart(SHARE_MEDIA share_media) {
                                        LogUtil.e(GsonUtils.toJson(share_media));
                                    }

                                    @Override
                                    public void onResult(SHARE_MEDIA share_media) {
                                        LogUtil.e(GsonUtils.toJson(share_media));
                                    }

                                    @Override
                                    public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                        ToastUtils.showShort(throwable.getMessage());
                                    }

                                    @Override
                                    public void onCancel(SHARE_MEDIA share_media) {
                                        LogUtil.e(GsonUtils.toJson(share_media));
                                    }
                                });
                        shareAction.share();
                    }
                });

                popUp.llShareForPyq.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Tencent.setIsPermissionGranted(true);
                        UMWeb web = new UMWeb(mUrl);
                        web.setTitle(mWebView.getTitle());
                        UMImage image;
                        if (!TextUtils.isEmpty(mPath)) {
                            image = new UMImage(mContext, mPath);
                        } else {
                            image = new UMImage(mContext, bitmap);
                        }
                        web.setThumb(image);
                        web.setDescription(TextUtils.isEmpty(mShareContent) ? "爱生活|爱旅行|艾卓悦" : mShareContent);
                        new ShareAction(WebViewActivity.this)
                                .setPlatform(SHARE_MEDIA.WEIXIN_CIRCLE).withMedia(web).setCallback(new UMShareListener() {
                                    @Override
                                    public void onStart(SHARE_MEDIA share_media) {
                                    }

                                    @Override
                                    public void onResult(SHARE_MEDIA share_media) {
                                    }

                                    @Override
                                    public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                        ToastUtils.showShort(throwable.getMessage());
                                    }

                                    @Override
                                    public void onCancel(SHARE_MEDIA share_media) {
                                    }
                                }).share();
                    }
                });
                popUp.llShareForWeibo.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Tencent.setIsPermissionGranted(true);
                        UMWeb web = new UMWeb(mUrl);
                        web.setTitle(mWebView.getTitle());
                        UMImage image;
                        if (!TextUtils.isEmpty(mPath)) {
                            image = new UMImage(mContext, mPath);
                        } else {
                            image = new UMImage(mContext, bitmap);
                        }
                        web.setThumb(image);
                        web.setDescription(TextUtils.isEmpty(mShareContent) ? "爱生活|爱旅行|艾卓悦" : mShareContent);
                        new ShareAction(WebViewActivity.this)
                                .setPlatform(SHARE_MEDIA.SINA).withMedia(web).setCallback(new UMShareListener() {
                                    @Override
                                    public void onStart(SHARE_MEDIA share_media) {
                                        LogUtil.e(GsonUtils.toJson(share_media));
                                    }

                                    @Override
                                    public void onResult(SHARE_MEDIA share_media) {
                                        LogUtil.e(GsonUtils.toJson(share_media));
                                    }

                                    @Override
                                    public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                        ToastUtils.showShort(throwable.getMessage());
                                    }

                                    @Override
                                    public void onCancel(SHARE_MEDIA share_media) {
                                        LogUtil.e(GsonUtils.toJson(share_media));
                                    }
                                }).share();
                    }
                });

                popUp.llShareForQqZone.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Tencent.setIsPermissionGranted(true);
                        UMWeb web = new UMWeb(mUrl);
                        web.setTitle(AppUtils.getAppName());
                        UMImage image;
                        if (!TextUtils.isEmpty(mPath)) {
                            image = new UMImage(mContext, mPath);
                        } else {
                            image = new UMImage(mContext, bitmap);
                        }
                        web.setThumb(image);
                        web.setDescription(TextUtils.isEmpty(mShareContent) ? "爱生活|爱旅行|艾卓悦" : mShareContent);
                        new ShareAction(WebViewActivity.this)
                                .setPlatform(SHARE_MEDIA.QZONE).withMedia(web).setCallback(new UMShareListener() {
                                    @Override
                                    public void onStart(SHARE_MEDIA share_media) {
                                    }

                                    @Override
                                    public void onResult(SHARE_MEDIA share_media) {
                                    }

                                    @Override
                                    public void onError(SHARE_MEDIA share_media, Throwable throwable) {
                                        ToastUtils.showShort(throwable.getMessage());
                                    }

                                    @Override
                                    public void onCancel(SHARE_MEDIA share_media) {
                                    }
                                }).share();
                    }
                });

                popUp.llShareLink.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mWebView.getUrl();
                        try {
                            ClipboardManager cm = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
                            ClipData mClipData = ClipData.newPlainText("Label", mWebView.getUrl().toString());
                            cm.setPrimaryClip(mClipData);
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    ToastUtils.showLong("复制成功");
                                }
                            });
                            popUp.dismiss();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });

                break;
        }
    }


    private void initBridgeWebView() {
//        //Handler做为通信桥梁的作用，接收处理来自H5数据及回传Native数据的处理，当h5调用send()发送消息的时候，调用MyHandlerCallBack
//        mWebView.setDefaultHandler(new MyHandlerCallBack(mOnSendDataListener));
//        //WebChromeClient主要辅助WebView处理Javascript的对话框、网站图标、网站title、加载进度等比等，不过它还能处理文件上传操作
////        mWebView.setWebChromeClient(new WebChromeClient());
//        // 如果不加这一行，当点击界面链接，跳转到外部时，会出现net::ERR_CACHE_MISS错误
//        // 需要在androidManifest.xml文件中声明联网权限
//        // <uses-permission android:name="android.permission.INTERNET"/>
//        if (Build.VERSION.SDK_INT >= 19) {
//            mWebView.getSettings().setCacheMode(WebSettings.LOAD_CACHE_ELSE_NETWORK);
//        }
//
//        //加载网页地址
////        mWebView.loadUrl("file:///android_asset/web.html");
////        mWebView.loadUrl("http://www.ijoyer.com");
    }

    static class MyHandlerCallBack implements BridgeHandler {
        private OnSendDataListener mSendDataListener;

        public MyHandlerCallBack(OnSendDataListener mSendDataListener) {
            this.mSendDataListener = mSendDataListener;
        }

        @Override
        public void handler(String data, CallBackFunction function) {
            Log.e("ijoyer", "接收数据为：" + data);
            if (!TextUtils.isEmpty(data) && mSendDataListener != null) {
                mSendDataListener.sendData(data);
            }
            function.onCallBack("Native已收到消息！");
        }

        public interface OnSendDataListener {
            void sendData(String data);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        mWebView.destroy();
    }

}

package com.ijoyer.camera.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.FileIOUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.cry.mediametaretriverwrapper.MediaMetadataRetrieverWrapper;
import com.cry.mediametaretriverwrapper.RetrieverProcessThread;
import com.detu.android_panoplayer.PanoPlayerImpl;
import com.detu.android_panoplayer.PanoPlayerUrl;
import com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView;
import com.google.android.exoplayer2.ExoPlaybackException;
import com.google.android.exoplayer2.ExoPlayerFactory;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.SimpleExoPlayer;
import com.google.android.exoplayer2.source.ExtractorMediaSource;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.trackselection.AdaptiveTrackSelection;
import com.google.android.exoplayer2.trackselection.DefaultTrackSelector;
import com.google.android.exoplayer2.trackselection.TrackSelection;
import com.google.android.exoplayer2.ui.AspectRatioFrameLayout;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.BandwidthMeter;
import com.google.android.exoplayer2.upstream.DataSource;
import com.google.android.exoplayer2.upstream.DefaultBandwidthMeter;
import com.google.android.exoplayer2.upstream.DefaultDataSourceFactory;
import com.google.vr.sdk.widgets.video.VrVideoEventListener;
import com.google.vr.sdk.widgets.video.VrVideoView;
import com.gyf.immersionbar.BarHide;
import com.gyf.immersionbar.ImmersionBar;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.entity.Song;
import com.icatch.mobilecam.ui.popupwindow.CutSuccessWindow;
import com.icatch.mobilecam.ui.popupwindow.MusicListPopupWindow;
import com.icatch.mobilecam.ui.popupwindow.VideoListPopupWindow;
import com.icatch.mobilecam.ui.popupwindow.VideoMergeLoadingWindow;
import com.icatch.mobilecam.ui.popupwindow.VideoSpeedWindow;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.TimeUtil;
import com.ijoyer.camera.bean.GetAudioBean;
import com.ijoyer.camera.bean.NoAudioBean;
import com.ijoyer.camera.bean.StickInfoImageView;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.camera.widget.BaseImageView;
import com.ijoyer.camera.widget.VideoEditProgressView;
import com.ijoyer.camera.widget.VideoEditView;
import com.ijoyer.mobilecam.R;
import com.pili.pldroid.player.common.Util;
import com.player.panoplayer.IPanoPlayerListener;
import com.player.panoplayer.IPanoPluginListener;
import com.player.panoplayer.enitity.PanoPlayerStatus;
import com.player.panoplayer.enitity.PanoPluginStatus;
import com.player.panoplayer.plugin.Plugin;
import com.player.panoplayer.plugin.VideoPlugin;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import Jni.VideoUitls;
import VideoHandle.EpEditor;
import VideoHandle.EpVideo;
import VideoHandle.OnEditorListener;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;
import butterknife.Unbinder;
import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import razerdp.basepopup.BasePopupWindow;

public class VideoCutNewActivity extends BugTagsBaseActivity {
    @BindView(R.id.videoEditView)
    VideoEditView videoEditView;
    @BindView(R.id.video_player)
    PlayerView videoPlayer;
    @BindView(R.id.tv_select_time_range)
    TextView tvSelectTimeRange;
    @BindView(R.id.tv_merge_video)
    TextView tvMergeVideo;
    @BindView(R.id.tv_add_music)
    TextView tvAddMusic;
    @BindView(R.id.tv_sure)
    TextView tvSure;
    @BindView(R.id.tv_cancel)
    TextView tvCancel;
    @BindView(R.id.ll_cut)
    LinearLayout llCut;
    @BindView(R.id.tv_video_speed)
    TextView tvVideoSpeed;
    @BindView(R.id.ll_title)
    LinearLayout llTitle;
    @BindView(R.id.vr_video)
    VrVideoView vrVideo;
    @BindView(R.id.pp_surfaceView)
    PanoPlayerSurfaceView ppSurfaceView;
    @BindView(R.id.tv_save)
    TextView tvSave;
    @BindView(R.id.ll_select_time_range)
    LinearLayout llSelectTimeRange;
    @BindView(R.id.ll_merge_video)
    LinearLayout llMergeVideo;
    @BindView(R.id.tv_remove)
    TextView tvRemove;
    @BindView(R.id.ll_remove)
    LinearLayout llRemove;
    @BindView(R.id.ll_add_music)
    LinearLayout llAddMusic;
    @BindView(R.id.iv_select_time_range)
    ImageView ivSelectTimeRange;
    @BindView(R.id.iv_merge_video)
    ImageView ivMergeVideo;
    @BindView(R.id.iv_remove)
    ImageView ivRemove;
    @BindView(R.id.iv_add_music)
    ImageView ivAddMusic;
    @BindView(R.id.iv_icon)
    ImageView ivIcon;
    @BindView(R.id.ll_save)
    LinearLayout llSave;
    @BindView(R.id.iv_speed)
    ImageView ivSpeed;
    @BindView(R.id.ll_speed)
    LinearLayout llSpeed;
    private Unbinder unbinder;
    private String path;
    private Context mContext;
    private SimpleExoPlayer exoPlayer;
    public String mVideoRotation = "90";
    private float mPixel = 1.778f;
    private List<Bitmap> mThumbBitmap = new ArrayList<>();
    public int mVideoHeight, mVideoWidth, mVideoDuration; //mIsNotComeLocal 1表示拍摄,mIsAnswer 1表示回答者
    //存储贴纸列表
    private ArrayList<BaseImageView> mViews = new ArrayList<>();
    private ArrayList<StickInfoImageView> stickerViews = new ArrayList<>();
    private long currentTime;
    private boolean isPlayVideo;
    private boolean isFinish = false;
    private boolean isEdit = false;
    private Handler myHandler = new Handler() {
        @Override
        public void handleMessage(Message msg) {
            if (mThumbBitmap != null) {
                mThumbBitmap.add(msg.arg1, (Bitmap) msg.obj);
            }
        }
    };
    private VideoListPopupWindow videoListPopupWindow;
    private MusicListPopupWindow musicListPopupWindow;
    private float startTimeSecond = 0;
    private float endTimeSecond = 0;
    private int mMinWidth;
    private int mMinHeight;
    private VideoMergeLoadingWindow videoMergeLoadingWindow;
    private VideoSpeedWindow videoSpeedWindow;
    private CutSuccessWindow cutSuccessWindow;
    private PanoPlayerUrl panoPlayerUrl;
    private PanoPlayerImpl panoramaPlayer;
    private VideoPlugin plugin;
    private long curTime;
    private long maxTime;
    private int isSuccessGetAudioCount;
    private int isSuccessGetNoAudioCount;
    private String mergeAudioPath;
    private String mergeNoAudioPath;
    private MediaMetadataRetrieverWrapper metadataRetriever;
    private boolean canSaveFile = false;
    private Timer saveTimer;
    private int saveTotalTime;
    private boolean isRecord;
    private String type = "";
    private String saveDir;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_cut);
        mContext = this;
        mergeAudioPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "MergeAllAudio" + ".MP3";
        mergeNoAudioPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "MergeAllNoAudio" + ".MP4";
        ImmersionBar.with(this)
                .hideBar(BarHide.FLAG_HIDE_BAR)
                .transparentBar()
                .transparentStatusBar()
                .transparentNavigationBar()
                .init();
        unbinder = ButterKnife.bind(this);
        cutSuccessWindow = new CutSuccessWindow(mContext);
        cutSuccessWindow.tvSure.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
//                Intent intent = new Intent(mContext, VideoCutNewActivity.class);
//                intent.putExtra("path", path);
//                intent.putExtra("canSaveFile", true);
//                intent.putExtra("isRecord", isRecord);
//                startActivity(intent);
//                ActivityUtils.finishActivity(VideoCutActivity.class);
//                cutSuccessWindow.dismiss();

                Intent intent = new Intent(mContext, VideoCutPicFrameSecondActivity.class);
                intent.putExtra("path", path);
                intent.putExtra("canSaveFile", true);
                intent.putExtra("isRecord", isRecord);
                intent.putExtra("Activity", this.getClass().toString());
                startActivity(intent);
                cutSuccessWindow.dismiss();
            }
        });

        cutSuccessWindow.tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                cutSuccessWindow.dismiss();
                path = getIntent().getStringExtra("path");
            }
        });
        path = getIntent().getStringExtra("path");
        saveDir = getIntent().getStringExtra("saveDir");
        isRecord = getIntent().getBooleanExtra("isRecord", false);
        boolean canSaveFile = getIntent().getBooleanExtra("canSaveFile", false);
        if (canSaveFile) {
            llSave.setVisibility(View.VISIBLE);
        }
        initThumbs();
        if (!isRecord) {
            initPlayer();
        } else {
            initVideo();
        }
//        initVrView();
    }

    private void initVrView() {
        vrVideo.setFullscreenButtonEnabled(false);//设置全屏按钮不可见
        vrVideo.setStereoModeButtonEnabled(false);//设置立体眼镜模式按钮不可见
        vrVideo.setInfoButtonEnabled(false);
        vrVideo.setEventListener(new VrVideoEventListener() {
            @Override
            public void onLoadSuccess() {
                super.onLoadSuccess();
//                seekBar.setMax((int) vr_video.getDuration());
            }

            @Override
            public void onNewFrame() {
                super.onNewFrame();
//                seekBar.setProgress((int) vr_video.getCurrentPosition());
            }
        });

        VrVideoView.Options options = new VrVideoView.Options();
        options.inputType = VrVideoView.Options.TYPE_MONO;
        options.inputFormat = VrVideoView.Options.FORMAT_DEFAULT;
        try {
            vrVideo.loadVideo(Uri.parse(path), options);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    private void initPlayer() {
        ppSurfaceView.setVisibility(View.VISIBLE);
        videoPlayer.setVisibility(View.GONE);
        panoramaPlayer = ppSurfaceView.getRender();
        panoramaPlayer.setPanoPlayerListener(new IPanoPlayerListener() {
            @Override
            public void onPanoPlayerStatusChanged(PanoPlayerStatus status, String s) {
                switch (status) {
                    case LOADING:
                        break;
                    case LOADED:
                        break;
                    case ERROR:
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPanoPlayerConfigLoaded(int i) {

            }
        });

        panoramaPlayer.setPanoPluginListener(new IPanoPluginListener() {
            @Override
            public void onPanoPluginStateChanged(PanoPluginStatus status, String s) {
                switch (status) {
                    case PREPARED:
                        ppSurfaceView.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                if(!isPlayVideo) {
                                    videoStar();
                                }
                            }
                        },400);
                        break;
                    case PLAYING:
                        break;
                    case PAUSE:
                        break;
                    case STOP:
                        break;
                    case FINISH:
                        isFinish = true;
//                        plugin = (VideoPlugin) panoramaPlayer.getPlugin();
//                        if (plugin != null) {
//                            plugin.refresh();
//                        }
                        break;
                    case CODEC_SWITCH:
                        break;
                    case ERROR:
                        break;
                    default:
                        break;
                }
            }

            @Override
            public void onPanoPluginProgressChanged(long curTime, long bufTime, long maxTime) {
                VideoCutNewActivity.this.curTime = curTime;
                VideoCutNewActivity.this.maxTime = maxTime;
                if (currentTime - 100 > curTime) {
                    //只允许 100毫秒的差异
                    LogUtil.e("test----纠正进度  view-currentTime："+currentTime+"   播放器curTime"+curTime);
                }
                videoEditView.setProgress(curTime);
            }
        });
        playLocalVideo(path);
    }

    private void playLocalVideo(String paths) {
        String url = String.format(PLAYER_CONFIG, "video", paths, -1);
        this.panoPlayerUrl = new PanoPlayerUrl();
        panoPlayerUrl.setXmlContent(url);
        panoramaPlayer.playByXml(panoPlayerUrl, null);
    }

    private static final String PLAYER_CONFIG = "<DetuVr>\n" +
            "   <settings init='pano1' initmode='default' enablevr='true' title=''/>\n" +
            "   <scenes>\n" +
            "       <scene name='pano1' title='' thumburl=''>\n" +
            "           <preview url='' />\n" +
            "           <image type='%s' url='%s' device='%d' isreplay_f4 = 'true'/>\n" +
            "           <view viewmode='default'  isptselect ='true' />\n" +
            "       </scene>\n" +
            "   </scenes>\n" +
            "</DetuVr>";

    private void initVideo() {
        ppSurfaceView.setVisibility(View.GONE);
        videoPlayer.setVisibility(View.VISIBLE);
        // 得到默认合适的带宽
        BandwidthMeter bandwidthMeter = new DefaultBandwidthMeter();
        // 创建跟踪的工厂
        TrackSelection.Factory factory = new AdaptiveTrackSelection.Factory(bandwidthMeter);
        // 创建跟踪器
        DefaultTrackSelector trackSelection = new DefaultTrackSelector(factory);
        // 创建播放器
        exoPlayer = ExoPlayerFactory.newSimpleInstance(mContext, trackSelection);

        // 生成数据媒体实例，通过该实例加载媒体数据
        DataSource.Factory dataSourceFactory = new DefaultDataSourceFactory(mContext, Util.getUserAgent(mContext, "PanoramaApp"));
        // 创建资源
        Uri uri = Uri.parse(path);
        MediaSource mediaSource = new ExtractorMediaSource.Factory(dataSourceFactory).createMediaSource(uri);
        // 将播放器附加到view
        videoPlayer.setPlayer(exoPlayer);
        // 准备播放
        exoPlayer.prepare(mediaSource);
        // 准备好了之后自动播放，如果已经准备好了，调用该方法实现暂停、开始功能
        exoPlayer.setRepeatMode(Player.REPEAT_MODE_ONE);
        // 添加事件监听
        exoPlayer.addListener(listener);
        videoPlayer.setShowShuffleButton(false);
        exoPlayer.setPlayWhenReady(true);

//        videoPlayer.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIT);
//        videoPlayer.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FILL);
        videoPlayer.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIXED_WIDTH);


    }

    private Player.DefaultEventListener listener = new Player.DefaultEventListener() {
        @Override
        public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
            if (playWhenReady && playbackState == Player.STATE_READY) {
                // media actually playing
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        videoStar();
                    }
                });
            } else if (playWhenReady) {
                // might be idle (plays after prepare()),
                // buffering (plays when data available)
                // or ended (plays when seek away from end)
            } else {
                // player paused in any state
            }

            // 视频播放状态
            switch (playbackState) {
                case Player.STATE_IDLE:
                    // 空闲
                    break;
                case Player.STATE_BUFFERING:
                    // 缓冲中
                    break;
                case Player.STATE_READY:
                    // 准备好
                    break;
                case Player.STATE_ENDED:
                    // 结束
                    isFinish = true;
                    break;
                default:
                    break;
            }
        }

        @Override
        public void onPlayerError(ExoPlaybackException error) {
            // 报错
            switch (error.type) {
                case ExoPlaybackException.TYPE_SOURCE:
                    // 加载资源时出错
                    break;
                case ExoPlaybackException.TYPE_RENDERER:
                    // 渲染时出错
                    break;
                case ExoPlaybackException.TYPE_UNEXPECTED:
                    // 意外的错误
                    break;
            }
        }


    };

    @Override
    protected void onResume() {
        super.onResume();
        if (!isRecord) {
            if (ppSurfaceView != null) {
                ppSurfaceView.onResume();
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (!isRecord) {
            if (ppSurfaceView != null) {
                ppSurfaceView.onPause();
            }

            Plugin plugin = panoramaPlayer.getPlugin();
            if (plugin instanceof VideoPlugin) {
                ((VideoPlugin) plugin).pause();
            }
        }

        videoStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (!isRecord) {
            if (ppSurfaceView != null) {
                ppSurfaceView.onDestroy();
            }

            Plugin plugin = panoramaPlayer.getPlugin();
            if (plugin instanceof VideoPlugin) {
                ((VideoPlugin) plugin).close();
            }

            if (metadataRetriever != null) {
                metadataRetriever.release();
            }
        } else {
            if (exoPlayer != null) {
                exoPlayer.release();
            }
        }


        unbinder.unbind();
    }



    /**
     * 初始化缩略图
     */
    private void initThumbs() {
        final MediaMetadataRetriever mediaMetadata = new MediaMetadataRetriever();
        mediaMetadata.setDataSource(mContext, Uri.parse(path));

        try {
            mVideoRotation = mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_ROTATION);
            mVideoWidth = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
            mVideoHeight = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
        } catch (NumberFormatException e) {
            e.printStackTrace();
            finish();
        }
        mPixel = (float) mVideoHeight / (float) mVideoWidth;
        mVideoDuration = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION));
        videoEditView.setTotalTime(mVideoDuration + 100);
        final int frame = mVideoDuration / (2 * 1000);
        final int frameTime;
        if (frame > 0) {
            frameTime = mVideoDuration / frame * 1000;
        } else {
            frameTime = 1 * 1000;
        }
//        new AsyncTask<Void, Void, Boolean>() {
//            @Override
//            protected Boolean doInBackground(Void... params) {
//                for (int x = 0; x < frame; x++) {
//                    Bitmap bitmap = mediaMetadata.getFrameAtTime(frameTime * x, MediaMetadataRetriever.OPTION_CLOSEST_SYNC);
//                    Message msg = myHandler.obtainMessage();
//                    msg.obj = bitmap;
//                    msg.arg1 = x;
//                    myHandler.sendMessage(msg);
//                }
//                mediaMetadata.release();
//                return true;
//            }
//
//            @Override
//            protected void onPostExecute(Boolean result) {
//                if (ActivityUtils.isActivityAlive(mContext)) {
//                    if (mThumbBitmap != null && videoEditView != null) {
//                        videoEditView.addImageView(mThumbBitmap);
//                    }
//                }
//            }
//        }.execute();

        List<Bitmap> mThumbBitmapV2 = new ArrayList<>();
        Handler handler = new Handler();
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (TextUtils.isEmpty(saveDir)) {
                    mThumbBitmapV2.remove(mThumbBitmapV2.size() - 1);
                    if (metadataRetriever!=null)
                        metadataRetriever.release();
                }
                if (videoEditView != null && mThumbBitmapV2 != null) {
                    videoEditView.addImageView(mThumbBitmapV2);
                }
            }
        };

        if (TextUtils.isEmpty(saveDir)) {
            metadataRetriever = new MediaMetadataRetrieverWrapper();
            metadataRetriever.setDataSource(path);
            metadataRetriever.getFramesInterval(1000, 5, new RetrieverProcessThread.BitmapCallBack() {
                @Override
                public void onComplete(final Bitmap frame) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mThumbBitmapV2.add(frame);
                            handler.removeCallbacks(runnable);
                            handler.postDelayed(runnable, 800);
                        }
                    });
                }
            });
        } else {
            for (int i = 0;i<FileUtils.listFilesInDir(saveDir).size();i++){
                if (FileUtils.listFilesInDir(saveDir).get(i).getName().contains("jpg")){
                    File file = FileUtils.listFilesInDir(saveDir).get(i);
                    byte[] str = FileIOUtils.readFile2BytesByMap(file);
                    Bitmap bitmap = ConvertUtils.bytes2Bitmap(str);
                    mThumbBitmapV2.add(bitmap);
                }
            }

            handler.removeCallbacks(runnable);
            handler.postDelayed(runnable, 0);
        }





        videoEditView.setOnSelectTimeChangeListener(new VideoEditView.OnSelectTimeChangeListener() {
            @Override
            public void isFinish(boolean finish) {
                isFinish = finish;
            }

            @Override
            public void selectTimeChange(long startTime, long endTime) {
                startTimeSecond = startTime / 1000f;
                endTimeSecond = endTime / 1000f;
            }

            @Override
            public void playChange(boolean isPlayVideo) {
                VideoCutNewActivity.this.isPlayVideo = isPlayVideo;
                try {
                    if (!isRecord) {
                        if (isPlayVideo) {
                            if (!isFinish) {
                                Plugin plugin = panoramaPlayer.getPlugin();
                                if (plugin instanceof VideoPlugin) {
                                    ((VideoPlugin) plugin).start();
                                }
                            }
                        } else {
                            Plugin plugin = panoramaPlayer.getPlugin();
                            if (plugin instanceof VideoPlugin) {
                                ((VideoPlugin) plugin).pause();
                            }
                        }
                    } else {
                        if (isPlayVideo) {
                            if (!isFinish) {
                                exoPlayer.setPlayWhenReady(true);
                            }
                        } else {
                            exoPlayer.setPlayWhenReady(false);
                        }
                    }


//                    if (isPlayVideo) {
//                        if (!isFinish) {
//                            exoPlayer.setPlayWhenReady(true);
//                        }
//                    } else {
//                        exoPlayer.setPlayWhenReady(false);
//                    }
                } catch (Exception e) {

                }
            }

            @Override
            public void videoProgressUpdate(long currentTime, boolean isVideoPlaying) {
                VideoCutNewActivity.this.currentTime = currentTime;
                if (currentTime == 0) {
                    isFinish = true;
                } else {
                    isFinish = false;
                }
                if (!isVideoPlaying) {
                    try {
                        if (!isRecord) {
                            if (maxTime - 1000 < curTime && currentTime == 0) {
                                isFinish = true;
                            } else {
                                isFinish = false;
                            }

                            if (!isFinish && currentTime != 0f) {
                                if (currentTime > maxTime - 1000) {
                                    Plugin plugin = panoramaPlayer.getPlugin();
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (plugin instanceof VideoPlugin) {
                                                ((VideoPlugin) plugin).seekTo((int) maxTime - 100);
                                            }
                                        }
                                    });

                                } else {
                                    Plugin plugin = panoramaPlayer.getPlugin();
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (plugin instanceof VideoPlugin) {
                                                ((VideoPlugin) plugin).seekTo(currentTime);
                                            }
                                        }
                                    });
                                }

                                if (isPlayVideo) {
                                    videoStar();
                                } else {
                                    videoStop();
                                }
                            }
                        } else {
                            if (exoPlayer.getDuration() < exoPlayer.getCurrentPosition()) {
                                isFinish = true;
                            }
                            if (!isFinish && currentTime != 0) {
                                if (currentTime > exoPlayer.getDuration()) {
                                    exoPlayer.seekTo(exoPlayer.getDuration() - 100);
                                } else {
                                    exoPlayer.seekTo((int) currentTime);
                                }
                                if (isPlayVideo) {
                                    videoStar();
                                } else {
                                    videoStop();
                                }
                            }
                        }


                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }

                for (int i = 0; i < mViews.size(); i++) {              ////遍历显示静态图
                    BaseImageView baseImageView = mViews.get(i);
                    long startTime = baseImageView.getStartTime();
                    long endTime = baseImageView.getEndTime();
                    if (currentTime >= startTime && currentTime <= endTime) {
                        if (baseImageView.isGif()) {
                            if (currentTime != 0) {
                                int frameIndex = baseImageView.getFrameIndex();
                                mViews.get(i).setFrameIndex(frameIndex + 1);
                            }
                            baseImageView.setVisibility(View.VISIBLE);
                        } else {
                            baseImageView.setVisibility(View.VISIBLE);
                        }
                    } else {
                        baseImageView.setVisibility(View.GONE);
                    }
                }
            }
        });
    }

    private void videoStar() {
        if (currentTime >= mVideoDuration) {
            return;
        }
        stickerViews.clear();
        for (BaseImageView baseImageView : mViews) {
            baseImageView.setVisibility(View.GONE);
        }
        videoEditView.videoStar(mViews);
    }

    private void videoStop() {
        if (currentTime >= mVideoDuration) {
            return;
        }
        stickerViews.clear();
        for (BaseImageView baseImageView : mViews) {
            baseImageView.setVisibility(View.GONE);
        }
        videoEditView.videoStop(mViews);
    }


    @OnClick({R.id.ll_play_video, R.id.ll_select_time_range, R.id.ll_merge_video, R.id.ll_remove, R.id.ll_add_music, R.id.tv_sure
            , R.id.tv_cancel, R.id.ll_save, R.id.ll_speed})
    public void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.ll_play_video:
                if (!isRecord) {
                    if (isFinish) {
                        Plugin plugin = panoramaPlayer.getPlugin();
                        if (plugin instanceof VideoPlugin) {
                            ((VideoPlugin) plugin).refresh();
                            ((VideoPlugin) plugin).seekTo(0);
                        }
                    }
                    if (isPlayVideo) {
                        videoStop();
                    } else {
                        if (isFinish) {
                            playLocalVideo(path);
                        } else {
                            videoStar();
                        }
                    }
                } else {
                    if (isFinish) {
                        exoPlayer.seekTo(0);
                    }

                    if (isPlayVideo) {
                        videoStop();
                    } else {
                        if (isFinish) {
                            exoPlayer.setPlayWhenReady(true);
                        } else {
                            videoStar();
                        }
                    }
                }

                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.white));
                tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.white));
                tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.white));
                tvRemove.setTextColor(mContext.getResources().getColor(R.color.white));

                ivSelectTimeRange.setImageResource(R.drawable.icon_cut_clip);
                ivMergeVideo.setImageResource(R.drawable.icon_cut_merge);
                ivRemove.setImageResource(R.drawable.icon_cut_remove);
                ivAddMusic.setImageResource(R.drawable.icon_cut_music);
                ivSpeed.setImageResource(R.drawable.icon_cut_clip_speed);

                isFinish = false;
                break;
            case R.id.ll_select_time_range:
                if ("ll_select_time_range".equals(type)) {
                    isEdit = !isEdit;
                } else if ("ll_remove".equals(type) || "".equals(type)) {
                    isEdit = true;
                } else {
                    isEdit = !isEdit;
                }
                type = "ll_select_time_range";
                tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.white));
                tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.white));
                tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.white));
                tvRemove.setTextColor(mContext.getResources().getColor(R.color.white));
                ivMergeVideo.setImageResource(R.drawable.icon_cut_merge);
                ivRemove.setImageResource(R.drawable.icon_cut_remove);
                ivAddMusic.setImageResource(R.drawable.icon_cut_music);
                ivSpeed.setImageResource(R.drawable.icon_cut_clip_speed);
                if (isEdit) {
                    ivSelectTimeRange.setImageResource(R.drawable.icon_cut_clip_select);
                    tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                    videoEditView.showEditView();
                    if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                        videoEditView.videoPlay(mViews);
                    }
                    llCut.setVisibility(View.VISIBLE);
                    float progress = 0f;
                    if (!isRecord) {
                        progress = (float) VideoCutNewActivity.this.curTime / (float) VideoCutNewActivity.this.maxTime;
                    } else {
                        progress = (float) exoPlayer.getCurrentPosition() / (float) exoPlayer.getDuration();
                        if (progress < 0.90) {
                            progress = progress + 0.1f;
                        }
                    }
                    videoEditView.videoEditProgressView.editBarRight.setX(videoEditView.videoEditProgressView.getWidth() * progress);
                    VideoEditProgressView progressView = videoEditView.videoEditProgressView;
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            progressView.endTime = progressView.totalTime * progressView.selectdAreaView.getRight() / progressView.getMeasuredWidth();
                        }
                    });
                } else {
                    llCut.setVisibility(View.GONE);
                    ivSelectTimeRange.setImageResource(R.drawable.icon_cut_clip);
                    tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                    videoEditView.dissMissEditView();
                }
                break;
            case R.id.ll_merge_video:
                type = "ll_merge_video";
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView.videoPlay(mViews);
                }
                isEdit = false;
                llCut.setVisibility(View.GONE);
                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.white));
                tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.white));
                tvRemove.setTextColor(mContext.getResources().getColor(R.color.white));

                ivSelectTimeRange.setImageResource(R.drawable.icon_cut_clip);
                ivMergeVideo.setImageResource(R.drawable.icon_cut_merge_select);
                ivRemove.setImageResource(R.drawable.icon_cut_remove);
                ivAddMusic.setImageResource(R.drawable.icon_cut_music);
                ivSpeed.setImageResource(R.drawable.icon_cut_clip_speed);

                videoEditView.dissMissEditView();
                if (videoListPopupWindow == null) {
                    videoListPopupWindow = new VideoListPopupWindow(mContext, isRecord);
                }
                videoListPopupWindow.showPopupWindow();
                videoListPopupWindow.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.white));
                        ivMergeVideo.setImageResource(R.drawable.icon_cut_merge);
                    }
                });
                videoListPopupWindow.tvSure.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        int count = 0;
                        for (int i = 0; i < videoListPopupWindow.mAdapter.getData().size(); i++) {
                            if (videoListPopupWindow.mAdapter.getData().get(i).isCheck) {
                                count++;
                            }
                        }

                        if (count >= 2) {
                            ArrayList<String> urls = new ArrayList<>();
                            int countSelect = 1;
                            for (int i = 0; i < videoListPopupWindow.mAdapter.getData().size(); i++) {
                                for (int j = 0; j < videoListPopupWindow.mAdapter.getData().size(); j++) {
                                    LocalPbItemInfo bean = videoListPopupWindow.mAdapter.getData().get(j);
                                    if (bean.isCheck) {
                                        if (countSelect == videoListPopupWindow.mAdapter.getData().get(j).selectPosition) {
                                            urls.add(videoListPopupWindow.mAdapter.getData().get(j).file.getPath());
                                            countSelect++;
                                            break;
                                        }
                                    }
                                }
                            }


//                            mergeVideoV2(urls);
                            if (!isRecord) {
                                mergeVideoV3(urls);
                            } else {
                                mergeVideo(urls);
                            }
                            videoListPopupWindow.dismiss();
                            if (videoMergeLoadingWindow == null)
                                videoMergeLoadingWindow = new VideoMergeLoadingWindow(mContext);
                            videoMergeLoadingWindow.showPopupWindow();
                        } else {
                            ToastUtils.showLong("最少选择2段视频合并");
                        }
                    }
                });
                break;
            case R.id.ll_speed:
                isEdit = false;
                videoEditView.dissMissEditView();
                type = "ll_speed";
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView.videoPlay(mViews);
                }
                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.white));
                tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.white));
                tvRemove.setTextColor(mContext.getResources().getColor(R.color.white));

                ivSelectTimeRange.setImageResource(R.drawable.icon_cut_clip);
                ivMergeVideo.setImageResource(R.drawable.icon_cut_merge);
                ivSpeed.setImageResource(R.drawable.icon_cut_clip_speed_select);
                ivRemove.setImageResource(R.drawable.icon_cut_remove);
                ivAddMusic.setImageResource(R.drawable.icon_cut_music);


                if (videoSpeedWindow == null)
                    videoSpeedWindow = new VideoSpeedWindow(mContext);
                videoSpeedWindow.showPopupWindow();
                videoSpeedWindow.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.white));
                        ivSpeed.setImageResource(R.drawable.icon_cut_clip_speed);
                    }
                });

                videoSpeedWindow.tvSure.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        videoSpeedWindow.dismiss();
                        if (videoMergeLoadingWindow == null)
                            videoMergeLoadingWindow = new VideoMergeLoadingWindow(mContext);
                        videoMergeLoadingWindow.showPopupWindow();
                        speedVideo(videoSpeedWindow.sbSingle.getProgressFloat());
                    }
                });

                break;
            case R.id.ll_remove:
                if ("ll_remove".equals(type)) {
                    isEdit = !isEdit;
                } else if ("ll_select_time_range".equals(type) || "".equals(type)) {
                    isEdit = true;
                } else {
                    isEdit = !isEdit;
                }
                type = "ll_remove";
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView.videoPlay(mViews);
                }
                llCut.setVisibility(View.GONE);
                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.white));
                tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.white));
                tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.white));
                videoEditView.dissMissEditView();

                ivSelectTimeRange.setImageResource(R.drawable.icon_cut_clip);
                ivMergeVideo.setImageResource(R.drawable.icon_cut_merge);
                ivAddMusic.setImageResource(R.drawable.icon_cut_music);
                ivSpeed.setImageResource(R.drawable.icon_cut_clip_speed);

                if (isEdit) {
                    ivRemove.setImageResource(R.drawable.icon_cut_remove_select);
                    tvRemove.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                    videoEditView.showEditView();
                    if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                        videoEditView.videoPlay(mViews);
                    }
                    llCut.setVisibility(View.VISIBLE);

                    float progress = 0f;
                    if (!isRecord) {
                        progress = (float) VideoCutNewActivity.this.curTime / (float) VideoCutNewActivity.this.maxTime;
                    } else {
                        progress = (float) exoPlayer.getCurrentPosition() / (float) exoPlayer.getDuration();
                        if (progress < 0.90) {
                            progress = progress + 0.1f;
                        }
                    }
                    videoEditView.videoEditProgressView.editBarRight.setX(videoEditView.videoEditProgressView.getWidth() * progress);
                    VideoEditProgressView progressView = videoEditView.videoEditProgressView;
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            progressView.endTime = progressView.totalTime * progressView.selectdAreaView.getRight() / progressView.getMeasuredWidth();
                        }
                    });
                } else {
                    llCut.setVisibility(View.GONE);
                    ivRemove.setImageResource(R.drawable.icon_cut_remove);
                    tvRemove.setTextColor(mContext.getResources().getColor(R.color.white));
                    videoEditView.dissMissEditView();
                }

                break;
            case R.id.ll_add_music:
                type = "ll_add_music";
                if (isPlayVideo) {   //如果已经处于播放状态，则暂停播放
                    videoEditView.videoPlay(mViews);
                }
                isEdit = false;
                llCut.setVisibility(View.GONE);
                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.white));
                tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.white));
                tvRemove.setTextColor(mContext.getResources().getColor(R.color.white));

                ivSelectTimeRange.setImageResource(R.drawable.icon_cut_clip);
                ivMergeVideo.setImageResource(R.drawable.icon_cut_merge);
                ivRemove.setImageResource(R.drawable.icon_cut_remove);
                ivAddMusic.setImageResource(R.drawable.icon_cut_music_select);
                ivSpeed.setImageResource(R.drawable.icon_cut_clip_speed);

                videoEditView.dissMissEditView();
                if (musicListPopupWindow == null) {
                    musicListPopupWindow = new MusicListPopupWindow(mContext);
                }
                musicListPopupWindow.showPopupWindow();
                musicListPopupWindow.setOnDismissListener(new BasePopupWindow.OnDismissListener() {
                    @Override
                    public void onDismiss() {
                        tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.white));
                        ivAddMusic.setImageResource(R.drawable.icon_cut_music);
                    }
                });
                musicListPopupWindow.tvSure.setOnClickListener(new View.OnClickListener() {
                    public void onClick(View v) {
                        int count = 0;
                        Song song = null;
                        for (int i = 0; i < musicListPopupWindow.mAdapter.getData().size(); i++) {
                            if (musicListPopupWindow.mAdapter.getData().get(i).isCheck) {
                                song = musicListPopupWindow.mAdapter.getData().get(i);
                                count++;
                                break;
                            }
                        }
                        if (count == 0 || song == null) {
                            ToastUtils.showLong("请选择需要的音乐");
                            return;
                        }

                        if (musicListPopupWindow.mediaPlayer == null) {
                            cutMusic(song.path, 0);
                        } else {
                            cutMusic(song.path, musicListPopupWindow.mediaPlayer.getCurrentPosition() / 1000);
                        }

//                        mergeVideoAndMusic(path,song.path,0,1);
//                        if (videoMergeLoadingWindow == null)
//                            videoMergeLoadingWindow = new VideoMergeLoadingWindow(mContext);
//                        videoMergeLoadingWindow.showPopupWindow();
//                        musicListPopupWindow.dismiss();
                    }
                });
                break;
            case R.id.tv_sure:
                isEdit = false;
                llCut.setVisibility(View.GONE);
                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                videoEditView.dissMissEditView();
                if (videoMergeLoadingWindow == null)
                    videoMergeLoadingWindow = new VideoMergeLoadingWindow(mContext);
                videoMergeLoadingWindow.showPopupWindow();
                if ("ll_select_time_range".equals(type)) {
                    cutVideo(path, startTimeSecond, videoEditView.videoEditProgressView.endTime / 1000);
                } else if ("ll_remove".equals(type)) {
                    removeVideo(path, startTimeSecond, videoEditView.videoEditProgressView.endTime / 1000);
                }
                break;
            case R.id.tv_cancel:
                isEdit = false;
                llCut.setVisibility(View.GONE);
                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                videoEditView.dissMissEditView();
                break;
            case R.id.ll_save:
                isEdit = false;
                llCut.setVisibility(View.GONE);
                tvSelectTimeRange.setTextColor(mContext.getResources().getColor(R.color.white));
                tvAddMusic.setTextColor(mContext.getResources().getColor(R.color.white));
                videoEditView.dissMissEditView();
                tvMergeVideo.setTextColor(mContext.getResources().getColor(R.color.white));
                tvVideoSpeed.setTextColor(mContext.getResources().getColor(R.color.white));
                tvRemove.setTextColor(mContext.getResources().getColor(R.color.white));
                tvSave.setTextColor(mContext.getResources().getColor(R.color.white));
                videoStop();

                VideoMergeLoadingWindow popUp = new VideoMergeLoadingWindow(mContext);
                popUp.showPopupWindow();
                popUp.numberProgressBar.setMax(10);
                if (saveTimer != null) {
                    saveTimer.cancel();
                }
                saveTimer = new Timer();
                String outPath = "";
                if (!isRecord) {
                    outPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".MP4";
                } else {
                    outPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + "_RECORD.MP4";
                }
                FileUtils.copy(path, outPath);
                String finalOutPath = outPath;
                MediaRefresh.scanFileAsync(mContext, outPath);
                saveTimer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        if (!ActivityUtils.isActivityAlive(mContext)) {
                            return;
                        }
                        saveTotalTime++;
                        if (saveTotalTime >= 9) {
                            saveTimer.cancel();
                            try {
                                if (popUp != null) {
                                    popUp.dismiss();
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (new File(finalOutPath).exists()) {
                                ToastUtils.showShort("保存成功");
                                if (new File(path).exists()) {
                                    new File(path).delete();
                                }
//                                Intent intent = new Intent(mContext, VideoCutNewActivity.class);
//                                intent.putExtra("path", finalOutPath);
//                                intent.putExtra("canSaveFile", false);
//                                intent.putExtra("isRecord", isRecord);
//                                startActivity(intent);
//                                ActivityUtils.finishActivity(VideoCutActivity.class);

                                Intent intent = new Intent(mContext, VideoCutPicFrameSecondActivity.class);
                                intent.putExtra("path", finalOutPath);
                                intent.putExtra("canSaveFile", false);
                                intent.putExtra("isRecord", isRecord);
                                intent.putExtra("Activity",this.getClass().toString());
                                startActivity(intent);
                            } else {
                                if (saveTotalTime > 15) {
                                    ToastUtils.showShort("保存失败");
                                }
                            }
                        } else {
                            popUp.numberProgressBar.setProgress(saveTotalTime);
                        }
                    }
                }, 0, 1000);

                break;
        }
    }

    private void removeVideo(String url, float startTime, float endTime) {
        if (startTime == 0 && videoEditView.isEditBarRightEnd()) {
            if (videoMergeLoadingWindow != null)
                videoMergeLoadingWindow.dismiss();
            ToastUtils.showLong("请选择合适的时间段");
        } else if (startTime == 0) {
            EpVideo epVideo = new EpVideo(url);
            epVideo.clip(endTime, VideoUitls.getDurationForFloat(url) / 1000 / 1000);
            checkWidthAndHeight(path);
            String outPath = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
            EpEditor.OutputOption outputOption = new EpEditor.OutputOption(outPath);

            EpEditor.exec(epVideo, outputOption, new OnEditorListener() {
                @Override
                public void onSuccess() {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            cutSuccessWindow.showPopupWindow();
                        }
                    });
                    ToastUtils.showLong("操作成功");
                    path = outPath;
                }

                @Override
                public void onFailure() {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.dismiss();
                    ToastUtils.showLong("操作失败");
                }

                @Override
                public void onProgress(float progress) {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
                }
            });
        } else if (videoEditView.isEditBarRightEnd()) {
            EpVideo epVideo = new EpVideo(url);
            epVideo.clip(0, startTime);
            checkWidthAndHeight(path);
            String outPath = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
            EpEditor.OutputOption outputOption = new EpEditor.OutputOption(outPath);

            EpEditor.exec(epVideo, outputOption, new OnEditorListener() {
                @Override
                public void onSuccess() {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            cutSuccessWindow.showPopupWindow();
                        }
                    });
                    ToastUtils.showLong("操作成功");
                    path = outPath;
                }

                @Override
                public void onFailure() {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.dismiss();
                    ToastUtils.showLong("操作失败");
                }

                @Override
                public void onProgress(float progress) {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
                }
            });
        } else {
            EpVideo epVideo = new EpVideo(url);
            epVideo.clip(0, startTime);
            checkWidthAndHeight(path);
            String outPath1 = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
            EpEditor.OutputOption outputOption = new EpEditor.OutputOption(outPath1);

            EpEditor.exec(epVideo, outputOption, new OnEditorListener() {
                @Override
                public void onSuccess() {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }


                    EpVideo epVideo = new EpVideo(url);
                    epVideo.clip(endTime, VideoUitls.getDuration(url) / 1000 / 1000);
                    checkWidthAndHeight(path);
                    String outPath2 = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
                    EpEditor.OutputOption outputOption = new EpEditor.OutputOption(outPath2);

                    EpEditor.exec(epVideo, outputOption, new OnEditorListener() {
                        @Override
                        public void onSuccess() {
                            try {
                                Thread.sleep(500);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }

                            ArrayList<String> strings = new ArrayList<>();
                            strings.add(outPath1);
                            strings.add(outPath2);
                            mergeVideo(strings);
                        }

                        @Override
                        public void onFailure() {
                            if (videoMergeLoadingWindow != null)
                                videoMergeLoadingWindow.dismiss();
                            ToastUtils.showLong("操作失败");
                        }

                        @Override
                        public void onProgress(float progress) {
                            if (videoMergeLoadingWindow != null)
                                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100 / 3) + 100 / 3);
                        }
                    });
                }

                @Override
                public void onFailure() {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.dismiss();
                    ToastUtils.showLong("操作失败");
                }

                @Override
                public void onProgress(float progress) {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100 / 3));
                }
            });
        }
    }

    public void speedVideo(float speed) {
        String outPath1 = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
        if (speed >= 0 && speed < 1) {
            speed = (float) ((0.5 + speed / 2));
        } else if (speed >= 1 && speed <= 2) {
            speed = speed;
        } else if (speed > 2 && speed <= 3) {
            speed = 2 + (speed - 2) * 2;
        } else if (speed > 3 && speed <= 4) {
            speed = 4 + (speed - 3) * 6;
        } else if (speed > 4 && speed <= 5) {
            speed = 10 + (speed - 4) * 10;
        }


        LogUtil.e("speed:" + speed);
        if (speed <= 4) {
            EpEditor.changePTS(path, outPath1, speed, EpEditor.PTS.ALL, new OnEditorListener() {
                @Override
                public void onSuccess() {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (videoMergeLoadingWindow != null) {
                                videoMergeLoadingWindow.dismiss();
                            }
                            cutSuccessWindow.showPopupWindow();
                        }
                    });
                    ToastUtils.showLong("操作成功");
                    path = outPath1;
                }

                @Override
                public void onFailure() {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (videoMergeLoadingWindow != null) {
                                videoMergeLoadingWindow.dismiss();
                            }
                        }
                    });
                    ToastUtils.showLong("操作失败");
                }

                @Override
                public void onProgress(float progress) {
                    videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
                    if (progress == 1) {
                        if (videoMergeLoadingWindow != null)
                            videoMergeLoadingWindow.dismiss();
                    }
                }
            });
        } else {
            float finalSpeed = speed;
            EpEditor.changePTS(path, outPath1, 4, EpEditor.PTS.ALL, new OnEditorListener() {
                @Override
                public void onSuccess() {
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    path = outPath1;

                    String outPath2 = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
                    float newSpeed1 = finalSpeed / 4;
                    if (newSpeed1 <= 4) {
                        EpEditor.changePTS(outPath1, outPath2, newSpeed1, EpEditor.PTS.ALL, new OnEditorListener() {
                            @Override
                            public void onSuccess() {
                                try {
                                    Thread.sleep(500);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                                path = outPath2;

                                try {
                                    Thread.sleep(500);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }

                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (videoMergeLoadingWindow != null) {
                                            videoMergeLoadingWindow.dismiss();
                                        }
                                        cutSuccessWindow.showPopupWindow();
                                    }
                                });
                                ToastUtils.showLong("操作成功");
                            }

                            @Override
                            public void onFailure() {
                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        runOnUiThread(new Runnable() {
                                            @Override
                                            public void run() {
                                                if (videoMergeLoadingWindow != null) {
                                                    videoMergeLoadingWindow.dismiss();
                                                }
                                            }
                                        });
                                        ToastUtils.showLong("操作失败");
                                    }
                                });

                            }

                            @Override
                            public void onProgress(float progress) {
                                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (50 + progress / 2 * 100));
                            }
                        });
                    } else {
                        String outPath3 = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
                        float newSpeed2 = newSpeed1 / 4;
                        EpEditor.changePTS(outPath1, outPath3, 4, EpEditor.PTS.ALL, new OnEditorListener() {
                            @Override
                            public void onSuccess() {
                                try {
                                    Thread.sleep(500);
                                } catch (InterruptedException e) {
                                    e.printStackTrace();
                                }
                                path = outPath3;

                                String outPath4 = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
                                if (newSpeed2 != 1) {
                                    EpEditor.changePTS(outPath1, outPath4, newSpeed2, EpEditor.PTS.ALL, new OnEditorListener() {
                                        @Override
                                        public void onSuccess() {
                                            try {
                                                Thread.sleep(500);
                                            } catch (InterruptedException e) {
                                                e.printStackTrace();
                                            }
                                            path = outPath4;
                                            runOnUiThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    if (videoMergeLoadingWindow != null){
                                                        videoMergeLoadingWindow.dismiss();
                                                    }
                                                    cutSuccessWindow.showPopupWindow();
                                                }
                                            });
                                            ToastUtils.showLong("操作成功");
                                        }

                                        @Override
                                        public void onFailure() {
                                            runOnUiThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    if (videoMergeLoadingWindow != null) {
                                                        videoMergeLoadingWindow.dismiss();
                                                    }
                                                }
                                            });
                                            ToastUtils.showLong("操作失败");
                                        }

                                        @Override
                                        public void onProgress(float progress) {
                                            videoMergeLoadingWindow.numberProgressBar.setProgress((int) (66 + progress / 3 * 100));
                                        }
                                    });
                                } else {
                                    if (videoMergeLoadingWindow != null)
                                        videoMergeLoadingWindow.dismiss();
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            cutSuccessWindow.showPopupWindow();
                                        }
                                    });
                                    ToastUtils.showLong("操作成功");
                                }
                            }

                            @Override
                            public void onFailure() {
                                runOnUiThread(new Runnable() {
                                    @Override
                                    public void run() {
                                        if (videoMergeLoadingWindow != null) {
                                            videoMergeLoadingWindow.dismiss();
                                        }
                                    }
                                });
                                ToastUtils.showLong("操作失败");
                            }

                            @Override
                            public void onProgress(float progress) {
                                if (newSpeed1 > 4) {
                                    videoMergeLoadingWindow.numberProgressBar.setProgress((int) (33 + progress / 3 * 100));
                                } else {
                                    videoMergeLoadingWindow.numberProgressBar.setProgress((int) (50 + progress / 2 * 100));
                                }
                            }
                        });
                    }
                }

                @Override
                public void onFailure() {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (videoMergeLoadingWindow != null) {
                                videoMergeLoadingWindow.dismiss();
                            }
                        }
                    });
                    ToastUtils.showLong("操作失败");
                }

                @Override
                public void onProgress(float progress) {
                    float newSpeed1 = finalSpeed / 4;
                    if (newSpeed1 > 4) {
                        videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress / 3 * 100));
                    } else {
                        videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress / 2 * 100));
                    }
                }
            });
        }
    }


    public void speedVideoV2(float speed) {
        String outPath = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");

        String cmd = "";
        EpEditor.execCmd(cmd, 500, new OnEditorListener() {
            @Override
            public void onSuccess() {
            }

            @Override
            public void onFailure() {
            }

            @Override
            public void onProgress(float progress) {
            }
        });
    }

    public void cutMusic(String url, int startTime) {
        if (videoMergeLoadingWindow == null)
            videoMergeLoadingWindow = new VideoMergeLoadingWindow(mContext);
        videoMergeLoadingWindow.showPopupWindow();
        videoMergeLoadingWindow.numberProgressBar.setProgress(0);
        int hour = startTime / 60 / 60;
        int minute = startTime / 60;
        int second = startTime % 60;

        String hourStr = String.format("%02d", hour);
        String minuteStr = String.format("%02d", minute);
        String secondStr = String.format("%02d", second);

        String startTimeStr = hourStr + ":" + minuteStr + ":" + secondStr;
        LogUtil.e("startTimeStr:" + startTimeStr);
        String outMp3 = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".mp3";
        String videoTime = VideoUitls.getDuration(path) / 1000 / 1000 + " ";
        String cmd = "-i " + url + " -ss " + startTimeStr + " -t " + videoTime + outMp3;
        EpEditor.execCmd(cmd, 500, new OnEditorListener() {
            @Override
            public void onSuccess() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        musicListPopupWindow.dismiss();
                    }
                });
                mergeVideoAndMusic(path, outMp3, 0f, 1f);
            }

            @Override
            public void onFailure() {
                ToastUtils.showLong("操作失败");
                musicListPopupWindow.dismiss();
                if (videoMergeLoadingWindow != null)
                    videoMergeLoadingWindow.dismiss();
            }

            @Override
            public void onProgress(float progress) {
            }
        });


    }

    public void cutVideo(String url, float startTime, float endTime) {
        if (startTime == 0 && endTime == 0) {
            if (videoMergeLoadingWindow != null)
                videoMergeLoadingWindow.dismiss();
            ToastUtils.showLong("请选择开始或结束时间");
            return;
        }

        EpVideo epVideo = new EpVideo(url);
        float duration = endTime - startTime;
        epVideo.clip(startTime, duration);
        checkWidthAndHeight(path);
        LogUtil.e("mMinWidth:" + mMinWidth + ",mMinHeight:" + mMinHeight);
        String outPath = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
        EpEditor.OutputOption outputOption = new EpEditor.OutputOption(outPath);

        EpEditor.exec(epVideo, outputOption, new OnEditorListener() {
            @Override
            public void onSuccess() {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (videoMergeLoadingWindow != null)
                    videoMergeLoadingWindow.dismiss();
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        cutSuccessWindow.showPopupWindow();
                    }
                });
                ToastUtils.showLong("操作成功");
                path = outPath;
            }

            @Override
            public void onFailure() {
                LogUtil.e("操作失败");
                if (videoMergeLoadingWindow != null)
                    videoMergeLoadingWindow.dismiss();
                ToastUtils.showLong("操作失败");
            }

            @Override
            public void onProgress(float progress) {
                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
                if (progress == 1) {
                    videoMergeLoadingWindow.dismiss();
                }
            }
        });
    }


    public void mergeVideoAndMusic(String videoUrl, String audioUrl, float videoVolume, float audioVolume) {
        String outPath = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
        String cmd = "-i " + videoUrl + " -i " + audioUrl + " -c:v copy -c:a aac -shortest " + outPath;
//        EpEditor.execCmd(cmd, 5000, new OnEditorListener() {
//            @Override
//            public void onSuccess() {
//                if (new File(audioUrl).exists()) {
//                    new File(audioUrl).delete();
//                }
//                if (videoMergeLoadingWindow!=null)
//                videoMergeLoadingWindow.dismiss();
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        cutSuccessWindow.showPopupWindow();
//                    }
//                });
//                ToastUtils.showLong("操作成功");
//                path = outPath;
//            }
//
//            @Override
//            public void onFailure() {
//                if (new File(audioUrl).exists()) {
//                    new File(audioUrl).delete();
//                }
//                musicListPopupWindow.dismiss();
//                if (videoMergeLoadingWindow!=null)
//                videoMergeLoadingWindow.dismiss();
//                ToastUtils.showLong("操作失败");
//            }
//
//            @Override
//            public void onProgress(float progress) {
//                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
//                if (progress == 1) {
//                    if (videoMergeLoadingWindow!=null)
//                    videoMergeLoadingWindow.dismiss();
//                }
//            }
//        });

        EpEditor.music(videoUrl, audioUrl, outPath, videoVolume, audioVolume, new OnEditorListener() {
            @Override
            public void onSuccess() {
                if (new File(audioUrl).exists()) {
                    new File(audioUrl).delete();
                }
                if (videoMergeLoadingWindow != null)
                    videoMergeLoadingWindow.dismiss();
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        cutSuccessWindow.showPopupWindow();
                    }
                });
                ToastUtils.showLong("操作成功");
                path = outPath;
            }

            @Override
            public void onFailure() {
                if (new File(audioUrl).exists()) {
                    new File(audioUrl).delete();
                }
                musicListPopupWindow.dismiss();
                if (videoMergeLoadingWindow != null)
                    videoMergeLoadingWindow.dismiss();
                ToastUtils.showLong("操作失败");
            }

            @Override
            public void onProgress(float progress) {
                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
                if (progress == 1) {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.dismiss();
                }
            }
        });


//        String cmd = "-ss 0 -t 5 -i " + audioUrl+ " -i " + videoUrl + " -vcodec copy -acodec copy " + outPath;
//        EpEditor.execCmd(cmd,5000,new OnEditorListener(){
//            @Override
//            public void onSuccess() {
//                if (new File(audioUrl).exists()) {
//                    new File(audioUrl).delete();
//                }
//                videoMergeLoadingWindow.dismiss();
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        cutSuccessWindow.showPopupWindow();
//                    }
//                });
//                ToastUtils.showLong("操作成功");
//                path = outPath;
//            }
//
//            @Override
//            public void onFailure() {
//                if (new File(audioUrl).exists()) {
//                    new File(audioUrl).delete();
//                }
//                musicListPopupWindow.dismiss();
//                videoMergeLoadingWindow.dismiss();
//                ToastUtils.showLong("操作失败");
//            }
//
//            @Override
//            public void onProgress(float progress) {
//                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
//                if (progress == 1) {
//                    videoMergeLoadingWindow.dismiss();
//                }
//            }
//        } );
    }


    private void checkWidthAndHeight(ArrayList<String> urls) {
        mMinWidth = 10000;
        mMinHeight = 10000;
        for (int i = 0; i < urls.size(); i++) {
            final MediaMetadataRetriever mediaMetadata = new MediaMetadataRetriever();
            mediaMetadata.setDataSource(mContext, Uri.parse(urls.get(i)));
            try {
                int width = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
                int height = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
                if (width <= mMinWidth) {
                    mMinWidth = width;
                }

                if (height <= mMinHeight) {
                    mMinHeight = height;
                }

            } catch (NumberFormatException e) {
                e.printStackTrace();
            }
        }
    }

    private void checkWidthAndHeight(String urls) {
        mMinWidth = 10000;
        mMinHeight = 10000;
        final MediaMetadataRetriever mediaMetadata = new MediaMetadataRetriever();
        mediaMetadata.setDataSource(mContext, Uri.parse(urls));
        try {
            int width = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
            int height = Integer.parseInt(mediaMetadata.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
            if (width <= mMinWidth) {
                mMinWidth = width;
            }

            if (height <= mMinHeight) {
                mMinHeight = height;
            }

        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    public void mergeVideo(ArrayList<String> urls) {
        long duration = 0;
        ArrayList<EpVideo> epVideos = new ArrayList<>();
        for (int i = 0; i < urls.size(); i++) {
            EpVideo epVideo = new EpVideo(urls.get(i));
            epVideos.add(epVideo);
            duration = duration + VideoUitls.getDuration(urls.get(i));
        }
        checkWidthAndHeight(urls);
        EpEditor.OutputOption outputOption = new EpEditor.OutputOption(StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".MP4");
        outputOption.setWidth(mMinWidth);
        outputOption.setHeight(mMinHeight);
        outputOption.frameRate = 60;//输出视频帧率,默认30
        outputOption.bitRate = 10;

        String txtPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "list" + ".txt";
        if (new File(txtPath).exists()) {
            new File(txtPath).delete();
        }
        File file = new File(txtPath);
        String content = "";
        for (int i = 0; i < urls.size(); i++) {
            content += "file  " + "'" + urls.get(i) + "'\n";
        }
        FileIOUtils.writeFileFromString(file, content);
        String outPath = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
//        String outPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".MP4";
        EpEditor.execCmd("-f concat -safe 0 -i " + file.getAbsolutePath() + " -c copy " + outPath, 5000, new OnEditorListener() {
            @Override
            public void onSuccess() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        cutSuccessWindow.showPopupWindow();
                    }
                });
                ToastUtils.showLong("操作成功");
                path = outPath;
            }

            @Override
            public void onFailure() {
                if (videoMergeLoadingWindow != null)
                    videoMergeLoadingWindow.dismiss();
                ToastUtils.showLong("操作失败");
            }

            @Override
            public void onProgress(float progress) {
                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100 / 3) + 2 * 100 / 3);
                if (progress == 1) {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.dismiss();
                }
            }
        });
    }

    public void mergeVideoV2(ArrayList<String> urls) {
        long duration = 0;
        ArrayList<EpVideo> epVideos = new ArrayList<>();
        for (int i = 0; i < urls.size(); i++) {
            EpVideo epVideo = new EpVideo(urls.get(i));
            epVideos.add(epVideo);
            duration = duration + VideoUitls.getDuration(urls.get(i));
        }
        checkWidthAndHeight(urls);
        EpEditor.OutputOption outputOption = new EpEditor.OutputOption(StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".MP4");
        outputOption.setWidth(mMinWidth);
        outputOption.setHeight(mMinHeight);
        outputOption.frameRate = 60;//输出视频帧率,默认30
        outputOption.bitRate = 10;

        String txtPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "list" + ".txt";
        if (new File(txtPath).exists()) {
            new File(txtPath).delete();
        }
        File file = new File(txtPath);
        String content = "";
        for (int i = 0; i < urls.size(); i++) {
            content += "file  " + "'" + urls.get(i) + "'\n";
        }
        FileIOUtils.writeFileFromString(file, content);
        String outPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".MP4";
        EpEditor.execCmd("-f concat -safe 0 -i " + file.getAbsolutePath() + " -c copy " + outPath, 5000, new OnEditorListener() {
            @Override
            public void onSuccess() {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        cutSuccessWindow.showPopupWindow();
                    }
                });
                ToastUtils.showLong("操作成功");
                path = outPath;
            }

            @Override
            public void onFailure() {
                if (videoMergeLoadingWindow != null)
                    videoMergeLoadingWindow.dismiss();
                ToastUtils.showLong("操作失败");
            }

            @Override
            public void onProgress(float progress) {
                videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
                if (progress == 1) {
                    if (videoMergeLoadingWindow != null)
                        videoMergeLoadingWindow.dismiss();
                }
            }
        });
    }


    public void mergeVideoV3(ArrayList<String> urls) {
        ArrayList<GetAudioBean> getAudioBeanList = new ArrayList<>();
        ArrayList<NoAudioBean> noAudioBeanList = new ArrayList<>();
        for (int i = 0; i < urls.size(); i++) {
            String outPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "JustAudio" + i + ".MP3";
            GetAudioBean audioBean = new GetAudioBean();
            audioBean.path = outPath;
            audioBean.url = urls.get(i);
            getAudioBeanList.add(audioBean);

            String outPathNoAudio = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "NoAudio" + i + ".MP4";
            NoAudioBean noAudioBean = new NoAudioBean();
            noAudioBean.path = outPathNoAudio;
            noAudioBean.url = urls.get(i);
            noAudioBeanList.add(noAudioBean);
        }

        isSuccessGetAudioCount = 0;
        isSuccessGetNoAudioCount = 0;

        new Thread(new Runnable() {
            @Override
            public void run() {

                //1.先分离 每个视频的 音频轨 出来合并
                getAudioData(getAudioBeanList, noAudioBeanList);
            }
        }).start();
    }

    private void getAudioData(ArrayList<GetAudioBean> audioBean, ArrayList<NoAudioBean> noAudioBeanList) {
        Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) {
                EpEditor.demuxer(audioBean.get(isSuccessGetAudioCount).url, audioBean.get(isSuccessGetAudioCount).path, EpEditor.Format.MP3, new OnEditorListener() {
                    @Override
                    public void onSuccess() {
                        isSuccessGetAudioCount++;
                        emitter.onNext(true);
                    }

                    @Override
                    public void onFailure() {
                        ToastUtils.showShort("fail -1");
                        emitter.onNext(false);
                    }

                    @Override
                    public void onProgress(float progress) {
                    }
                });
            }
        })
                .subscribeOn(Schedulers.computation())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Boolean>() {
                    @Override
                    public void onSubscribe(Disposable d) {
                    }

                    @Override
                    public void onNext(Boolean success) {
                        if (isSuccessGetAudioCount >= audioBean.size()) {
                            ToastUtils.showShort("success -1");
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    if (videoMergeLoadingWindow != null)
                                        videoMergeLoadingWindow.numberProgressBar.setProgress(20);
                                }
                            });
                            String txtPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "list" + ".txt";
                            if (new File(txtPath).exists()) {
                                new File(txtPath).delete();
                            }
                            File file = new File(txtPath);
                            String content = "";
                            for (int i = 0; i < audioBean.size(); i++) {
                                content += "file  " + "'" + audioBean.get(i).path + "'\n";
                            }
                            FileIOUtils.writeFileFromString(file, content);
                            String mergeAudioPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "MergeAllAudio" + ".MP3";
                            EpEditor.execCmd("-f concat -safe 0 -i " + file.getAbsolutePath() + " -c copy " + mergeAudioPath, 5000, new OnEditorListener() {
                                @Override
                                public void onSuccess() {
                                    ToastUtils.showShort("success -2");
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (videoMergeLoadingWindow != null)
                                                videoMergeLoadingWindow.numberProgressBar.setProgress(40);
                                        }
                                    });
                                    for (GetAudioBean bean : audioBean) {
                                        if (new File(bean.path).exists()) {
                                            new File(bean.path).delete();
                                        }
                                    }
                                    //2.去除视频 每个音频轨
                                    getNoAudio(noAudioBeanList);
                                }

                                @Override
                                public void onFailure() {
                                    ToastUtils.showShort("fail -2");
                                    if (videoMergeLoadingWindow != null)
                                        videoMergeLoadingWindow.dismiss();
                                    for (GetAudioBean bean : audioBean) {
                                        if (new File(bean.path).exists()) {
                                            new File(bean.path).delete();
                                        }
                                    }
                                }

                                @Override
                                public void onProgress(float progress) {
                                }
                            });
                        } else {
                            getAudioData(audioBean, noAudioBeanList);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                    }

                    @Override
                    public void onComplete() {
                    }
                });


    }

    private void getNoAudio(ArrayList<NoAudioBean> noAudioBeanList) {
        Observable.create(new ObservableOnSubscribe<Boolean>() {
            @Override
            public void subscribe(ObservableEmitter<Boolean> emitter) {
                EpEditor.execCmd("-i " + noAudioBeanList.get(isSuccessGetNoAudioCount).url + " -c:v copy -an " + noAudioBeanList.get(isSuccessGetNoAudioCount).path, 500, new OnEditorListener() {
                    @Override
                    public void onSuccess() {
                        isSuccessGetNoAudioCount++;
                        emitter.onNext(true);
                    }

                    @Override
                    public void onFailure() {
                        ToastUtils.showShort("fail -3");
                        emitter.onNext(false);
                    }

                    @Override
                    public void onProgress(float progress) {
                    }
                });
            }
        })
                .subscribeOn(Schedulers.computation())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Observer<Boolean>() {
                    @Override
                    public void onSubscribe(Disposable d) {

                    }

                    @Override
                    public void onNext(Boolean success) {
                        if (isSuccessGetNoAudioCount >= noAudioBeanList.size()) {
                            ToastUtils.showShort("success -3");
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    if (videoMergeLoadingWindow != null)
                                        videoMergeLoadingWindow.numberProgressBar.setProgress(60);
                                }
                            });
                            //3.合并已去除音频轨的视频
                            String txtPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + "list" + ".txt";
                            if (new File(txtPath).exists()) {
                                new File(txtPath).delete();
                            }
                            File file = new File(txtPath);
                            String content = "";
                            for (int i = 0; i < noAudioBeanList.size(); i++) {
                                content += "file  " + "'" + noAudioBeanList.get(i).path + "'\n";
                            }
                            FileIOUtils.writeFileFromString(file, content);

                            EpEditor.execCmd("-f concat -safe 0 -i " + file.getAbsolutePath() + " -c copy " + mergeNoAudioPath, 500, new OnEditorListener() {
                                @Override
                                public void onSuccess() {
                                    //4.音频视频合并。
                                    ToastUtils.showShort("success -4");
                                    runOnUiThread(new Runnable() {
                                        @Override
                                        public void run() {
                                            if (videoMergeLoadingWindow != null)
                                                videoMergeLoadingWindow.numberProgressBar.setProgress(80);
                                        }
                                    });
                                    for (NoAudioBean bean : noAudioBeanList) {
                                        if (new File(bean.path).exists()) {
                                            new File(bean.path).delete();
                                        }
                                    }
//                                    String outPath = StorageUtil.getRootPath(mContext) + AppInfo.DOWNLOAD_PATH_VIDEO + TimeUtil.getNowDateTime() + ".MP4";
                                    String outPath = getOutTempPath(TimeUtil.getNowDateTime() + "cut.MP4");
                                    String cmd = "-i " + mergeNoAudioPath + " -i " + mergeAudioPath + " -c:v copy -c:a aac -shortest " + outPath;
//                                    EpEditor.execCmd(cmd, 5000, new OnEditorListener() {
//                                        @Override
//                                        public void onSuccess() {
//                                            if (new File(mergeNoAudioPath).exists()) {
//                                                new File(mergeNoAudioPath).delete();
//                                            }
//                                            if (new File(mergeAudioPath).exists()) {
//                                                new File(mergeAudioPath).delete();
//                                            }
//                                            ToastUtils.showLong("操作成功");
//                                            runOnUiThread(new Runnable() {
//                                                @Override
//                                                public void run() {
//                                                    if (videoMergeLoadingWindow != null) {
//                                                        videoMergeLoadingWindow.dismiss();
//                                                    }
//                                                    cutSuccessWindow.showPopupWindow();
//                                                }
//                                            });
//                                            path = outPath;
//                                        }
//
//                                        @Override
//                                        public void onFailure() {
//                                            if (new File(mergeNoAudioPath).exists()) {
//                                                new File(mergeNoAudioPath).delete();
//                                            }
//                                            if (new File(mergeAudioPath).exists()) {
//                                                new File(mergeAudioPath).delete();
//                                            }
//                                            ToastUtils.showShort("fail -5");
//                                            if (videoMergeLoadingWindow!=null)
//                                                videoMergeLoadingWindow.dismiss();
//                                        }
//
//                                        @Override
//                                        public void onProgress(float progress) {
//                                            videoMergeLoadingWindow.numberProgressBar.setProgress((int) (progress * 100));
//                                            if (progress == 1) {
//                                                if (videoMergeLoadingWindow!=null)
//                                                    videoMergeLoadingWindow.dismiss();
//                                            }
//                                        }
//                                    });

                                    EpEditor.music(mergeNoAudioPath, mergeAudioPath, outPath, 1, 1, new OnEditorListener() {
                                        @Override
                                        public void onSuccess() {
                                            if (new File(mergeNoAudioPath).exists()) {
                                                new File(mergeNoAudioPath).delete();
                                            }
                                            if (new File(mergeAudioPath).exists()) {
                                                new File(mergeAudioPath).delete();
                                            }
                                            ToastUtils.showLong("操作成功");
                                            runOnUiThread(new Runnable() {
                                                @Override
                                                public void run() {
                                                    if (videoMergeLoadingWindow != null) {
                                                        videoMergeLoadingWindow.dismiss();
                                                    }
                                                    cutSuccessWindow.showPopupWindow();
                                                }
                                            });
                                            path = outPath;
                                        }

                                        @Override
                                        public void onFailure() {
                                            if (new File(mergeNoAudioPath).exists()) {
                                                new File(mergeNoAudioPath).delete();
                                            }
                                            if (new File(mergeAudioPath).exists()) {
                                                new File(mergeAudioPath).delete();
                                            }
                                            ToastUtils.showShort("fail -5");
                                            if (videoMergeLoadingWindow != null)
                                                videoMergeLoadingWindow.dismiss();
                                        }

                                        @Override
                                        public void onProgress(float progress) {
                                        }
                                    });
                                }

                                @Override
                                public void onFailure() {
                                    for (NoAudioBean bean : noAudioBeanList) {
                                        if (new File(bean.path).exists()) {
                                            new File(bean.path).delete();
                                        }
                                    }
                                    ToastUtils.showShort("fail -4");
                                    if (videoMergeLoadingWindow != null)
                                        videoMergeLoadingWindow.dismiss();
                                }

                                @Override
                                public void onProgress(float progress) {
                                }
                            });
                        } else {
                            getNoAudio(noAudioBeanList);
                        }
                    }

                    @Override
                    public void onError(Throwable e) {
                        e.printStackTrace();
                    }

                    @Override
                    public void onComplete() {
                    }
                });
    }


    private String getOutTempPath(String str) {
        FileUtils.createOrExistsDir(StorageUtil.getRootPath(mContext) + AppInfo.TEMP_DOWNLOAD_PATH);
        String dir = StorageUtil.getRootPath(mContext) + AppInfo.TEMP_DOWNLOAD_PATH;
        File file = new File(dir);
        if (!file.exists()) {
            file.mkdir();
        }
        return dir + str;
    }
}

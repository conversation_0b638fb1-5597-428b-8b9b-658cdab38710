package com.ijoyer.camera.activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import com.blankj.utilcode.util.AppUtils;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.ijoyer.camera.base.BaseSwipeBackActivity;
import com.ijoyer.camera.widget.SwipeBackLayout;
import com.ijoyer.mobilecam.R;
import butterknife.BindView;
import butterknife.ButterKnife;
public class AboutActivity extends BaseSwipeBackActivity {
    @BindView(R.id.tv_version)
    TextView tvVersion;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    @BindView(R.id.collapsing_toolbar)
    CollapsingToolbarLayout collapsingToolbar;
    public static void start(Context context) {
        Intent intent = new Intent(context, AboutActivity.class);
        context.startActivity(intent);
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_about);
        ButterKnife.bind(this);
        setDragEdge(SwipeBackLayout.DragEdge.LEFT);
        initView();
    }
    private void initView() {
        tvVersion.setText("Version " + AppUtils.getAppVersionName());
        collapsingToolbar.setTitle(getString(R.string.ijoyer_about));
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        toolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AboutActivity.this.onBackPressed();
            }
        });
    }
    @Override
    protected void onResume() {
        super.onResume();
        AppInfo.checkLocationDialog(this);
    }
}

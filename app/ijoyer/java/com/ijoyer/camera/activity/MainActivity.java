package com.ijoyer.camera.activity;

import static android.Manifest.permission.ACCESS_FINE_LOCATION;

import android.animation.Animator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.ColorDrawable;
import android.location.LocationManager;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RadioGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.Toolbar;
import androidx.core.app.ActivityCompat;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.Presenter.PreviewPresenter;
import com.icatch.mobilecam.Presenter.RemoteMultiPbPresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.AppInfo.ConfigureInfo;
import com.icatch.mobilecam.data.SystemInfo.MWifiManager;
import com.icatch.mobilecam.ui.activity.LocalMultiPbActivity;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.utils.ClickUtils;
import com.icatch.mobilecam.utils.PermissionTools;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.ToastUtil;
import com.ijoyer.camera.Presenter.LaunchPresenter;
import com.ijoyer.camera.ui.Interface.LaunchView;
import com.ijoyer.camera.ui.PermissionPopup;
import com.ijoyer.camera.ui.adapter.CameraSlotAdapter;
import com.ijoyer.camera.utils.AnimUtil;
import com.ijoyer.mobilecam.BuildConfig;
import com.ijoyer.mobilecam.R;
import com.qmuiteam.qmui.util.QMUIDeviceHelper;
import com.tencent.bugly.crashreport.CrashReport;

import java.io.File;
import java.util.List;
import java.util.Objects;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;

public class MainActivity extends BugTagsBaseActivity implements BottomNavigationView.OnNavigationItemSelectedListener, LaunchView {
    private static final String TAG = MainActivity.class.getSimpleName();
    /**
     * 上次退出预览页面的时间，有个bug ：当在退出预览页面后快速再次进入预览界面会导致相机连接失败，
     */
    public static long lastExitPreviewActivityTime;

    //权限弹窗显示消息
    private static final int MsgShowPopupWindow = 1000;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    @BindView(R.id.navigation)
    BottomNavigationView mNavigation;
    @BindView(R.id.txt_link)
    TextView mTxtLink;
    @BindView(R.id.txt_membership)
    TextView mTxtMembership;
    @BindView(R.id.txt_find)
    TextView mTxtFind;
    @BindView(R.id.txt_version)
    TextView mTxtVersion;
    @BindView(R.id.tv_faq)
    TextView tvFaq;
    @BindView(R.id.layout_fragment)
    FrameLayout layoutFragment;
    @BindView(R.id.vpBackground)
    ViewPager vpBackground;
    private Unbinder unbinder;
    private LaunchPresenter launchPresenter;
    private Context mContext;
    public RemoteMultiPbPresenter remoteMultiPbPresenter;
    //a6 自动拍摄计数器
    private int openAutoCaptureWithA6Count = 0;
    private  PopupWindow mPermissionPopup;
    //权限弹窗显示消息
    private String reqPermissionDesc;

    //通用的handler
    private final Handler handler = new Handler(Looper.getMainLooper()){
        @Override
        public void handleMessage(@NonNull Message msg) {
            if (msg.what == MsgShowPopupWindow) {
                checkShowPermissionPopupWindows(true);//显示请求权限时，顶部的弹窗
            }
        }
    };

    private final Handler backgroundHandle = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            int curr = vpBackground.getCurrentItem() + 1;
            if (curr >= vpBackground.getChildCount()) {
                curr = 0;
            }
            vpBackground.setCurrentItem(curr, true);
            backgroundHandle.sendEmptyMessageDelayed(0, 3 * 1000);
        }
    };



    protected boolean showHomeAsUp() {
        return false;
    }

    public static void start(Context context) {
        Intent intent = new Intent(context, MainActivity.class);
        context.startActivity(intent);
    }

    protected int getLayoutId() {
        return R.layout.activity_main_ijoyer;
    }

    private void initToolBar() {
        if (toolbar == null) {
            throw new NullPointerException("toolbar can not be null");
        }
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(showHomeAsUp());
        getSupportActionBar().setElevation(0);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            toolbar.setElevation(0);
        }
    }

    protected void initView() {
        initBackground();
        animUtil = new AnimUtil();
        mNavigation.setOnNavigationItemSelectedListener(this);
        mNavigation.setLabelVisibilityMode(1);
        mTxtLink.setOnClickListener(view -> {
            if (mTxtLink.getText().toString().contains("已连接")) {
                return;
            }
            launchPresenter.navigationAction = R.id.txt_link;
            if (!MWifiManager.isWifiConnected(mContext)) {
                startPopupWindow();
            } else {
                launchPresenter.launchCameraForMain();
            }
        });
        mTxtMembership.setClickable(true);
        mTxtMembership.setOnClickListener(view -> {
            Intent intent = new Intent(mContext, WebViewActivity.class);
            intent.putExtra("url", "http://www.ijoyer.com/passport/");
            startActivity(intent);
//                openUrl("http://www.ijoyer.com/passport/");
        });

        tvFaq.setClickable(true);
        tvFaq.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mContext, WebViewActivity.class);
                intent.putExtra("url", "https://www.ijoyer.com/mobile/home/<USER>/faq");
                startActivity(intent);
            }
        });


        mTxtFind.setClickable(true);
        mTxtFind.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(mContext, WebViewActivity.class);
                intent.putExtra("url", "http://www.ijoyer.com/pictures");
                startActivity(intent);
//                openUrl("http://www.ijoyer.com/pictures");
            }
        });
        mTxtVersion.setText("v" + AppUtils.getAppVersionName());

//        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
//        ActivityManager am = (ActivityManager) PanoramaApp.getContext().getSystemService(Context.ACTIVITY_SERVICE);
//        am.getMemoryInfo(memoryInfo);
//        tvFaq.setText(JSON.toJSONString(memoryInfo));

        if (BuildConfig.DEBUG){//方便测试
            toolbar.setOnClickListener(v -> {
                openAutoCaptureWithA6Count++;
                toolbar.postDelayed(() -> openAutoCaptureWithA6Count = 0, 3000); // 3秒后重置点击次数
                if (openAutoCaptureWithA6Count == 5) {
                    PreviewPresenter.openAutoCaptureWithA6 = !PreviewPresenter.openAutoCaptureWithA6;
                    ToastUtil.showLongToast("A6s 自动拍摄" + (PreviewPresenter.openAutoCaptureWithA6 ? "开启" : "关闭") +
                            "，仅在本次启动app 有效\n再次快速点击5次切换");
                }
            });
        }
    }

    private void initBackground() {
        int[] imgs = new int[]{R.mipmap.a6p, R.mipmap.a3s};
        ImageView[] imageViews = new ImageView[imgs.length];
        for (int i = 0; i < imageViews.length; i++) {
            imageViews[i] = new ImageView(this);
            imageViews[i].setImageResource(imgs[i]);
        }
        vpBackground.setAdapter(new PagerAdapter() {
            @Override
            public int getCount() {
                return imgs.length;
            }

            @Override
            public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
                //将当前位置的view视图删除
                container.removeView(imageViews[position]);
            }

            @NonNull
            @Override
            public Object instantiateItem(@NonNull ViewGroup container, int position) {
                container.addView(imageViews[position]);
                return imageViews[position];
            }

            @Override
            public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
                return view == object;
            }
        });
//        backgroundHandle.sendEmptyMessageDelayed(0, 3 * 1000);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        int layoutId = getLayoutId();
        setContentView(layoutId);
        this.mContext = this;
        unbinder = ButterKnife.bind(this);
        initToolBar();
        initView();
        launchPresenter = new LaunchPresenter(this);
        PanoramaApp.getInstance().setLaunchPresenter(launchPresenter);
//        if (Build.VERSION.SDK_INT < 23 || PermissionTools.checkAllSelfPermission(this)) {
//            ConfigureInfo.getInstance().initCfgInfo(this.getApplicationContext());
//        } else {
//            PermissionTools.requestAllPermissions(this);
//        }
        AppLog.i(TAG, "end onCreate");
        LogUtils.file("MainActivity OnCreate()");

//        launchPresenter.launchCameraForMain(true,2);

        FileUtils.createOrExistsDir(Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH);
        List<File> files = FileUtils.listFilesInDir(Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH);
        for (int i = 0; i < files.size(); i++) {
            if (files.get(i).getName().endsWith("mp3")) {
                files.get(i).delete();
            }
        }
    }

    private void requestLocationPermission(@NonNull MenuItem item, int id) {
        if (id == R.id.navigation_album) {
            String[] permissions = new String[]{PermissionConstants.STORAGE, PermissionConstants.PHONE};
            //这里我尽量少改动，最好还是搞一个全局权限控制器
            String reqStr = "为使用相册功能，请授予存储权限、设备信息权限用于操作手机相册和连接相机";
            checkAndShowRequestPermissionDialog(reqStr, permissions, () -> goToNext(id));
        } else if (id == R.id.navigation_shot || id == R.id.navigation_settings) {
            //做个弹窗先让用户确认是不是要获取权限才行
            String reqStr = "为使用拍摄功能，请授予存储权限、麦克风权限、位置权限和设备信息权限以操作手机相册和连接相机进行拍摄";
            String[] permissions = new String[]{PermissionConstants.STORAGE, PermissionConstants.LOCATION,
                    PermissionConstants.MICROPHONE, PermissionConstants.PHONE,ACCESS_FINE_LOCATION};
            checkAndShowRequestPermissionDialog(reqStr, permissions, () -> goToNext(id));
        } else if (id == R.id.navigation_find) {
            Intent intent = new Intent(mContext, WebViewActivity.class);
            intent.putExtra("url", "http://www.ijoyer.com/pictures");
            startActivity(intent);
//            openUrl("http://www.ijoyer.com/pictures");
        } else if (id == R.id.navigation_mine) {
            Intent intent = new Intent(mContext, WebViewActivity.class);
            intent.putExtra("url", "http://www.ijoyer.com/passport/");
            startActivity(intent);
//            openUrl("http://www.ijoyer.com/passport/");
        }
    }

    private void checkAndShowRequestPermissionDialog(String reqDescStr, String[] permissions, Runnable runnable) {
        if (PermissionUtils.isGranted(permissions)) {
            if (runnable != null) {
                runnable.run();
            }
            return;
        }
        PermissionPopup popup = new PermissionPopup(mContext);
        popup.setReqDesc(reqDescStr);
        popup.showPopupWindow();
        popup.tvRight.setOnClickListener(v -> {
            popup.dismiss();
            PermissionUtils.permission(permissions)
                    .rationale((activity, shouldRequest) -> new Handler().postDelayed(() ->
                            shouldRequest.again(true), 100))
                    .callback(new PermissionUtils.FullCallback() {
                        @Override
                        public void onGranted(List<String> permissionsGranted) {
                            checkShowPermissionPopupWindows(false);
                            if (PermissionTools.checkAllSelfPermission(MainActivity.this)){
                                if (runnable != null) {
                                    runnable.run();
                                }
                            }
                        }

                        @Override
                        public void onDenied(List<String> permissionsDeniedForever, List<String> permissionsDenied) {
                            checkShowPermissionPopupWindows(false);
                            if (!permissionsDeniedForever.isEmpty()) {
                                String[] systemRequestArray = permissionsDenied.toArray(new String[permissionsDenied.size()]);
                                ActivityCompat.requestPermissions((MainActivity) mContext, systemRequestArray, PermissionTools.ALL_REQUEST_CODE);
                            }
                        }
                    }).request();

            reqPermissionDesc = reqDescStr;//缓存一下
            // 延迟 300 毫秒是为了避免出现 PopupWindow 显示然后立马消失的情况
            // 因为框架没有办法在还没有申请权限的情况下，去判断权限是否永久拒绝了，必须要在发起权限申请之后
            // 所以只能通过延迟显示 PopupWindow 来做这件事，如果 300 毫秒内权限申请没有结束，证明本次申请的权限没有永久拒绝
            handler.sendEmptyMessageDelayed(MsgShowPopupWindow, 300);
        });
    }

    //部分商店要求，需要在请求权限时，显示权限弹窗
    private void checkShowPermissionPopupWindows(boolean show) {
        if (show) {
            handler.removeMessages(MsgShowPopupWindow);
            if (isFinishing() || isDestroyed()) {
                return;
            }
            if (TextUtils.isEmpty(reqPermissionDesc)) {
                return;
            }
            ToastUtils.showShort("请授予权限");
            ViewGroup decorView = (ViewGroup) getWindow().getDecorView();
            if (mPermissionPopup == null) {
                View contentView = LayoutInflater.from(this)
                        .inflate(R.layout.permission_description_popup, decorView, false);
                mPermissionPopup = new PopupWindow(this);
                mPermissionPopup.setContentView(contentView);
                mPermissionPopup.setWidth(WindowManager.LayoutParams.MATCH_PARENT);
                mPermissionPopup.setHeight(WindowManager.LayoutParams.WRAP_CONTENT);
                mPermissionPopup.setAnimationStyle(android.R.style.Animation_Dialog);
                mPermissionPopup.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
                mPermissionPopup.setTouchable(true);
                mPermissionPopup.setOutsideTouchable(true);
            }
            TextView messageView = mPermissionPopup.getContentView().findViewById(R.id.tv_permission_description_message);
            messageView.setText(reqPermissionDesc);
            // 注意：这里的 PopupWindow 只是示例，没有监听 Activity onDestroy 来处理 PopupWindow 生命周期
            mPermissionPopup.showAtLocation(decorView, Gravity.TOP, 0, 0);
        } else {
            if (mPermissionPopup != null && mPermissionPopup.isShowing()) {
                mPermissionPopup.dismiss();
            }
            handler.removeMessages(MsgShowPopupWindow);
        }
    }

    @Override
    protected void onPause() {
        backgroundHandle.removeCallbacksAndMessages(null);
        super.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        AppLog.i(TAG, "onResume()");
        toolbar.setTitle(R.string.ijoyer_title);
        toolbar.setVisibility(View.VISIBLE);
        if (popupWindow != null) {
            popupWindow.dismiss();
        }
        //在这里解绑会导致进入相册退出来后进入拍摄崩溃
//        unbindNetwork();
        launchPresenter.registerWifiReceiver();
        launchPresenter.setView(this);
        //应VIVO 应用商店审核要求，在没有操作的时候不能自动获取ssid
        launchPresenter.initCamSlotList();
        AppInfo.checkLocationDialog(this);

        deleteTemp();
        backgroundHandle.sendEmptyMessageDelayed(0, 3 * 1000);
    }

    //解绑已经绑定的网络
    private void unbindNetwork() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        connectivityManager.bindProcessToNetwork(null); // 解绑当前进程绑定的网络
    }

    @Override
    protected void onStop() {
        super.onStop();
        launchPresenter.unregisterWifiReceiver();
    }

    @Override
    protected void onDestroy() {
        LogUtils.file("MainActivity onDestroy()");
        super.onDestroy();
        unbinder.unbind();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
//        getMenuInflater().inflate(R.menu.menu_main, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();
        switch (id) {
            case android.R.id.home:
                finishAfterTransition();
                break;
            case R.id.menuSlotList:
                checkPermission(item, id);
                break;
            case R.id.menuHelp:
                Intent intent = new Intent(mContext, WebViewActivity.class);
                intent.putExtra("url", "http://www.ijoyer.com/service");
                startActivity(intent);
//                openUrl("http://www.ijoyer.com/service");
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        if (ClickUtils.isFastDoubleClick(mNavigation)) {
            AppLog.e(TAG, "double click navigation");
            return true;
        }
        int id = item.getItemId();
        launchPresenter.navigationAction = id;
        checkPermission(item, id);

        return true;
    }

    private void checkPermission(@NonNull MenuItem item, int id) {
        if (PermissionTools.checkAllSelfPermission(this)) {
            ConfigureInfo.getInstance().initCfgInfo(this.getApplicationContext());
            goToNext(id);
        } else {
            requestLocationPermission(item, id);
        }
    }

    private void goToNext(int id) {
        switch (id) {
            case R.id.navigation_album:
                if (!MWifiManager.isWifiConnected(mContext)) {
                    Intent intent = new Intent();
                    intent.setClass(mContext, LocalMultiPbActivity.class);
                    startActivity(intent);
                } else {
                    launchPresenter.launchCameraForMain();
                }
                break;
            case R.id.navigation_shot:
                if (!MWifiManager.isWifiConnected(this)) {
                    startPopupWindow();
                } else {
                    //退出后5 秒不给连接
                    LogUtils.dTag(TAG, "进入：lastExitPreviewActivityTime=" + (System.currentTimeMillis() - lastExitPreviewActivityTime));
                    if ((System.currentTimeMillis() - lastExitPreviewActivityTime) > (1000 * 3)) {
                        launchPresenter.launchCameraForMain();
                    } else {
                        ToastUtil.showShortToast("相机正在复位，请稍候");
                        LogUtils.wTag(TAG, "距离上次退出预览界面时间太近，拦截掉");
                        LogUtils.file(TAG, "距离上次退出预览界面时间太近，拦截掉");
                    }
                }
                break;
            case R.id.navigation_settings:
                SettingsActivity.start(mContext);
                break;
            case R.id.menuSlotList:
                CamSlotListActivity.start(this);
                break;
            case R.id.navigation_find:
                Intent intent2 = new Intent(mContext, WebViewActivity.class);
                intent2.putExtra("url", "http://www.ijoyer.com/pictures");
                startActivity(intent2);
//                openUrl("http://www.ijoyer.com/pictures");
                break;
            case R.id.navigation_mine:
                Intent intent3 = new Intent(mContext, WebViewActivity.class);
                intent3.putExtra("url", "http://www.ijoyer.com/passport/");
                startActivity(intent3);
//                openUrl("http://www.ijoyer.com/passport/");
//                Intent intent = new Intent();
//                intent.setClass(this, PanoramaPlayerActivityV3.class);
//                intent.putExtra("playTag", 10);
//                startActivity(intent);
                break;
            default:
                break;
        }
    }

    @Override
    public void startPopupWindow() {
        bottomWindow(mNavigation);
        toggleBright();
    }

    private AnimUtil animUtil;
    private float bgAlpha = 1f;
    private boolean bright = false;
    private PopupWindow popupWindow;
    private static int selectedAlbum = R.id.btn_local_album;

    private void toggleBright() {
        animUtil.setValueAnimator(0.5f, 1f, 350);
        animUtil.addUpdateListener(new AnimUtil.UpdateListener() {
            @Override
            public void progress(float progress) {
                bgAlpha = bright ? progress : (1.5f - progress);
                backgroundAlpha(bgAlpha);
            }
        });
        animUtil.addEndListner(new AnimUtil.EndListener() {
            @Override
            public void endUpdate(Animator animator) {
                bright = !bright;
            }
        });
        animUtil.startAnimator();
    }

    private void backgroundAlpha(float bgAlpha) {
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.alpha = bgAlpha;
        getWindow().setAttributes(lp);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND);
    }

    private void bottomWindow(View view) {
        if (view == null || (popupWindow != null && popupWindow.isShowing())) {
            return;
        }
        int layoutId = R.layout.connect_popup_layout;
        LinearLayout layout = (LinearLayout) getLayoutInflater().inflate(layoutId, null);
        popupWindow = new PopupWindow(layout,
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT);
        popupWindow.setFocusable(true);
        popupWindow.setBackgroundDrawable(new BitmapDrawable());
        popupWindow.setAnimationStyle(R.style.popup_window);
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        popupWindow.showAtLocation(view, Gravity.LEFT | Gravity.BOTTOM, 0, -location[1]);
        popupWindow.setOnDismissListener(new PopupWindow.OnDismissListener() {
            @Override
            public void onDismiss() {
                toggleBright();
            }
        });
        if (false && R.id.navigation_album == launchPresenter.navigationAction) {
            ((RadioGroup) layout.findViewById(R.id.radioGroup)).check(selectedAlbum);
            ((RadioGroup) layout.findViewById(R.id.radioGroup)).setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(RadioGroup radioGroup, int checkedId) {
                    switch (checkedId) {
                        case R.id.btn_remote_album:
                            selectedAlbum = R.id.btn_remote_album;
                            break;
                        case R.id.btn_local_album:
                            selectedAlbum = R.id.btn_local_album;
                            break;
                    }
                }
            });
            layout.findViewById(R.id.open).setOnClickListener(view2 -> {
                switch (selectedAlbum) {
                    case R.id.btn_remote_album:
                        popupWindow.dismiss();
                        launchPresenter.navigationAction = selectedAlbum;
                        if (!MWifiManager.isWifiConnected(mContext)) {
                            startPopupWindow();
                        } else {
                            launchPresenter.launchCameraForMain();
                        }
                        break;
                    case R.id.btn_local_album:
                        Intent intent = new Intent();
                        AppLog.i(TAG, "click the local photo");
                        intent.putExtra("CUR_POSITION", 0);
                        intent.setClass(mContext, LocalMultiPbActivity.class);
                        startActivity(intent);
                        break;
                }
            });
        } else {
            layout.findViewById(R.id.connect).setOnClickListener(view1 -> {
                startActivity(new Intent(Settings.ACTION_WIFI_SETTINGS));
            });
        }
    }

    @Override
    public void refreshLinkStatus(String status) {
        if (!TextUtils.isEmpty(status) && mTxtLink != null) {
            mTxtLink.setText(status);
        }
    }

    @Override
    public void setListViewAdapter(CameraSlotAdapter cameraSlotAdapter) {
    }

    @Override
    public void setBackBtnVisibility(boolean visibility) {
    }

    @Override
    public void setLaunchLayoutVisibility(int visibility) {
    }

    @Override
    public void setLaunchSettingFrameVisibility(int visibility) {
    }

    @Override
    public void fragmentPopStackOfAll() {
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        boolean retValue = true;
        switch (requestCode) {
            case PermissionTools.ALL_REQUEST_CODE:
                AppLog.i(TAG, "ALL_REQUEST_CODE permissions.length = " + permissions.length);
                AppLog.i(TAG, "grantResults.length = " + grantResults.length);
                for (int i = 0; i < permissions.length; i++) {
                    AppLog.i(TAG, "permissions:" + permissions[i] + " grantResults:" + grantResults[i]);
                    if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                        retValue = false;
                        break;
                    }
                }
                if (retValue) {
                    ConfigureInfo.getInstance().initCfgInfo(this.getApplicationContext());
                } else {
                    AppDialog.showDialogWarnV2(this, R.string.permission_is_denied_info);
                }
                break;
            case PermissionTools.CAMERA_REQUEST_CODE:
                for (int i = 0; i < permissions.length; i++) {
                    if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                        retValue = false;
                        break;
                    }
                }
                if (retValue) {
                } else {
                    AppDialog.showDialogWarn(this, R.string.camera_permission_is_denied_info);
                }
                break;
            default:
                break;
        }
    }

    private void openUrl(String url) {
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        Uri content_url = Uri.parse(url);
        intent.setData(content_url);
        startActivity(intent);
    }

    @Override
    public void onBackPressed() {
        AlertDialog.Builder builder;
        builder = new AlertDialog.Builder(this, R.style.MyAlertDialog);
        builder.setTitle(getString(R.string.reminder));
        builder.setMessage(getString(R.string.exit_app));
        builder.setNegativeButton(android.R.string.cancel, null);
        builder.setPositiveButton(R.string.ok, (dialogInterface, i) -> finish());
        builder.show();
    }

    public static boolean isLocServiceEnable(Context context) {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        boolean gps = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
        boolean network = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
        if (gps || network) {
            return true;
        }
        return false;
    }

    private void deleteTemp() {
        FileUtils.createOrExistsDir(StorageUtil.getRootPath(getApplicationContext()) + AppInfo.TEMP_DOWNLOAD_PATH);
        String dir = StorageUtil.getRootPath(getApplicationContext()) + AppInfo.TEMP_DOWNLOAD_PATH;
        FileUtils.deleteFilesInDir(dir);
    }
}

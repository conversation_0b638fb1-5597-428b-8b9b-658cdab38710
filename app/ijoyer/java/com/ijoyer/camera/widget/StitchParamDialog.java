package com.ijoyer.camera.widget;
import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatDialog;
import com.blankj.utilcode.util.SPUtils;
import com.detu.szStitch.StitchUtils;
import com.ijoyer.mobilecam.R;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
public class StitchParamDialog extends AppCompatDialog implements View.OnClickListener {
    private Context context;
    private int layoutResID;
    private SPUtils spUtils;
    public StitchParamDialog(Context context, int layoutResID) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
        this.spUtils = SPUtils.getInstance(StitchUtils.SP);
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window dialogWindow = getWindow();
        dialogWindow.setGravity(Gravity.CENTER);
        setContentView(layoutResID);
        WindowManager windowManager = ((Activity) context).getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.width = display.getWidth() * 5 / 6;
        getWindow().setAttributes(lp);
        setCanceledOnTouchOutside(true);
        this.initView();
    }
    private void initView() {
        etWidth = findViewById(R.id.et_width);
        etHeight = findViewById(R.id.et_height);
        int width = spUtils.getInt(StitchUtils.KEY_WIDTH, StitchUtils.DEFAULT_WIDTH);
        int height = spUtils.getInt(StitchUtils.KEY_HEIGHT, StitchUtils.DEFAULT_HEIGHT);
        etWidth.setText(width + "", TextView.BufferType.EDITABLE);
        etHeight.setText(height + "", TextView.BufferType.EDITABLE);
        rgOpt = findViewById(R.id.rg_opt);
        int opt = spUtils.getInt(StitchUtils.KEY_OPT, StitchUtils.DEFAULT_OPT);
        rgOpt.check(opt == 1 ? R.id.rb_opt_on : R.id.rb_opt_off);
        rgMulti = findViewById(R.id.rg_multi);
        int multi = spUtils.getInt(StitchUtils.KEY_MULTI, StitchUtils.DEFAULT_MULTI);
        rgMulti.check(multi == 1 ? R.id.rb_multi_on : R.id.rb_multi_off);
        rgMutiluv = findViewById(R.id.rg_mutiluv);
        int mutiluv = spUtils.getInt(StitchUtils.KEY_MUTILUV, StitchUtils.DEFAULT_MUTILUV);
        rgMutiluv.check(mutiluv == 1 ? R.id.rb_mutiluv_on : R.id.rb_mutiluv_off);
        findViewById(R.id.ok).setOnClickListener(this);
    }
    EditText etWidth, etHeight;
    RadioGroup rgOpt, rgMulti, rgMutiluv;
    @Override
    public void onClick(View v) {
        if (R.id.ok == v.getId()) {
            int width = Integer.parseInt(etWidth.getEditableText().toString().trim());
            int height = Integer.parseInt(etHeight.getEditableText().toString().trim());
            if ((width >= 1 && height >= 1) && (height * 2 == width) && (width <= StitchUtils.RESOLUTION_MAX)) {
                spUtils.put(StitchUtils.KEY_WIDTH, width);
                spUtils.put(StitchUtils.KEY_HEIGHT, height);
            } else {
                AppDialog.showDialogWarn(context, "分辨率设置不合理！建议设置常规分辨率，且比例为2:1，最大为8K分辨率。");
                return;
            }
            spUtils.put(StitchUtils.KEY_OPT, R.id.rb_opt_on == rgOpt.getCheckedRadioButtonId() ? 1 : 0);
            spUtils.put(StitchUtils.KEY_MULTI, R.id.rb_multi_on == rgMulti.getCheckedRadioButtonId() ? 1 : 0);
            spUtils.put(StitchUtils.KEY_MUTILUV, R.id.rb_mutiluv_on == rgMutiluv.getCheckedRadioButtonId() ? 1 : 0);
            dismiss();
        }
    }
}

package com.ijoyer.camera.widget;
import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatDialog;
import com.blankj.utilcode.util.SPUtils;
import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.ijoyer.mobilecam.R;
import static com.ijoyer.camera.activity.SettingsActivity.WHAT_REQUEST_ALBUM;
public class SettingLogoDialog extends AppCompatDialog implements View.OnClickListener {
    private Context context;
    private int layoutResID;
    private SPUtils spUtils;
    private Handler handler;
    public SettingLogoDialog(Context context, int layoutResID) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
        this.spUtils = SPUtils.getInstance(StitchUtils.SP);
    }
    public SettingLogoDialog(Context context, int layoutResID, Handler handler) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
        this.spUtils = SPUtils.getInstance(StitchUtils.SP);
        this.handler = handler;
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window dialogWindow = getWindow();
        dialogWindow.setGravity(Gravity.CENTER);
        setContentView(layoutResID);
        WindowManager windowManager = ((Activity) context).getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.width = display.getWidth() * 5 / 6;
        getWindow().setAttributes(lp);
        setCanceledOnTouchOutside(true);
        this.initView();
    }
    RadioGroup rgLogo;
    StitchUtils.LogoType type;
    EditText etSize;
    private void initView() {
        rgLogo = findViewById(R.id.rg_logo);
        type = StitchUtils.LogoType.values()[spUtils.getInt(StitchUtils.KEY_LOGO, StitchUtils.DEFAULT_LOGO.ordinal())];
        switch (type) {
            case NONE:
                rgLogo.check(R.id.rb_none);
                break;
            case IJOYER:
                rgLogo.check(R.id.rb_ijoyer);
                break;
            case CUSTOMER:
                rgLogo.check(R.id.rb_customer);
                break;
        }
        etSize = findViewById(R.id.et_size);
        int size = spUtils.getInt(StitchUtils.KEY_LOGO_SIZE, StitchUtils.DEFAULT_LOGO_SIZE);
        etSize.setText(size + "", TextView.BufferType.EDITABLE);
        rgLogo.setOnCheckedChangeListener((radioGroup, checkedId) -> {
            if (R.id.rb_customer == checkedId) {
            } else if (R.id.rb_ijoyer == checkedId) {
                spUtils.put(StitchUtils.KEY_LOGO, StitchUtils.LogoType.IJOYER.ordinal());
            } else if (R.id.rb_none == checkedId) {
                spUtils.put(StitchUtils.KEY_LOGO, StitchUtils.LogoType.NONE.ordinal());
            }
        });
        findViewById(R.id.ok).setOnClickListener(this);
        findViewById(R.id.rb_customer).setOnClickListener(this);
    }
    @Override
    public void onClick(View v) {
        if (R.id.ok == v.getId()) {
            int size = Integer.parseInt(etSize.getEditableText().toString().trim());
            if (size <= 40 && size >= 5) {
                spUtils.put(StitchUtils.KEY_LOGO_SIZE, size);
            } else {
                AppDialog.showDialogWarn(context, "尺寸超出范围，请设置5~40之间.");
                return;
            }
            int checkedId = rgLogo.getCheckedRadioButtonId();
            switch (checkedId) {
                case R.id.rb_none:
                    spUtils.put(StitchUtils.KEY_LOGO, StitchUtils.LogoType.NONE.ordinal());
                    break;
                case R.id.rb_ijoyer:
                    spUtils.put(StitchUtils.KEY_LOGO, StitchUtils.LogoType.IJOYER.ordinal());
                    break;
                case R.id.rb_customer:
                    break;
            }
        } else if (R.id.rb_customer == v.getId()) {
            handler.sendEmptyMessage(WHAT_REQUEST_ALBUM);
        }
        dismiss();
    }
}

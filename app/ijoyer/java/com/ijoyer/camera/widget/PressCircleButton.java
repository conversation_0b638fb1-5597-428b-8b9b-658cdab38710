package com.ijoyer.camera.widget;
import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.MotionEvent;
import com.ijoyer.mobilecam.R;
public class PressCircleButton extends androidx.appcompat.widget.AppCompatButton {
    public PressCircleButton(Context context) {
        super(context);
    }
    public PressCircleButton(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
    public PressCircleButton(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                this.setBackgroundResource(R.drawable.circle_button_press);
                break;
            case MotionEvent.ACTION_UP:
                this.setBackgroundResource(R.drawable.circle_button_bg);
                break;
        }
        return super.onTouchEvent(event);
    }
}

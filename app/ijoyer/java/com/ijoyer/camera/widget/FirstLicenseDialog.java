package com.ijoyer.camera.widget;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Paint;
import android.os.Bundle;
import android.os.Handler;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDialog;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.SPUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.ui.activity.LicenseAgreementActivity;
import com.icatch.mobilecam.utils.CrashHandler;
import com.ijoyer.camera.activity.MainActivity;
import com.ijoyer.mobilecam.R;
import com.umeng.analytics.MobclickAgent;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.socialize.PlatformConfig;

public class FirstLicenseDialog extends AppCompatDialog implements View.OnClickListener {

    private Context context;
    private int layoutResID;
    public TextView tvAgreement;
    public TextView tvPrivate;


    public FirstLicenseDialog(Context context) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = R.layout.license_agreement_first;
    }

    public FirstLicenseDialog(Context context, int layoutResID) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window dialogWindow = getWindow();
        dialogWindow.setGravity(Gravity.CENTER);
        setContentView(layoutResID);
        WindowManager windowManager = ((Activity) context).getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.width = display.getWidth() * 5 / 6;
        getWindow().setAttributes(lp);
        setCanceledOnTouchOutside(false);
        this.initView();
    }

    private void initView() {
        tvAgreement = findViewById(R.id.tv_agreement);
        tvPrivate = findViewById(R.id.tv_private);

        tvAgreement.setOnClickListener(v -> {
            Intent mainIntent = new Intent(context, LicenseAgreementActivity.class);
            mainIntent.putExtra("type","Agreement");
            context.startActivity(mainIntent);
        });
        tvAgreement.setPaintFlags(Paint.UNDERLINE_TEXT_FLAG);
        tvAgreement.getPaint().setAntiAlias(true);

        tvPrivate.setOnClickListener(v -> {
            Intent mainIntent = new Intent(context, LicenseAgreementActivity.class);
            mainIntent.putExtra("type","Private");
            context.startActivity(mainIntent);
        });

        tvPrivate.setPaintFlags(Paint.UNDERLINE_TEXT_FLAG);
        tvPrivate.getPaint().setAntiAlias(true);
        findViewById(R.id.cancel).setOnClickListener(this);
        findViewById(R.id.ok).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (R.id.ok == v.getId()) {
            SPUtils.getInstance().put("isFirst", true);
            new Handler().postDelayed(() -> {
                MainActivity.start(context);
                ((AppCompatActivity) context).finish();
                UMConfigure.preInit(context,"62a2b6f588ccdf4b7e900d62"
                        ,"umeng");
                UMConfigure.init(getContext(),"62a2b6f588ccdf4b7e900d62"
                        ,"umeng",UMConfigure.DEVICE_TYPE_PHONE,"");
                // 微信设置
                PlatformConfig.setWeixin("wx4c84b24b71ce93ce","7c7c8988dcb770af02dc235d7bc56c6f");
                PlatformConfig.setWXFileProvider(AppUtils.getAppPackageName()+".fileprovider");
                // QQ设置
                PlatformConfig.setQQZone("**********","T8H27evxdjKYBORW");
                PlatformConfig.setQQFileProvider(AppUtils.getAppPackageName()+".fileprovider");
                // 新浪微博设置
                PlatformConfig.setSinaWeibo("**********","f18ce55760c615e43584614c3bf81766","http://sns.whalecloud.com");
                PlatformConfig.setSinaFileProvider(AppUtils.getAppPackageName()+".fileprovider");
                MobclickAgent.setDebugMode(false);

                CrashHandler.getInstance().init(getContext());
                UMConfigure.setLogEnabled(false);
                PanoramaApp.setIjoyerLogToFile(true);

                PanoramaApp.initBugly();
            }, 500);
            dismiss();
        } else if (R.id.cancel == v.getId()) {
            android.os.Process.killProcess(android.os.Process.myPid());
        }
    }
}

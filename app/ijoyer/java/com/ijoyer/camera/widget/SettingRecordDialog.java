package com.ijoyer.camera.widget;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatDialog;

import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

public class SettingRecordDialog extends AppCompatDialog implements View.OnClickListener {
    private Context context;
    private int layoutResID;
    private SPUtils spUtils;
    private Handler handler;
    private int typeInt;

    public SettingRecordDialog(Context context, int layoutResID) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
        this.spUtils = SPUtils.getInstance(SP);
    }

    public SettingRecordDialog(Context context, int layoutResID, Handler handler) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
        this.spUtils = SPUtils.getInstance(SP);
        this.handler = handler;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window dialogWindow = getWindow();
        dialogWindow.setGravity(Gravity.CENTER);
        setContentView(this.layoutResID);
        WindowManager windowManager = ((Activity) context).getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.width = display.getWidth() * 5 / 6;
        getWindow().setAttributes(lp);
        setCanceledOnTouchOutside(true);
        this.initView();
    }

    public static final String SP = "record_param";
    public static final String KEY_RECORD = "KEY_RECORD";
    public static final String VIDEO_SETTING = "VIDEO_SETTING";
    public static final String VIDEO_SETTING_CUSTOM = "VIDEO_SETTING_CUSTOM";

    public static final RecordType DEFAULT_RECORD = RecordType.HD;

    public enum RecordType {
        SD,
        HD,
        FHD,
        CUSTOM
    }

    private RadioGroup rgRecord;
    private ImageView ivLeft;
    private ImageView ivRight;
    private RecordType type;
    private IndicatorSeekBar sbContrastRatio;
    private boolean isChange = false;
    private LinearLayout llCustom;


    private void initView() {
        rgRecord = findViewById(R.id.rg_record);
        ivLeft = findViewById(R.id.iv_left);
        ivRight = findViewById(R.id.iv_right);
        llCustom = findViewById(R.id.ll_custom);

        rgRecord.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                switch (checkedId) {
                    case R.id.rb_sd:
                        llCustom.setVisibility(View.GONE);
                        spUtils.put(KEY_RECORD, RecordType.SD.ordinal());
                        SPUtils.getInstance().put(VIDEO_SETTING, 0);
                        break;
                    case R.id.rb_hd:
                        llCustom.setVisibility(View.GONE);
                        spUtils.put(KEY_RECORD, RecordType.HD.ordinal());
                        SPUtils.getInstance().put(VIDEO_SETTING, 1);
                        break;
                    case R.id.rb_fhd:
                        llCustom.setVisibility(View.GONE);
                        spUtils.put(KEY_RECORD, RecordType.FHD.ordinal());
                        SPUtils.getInstance().put(VIDEO_SETTING, 2);
                        break;
                    case R.id.rb_custom:
                        llCustom.setVisibility(View.VISIBLE);
                        spUtils.put(KEY_RECORD, RecordType.CUSTOM.ordinal());
                        SPUtils.getInstance().put(VIDEO_SETTING, 3);
                        break;
                }
            }

        });

        ivLeft.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int type = SPUtils.getInstance().getInt(VIDEO_SETTING, 1);
                if (type != 3) {
                    ToastUtils.showLong("请选择自定义模式");
                    return;
                }
                sbContrastRatio.setProgress(sbContrastRatio.getProgressFloat() - 1000F);
            }
        });

        ivRight.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int type = SPUtils.getInstance().getInt(VIDEO_SETTING, 1);
                if (type != 3) {
                    ToastUtils.showLong("请选择自定义模式");
                    return;
                }
                sbContrastRatio.setProgress(sbContrastRatio.getProgressFloat() + 1000F);
            }
        });

        sbContrastRatio = findViewById(R.id.sb_contrast_ratio);

        sbContrastRatio.setProgress(SPUtils.getInstance().getInt(VIDEO_SETTING_CUSTOM, 22000));
        sbContrastRatio.setOnSeekChangeListener(new OnSeekChangeListener() {
            @Override
            public void onSeeking(SeekParams seekParams) {

            }

            @Override
            public void onStartTrackingTouch(IndicatorSeekBar seekBar) {

            }

            @Override
            public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                SPUtils.getInstance().put(VIDEO_SETTING_CUSTOM, seekBar.getProgress());
            }
        });

        typeInt = SPUtils.getInstance().getInt(VIDEO_SETTING, 1);
        if (typeInt == 0) {
            llCustom.setVisibility(View.GONE);
            rgRecord.check(R.id.rb_sd);
        } else if (typeInt == 1) {
            llCustom.setVisibility(View.GONE);
            rgRecord.check(R.id.rb_hd);
        } else if (typeInt == 2) {
            llCustom.setVisibility(View.GONE);
            rgRecord.check(R.id.rb_fhd);
        } else if (typeInt == 3) {
            llCustom.setVisibility(View.VISIBLE);
            rgRecord.check(R.id.rb_custom);
        }

//        type = RecordType.values()[spUtils.getInt(KEY_RECORD, DEFAULT_RECORD.ordinal())];
//        switch (type) {
//            case SD:
//                rgRecord.check(R.id.rb_sd);
//                break;
//            case HD:
//                rgRecord.check(R.id.rb_hd);
//                break;
//            case FHD:
//                rgRecord.check(R.id.rb_fhd);
//                break;
//            case CUSTOM:
//                rgRecord.check(R.id.rb_custom);
//                break;
//        }

        findViewById(R.id.ok).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
//        int checkedId = rgRecord.getCheckedRadioButtonId();
//        switch (checkedId) {
//            case R.id.rb_sd:
//                spUtils.put(KEY_RECORD, RecordType.SD.ordinal());
//                SPUtils.getInstance().put(VIDEO_SETTING, 0);
//                break;
//            case R.id.rb_hd:
//                spUtils.put(KEY_RECORD, RecordType.HD.ordinal());
//                SPUtils.getInstance().put(VIDEO_SETTING, 1);
//                break;
//            case R.id.rb_fhd:
//                spUtils.put(KEY_RECORD, RecordType.FHD.ordinal());
//                SPUtils.getInstance().put(VIDEO_SETTING, 2);
//                break;
//            case R.id.rb_custom:
//                spUtils.put(KEY_RECORD, RecordType.CUSTOM.ordinal());
//                SPUtils.getInstance().put(VIDEO_SETTING, 3);
//                break;
//        }

        if (R.id.ok == v.getId()) {
            isChange = true;
            dismiss();
        }

    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (!isChange) {
            switch (typeInt) {
                case 0:
                    spUtils.put(KEY_RECORD, RecordType.SD.ordinal());
                    SPUtils.getInstance().put(VIDEO_SETTING, 0);
                    break;
                case 1:
                    spUtils.put(KEY_RECORD, RecordType.HD.ordinal());
                    SPUtils.getInstance().put(VIDEO_SETTING, 1);
                    break;
                case 2:
                    spUtils.put(KEY_RECORD, RecordType.FHD.ordinal());
                    SPUtils.getInstance().put(VIDEO_SETTING, 2);
                    break;
                case 3:
                    spUtils.put(KEY_RECORD, RecordType.CUSTOM.ordinal());
                    SPUtils.getInstance().put(VIDEO_SETTING, 3);
                    SPUtils.getInstance().put(VIDEO_SETTING_CUSTOM, sbContrastRatio.getProgress());
                    break;
            }
        }
    }

}

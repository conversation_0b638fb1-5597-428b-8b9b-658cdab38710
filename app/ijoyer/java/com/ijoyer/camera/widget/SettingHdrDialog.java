package com.ijoyer.camera.widget;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.view.Display;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioGroup;

import androidx.appcompat.app.AppCompatDialog;

import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.icatch.mobilecam.Application.Const;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

public class SettingHdrDialog extends AppCompatDialog implements View.OnClickListener {
    private Context context;
    private int layoutResID;
    private SPUtils spUtils;
    private Handler handler;
    private int typeInt;

    public SettingHdrDialog(Context context, int layoutResID) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
        this.spUtils = SPUtils.getInstance(SP);
    }

    public SettingHdrDialog(Context context, int layoutResID, Handler handler) {
        super(context, R.style.stitch_param_dialog);
        this.context = context;
        this.layoutResID = layoutResID;
        this.spUtils = SPUtils.getInstance(SP);
        this.handler = handler;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Window dialogWindow = getWindow();
        dialogWindow.setGravity(Gravity.CENTER);
        setContentView(this.layoutResID);
        WindowManager windowManager = ((Activity) context).getWindowManager();
        Display display = windowManager.getDefaultDisplay();
        WindowManager.LayoutParams lp = getWindow().getAttributes();
        lp.width = display.getWidth() * 5 / 6;
        getWindow().setAttributes(lp);
        setCanceledOnTouchOutside(true);
        this.initView();
    }

    public static final String SP = "record_param";
    public static final String HDR_SETTING = "HDR_SETTING";


    private RadioGroup rgRecord;
    private boolean isChange = false;


    private void initView() {
        rgRecord = findViewById(R.id.rg_record);

        rgRecord.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                switch (checkedId) {
                    case R.id.rb_sd:
                        SPUtils.getInstance().put(Const.SP.HDR_DATA, 0);
                        break;
                    case R.id.rb_hd:
                        SPUtils.getInstance().put(Const.SP.HDR_DATA, 1);
                        break;
                    case R.id.rb_fhd:
                        SPUtils.getInstance().put(Const.SP.HDR_DATA, 2);
                        break;
                }
            }

        });


        typeInt = SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0);
        if (typeInt == 0) {
            rgRecord.check(R.id.rb_sd);
        } else if (typeInt == 1) {
            rgRecord.check(R.id.rb_hd);
        } else if (typeInt == 2) {
            rgRecord.check(R.id.rb_fhd);
        }


        findViewById(R.id.ok).setOnClickListener(this);
    }

    @Override
    public void onClick(View v) {
        if (R.id.ok == v.getId()) {
            isChange = true;
            dismiss();
        }

    }

    @Override
    public void dismiss() {
        super.dismiss();
        if (!isChange) {
            switch (typeInt) {
                case 0:
                    SPUtils.getInstance().put(Const.SP.HDR_DATA, 0);
                    break;
                case 1:
                    SPUtils.getInstance().put(Const.SP.HDR_DATA, 1);
                    break;
                case 2:
                    SPUtils.getInstance().put(Const.SP.HDR_DATA, 2);
                    break;
            }
        }
    }
}

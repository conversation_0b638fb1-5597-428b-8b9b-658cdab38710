<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/round_angle_white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:gravity="center"
            android:text="@string/complementary_ground_logo_setting"
            android:textColor="@color/secondary_text"
            android:textSize="@dimen/first_title_size" />
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@color/gray" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="6dp"
            android:gravity="center_vertical"
            android:text="@string/note_complementary_ground_logo_setting"
            android:textColor="@color/red" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center">
            <RadioGroup
                android:id="@+id/rg_logo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="vertical">
                <RadioButton
                    android:id="@+id/rb_none"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:text="@string/no_logo"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />
                <RadioButton
                    android:id="@+id/rb_ijoyer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:text="IJOYER LOGO"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />
                <RadioButton
                    android:id="@+id/rb_customer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:gravity="center_vertical"
                    android:text="@string/customize_logo"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />
            </RadioGroup>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:text="@string/logo_size"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_15" />
            <EditText
                android:id="@+id/et_size"
                android:layout_width="@dimen/dp_50"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:digits="0123456789"
                android:gravity="center"
                android:inputType="number"
                android:text="100"
                tool:text="100" />
        </RelativeLayout>
        <com.ijoyer.camera.widget.PressCircleButton
            android:id="@+id/ok"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@drawable/circle_button_bg"
            android:gravity="center"
            android:text="@string/ok"
            android:textColor="@color/grayWhite"
            android:textSize="@dimen/ok_btn_text_size" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

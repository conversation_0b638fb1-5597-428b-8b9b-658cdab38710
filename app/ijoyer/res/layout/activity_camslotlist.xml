<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grayWhite"
    tools:context="com.ijoyer.camera.activity.CamSlotListActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="?attr/colorPrimary"
            app:expandedTitleTextAppearance="@style/CollapsingToolbarTitleStyle"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorPrimary"
                android:gravity="center_vertical"
                android:orientation="vertical"
                android:paddingLeft="@dimen/dp_40"
                android:paddingBottom="@dimen/dp_20">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginBottom="5dp"
                    android:paddingLeft="18dp"
                    android:paddingRight="18dp"
                    android:text="@string/ijoyer_connect_message1"
                    android:textColor="@color/grayWhite"
                    android:textSize="15sp" />
                <TextView
                    android:visibility="gone"
                    android:id="@+id/count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp_15"
                    android:layout_marginBottom="5dp"
                    android:paddingLeft="18dp"
                    android:paddingRight="18dp"
                    android:text="○ 每个手机最多可保存%1$d台相机的配对信息。点击“－”可删除配对信息。"
                    android:textColor="@color/grayWhite"
                    android:textSize="15sp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="5dp"
                    android:paddingLeft="18dp"
                    android:paddingRight="18dp"
                    android:text="@string/ijoyer_connect_message2"
                    android:textColor="@color/grayWhite"
                    android:textSize="15sp" />
            </LinearLayout>
            <!--        <androidx.appcompat.widget.Toolbar-->
            <!--            android:id="@+id/toolbar"-->
            <!--            android:layout_width="match_parent"-->
            <!--            android:layout_height="?attr/actionBarSize"-->
            <!--            android:background="@color/primary"-->
            <!--            app:popupTheme="@style/FullScreenTheme.PopupOverlay" />-->
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@android:color/transparent"
                app:layout_collapseMode="pin" />
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">
        <!--        <LinearLayout-->
        <!--            android:layout_width="match_parent"-->
        <!--            android:layout_height="match_parent"-->
        <!--            android:orientation="vertical">-->
        <view
            android:id="@+id/cam_slot_listview"
            class="com.ijoyer.camera.activity.CamSlotListActivity$NestedListView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:choiceMode="singleChoice"
            android:divider="@android:color/transparent"
            android:dividerHeight="18dp"
            android:orientation="vertical"
            android:padding="18dp" />
        <!--        </LinearLayout>-->
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>

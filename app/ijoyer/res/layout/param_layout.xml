<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/round_angle_white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/dp_5"
            android:gravity="center"
            android:text="@string/stitching_parameter_settings"
            android:textColor="@color/secondary_text"
            android:textSize="@dimen/first_title_size" />
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_margin="@dimen/dp_5"
            android:background="@color/gray" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/dp_10"
            android:text="当前算法库版本：libszstitch_android_1.3.3"
            android:textColor="@color/red"
            android:visibility="gone" />
        <RelativeLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center_vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:text="@string/image_resolution"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_14" />
            <EditText
                android:id="@+id/et_width"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/X"
                android:digits="0123456789"
                android:gravity="center"
                android:inputType="number"
                android:text="1"
                tool:text="1111" />
            <TextView
                android:id="@+id/X"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/et_height"
                android:gravity="center_vertical"
                android:text="X"
                android:textSize="@dimen/text_size_15" />
            <EditText
                android:id="@+id/et_height"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:digits="0123456789"
                android:gravity="center"
                android:inputType="number"
                android:text="2"
                tool:text="2222" />
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center_vertical">
            <!--[isopt]-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:text="@string/optimization"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_14" />
            <RadioGroup
                android:id="@+id/rg_opt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/rb_opt_on"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ON"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_14" />
                <RadioButton
                    android:id="@+id/rb_opt_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="OFF"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_14" />
            </RadioGroup>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center_vertical">
            <!-- [ismulti]-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:text="@string/remove_chromatic_aberration"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_14" />
            <RadioGroup
                android:id="@+id/rg_multi"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/rb_multi_on"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ON"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_14" />
                <RadioButton
                    android:id="@+id/rb_multi_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="OFF"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_14" />
            </RadioGroup>
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center_vertical">
            <!--[mutiluv]-->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:text="@string/strengthen_chromatic_aberration_fusion"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_14" />
            <RadioGroup
                android:id="@+id/rg_mutiluv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal">
                <RadioButton
                    android:id="@+id/rb_mutiluv_on"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ON"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_14" />
                <RadioButton
                    android:id="@+id/rb_mutiluv_off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="OFF"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_14" />
            </RadioGroup>
        </RelativeLayout>
        <com.ijoyer.camera.widget.PressCircleButton
            android:id="@+id/ok"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@drawable/circle_button_bg"
            android:gravity="center"
            android:text="@string/ok"
            android:textColor="@color/grayWhite"
            android:textSize="@dimen/ok_btn_text_size" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center"
            android:text="注意：如变更了算法默认参数而导致拼接图像失败，请及时联系算法公司！"
            android:textColor="@color/red"
            android:textSize="@dimen/text_size_10"
            android:visibility="gone" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

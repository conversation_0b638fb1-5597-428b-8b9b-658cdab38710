<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/f4ce128726cfbd39a96bb7c49f8378f"
    tools:context="com.ijoyer.camera.activity.SplashActivity">
    <TextView
        android:id="@+id/txt_ijy"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="100dp"
        android:text="艾卓悦"
        android:textColor="@color/white"
        android:textSize="40sp" />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/txt_ijy"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/dp_15"
        android:text="全景相机"
        android:textColor="@color/white"
        android:textSize="50sp" />
    <yanzhikai.textpath.SyncTextPathView
        android:id="@+id/path_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="@dimen/dp_15"
        app:duration="3000"
        app:paintStrokeColor="@color/red"
        app:paintStrokeWidth="@dimen/dp_2"
        app:pathStrokeColor="@color/white"
        app:showPainter="true"
        app:text="@string/ijoyer"
        app:textInCenter="true"
        app:textSize="45sp" />
</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/round_angle_white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center"
            android:text="@string/screen_recording_settings"
            android:textColor="@color/secondary_text"
            android:textSize="@dimen/first_title_size" />
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@color/gray" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="6dp"
            android:gravity="center_vertical"
            android:text="@string/note_screen"
            android:textColor="@color/red" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center">
            <RadioGroup
                android:id="@+id/rg_record"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:orientation="vertical">
                <RadioButton
                    android:id="@+id/rb_sd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:text="@string/fluent"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />
                <RadioButton
                    android:id="@+id/rb_hd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:text="@string/hd"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />
                <RadioButton
                    android:id="@+id/rb_fhd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:gravity="center_vertical"
                    android:text="@string/fhd"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />

                <RadioButton
                    android:id="@+id/rb_custom"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:gravity="center_vertical"
                    android:text="@string/custom"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />
                
            </RadioGroup>

        </RelativeLayout>


        <LinearLayout
            android:id="@+id/ll_custom"
            android:paddingStart="5dp"
            android:paddingEnd="5dp"
            android:gravity="center_vertical"
            android:layout_gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="50dp">

            <ImageView
                android:background="@drawable/shape_black_1000dp"
                android:id="@+id/iv_left"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_home_arrow_left_white"
                app:layout_constraintBottom_toBottomOf="@+id/nsv_bottom"
                app:layout_constraintEnd_toStartOf="@+id/iv_right" />

            <TextView
                android:layout_marginStart="10dp"
                android:text="低"
                android:textColor="@color/primary_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>


            <com.warkiz.widget.IndicatorSeekBar
                app:isb_min="500"
                app:isb_max="45000"
                app:isb_progress="22000"
                android:id="@+id/sb_contrast_ratio"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:isb_show_indicator="none"
                app:isb_show_thumb_text="false"
                app:isb_thumb_color="@color/colorPrimary"
                app:isb_thumb_size="15dp"
                app:isb_track_background_color="@color/text_line"
                app:isb_track_progress_color="@color/colorPrimary"
                app:isb_track_progress_size="6dp" />

            <TextView
                android:layout_marginStart="10dp"
                android:text="高"
                android:textColor="@color/primary_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <ImageView
                android:background="@drawable/shape_black_1000dp"
                android:id="@+id/iv_right"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginStart="10dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_home_arrow_right_white"
                app:layout_constraintBottom_toBottomOf="@+id/nsv_bottom"
                app:layout_constraintEnd_toEndOf="@+id/cl_content" />


        </LinearLayout>

        <com.ijoyer.camera.widget.PressCircleButton
            android:id="@+id/ok"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@drawable/circle_button_bg"
            android:gravity="center"
            android:text="@string/ok"
            android:textColor="@color/grayWhite"
            android:textSize="@dimen/ok_btn_text_size" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

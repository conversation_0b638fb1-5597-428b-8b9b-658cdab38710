<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:elevation="0dp"
    android:theme="@style/AppTheme.AppBarOverlay">
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        app:collapsedTitleGravity="center"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:popupTheme="@style/AppTheme.PopupOverlay"
        app:titleMargin="0dp" />
</com.google.android.material.appbar.AppBarLayout>

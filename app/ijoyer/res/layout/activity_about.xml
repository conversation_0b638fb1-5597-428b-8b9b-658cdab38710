<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/about_header_height"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/collapsing_toolbar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:contentScrim="?attr/colorPrimary"
            app:expandedTitleTextAppearance="@style/CollapsingToolbarTitleStyle"
            app:layout_scrollFlags="scroll|exitUntilCollapsed">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/about_header_height"
                android:background="@color/colorPrimary"
                android:gravity="center"
                android:orientation="vertical">
                <com.ijoyer.camera.widget.CustomRoundAngleImageView
                    android:layout_width="120dp"
                    android:layout_height="120dp"
                    android:layout_marginLeft="@dimen/dp_10"
                    android:layout_marginTop="@dimen/dp_10"
                    android:layout_marginRight="@dimen/dp_10"
                    android:layout_marginBottom="@dimen/dp_10"
                    android:scaleType="centerCrop"
                    android:src="@drawable/ilogo"
                    app:radius="@dimen/dp_15" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:gravity="center"
                    android:text="@string/ijoyer"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:visibility="gone" />
                <TextView
                    android:id="@+id/tv_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:visibility="visible" />
            </LinearLayout>
            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@android:color/transparent"
                app:layout_collapseMode="pin" />
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/about_card_margin">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_10"
                android:text="@string/introduce_and_help"
                android:textColor="@color/md_grey_600" />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_card_nopic"
                android:padding="16dp">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:autoLink="web"
                    android:text="@string/introduce"
                    android:textColor="@color/md_grey_800"
                    android:textIsSelectable="true"
                    android:textSize="14sp" />
            </RelativeLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginTop="12dp"
                android:background="@color/md_grey_500"
                android:visibility="gone" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/about_coder"
                android:textColor="@color/md_grey_600" />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                android:background="@drawable/bg_card_nopic">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/introduce_about_coder"
                    android:textColor="@color/md_grey_600"
                    android:textSize="13sp"
                    tools:ignore="HardcodedText" />
            </RelativeLayout>
            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginTop="12dp"
                android:background="@color/md_grey_500"
                android:visibility="gone" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginBottom="12dp"
                android:text="@string/open_source_lis"
                android:textColor="@color/md_grey_600"
                android:visibility="gone" />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@drawable/bg_card_nopic"
                android:padding="16dp"
                android:visibility="gone">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/open_source_libraries_list"
                    android:textColor="@color/md_grey_600"
                    android:textSize="13sp"
                    tools:ignore="HardcodedText" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout>

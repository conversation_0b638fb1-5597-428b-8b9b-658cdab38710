<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:orientation="vertical">


    <com.google.vr.sdk.widgets.video.VrVideoView
        android:id="@+id/vr_video"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="60dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/videoEditView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView
        android:id="@+id/pp_surfaceView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="60dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/videoEditView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.exoplayer2.ui.PlayerView
        android:id="@+id/video_player"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="40dp"
        android:layout_marginBottom="60dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/videoEditView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:use_controller="false" />

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginTop="40dp"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/videoEditView"
        app:layout_constraintStart_toStartOf="@+id/videoEditView">

        <LinearLayout
            android:visibility="gone"
            android:id="@+id/ll_select_time_range"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_select_time_range"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_cut_clip" />

            <TextView
                android:id="@+id/tv_select_time_range"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center"
                android:text="裁剪视频"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_merge_video"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_merge_video"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_cut_merge" />


            <TextView
                android:id="@+id/tv_merge_video"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center"
                android:text="合并视频"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_speed"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_speed"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_cut_clip_speed" />

            <TextView
                android:id="@+id/tv_video_speed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center"
                android:text="视频变速"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="visible" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_remove"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">


            <ImageView
                android:id="@+id/iv_remove"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_cut_remove" />

            <TextView
                android:id="@+id/tv_remove"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center"
                android:text="剪辑视频"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>


        <LinearLayout
            android:id="@+id/ll_add_music"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical">


            <ImageView
                android:id="@+id/iv_add_music"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_cut_music" />

            <TextView
                android:id="@+id/tv_add_music"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center"
                android:text="添加音乐"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_save"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ImageView
                android:id="@+id/iv_icon"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:adjustViewBounds="true"
                android:src="@drawable/icon_save" />

            <TextView
                android:id="@+id/tv_save"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="5dp"
                android:gravity="center"
                android:text="保存"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:visibility="visible" />

        </LinearLayout>


    </LinearLayout>


    <LinearLayout
        android:id="@+id/ll_cut"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/videoEditView"
        app:layout_constraintEnd_toEndOf="@+id/videoEditView"
        app:layout_constraintStart_toStartOf="@+id/videoEditView">

        <TextView
            android:id="@+id/tv_sure"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:padding="20dp"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:padding="20dp"
            android:text="取消"
            android:textColor="@color/white"
            android:textSize="16sp" />

    </LinearLayout>


    <com.ijoyer.camera.widget.VideoEditView
        android:id="@+id/videoEditView"
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/ll_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.95" />

</androidx.constraintlayout.widget.ConstraintLayout>
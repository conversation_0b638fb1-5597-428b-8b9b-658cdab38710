<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="10dp"
    android:layout_marginTop="5dp"
    android:background="@drawable/ijy_round_angle_view"
    android:orientation="horizontal">
    <LinearLayout
        android:id="@+id/slot_layout_add"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="3"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/index"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginEnd="@dimen/dp_5"
                android:gravity="center"
                android:textColor="@color/grayWhite"
                android:textSize="22sp"
                tool:text="1" />
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="5dp"
                android:src="@drawable/add_slot" />
            <TextView
                android:id="@+id/slot_add_camera"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp_10"
                android:gravity="center_vertical"
                android:text="@string/text_add_new_camera"
                android:textColor="#8b8b8b"
                android:textSize="15sp" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <com.icatch.mobilecam.ui.ExtendComponent.RoundAngleImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_margin="5dp"
            android:background="#1d1d1d"
            android:scaleType="centerCrop"
            app:roundHeight="15dp"
            app:roundWidth="15dp" />
    </LinearLayout>
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.ijoyer.camera.activity.MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:theme="@style/AppTheme.AppBarOverlay"
            app:elevation="0dp">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                style="@style/action_bar" />
        </com.google.android.material.appbar.AppBarLayout>

        <FrameLayout
            android:id="@+id/layout_fragment"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <androidx.viewpager.widget.ViewPager
                android:layout_width="match_parent"
                android:id="@+id/vpBackground"
                android:layout_height="match_parent"/>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left"
                android:layout_marginLeft="16dp"
                android:layout_marginTop="10dp"
                android:gravity="center"
                android:visibility="visible">

                <TextView
                    android:id="@+id/tv_faq"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/faq"
                    android:textColor="@color/selector_text_press_color"
                    android:textSize="18dp" />

                <TextView
                    android:id="@+id/txt_membership"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/member_center"
                    android:textColor="@color/selector_text_press_color"
                    android:textSize="18dp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/txt_find"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp_15"
                    android:text="@string/featured"
                    android:textColor="@color/selector_text_press_color"
                    android:textSize="18dp"
                    android:visibility="gone" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginTop="5dp"
                android:layout_marginRight="0dp"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/txt_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/dp_15"
                    android:text="Version"
                    android:textColor="@android:color/holo_blue_dark"
                    android:textSize="18dp"
                    android:visibility="gone" />
                <!--                <TextView-->
                <!--                    android:layout_width="wrap_content"-->
                <!--                    android:layout_height="wrap_content"-->
                <!--                    android:layout_margin="@dimen/dp_15"-->
                <!--                    android:text="硬件调试用"-->
                <!--                    android:textColor="@android:color/holo_red_dark"-->
                <!--                    android:textSize="18dp" />-->
            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="200dp"
                android:text="全景相机"
                android:textColor="@color/black"
                android:textSize="33dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/txt_link"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="220dp"
                android:textColor="@color/red"
                android:textSize="16dp"
                tools:text="sdfsdfsdf" />
        </FrameLayout>
        <!--        android:text="  连接相机WIFI热点 立即连接 ▶"-->
        <com.google.android.material.bottomnavigation.BottomNavigationView
            android:id="@+id/navigation"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?android:attr/windowBackground"
            android:paddingStart="18dp"
            android:paddingEnd="18dp"
            android:paddingBottom="12dp"
            app:itemIconSize="20dp"
            app:menu="@menu/navigation" />
    </LinearLayout>
</FrameLayout>

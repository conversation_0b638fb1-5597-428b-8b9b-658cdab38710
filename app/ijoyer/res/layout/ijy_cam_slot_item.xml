<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/ijy_round_angle_view"
    android:gravity="center_vertical"
    android:orientation="horizontal">
    <LinearLayout
        android:id="@+id/slot_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="3"
        android:gravity="center_vertical"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/index"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginEnd="@dimen/dp_5"
                android:gravity="center"
                android:textColor="@color/grayWhite"
                android:textSize="22sp"
                tool:text="1" />
            <ImageView
                android:id="@+id/slot_connect_sign"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="5dp"
                android:src="@drawable/ic_add_circle_24dp" />
            <TextView
                android:id="@+id/slot_connect_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginStart="@dimen/dp_10"
                android:gravity="center_vertical"
                android:text="Disconnected"
                android:textColor="@color/greyish_white"
                android:textSize="15sp" />
        </LinearLayout>
        <TextView
            android:id="@+id/slot_camera_name"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="30dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:text="WDV8000_FDG"
            android:textColor="@color/white"
            android:textSize="15sp" />
    </LinearLayout>
    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:gravity="center_vertical">
        <com.icatch.mobilecam.ui.ExtendComponent.RoundAngleImageView
            android:id="@+id/slotPhoto"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_margin="5dp"
            android:background="#1d1d1d"
            android:scaleType="centerCrop"
            app:roundHeight="15dp"
            app:roundWidth="15dp" />
        <ImageView
            android:id="@+id/delete_camera"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:background="@drawable/ic_remove_circle_red_24dp" />
    </RelativeLayout>
</LinearLayout>

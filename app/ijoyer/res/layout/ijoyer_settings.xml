<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="com.ijoyer.camera.activity.SettingsActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/FullScreenTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/FullScreenTheme.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>
    <com.ijoyer.camera.widget.CustomRoundAngleImageView
        android:layout_width="match_parent"
        android:layout_height="185dp"
        android:layout_marginLeft="5dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="5dp"
        android:layout_marginBottom="5dp"
        android:background="@color/grayWhite"
        android:scaleType="centerCrop"
        android:src="@drawable/setting_header"
        app:radius="@dimen/dp_15" />
    <Button
        android:id="@+id/help"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:text="@string/ijoyer_about"
        android:visibility="gone" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/grayWhite"
        android:gravity="center"
        android:orientation="vertical">
        <ListView
            android:id="@+id/setup_menu_listView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@color/divider"
            android:dividerHeight="0.5dp"
            android:padding="8dp"
            android:visibility="visible" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="正在开发中..."
            android:textSize="@dimen/dp_30"
            android:visibility="gone" />
    </LinearLayout>
</LinearLayout>

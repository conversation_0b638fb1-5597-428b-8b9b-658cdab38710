<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <Toolbar
        app:collapsedTitleGravity="center"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/toolbar"
        android:clipToPadding="true"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:minHeight="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:theme="@style/MyToolBar"
        android:fitsSystemWindows="true">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:singleLine="true"
            android:ellipsize="end"
            android:text="IJOYER"
            android:textSize="22sp"
            android:textColor="#FFFFFF"
            />

        <ImageView
            android:padding="5dp"
            android:id="@+id/iv_share"
            android:src="@drawable/icon_share"
            android:adjustViewBounds="true"
            android:layout_marginEnd="10dp"
            android:layout_gravity="end"
            android:layout_width="30dp"
            android:layout_height="30dp"/>

    </Toolbar>

    <androidx.constraintlayout.widget.ConstraintLayout

        android:id="@+id/cl_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:src="@drawable/ic_arrow_back_black_18dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:src="@drawable/ic_share_black_24dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>



    <com.ijoyer.camera.ui.IjoyerWebView
        app:layout_constraintTop_toBottomOf="@+id/toolbar"
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/webView"
        android:visibility="invisible"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>


    <androidx.core.widget.ContentLoadingProgressBar
        android:layout_width="wrap_content"
        app:layout_constraintTop_toTopOf="@id/webView"
        app:layout_constraintBottom_toBottomOf="@id/webView"
        app:layout_constraintStart_toStartOf="@id/webView"
        app:layout_constraintEnd_toEndOf="@id/webView"
        android:id="@+id/loadingView"
        android:progressTint="?attr/colorPrimary"
        style="?android:attr/progressBarStyleInverse"
        android:layout_height="wrap_content"/>
</androidx.constraintlayout.widget.ConstraintLayout>
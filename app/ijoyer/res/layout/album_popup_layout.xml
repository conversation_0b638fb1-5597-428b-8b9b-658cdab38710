<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="15dp"
        android:background="@drawable/popup_round_angle_view"
        android:orientation="vertical"
        android:padding="15dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="10dp"
            android:text="我的相册"
            android:textColor="@color/red"
            android:textSize="18sp"
            android:visibility="gone" />
        <RadioGroup
            android:id="@+id/radioGroup"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/dp_10"
            android:layout_marginRight="@dimen/dp_10"
            android:orientation="vertical">
            <RadioButton
                android:id="@+id/btn_remote_album"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_50"
                android:button="@null"
                android:checked="true"
                android:drawableRight="@android:drawable/btn_radio"
                android:text="  相机相册（相机设备SD卡）"
                android:textColor="@android:color/primary_text_light"
                android:textSize="@dimen/text_size_16" />
            <RadioButton
                android:id="@+id/btn_local_album"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp_50"
                android:button="@null"
                android:drawableRight="@android:drawable/btn_radio"
                android:text="  手机相册（手机的内部存储）"
                android:textColor="@android:color/primary_text_light"
                android:textSize="@dimen/text_size_16" />
        </RadioGroup>
        <com.ijoyer.camera.widget.PressCircleButton
            android:id="@+id/open"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:background="@drawable/circle_button_bg"
            android:text="打开"
            android:textColor="@color/white"
            android:textSize="18sp" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="10dp"
            android:text="注意：打开「相机相册」需先连接WIFI热点"
            android:textColor="@color/red"
            android:textSize="12dp" />
    </LinearLayout>
</LinearLayout>

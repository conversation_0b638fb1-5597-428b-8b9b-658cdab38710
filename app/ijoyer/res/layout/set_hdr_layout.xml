<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/round_angle_white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center"
            android:text="@string/hdr_title"
            android:textColor="@color/secondary_text"
            android:textSize="@dimen/first_title_size" />
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@color/gray" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="6dp"
            android:gravity="center_vertical"
            android:text="@string/hdr_tip"
            android:textColor="@color/red" />
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginRight="@dimen/dp_5"
            android:gravity="center">
            <RadioGroup
                android:id="@+id/rg_record"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/rb_hd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:text="@string/hdr_no"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />
                <RadioButton
                    android:id="@+id/rb_sd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:text="@string/hdr_str0"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />

                <RadioButton
                    android:id="@+id/rb_fhd"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:button="@null"
                    android:drawableEnd="@android:drawable/btn_radio"
                    android:gravity="center_vertical"
                    android:text="@string/hdr_yes"
                    android:textAlignment="viewStart"
                    android:textColor="@color/primary_text"
                    android:textSize="@dimen/text_size_15" />


            </RadioGroup>

        </RelativeLayout>



        <com.ijoyer.camera.widget.PressCircleButton
            android:id="@+id/ok"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_50"
            android:layout_gravity="center"
            android:layout_marginLeft="@dimen/dp_5"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginRight="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@drawable/circle_button_bg"
            android:gravity="center"
            android:text="@string/ok"
            android:textColor="@color/grayWhite"
            android:textSize="@dimen/ok_btn_text_size" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    android:background="@drawable/shape_ffffff_top"
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <LinearLayout
            android:layout_marginStart="20dp"
            android:layout_marginEnd="36dp"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:text="分享网页到"
                android:textSize="16sp"
                android:textColor="#000000"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>
        </LinearLayout>

    </LinearLayout>


    <View
        android:background="#eeeeee"
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>

    <LinearLayout
        android:gravity="center"
        android:layout_marginTop="20dp"
        android:paddingEnd="16dp"
        android:paddingStart="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_weight="1"
            android:id="@+id/ll_share_for_wx"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:background="@drawable/icon_vcoo_wx"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="微信"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


        <LinearLayout
            android:layout_weight="1"
            android:id="@+id/ll_share_for_pyq"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:background="@drawable/icon_vcoo_pyq"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="朋友圈"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


        <LinearLayout
            android:layout_weight="1"
            android:id="@+id/ll_share_for_qq"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:background="@drawable/icon_qq"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="QQ"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <LinearLayout
            android:layout_weight="1"
            android:id="@+id/ll_share_for_wb"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:background="@drawable/icon_weibo"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="微博"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <LinearLayout
            android:layout_weight="1"
            android:id="@+id/ll_share_for_qq_zone"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:padding="3dp"
                android:src="@drawable/icon_qq_zone"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="QQ空间"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


    </LinearLayout>

    <View
        android:background="#eeeeee"
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="1dp"/>

    <LinearLayout
        android:gravity="center"
        android:layout_marginTop="20dp"
        android:paddingEnd="16dp"
        android:paddingStart="16dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_weight="1"
            android:id="@+id/ll_share_link"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:padding="3dp"
                android:src="@drawable/icon_copy_link"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="复制链接"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


        <LinearLayout
            android:visibility="invisible"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:background="@drawable/icon_vcoo_pyq"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="朋友圈"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <LinearLayout
            android:visibility="invisible"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:background="@drawable/icon_qq"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="QQ"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <LinearLayout
            android:visibility="invisible"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:background="@drawable/icon_weibo"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="微博"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <LinearLayout
            android:visibility="invisible"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="vertical"
            android:layout_width="0dp"
            android:layout_height="wrap_content">

            <ImageView
                android:padding="3dp"
                android:src="@drawable/icon_qq_zone"
                android:adjustViewBounds="true"
                android:layout_width="40dp"
                android:layout_height="40dp"/>

            <TextView
                android:text="QQ空间"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>


    </LinearLayout>


    <View
        android:background="#eeeeee"
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="10dp"/>

    <TextView
        android:id="@+id/tv_cancel"
        android:textColor="@color/black"
        android:textSize="18sp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:gravity="center"
        android:text="取消"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>


</LinearLayout>
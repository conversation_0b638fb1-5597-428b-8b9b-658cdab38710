<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="15dp"
        android:background="@drawable/popup_round_angle_view"
        android:orientation="vertical"
        android:padding="15dp">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="10dp"
            android:text="@string/use_wifi"
            android:textColor="@color/red"
            android:textSize="18sp" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:layout_marginBottom="10dp"
            android:text="@string/use_wifi_message1"
            android:textColor="@android:color/primary_text_light"
            android:textSize="15sp" />
        <!--android:text="○ 相机开机后，在手机Wi-Fi列表中点击“IJOYER”开头的相机Wi-Fi进行连接，默认密码1234567890"-->
        <!--○ 相机开机后再打开相机WIFI热点，然后在手机WIFI列表中点击“IJOYER”开头的WIFI名称并输入连接密码：1234567890-->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:layout_marginBottom="10dp"
            android:text="@string/use_wifi_message2"
            android:textColor="@android:color/primary_text_light"
            android:textSize="15sp" />
        <com.ijoyer.camera.widget.PressCircleButton
            android:id="@+id/connect"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="10dp"
            android:background="@drawable/circle_button_bg"
            android:text="@string/go_to_connect"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </LinearLayout>
</LinearLayout>

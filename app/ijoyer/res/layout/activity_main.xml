<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView
        android:id="@+id/glview"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent">


    </com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView>


    <TextView
        android:id="@+id/getFov"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_marginTop="10dp"
        android:textSize="18sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/seekBarLayout"
        android:orientation="horizontal">

        <Button
            android:id="@+id/bt_viewmode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="模式选择" />

        <Button
            android:id="@+id/bt_chooseSwitch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择开关" />

        <Button
            android:id="@+id/bt_replay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="重播" />

        <Button
            android:id="@+id/bt_litedecor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="轻热点" />


        <Button
            android:id="@+id/bt_startrecord"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="开始录制" />

        <Button
            android:id="@+id/bt_stoprecord"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="结束录制" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/seekBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="20dp"
        android:orientation="horizontal">


        <TextView
            android:id="@+id/tv_curTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="@android:color/white" />

        <SeekBar
            android:id="@+id/seekBar"
            style="@style/Widget.AppCompat.ProgressBar.Horizontal"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:progress="0" />

        <TextView
            android:id="@+id/tv_totalTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00"
            android:textColor="@android:color/white" />

    </LinearLayout>


</RelativeLayout>

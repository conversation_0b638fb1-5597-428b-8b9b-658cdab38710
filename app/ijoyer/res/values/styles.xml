<resources>
    <!-- Base application theme. -->
    <style name="SplashTheme" parent="Theme.AppCompat" />
    <style name="MyAppTheme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
    </style>
    <style name="MyMultiPbTheme.NoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/black</item>
        <item name="colorPrimaryDark">@color/black</item>
    </style>
    <style name="MyPvTheme.ActionBar" parent="Theme.AppCompat.Light">
        <!--        <item name="android:windowTranslucentStatus">true</item>-->
    </style>
    <style name="IJYAppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
        <!--        <item name="colorPrimary">@android:color/transparent</item>-->
        <!--        <item name="colorPrimaryDark">@android:color/transparent</item>-->
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="CollapsingToolbarTitleStyle" parent="@android:style/TextAppearance">
        <item name="android:textColor">@android:color/transparent</item>
    </style>
    <style name="Theme.Swipe.Back" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>
    <style name="ThemeSwipeBack" parent="Theme.Swipe.Back">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimary</item>
    </style>
    <style name="action_bar">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">?attr/actionBarSize</item>
    </style>
    <style name="popup_window">
        <item name="android:windowEnterAnimation">@anim/window_out</item>
        <item name="android:windowExitAnimation">@anim/window_back</item>
    </style>
    <style name="stitch_param_dialog" parent="android:Theme.Dialog">
        <!-- 背景颜色及透明程度 -->
        <item name="android:windowBackground">@color/full_transparent</item>
        <!-- 是否半透明 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 是否没有标题 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否背景模糊 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 设置背景模糊的透明度-->
        <item name="android:backgroundDimAmount">0.6</item>
    </style>
    <style name="MyAlertDialog" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowBackground">@drawable/inner_dialog_white_bg</item>
        <!--        <item name="android:background">@color/full_transparent</item>-->
    </style>
    <style name="NoTitle_FullScreen" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="MyToolBar" parent="ThemeOverlay.AppCompat.Light">
        <item name="colorControlNormal">@android:color/white</item>
    </style>

</resources>

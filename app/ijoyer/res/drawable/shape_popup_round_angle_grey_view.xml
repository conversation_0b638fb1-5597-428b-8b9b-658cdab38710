<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    <solid android:color="@color/half_transparent_grey" />

    <!--    <gradient-->
    <!--        android:angle="270"-->
    <!--        android:centerColor="#181818"-->
    <!--        android:endColor="#070707"-->
    <!--        android:startColor="#2d2d2d" />-->

    <!--    <stroke-->
    <!--        android:width="1dp"-->
    <!--        android:color="#2d2d2d" />-->

    <corners android:radius="15dp" />

    <padding
        android:bottom="15dp"
        android:left="15dp"
        android:right="15dp"
        android:top="15dp" />

</shape>
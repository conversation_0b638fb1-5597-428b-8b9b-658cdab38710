<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="rectangle">

    <!--    <solid android:color="#177f12" />-->
    <solid android:color="@color/colorPrimaryDark" />

<!--        <gradient-->
<!--            android:angle="270"-->
<!--            android:centerColor="#181818"-->
<!--            android:endColor="#070707"-->
<!--            android:startColor="#2d2d2d" />-->

<!--        <stroke-->
<!--            android:width="1dp"-->
<!--            android:color="#2d2d2d" />-->


    <stroke
        android:width="1dp"
        android:color="@color/colorPrimary" />

    <corners android:radius="15dp" />

    <padding
        android:bottom="10dp"
        android:left="10dp"
        android:right="10dp"
        android:top="10dp" />

</shape>
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.ijoyer.mobilecam">
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STAT" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />


    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STAT" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />

    <!--如果是安卓10.0，需要后台获取连接的wifi名称则添加进程获取位置信息权限 -->
    <!--    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />-->
    <uses-feature android:name="android.hardware.usb.host" />
    <uses-permission android:name="android.permission.INJECT_EVENTS" />
    <!--    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />-->
    <!--    android.permission.MANAGE_MEDIA_PROJECTION-->
    <uses-sdk tools:overrideLibrary="com.detu.libszstitch"/>

    <queries>
        <package android:name="com.tencent.mm" />
        <package android:name="com.tencent.mobileqq" />
        <package android:name="com.sina.weibo" />
        <package android:name="com.tencent.wework" />
        <package android:name="com.qzone" />
        <package android:name="com.alibaba.android.rimet" />
        <package android:name="com.eg.android.AlipayGphone" />
        <package android:name="com.instagram.android" />
    </queries>

    <application
        android:name="com.icatch.mobilecam.Application.PanoramaApp"
        android:allowBackup="true"
        android:debuggable="false"
        android:icon="@mipmap/icon"
        android:label="@string/ijoyer2"
        android:largeHeap="true"
        android:resizeableActivity="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/IJYAppTheme"
        android:usesCleartextTraffic="true"
        tools:ignore="HardcodedDebugMode"
        tools:replace="android:label">
        <activity
            android:name="com.ijoyer.camera.activity.SplashActivity"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.ijoyer.camera.activity.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait">
            <!--                        <intent-filter>-->
            <!--                            <action android:name="android.intent.action.MAIN" />-->
            <!--                            <category android:name="android.intent.category.LAUNCHER" />-->
            <!--                        </intent-filter>-->
        </activity>
        <activity
            android:name="com.ijoyer.camera.activity.CamSlotListActivity"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait" />
        <activity
            android:name="com.ijoyer.camera.activity.SettingsActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar" />
        <activity
            android:name="com.ijoyer.camera.activity.AboutActivity"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/ThemeSwipeBack" />
        <activity
            android:name="com.icatch.mobilecam.ui.activity.PreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_preview"
            android:launchMode="singleTask"
            android:theme="@style/MyPvTheme.ActionBar" />

        <activity
            android:name="com.icatch.mobilecam.ui.activity.PreviewActivityV2"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_preview"
            android:launchMode="singleTask"
            android:theme="@style/MyPvTheme.ActionBar" />

        <activity
            android:name="com.ijoyer.camera.activity.WebViewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="IJOYER"
            android:launchMode="singleTask"
            android:theme="@style/MyAppTheme.NoActionBar"
            />

        <!--            android:theme="@style/MyPvTheme.ActionBar"-->


        <activity
            android:name="com.icatch.mobilecam.ui.activity.LocalMultiPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="手机相册"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar" />

        <activity
            android:name="com.ijoyer.camera.activity.RecordPlayActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="播放器"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/NoTitle_FullScreen" />


        <activity
            android:name="com.icatch.mobilecam.ui.activity.LocalPhotoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_local_photo"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/Theme.AppCompat" />
        <activity
            android:name="com.icatch.mobilecam.ui.activity.LocalVideoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_pb_local_video"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/NoTitle_FullScreen" />
        <activity
            android:name="com.icatch.mobilecam.ui.activity.RemoteMultiPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="相机相册"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar" />
        <activity
            android:name="com.icatch.mobilecam.ui.activity.PhotoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_photo_pb"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/Theme.AppCompat" />
        <activity
            android:name="com.icatch.mobilecam.ui.activity.VideoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_panorama_video_pb"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/Theme.AppCompat" />

        <activity
            android:name="com.ijoyer.camera.activity.VideoCutPicFrameSecondActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_panorama_video_pb"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/ActivityTranslucent" />


        <activity
            android:name="com.ijoyer.camera.activity.VideoCutActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="视频剪辑"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar"  />

        <activity
            android:name="com.ijoyer.camera.activity.VideoCutNewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="视频剪辑"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar"  />

        <activity
            android:name="com.ijoyer.camera.activity.PicCutActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="视频剪辑"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar"  />



        <activity
            android:name="com.icatch.mobilecam.ui.activity.LicenseAgreementActivity"
            android:label="@string/title_privacy_policy1"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/ThemeSwipeBack" />
        <activity
            android:name="com.detu.szStitch.StitchDemo"
            android:icon="@drawable/my_app_icon"
            android:label="Stitch"
            android:theme="@style/Theme.AppCompat.DayNight.Dialog">
            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.MAIN" />-->
            <!--                <category android:name="android.intent.category.LAUNCHER" />-->
            <!--            </intent-filter>-->
        </activity>
        <activity
            android:name="com.ijoyer.camera.activity.PanoramaPlayerActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:icon="@drawable/my_app_icon"
            android:label="Player"
            android:launchMode="singleTask"
            android:theme="@style/NoTitle_FullScreen">
            <!--
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            -->
        </activity>

        <activity
            android:name="com.ijoyer.camera.activity.PanoramaPlayerActivityV2"
            android:configChanges="keyboard|orientation|screenSize"
            android:launchMode="singleTask" />

        <activity
            android:name="com.ijoyer.camera.activity.PanoramaPlayerActivityV3"
            android:configChanges="keyboard|orientation|screenSize"
            android:launchMode="singleTask" />

        <service
            android:name="com.ijoyer.camera.service.ScreenRecordService"
            android:enabled="true"
            android:exported="false" />


        <activity
            android:name="com.ijoyer.mobilecam.wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"/>

        <activity
            android:name="com.ijoyer.mobilecam.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop"/>

        <activity
            android:name="com.tencent.tauth.AuthActivity"
            android:launchMode="singleTask"
            android:noHistory="true">
                        <intent-filter>
                            <action android:name="android.intent.action.VIEW"/>
                            <category android:name="android.intent.category.DEFAULT"/>
                            <category android:name="android.intent.category.BROWSABLE"/>
                            <data android:scheme="tencent1111639672"/>
                        </intent-filter>
        </activity>
        <activity
            android:name="com.tencent.connect.common.AssistActivity"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.icatch.mobilecam.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_path_ijy" />
        </provider>

        <meta-data
            android:name="UMENG_APPKEY"
            android:value="6247a7310059ce2bad1ab487" />
        <meta-data
            android:name="UMENG_CHANNEL"
            android:value="umeng" />

    </application>
</manifest>
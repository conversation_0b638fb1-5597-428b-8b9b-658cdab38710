apply plugin: 'com.android.application'
//apply plugin: 'com.bugtags.library.plugin'
//华为图像sdk
//apply plugin: 'com.huawei.agconnect'
apply plugin: 'com.huawei.agconnect'

android {
    compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    ndkVersion "22.0.7026061"
    defaultConfig {
        applicationId "com.ijoyer.mobilecam"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode rootProject.ext.versionCode
        versionName rootProject.ext.versionName
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        multiDexEnabled true
        ndk {
            //abiFilters 'armeabi-v7a', 'arm64Z-v8a', 'armeabi', 'x86', 'x86_64'
            //stitch lack 'arm64-v8a', 'armeabi', 'x86', 'x86_64' 设置支持的SO库架构（开发者可以根据需要，选择一个或多个平台的so）
//            abiFilters 'armeabi-v7a'
//            abiFilters 'armeabi-v7a', 'arm64-v8a'
            abiFilters 'arm64-v8a'
        }
//        splits {
//            abi {
//                enable true
//                reset()
//                include 'x86', 'x86_64', 'armeabi-v7a', 'arm64-v8a' //select ABIs to build APKs for
//                universalApk true //generate an additional APK that contains all the ABIs
//            }
//        }
//        project.ext.versionCodes = ['armeabi': 1, 'armeabi-v7a': 2, 'arm64-v8a': 3, 'mips': 5, 'mips64': 6, 'x86': 8, 'x86_64': 9]


    }
    signingConfigs {
        release {
            storeFile file('../ijoyer.jks')
            storePassword "ijoyer"
            keyAlias "ijoyer"
            keyPassword "ijoyer"
        }
    }
    buildTypes {

        release {
            signingConfig signingConfigs.release
            debuggable false
//            //混淆
            minifyEnabled true
            //Zipalign优化
            zipAlignEnabled true
            // 移除无用的resource文件
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            proguardFiles.add(file('../../proguard-gvr.txt'))
        }
        debug {
            signingConfig signingConfigs.release
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            proguardFiles.add(file('../../proguard-gvr.txt'))
        }


//        release {
//            minifyEnabled false
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
//            proguardFiles.add(file('../../proguard-gvr.txt'))
//        }
    }
    dexOptions {
        javaMaxHeapSize "2g"
    }
    packagingOptions {
//        exclude 'META-INF/NOTICE' // will not include NOTICE file
//        exclude 'META-INF/LICENSE' // will not include LICENSE file
//        exclude 'META-INF/DEPENDENCIES' // will not include LICENSE file  ·
//        exclude 'META-INF/LICENSE.txt'
//        exclude 'META-INF/NOTICE.txt'
        exclude 'generatehdr.bat'
        //优先使用拼接库的so
        pickFirst 'app/src/main/jniLibs/arm64-v8a/libijkffmpeg.so'
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['src/main/jniLibs']
            res.srcDirs += ['detu/res']
            java.srcDirs += ['detu/java']
            assets.srcDirs += ['detu/assets']
//            manifest.srcFile 'detu/DManifest.xml'
            res.srcDirs += ['ijoyer/res']
            java.srcDirs += ['ijoyer/java']
            assets.srcDirs += ['ijoyer/assets']
            manifest.srcFile 'ijoyer/IManifest.xml'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }


//    packagingOptions {
//        pickFirst  'lib/armeabi/libijkplayer.so'
//        pickFirst  'lib/armeabi-v7a/libijkplayer.so'
//        pickFirst  'lib/armeabi/libijksdl.so'
//        pickFirst  'lib/armeabi-v7a/libijksdl.so'
//        pickFirst  'lib/armeabi/libijkffmpeg.so'
//        pickFirst  'lib/armeabi-v7a/libijkffmpeg.so'
//    }
}

//bugtags {
//    //自动上传符号表功能配置，如果需要根据 build varint 配置，请参考帮助中心->符号表->Android 符号表->配置自动上传符号表
//    appKey "42c925545aacb539e96e54ab47563b7a"  //这里是你的 appKey
//    appSecret "b75eb9e6c32d5debcd2ad8f58491d3b8"    //这里是你的 appSecret，管理员在设置页可以查看
//    mappingUploadEnabled true
//
//}

repositories {
    flatDir {
        dirs 'libs'
    }
}

android {
    defaultConfig {
        manifestPlaceholders = [qqappid: "1111639672"]
    }
}

//repositories {
//    flatDir {
//        dirs "detu/libs"
//    }
//}
dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation files('libs/stickyheadergridviewlib.jar')
    implementation files('libs/photoView.jar')
    implementation files('libs/icatchbluetoothsdk.jar')
    implementation files('libs/google-api-client-1.22.0.jar')
    implementation files('libs/google-api-client-android-1.22.0.jar')
    implementation files('libs/google-api-services-tasks-v1-1.2.2-beta.jar')
    implementation files('libs/google-api-services-youtube-v3-rev180-1.22.0.jar')
    implementation files('libs/google-collections-1.0.jar')
    implementation files('libs/google-http-client-1.22.0.jar')
    implementation files('libs/google-http-client-jackson2-1.22.0.jar')
    implementation files('libs/google-oauth-client-1.22.0.jar')
    implementation files('libs/google-oauth-client-java6-1.22.0.jar')
    implementation files('libs/jackson-core-2.1.3.jar')
    implementation files('libs/google-android-oauth.jar')

    // Dependency for Google Sign-In
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.recyclerview:recyclerview:1.1.0'
    //统一所有的kotlin 版本
    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.8.10"))
    implementation "androidx.activity:activity:1.7.2"
    implementation 'com.google.android.material:material:1.2.1'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.android.gms:play-services-auth:19.0.0'

    //implementation 'com.facebook.android:facebook-android-sdk:4.27.0'
    implementation 'com.google.zxing:core:3.4.1'

    //implementation 'com.github.scribejava:scribejava-apis:2.8.1'
    implementation 'com.studioidan.httpagent:httpagent:1.0.3@aar'
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    implementation 'org.jetbrains:annotations:15.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
    implementation 'com.squareup.okhttp:okhttp:2.7.5'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'

    //implementation 'com.google.firebase:firebase-ads:19.4.0'
    testImplementation 'junit:junit:4.13.2'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation files('libs/ICatchtekVR.jar')
    implementation files('libs/ICatchtekReliant.jar')
    implementation files('libs/ICatchtekControl.jar')

    implementation 'com.tencent.bugly:crashreport:4.1.9.3'

    //implementation 'com.nostra13.universalimageloader:universal-image-loader:1.9.5'
    implementation 'com.contrarywind:Android-PickerView:4.1.9'

    //implementation 'com.google.firebase:firebase-ads:17.0.0'

    //implementation 'com.google.firebase:firebase-core:16.0.4'

    //implementation 'com.google.android.ads.consent:consent-library:1.0.6'

    //implementation 'com.google.android.gms:play-services-ads:19.4.0'

    //implementation 'com.google.firebase:firebase-ads:19.4.0'

    //implementation(name: 'android_panoplayer-release_1.4.0', ext: 'aar')

    //implementation(name: 'panoplayer-release_1.4.0', ext: 'aar')

    //implementation(name: 'weightmodule_1.4.0', ext: 'aar')

    //implementation(name: 'libszstitch_android_1.0.12', ext: 'aar')

    //implementation 'com.google.code.gson:gson:2.8.6'

    implementation project(path: ':aar:libszstitch_android')
//    implementation project(path: ':sdk')
    implementation 'com.blankj:utilcodex:1.31.1'
    implementation 'com.yanzhikai:TextPathView:0.1.3'
    implementation 'com.jakewharton:butterknife:10.2.3'
    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'
    implementation 'androidx.exifinterface:exifinterface:1.3.3'
    implementation 'com.gyf.immersionbar:immersionbar:3.0.0'

    implementation 'com.guolindev.permissionx:permissionx:1.5.0'

//    implementation('com.github.CarGuo.GSYVideoPlayer:GSYVideoPlayer:v8.1.7-release-jitpack') {
//        exclude group: 'com.google.guava'
//    }
//    implementation files('libs/pldroid-player-2.2.3.jar')

//    implementation ('xyz.doikki.android.dkplayer:dkplayer-java:3.1.2'){
//        exclude group: 'com.google.guava'
//    }
//
//    implementation ('xyz.doikki.android.dkplayer:dkplayer-ui:3.1.2'){
//        exclude group: 'com.google.guava'
//    }
//
//    implementation ('xyz.doikki.android.dkplayer:player-exo:3.1.2'){
//        exclude group: 'com.google.guava'
//    }
//
//    implementation ('xyz.doikki.android.dkplayer:player-ijk:3.1.2'){
//        exclude group: 'com.google.guava'
//    }

    implementation 'com.google.android.exoplayer:exoplayer:2.8.4'
    implementation 'com.google.android.exoplayer:exoplayer-core:2.8.4'
    implementation 'com.google.android.exoplayer:exoplayer-ui:2.8.4'
    implementation project(':joevideolib')
//    implementation project(':easyPhotos')
    implementation 'com.github.razerdp:BasePopup:2.2.3'
    api 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.4'
    implementation 'com.daimajia.numberprogressbar:library:1.4@aar'

//    implementation 'com.github.Jay-Goo:RangeSeekBar:v3.0.0'
//    implementation 'com.xw.repo:bubbleseekbar:3.20-lite'
    implementation 'com.github.warkiz.widget:indicatorseekbar:2.1.2'

    implementation 'com.google.vr:sdk-base:1.180.0'
    implementation 'com.google.vr:sdk-panowidget:1.180.0'
    implementation 'com.google.vr:sdk-videowidget:1.180.0'

    implementation 'com.github.ashqal:MD360Player4Android:2.5.0'
//    implementation 'com.bugtags.library:bugtags-lib:3.1.3'
    implementation 'com.github.deepsadness:MediaMetadataRetrieverWrapper:0.2'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.7'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    implementation 'io.reactivex:rxandroid:1.2.1'


//    implementation(name:'android_panoplayer-release_1.4.0', ext:'aar')
//    implementation(name:'panoplayer-release_1.4.0.1', ext:'aar')
    implementation(name: 'android_panoplayer-release_1.3.6', ext: 'aar')
    implementation(name: 'panoplayer-release_1.5.0', ext: 'aar')
    implementation(name: 'weightmodule_1.4.0', ext: 'aar')

//    implementation project(path: ':aar:android_panoplayer-release')
//    implementation project(path: ':aar:panoplayer-release')
//    implementation project(path: ':aar:weightmodule')

    implementation 'jp.co.cyberagent.android:gpuimage:2.1.0'

//    implementation 'com.github.microshow:RxFFmpeg:4.9.0'

    api 'org.greenrobot:eventbus:3.1.1'

    // 友盟基础组件库（所有友盟业务SDK都依赖基础组件库）
    implementation 'com.umeng.umsdk:common:9.4.4'//必选
    implementation 'com.umeng.umsdk:asms:1.4.1'//必选
    implementation 'com.umeng.umsdk:apm:1.6.4'
// 分享相关库
    implementation 'com.umeng.umsdk:share-core:7.1.6'//分享核心库，必选
    implementation 'com.umeng.umsdk:share-board:7.1.6'//分享面板功能，可选F
    implementation 'com.umeng.umsdk:share-wx:7.1.7'//微信完整版
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android-without-mta:6.8.0'//微信官方依赖库，必选
    implementation 'com.umeng.umsdk:share-qq:7.1.7'//QQ完整版
    implementation files('libs/open_sdk_3.5.7.4_r1bc9afe_lite.jar') //QQ官方依赖库，必选
    implementation 'com.squareup.okhttp3:okhttp:3.14.9'//QQ官方sdk 3.53及之后版本需要集成okhttp3.x，必选
    implementation 'com.umeng.umsdk:share-sina:7.1.7'//新浪微博完整版

    implementation 'io.github.sinaweibosdk:core:11.11.1@aar'//新浪微博官方SDK依赖库，必选
    implementation 'org.jsoup:jsoup:1.12.1'

//    implementation 'com.huawei.hms:video-editor-ui:1.3.0.300'

//    implementation 'com.github.HuanTanSheng:EasyPhotos:3.1.5'  //androidx版本，支持android 10、11，永久维护

//    //华为图像增强sdk
//    //引入基础SDK
//    implementation 'com.huawei.hms:ml-computer-vision-imagesuperresolution:3.5.0.301'
//    //引入图像超分辨率模型包
//    implementation 'com.huawei.hms:ml-computer-vision-imagesuperresolution-model:3.5.0.301'
//    implementation 'com.huawei.hms:video-editor-sdk:1.2.0.300'
    implementation 'com.huawei.hms:video-editor-ui:1.1.0.304'
    api 'com.squareup.retrofit2:retrofit:2.5.0'
    api 'com.squareup.retrofit2:converter-gson:2.5.0'
    api 'com.squareup.retrofit2:adapter-rxjava2:2.5.0'


    implementation 'com.huawei.hms:image-vision:1.0.3.303'
    implementation 'com.huawei.hms:image-vision-fallback:1.0.3.303'

    implementation 'com.huawei.hms:image-render:1.0.3.304'
    implementation 'com.huawei.hms:video-editor-recorder:1.0.0.301'

//    implementation 'com.zhouyou:signseekbar:1.0.6'
    implementation 'com.github.SheHuan:NiceImageView:1.0.5'

    implementation 'com.quickbirdstudios:opencv:4.5.3'

    api 'com.alipay.sdk:alipaysdk-android:+@aar'
//    api 'com.tencent.mm.opensdk:wechat-sdk-android:+'
    implementation 'com.github.wendux:DSBridge-Android:v3.0.0'
    implementation 'com.gzsll.jsbridge:library:1.1.0'

    implementation 'com.github.lzyzsd:jsbridge:1.0.4'

    implementation 'com.kyleduo.switchbutton:library:2.1.0'
    implementation 'io.github.lucksiege:pictureselector:v3.11.2'
    implementation "com.kongzue.dialogx:DialogX:0.0.49"
    implementation project(':third-party:qmui_part')
    implementation 'com.tencent:mmkv:1.3.7'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'

    //https://github.com/iqiyi/xCrash/blob/master/README.zh-CN.md
    //xCrash 能为安卓 app 提供捕获 java 崩溃，native 崩溃和 ANR 的能力。不需要 root 权限或任何系统权限。
    implementation 'com.iqiyi.xcrash:xcrash-android-lib:3.0.0'

    implementation 'com.github.MasayukiSuda:Mp4Composer-android:v0.4.1'
    implementation 'com.googlecode.mp4parser:isoparser:1.1.22'
}


apply plugin: 'com.google.gms.google-services'
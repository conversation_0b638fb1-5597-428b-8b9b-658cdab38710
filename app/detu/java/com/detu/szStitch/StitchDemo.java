package com.detu.szStitch;
import android.content.Context;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.exifinterface.media.ExifInterface;
import com.blankj.utilcode.util.Utils;
import com.detu.libszstitch.SzStitch;
import com.ijoyer.mobilecam.R;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
public class StitchDemo extends AppCompatActivity {
    
    
    
    
    
    
    private static final String TAG = StitchDemo.class.getSimpleName();
    private Context mContext;
    private static final String accessKey = "ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO/GJcaKw==";
    private static final String L_A = "L_A.jpeg";
    private static final String L_B = "L_B.jpeg";
    private static final String L_C = "L_C.jpeg";
    private static final String L_D = "L_D.jpeg";
    private static final String LOGO = "logo.jpg";
    private static final String CACHE_PANO = "pano.jpg"; 
    private String cali_str = null;
    class MyHandler extends Handler {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
        }
    }
    private MyHandler handler = new MyHandler();
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.szstitch_activity_main);
        Utils.init(getApplication());
        this.mContext = this;
        findViewById(R.id.stitch).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                long start = System.currentTimeMillis();
                
                cali_str = "GiWY0GwbrHij2CQzLsNtL1jvof5TQVBdKURDa7ha9dtqBqM8fCBWAgKf/Dv9LQNnSrr1CE5bzIsJmO3fHNqy7jw5B9qs7BolEoZDCL7aMtGeKWkqaz4qzqMLrXuXT8akPwtQmXQUYVIGXgRNkRu8kEDYtzy1QOBIj5gEZDVyjMJo+d9e73PPbcz1qsvufAljUV0+IYJxm0wkPntLG7PEmSnlGCVZk88Cn2iMzMqD1iV2/LoN1fZb7KsylqBTik5cBMoYH/jq5po9h88JHF8K7pE0VyCPsgjSc6AzYDNeNidk9WQ3jtLszRfOGbfzl7tm4D3z/rxJGw+yMIy6Y5hAKlpYO+FpFpjl3JnmyaFM4h9HDyqmaSb2dNPJFFwsL9/uMncl9WUHgq63nWIuX6xRotY8TEpz73yrYbsA5eplU8NWgvuW0Eg4CbPhe2wFQAEdxPgpTmoKtuguYKOTkaTsSkAVZ4zfaA5kJ+/A7B+kHA+kaSoNm3Tx7lWOsv6DzpU2eZER+y9HNizaDqtvW6wObJ3oxeRXpVHc+ZRPVKh/AdVgah53+zu4waAaN2wD4Y5ZxhkTE6oO5fvhJPmgc1PLZRC2Hq1XS+QiJ26DpbRlReCVaa5EabZEzjidUPeJ3q1oael4PgD4ThtPwb4XJOEiDUghIxVF8P842I0kYrfTZroklhdmECQaoZT2U84BPtjb7RGt1/9WIh46ZYrkxCRooIt9i3dCXVIowTyuUgSA+Gsq1OP3W5UDPB9daebkV5NNZN08I+U6ye6tF0U9ny9OpiMpQ7LVvA45WTmRxqId0BNPpVtc+dQcXk6DC3oSCNm2P1kII1KFBGMxaaOjArytIQdYG+ngYvS5qDxTp03KZwZTgknKE4fHFEDKNbD/T0gJe9IY83wuWi/iZfMEydMOF/aXHSxQE/9PA1BCF3e0jswfMF0p1ty2IwANI+0JRX/geram/kU4druSkiq9WprrfxrMkxOeSc6H51n7QbYGdewdsUCDHGfLjXnQreSZaJSh";
                try {
                    if (0 == stitchExec(
                            getApplication().getAssets().open(L_A),
                            getApplication().getAssets().open(L_D),
                            getApplication().getAssets().open(L_C),
                            getApplication().getAssets().open(L_B)
                    )) {
                        File cacheImage = new File(getCacheDir().getAbsolutePath() + "/" + CACHE_PANO);
                        File newImage = new File(Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH + CACHE_PANO);
                        copyFile(newImage, new FileInputStream(cacheImage));
                        AppToast.show(mContext, "pano.jpg copy success!");
                    } else {
                        AppToast.show(mContext, "拼接失败！！！");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                long end = System.currentTimeMillis();
                Log.i(TAG, "拼接全过程耗时：" + (end - start) + "ms");
            }
        });
        findViewById(R.id.stitch_logo).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                long start = System.currentTimeMillis();
                
                try {
                    cali_str = getCaliTextOfExifByFile(getApplication().getAssets().open("20201216_113819_0.JPG"));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (TextUtils.isEmpty(cali_str)) {
                    AppToast.show(mContext, "标定参数为空！");
                }
                try {
                    
                    if (0 == stitchExec(
                            getApplication().getAssets().open("20201216_113819_0.JPG"),
                            getApplication().getAssets().open("20201216_113819_3.JPG"),
                            getApplication().getAssets().open("20201216_113819_2.JPG"),
                            getApplication().getAssets().open("20201216_113819_1.JPG")
                            , getApplication().getAssets().open(LOGO)
                    )) {
                        
                        File cacheImage = new File(getCacheDir().getAbsolutePath() + "/" + CACHE_PANO);
                        File newImage = new File(Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH + CACHE_PANO);
                        copyFile(newImage, new FileInputStream(cacheImage));
                        AppToast.show(mContext, "pano.jpg copy success!");
                    } else {
                        AppToast.show(mContext, "拼接失败！！！");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                long end = System.currentTimeMillis(); 
                Log.i(TAG, "拼接全过程耗时：" + (end - start) + "ms");
            }
        });
        findViewById(R.id.stitch_logo2).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                long start = System.currentTimeMillis();
                
                try {
                    cali_str = getCaliTextOfExifByFile(getApplication().getAssets().open("20201216_113419_0.JPG"));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                if (TextUtils.isEmpty(cali_str)) {
                    AppToast.show(mContext, "标定参数为空！");
                }
                try {
                    
                    if (0 == stitchExec(
                            getApplication().getAssets().open("20201216_113419_0.JPG"),
                            getApplication().getAssets().open("20201216_113419_3.JPG"),
                            getApplication().getAssets().open("20201216_113419_2.JPG"),
                            getApplication().getAssets().open("20201216_113419_1.JPG")
                            , getApplication().getAssets().open(LOGO)
                    )) {
                        
                        File cacheImage = new File(getCacheDir().getAbsolutePath() + "/" + CACHE_PANO);
                        File newImage = new File(Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH + CACHE_PANO);
                        copyFile(newImage, new FileInputStream(cacheImage));
                        AppToast.show(mContext, "pano.jpg copy success!");
                    } else {
                        AppToast.show(mContext, "拼接失败！！！");
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
                long end = System.currentTimeMillis(); 
                Log.i(TAG, "拼接全过程耗时：" + (end - start) + "ms");
            }
        });
    } 
    public static String getCaliTextOfExifByFile(@NonNull InputStream inputStream) {
        long start = System.currentTimeMillis();
        if (null == inputStream) throw new AssertionError();
        ExifInterface exifInterface = null;
        try {
            exifInterface = new ExifInterface(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (null != exifInterface) {
            byte[] makerNote = exifInterface.getAttributeBytes(ExifInterface.TAG_MAKER_NOTE);
            if (null == makerNote || 0 == makerNote.length) {
                return null;
            } else {
                int caliByteCount = 0;
                final int OFFSET = 33664 + "cali:".length();
                for (int index = OFFSET; index != makerNote.length; ++index) {
                    if ('\r' == makerNote[index]) {
                        break;
                    }
                    caliByteCount++;
                }
                byte[] caliBytes = new byte[caliByteCount];
                System.arraycopy(makerNote, OFFSET, caliBytes, 0, caliByteCount);
                String caliText = null;
                try {
                    caliText = new String(caliBytes, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (null != caliText) {
                    
                    Log.i(TAG, "cali text of makerNote : " + caliText);
                    long end = System.currentTimeMillis();
                    Log.i(TAG, "getCaliTextOfExifByFile, take time : " + (end - start) + "ms");
                    return caliText;
                }
            }
        }
        return null;
    }
    private int stitchExec(InputStream is1, InputStream is2, InputStream is3, InputStream is4) {
        return stitchExec(is1, is2, is3, is4, null);
    }
    private int stitchExec(InputStream is1, InputStream is2, InputStream is3, InputStream is4, InputStream logoInputStream) {
        if (is1 == null) throw new AssertionError();
        if (is2 == null) throw new AssertionError();
        if (is3 == null) throw new AssertionError();
        if (is4 == null) throw new AssertionError();
        try {
            String rootDir = getApplication().getCacheDir().getAbsolutePath();
            
            String filePathA = new File(rootDir, L_A).getAbsolutePath();
            String filePathB = new File(rootDir, L_B).getAbsolutePath();
            String filePathC = new File(rootDir, L_C).getAbsolutePath();
            String filePathD = new File(rootDir, L_D).getAbsolutePath();
            copyFile(new File(filePathA), is1);
            copyFile(new File(filePathB), is2);
            copyFile(new File(filePathC), is3);
            copyFile(new File(filePathD), is4);
            JSONArray jsonArray = new JSONArray();
            jsonArray.put(filePathA).put(filePathB).put(filePathC).put(filePathD);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("calistr", cali_str);
            jsonObject.put("accesskey", accessKey);
            jsonObject.put("cachefile", getCacheDir().getAbsolutePath() + "/");
            
            jsonObject.put("dstwidth", 8000);
            jsonObject.put("dstheight", 4000);
            jsonObject.put("isopt", 1);
            jsonObject.put("ismulti", 1);
            jsonObject.put("img", jsonArray);
            jsonObject.put("mutiluv", 1);
            if (logoInputStream != null) {
                final int DEFAULT_HEIGHT = 20;
                
                String logoPath = new File(rootDir, LOGO).getAbsolutePath();
                copyFile(new File(logoPath), logoInputStream);
                jsonObject.put("logofile", logoPath);
                jsonObject.put("logoheight", DEFAULT_HEIGHT);
            }
            String jsonString = jsonObject.toString();
            Log.i(TAG, "jsonString : " + jsonString);
            long start = System.currentTimeMillis();
            int result = SzStitch.CaliExecByContent(jsonString);
            long end = System.currentTimeMillis();
            Log.i(TAG, "SzStitch.CaliExecByContent, take time : " + (end - start) + "ms");
            Log.i(TAG, "SzStitch.CaliExecByContent, result : " + result);
            return result;
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return -1;
    }
    private static boolean copyFile(File fileCopyDest, InputStream inputStream) {
        long start = System.currentTimeMillis();
        if (inputStream == null) throw new AssertionError();
        if (fileCopyDest == null) throw new AssertionError();
        BufferedOutputStream bos = null;
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        try {
            bos = new BufferedOutputStream(new FileOutputStream(fileCopyDest));
            int length = 0;
            byte[] buffer = new byte[10240];
            while ((length = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, length);
            }
            Log.i(TAG, "copyFile success ：" + fileCopyDest.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
            Log.i(TAG, "copyFile fail ：" + fileCopyDest.getAbsolutePath());
            return false;
        } finally {
            try {
                if (bos != null) {
                    bos.close();
                }
                if (bis != null) {
                    bis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        long end = System.currentTimeMillis();
        Log.d(TAG, "copyFile take time : " + (end - start) + "ms");
        return true;
    }
    
}

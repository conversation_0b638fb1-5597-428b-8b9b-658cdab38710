package com.detu.szStitch;

import static android.os.Environment.getExternalStorageDirectory;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.text.TextUtils;

import androidx.exifinterface.media.ExifInterface;

import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PathUtils;
import com.blankj.utilcode.util.SPUtils;
import com.detu.libszstitch.SzStitch;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.SdkApi.FileOperation;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.SPKey;
import com.icatchtek.reliant.customer.type.ICatchFile;
import com.icatchtek.reliant.customer.type.ICatchFileType;
import com.ijoyer.camera.utils.LogUtil;
import com.tencent.mmkv.MMKV;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.opencv.core.Core;
import org.opencv.core.CvType;
import org.opencv.core.Mat;
import org.opencv.core.MatOfInt;
import org.opencv.core.Scalar;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class StitchUtils {
    private static final String TAG = StitchUtils.class.getSimpleName();
    public static final String SP = "stitch_param";
    private static SPUtils spUtils = SPUtils.getInstance(SP);

    public enum LogoType {
        NONE,
        IJOYER,
        CUSTOMER
    }

    public static final String KEY_LOGO = "logo";
    public static final LogoType DEFAULT_LOGO = LogoType.NONE;
    public static final String KEY_LOGO_SIZE = "logoheight";
    public static final int DEFAULT_LOGO_SIZE = 13;
    public static final int RESOLUTION_MAX = 8000;
    public static final int RESOLUTION_MAX_DOUBLE = 6144;
    public static final String KEY_WIDTH = "dstwidth";
    public static final String KEY_HEIGHT = "dstheight";
    public static final int DEFAULT_WIDTH = RESOLUTION_MAX;
    public static final int DEFAULT_HEIGHT = RESOLUTION_MAX / 2;
    public static final int DEFAULT_WIDTH_DOUBLE = RESOLUTION_MAX_DOUBLE;
    public static final int DEFAULT_HEIGHT_DOUBLE = RESOLUTION_MAX_DOUBLE / 2;
    public static final String KEY_OPT = "isopt";
    public static final int DEFAULT_OPT = 0;
    public static final String KEY_MULTI = "ismulti";
    public static final int DEFAULT_MULTI = 1;
    public static final String KEY_MUTILUV = "mutiluv";
    public static final int DEFAULT_MUTILUV = 1;
    public static final String accessKey = "ZQnAbZjCvNCjXY9rFSHtBct1XfkvOv5d6w9FLD5WPyddiDE584MbKL1na5X0Nao22bnsFRXu6bC8ZGO/GJcaKw==";
    private static final String PANO_JPG = "pano.jpg";
    private static final String IJOYER_LOGO_JPG = "ijoyer_logo.jpg";
    public static final String CUSTOMER_LOGO = "customer.jpg";
    public static final String CACHE_LOGO = "logo.jpg";
    public static final String REGEX = "\\d{8}_\\d{6}_[0-3]\\w{0,1}(.JPEG|.jpeg|.JPG|.jpg)$";
    public static final String REGEX_RECORD = "\\d{8}_\\d{6}_RECORD(.mp4|.MP4)$";
    public static final String REGEX_FRONT = "\\d{8}_\\d{6}_";

    public static String getCaliTextOfExifByFile(InputStream inputStream) {
        String newestNewestZdStr = getNewestZdStr();//这里大概需要一毫秒，每一次都拿新的
        if (!TextUtils.isEmpty(newestNewestZdStr)) {
            return newestNewestZdStr;
        }
        long start = System.currentTimeMillis();
        if (null == inputStream) throw new AssertionError();
        ExifInterface exifInterface = null;
        try {
            exifInterface = new ExifInterface(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (null != exifInterface) {
            byte[] makerNote = exifInterface.getAttributeBytes(ExifInterface.TAG_MAKER_NOTE);
            if (null == makerNote || 0 == makerNote.length) {
                return null;
            } else {
                int caliByteCount = 0;
                final int OFFSET = 33664 + "cali:".length();
                for (int index = OFFSET; index != makerNote.length; ++index) {
                    if ('\r' == makerNote[index]) {
                        break;
                    }
                    caliByteCount++;
                }
                byte[] caliBytes = new byte[caliByteCount];
                System.arraycopy(makerNote, OFFSET, caliBytes, 0, caliByteCount);
                String caliText = null;
                try {
                    caliText = new String(caliBytes, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (null != caliText) {

                    long end = System.currentTimeMillis();
                    AppLog.i(TAG, "getCaliTextOfExifByFile, take time : " + (end - start) + "ms");
                    AppLog.i(TAG, "cali text of makerNote : " + caliText);
                    return caliText;
                }
            }
        }
        return null;
    }

    /**
     * 获取最新的zd 文件（标定参数）
     */
    public static String getNewestZdStr() {
        //用一个临时文件名
        String fileName = System.currentTimeMillis() + ".txt";
        String filePath = PanoramaApp.getContext().getCacheDir().getAbsolutePath() + "/" + fileName;
        LogUtil.d("获取一次性的标定参数:" + filePath);
        ICatchFile iCatchFile = new ICatchFile(1, ICatchFileType.ICH_FILE_TYPE_TEXT, "/zd.info", "zd.info", 0);

        if (CameraManager.getInstance().getCurCamera() == null) {
            return null;
        }

        FileOperation fileOperation = CameraManager.getInstance().getCurCamera().getFileOperation();
        boolean retValue = fileOperation.downloadFile(iCatchFile, filePath);
        if (retValue) {
            //标定参数下载成功
            try {
                byte[] fileContent = Files.readAllBytes(Paths.get(filePath));
                String zdStr = new String(fileContent);
                LogUtils.file("从标定文件中获取到的标定参数：" + zdStr);
                zdStr = zdStr.substring(5);//cali:
                LogUtil.d("临时标定参数获取成功：" + zdStr);
                return zdStr;
            } catch (FileNotFoundException e) {
                LogUtil.e("获取标定参数失败：文件不存在");
                LogUtils.file("获取临时标定参数失败：文件不存在");
                return null;
            } catch (Exception e) {
                LogUtil.e("获取标定参数失败：其他错误:" + e.getMessage());
                LogUtils.file("获取临时标定参数失败：" + e.getMessage());

                return null;
            } finally {
                //删掉
                FileUtils.delete(filePath);
            }
        } else {
            LogUtil.e("标定参数下载失败");
            return null;
        }
    }

    /**
     * A3
     */
    public static String getCaliTextOfExifByFileV2(InputStream inputStream) {
        long start = System.currentTimeMillis();
        if (null == inputStream) throw new AssertionError();
        ExifInterface exifInterface = null;
        try {
            exifInterface = new ExifInterface(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (null != exifInterface) {
            byte[] makerNote = exifInterface.getAttributeBytes(ExifInterface.TAG_MAKER_NOTE);
            if (null == makerNote || 0 == makerNote.length) {
                return null;
            } else {
                int caliByteCount = 0;
                final int OFFSET = 33664;
                for (int index = OFFSET; index != makerNote.length; ++index) {
                    if ('\r' == makerNote[index]) {
                        break;
                    }
                    caliByteCount++;
                }
                byte[] caliBytes = new byte[caliByteCount];
                System.arraycopy(makerNote, OFFSET, caliBytes, 0, caliByteCount);
                String caliText = null;
                try {
                    caliText = new String(caliBytes, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (null != caliText) {

                    long end = System.currentTimeMillis();
                    AppLog.i(TAG, "getCaliTextOfExifByFile, take time : " + (end - start) + "ms");
                    AppLog.i(TAG, "cali text of makerNote : " + caliText);
                    return caliText.trim().substring(5);
                }
            }
        }
        return null;
    }

    public static int stitchExecOne(final Context context,
                                    final String filePathA,
                                    final String tmp) {
        if (null == filePathA) throw new AssertionError();
        AppLog.d(TAG, "filePathA:" + filePathA);
        String fileNameA = new File(filePathA).getName();
        AppLog.d(TAG, "fileNameA:" + fileNameA);
        String cacheDir = context.getCacheDir().getAbsolutePath();

        String cacheFilePathA = new File(cacheDir, fileNameA).getAbsolutePath();
        String cali_str = "";
        InputStream inputStream = null;
        try (
                InputStream isA = new FileInputStream(filePathA);
        ) {
            copyFile(new File(cacheFilePathA), isA);
            inputStream = new FileInputStream(cacheFilePathA);
            cali_str = StitchUtils.getCaliTextOfExifByFileV2(inputStream);
            if (TextUtils.isEmpty(cali_str)) {
                cali_str = "aafOG91hSyQ5mo/CvxlrAtDNQqi/8Yo+5LNB6J7VP5FDrd0oBbJ06ZkC9AdnFgadJEBmeuheAP/qEvQj2P8fJYIONUtpl3SnHKrCCh6Du9KqUBW+zuZID+JRswwPn9K8dEvXBoHW1btrURZDxb2Zq84gaIQuLFGfbCgycrwxM24etiL6kOWg4wfIVB03zQmxavOjSYDkACAk7KuCBlP0ugM4D/xEuwiGUhZ4G51x/GMaqlRvFPEYXBU4K/tllfHQhoZBe12BhaSR4DOkf2X9QKDrFdA+ki4CBJFiWNbNToNqscui7BXBgDhflEyGeSOo1WpK9njJL9cM0lNeb58sIjjUB0anZvXEqOZbknpsmXuOi9nRfYoqKf7KNhyYZzuFbVtjxv78Ex8pas5QETONMQkIDYq88kDYYvkNa+gKj153oWIKM76lsBSxRxMnuOTpVuFmwQ09iAKzLpdsGQK2+13+r7XoWK+rj4BcW86fHGF8Tw5cWWDrcBhnsUgNHL6JBWmpXxqmvNQCpbJZs763eYEYWJxeg02Hz+2fQSYMudgxV1VMbTrVCRjgfDVp5bEp4BMUjajqU+jNnyCD9UR1qQ==";
                AppToast.show(context, "标定参数出错！");
            }
            AppLog.i(TAG, "cali_str : " + cali_str);
            AppLog.d(TAG, "cacheFilePathA:" + cacheFilePathA);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("calistr", cali_str);
            jsonObject.put("accesskey", accessKey);
            jsonObject.put("cachefile", cacheDir + "/");
            JSONArray jsonArray = new JSONArray();
            jsonArray.put(filePathA);
            jsonObject.put("img", jsonArray);
            jsonObject.put(KEY_WIDTH, 6144);
            jsonObject.put(KEY_HEIGHT, 6144 / 2);
            jsonObject.put(KEY_OPT, spUtils.getInt(KEY_OPT, DEFAULT_OPT));
            jsonObject.put("type", 0);
            jsonObject.put(KEY_MULTI, spUtils.getInt(KEY_MULTI, DEFAULT_MULTI));
            jsonObject.put(KEY_MUTILUV, spUtils.getInt(KEY_MUTILUV, DEFAULT_MUTILUV));
            int logoSize = spUtils.getInt(KEY_LOGO_SIZE, DEFAULT_LOGO_SIZE);
            int logoParam = spUtils.getInt(KEY_LOGO, DEFAULT_LOGO.ordinal());
            switch (LogoType.values()[logoParam]) {
                case NONE:
                    String iLogoPathTest = new File(cacheDir, "test.png").getAbsolutePath();
                    copyFile(new File(iLogoPathTest), context.getAssets().open("test.png"));
                    jsonObject.put("logofile", iLogoPathTest);
                    jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    jsonObject.put("type", 2);
                    jsonObject.put("devicetype", 1);
                    break;
                case IJOYER:
                    String iLogoPath = new File(cacheDir, IJOYER_LOGO_JPG).getAbsolutePath();
                    copyFile(new File(iLogoPath), context.getAssets().open(IJOYER_LOGO_JPG));
                    jsonObject.put("logofile", iLogoPath);
                    jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    jsonObject.put("type", 2);
                    jsonObject.put("devicetype", 1);
                    AppLog.d(TAG, "ijoyer logo file:" + iLogoPath);
                    break;
                case CUSTOMER:
//                    String customerLogoPath = new File(cacheDir, CUSTOMER_LOGO).getAbsolutePath();
//                    copyFile(new File(customerLogoPath), context.getAssets().open(CUSTOMER_LOGO));
                    File file = new File(getExternalStorageDirectory() + AppInfo.PHOTO_TEMP + StitchUtils.CUSTOMER_LOGO);
//                    File file = new File(context.getCacheDir().getAbsolutePath() + "/" + StitchUtils.CUSTOMER_LOGO);
                    String customerLogoPath = file.getAbsolutePath();
                    if (TextUtils.isEmpty(FileUtils.getSize(customerLogoPath))) {
                        String iLogoPathTest2 = new File(cacheDir, "test.png").getAbsolutePath();
                        copyFile(new File(iLogoPathTest2), context.getAssets().open("test.png"));
                        jsonObject.put("logofile", iLogoPathTest2);
                        jsonObject.put(KEY_LOGO_SIZE, logoSize);
                        jsonObject.put("type", 2);
                        jsonObject.put("devicetype", 1);
                    } else {
                        jsonObject.put("logofile", customerLogoPath);
                        jsonObject.put(KEY_LOGO_SIZE, logoSize);
                        jsonObject.put("type", 2);
                        jsonObject.put("devicetype", 1);
                    }
                    AppLog.d(TAG, "customer logo file:" + customerLogoPath);
                    break;
                default:
                    String testLogoPath = new File(cacheDir, CACHE_LOGO).getAbsolutePath();
                    copyFile(new File(testLogoPath), context.getAssets().open(CACHE_LOGO));
                    jsonObject.put("logofile", testLogoPath);
                    jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    AppLog.d(TAG, "detu logo file:" + testLogoPath);
                    break;
            }
            String jsonString = jsonObject.toString();
            AppLog.i(TAG, "json param : " + jsonString);
            long start = System.currentTimeMillis();
            int result = SzStitch.CaliExecByContent(jsonString);
            long end = System.currentTimeMillis();
            AppLog.i(TAG, "CaliExecByContent, take time : " + (end - start) + "ms");
            AppLog.i(TAG, "CaliExecByContent, result : " + result);

            File cacheImage = new File(cacheDir + "/" + PANO_JPG);
            imageEnhancement(cacheImage);//开启图片增强
            String newImageName = tmp.substring(0, tmp.indexOf(".")) + "_" + System.currentTimeMillis() / 1000 + tmp.substring(tmp.indexOf("."));
            String newImagePath = getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + newImageName;
            File newImage = new File(newImagePath);
            cacheImage.createNewFile();
            FileInputStream fileInputStream = new FileInputStream(cacheImage);
            copyFile(newImage, fileInputStream);
            MediaRefresh.scanFileAsync(context, newImage.getAbsolutePath());

//            ThreadUtils.runOnUiThread(new Runnable() {
//                @Override
//                public void run() {
//                    Bitmap bitmap = BitmapFactory.decodeFile(newImagePath, getBitmapOption(1)); //将图片的长和宽缩小味原来的1/2
//                    GPUImage gpuImage = new GPUImage(context);
//                    gpuImage.setImage(bitmap);
//                    gpuImage.setFilter(new GPUImageSaturationFilter((float) 1.2));
//                    gpuImage.saveToPictures("GPUImage", newImageName, new GPUImage.OnPictureSavedListener() {
//                        @Override
//                        public void onPictureSaved(Uri uri) {
//
//                        }
//
//                        @Override
//                        public void onSuccess() {
////                            String newImageBrightnessName = tmp.substring(0, tmp.indexOf(".")) + "_" + System.currentTimeMillis() / 1000 + tmp.substring(tmp.indexOf("."));
////                            GPUImage gpuImage = new GPUImage(context);
////                            gpuImage.setImage(bitmap);
////                            gpuImage.setFilter(new GPUImageBrightnessFilter((float) 0.15));
////                            gpuImage.saveToPictures("GPUImage", newImageBrightnessName, null);
//                        }
//                    });
//                }
//            });


            if (cacheImage.exists()) {
                cacheImage.delete();
                MediaRefresh.scanFileAsync(PanoramaApp.getContext(), cacheImage.getAbsolutePath());
            }

            return result;
        } catch (JSONException | IOException e) {
            e.printStackTrace();
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return -1;
    }

    public static BitmapFactory.Options getBitmapOption(int inSampleSize) {
        System.gc();
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inPurgeable = true;
        options.inSampleSize = inSampleSize;
        return options;
    }


    public static int stitchExec(final Context context,
                                 final String filePathA, final String filePathB,
                                 final String filePathC, final String filePathD,
                                 final String tmp) {
        if (null == filePathA) throw new AssertionError();
        if (null == filePathB) throw new AssertionError();
        if (null == filePathC) throw new AssertionError();
        if (null == filePathD) throw new AssertionError();
        String fileNameA = new File(filePathA).getName();
        String fileNameB = new File(filePathB).getName();
        String fileNameC = new File(filePathC).getName();
        String fileNameD = new File(filePathD).getName();
        String cacheDir = context.getCacheDir().getAbsolutePath();

        String cacheFilePathA = new File(cacheDir, fileNameA).getAbsolutePath();
        String cacheFilePathB = new File(cacheDir, fileNameB).getAbsolutePath();
        String cacheFilePathC = new File(cacheDir, fileNameC).getAbsolutePath();
        String cacheFilePathD = new File(cacheDir, fileNameD).getAbsolutePath();
        String cali_str = "";
        InputStream inputStream = null;
        try (
                InputStream isA = new FileInputStream(filePathA);
                InputStream isB = new FileInputStream(filePathB);
                InputStream isC = new FileInputStream(filePathC);
                InputStream isD = new FileInputStream(filePathD);
        ) {
            copyFile(new File(cacheFilePathA), isA);
            copyFile(new File(cacheFilePathB), isB);
            copyFile(new File(cacheFilePathC), isC);
            copyFile(new File(cacheFilePathD), isD);
            inputStream = new FileInputStream(cacheFilePathA);
            cali_str = StitchUtils.getCaliTextOfExifByFile(inputStream);
            if (TextUtils.isEmpty(cali_str)) {
                AppToast.show(context, "标定参数为空！");
            }
            JSONArray jsonArray = new JSONArray();
            jsonArray.put(cacheFilePathA).put(cacheFilePathB).put(cacheFilePathC).put(cacheFilePathD);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("calistr", cali_str);
            jsonObject.put("accesskey", accessKey);
            jsonObject.put("cachefile", cacheDir + "/");
            jsonObject.put("img", jsonArray);
            jsonObject.put(KEY_WIDTH, spUtils.getInt(KEY_WIDTH, DEFAULT_WIDTH));
            jsonObject.put(KEY_HEIGHT, spUtils.getInt(KEY_HEIGHT, DEFAULT_HEIGHT));
            jsonObject.put(KEY_OPT, spUtils.getInt(KEY_OPT, DEFAULT_OPT));
            jsonObject.put(KEY_MULTI, spUtils.getInt(KEY_MULTI, DEFAULT_MULTI));
            jsonObject.put(KEY_MUTILUV, spUtils.getInt(KEY_MUTILUV, DEFAULT_MUTILUV));
            int logoSize = spUtils.getInt(KEY_LOGO_SIZE, DEFAULT_LOGO_SIZE);
            int logoParam = spUtils.getInt(KEY_LOGO, DEFAULT_LOGO.ordinal());
            switch (LogoType.values()[logoParam]) {
                case NONE:
                    break;
                case IJOYER:
                    String iLogoPath = new File(cacheDir, IJOYER_LOGO_JPG).getAbsolutePath();
                    copyFile(new File(iLogoPath), context.getAssets().open(IJOYER_LOGO_JPG));
                    jsonObject.put("logofile", iLogoPath);
                    jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    AppLog.d(TAG, "ijoyer logo file:" + iLogoPath);
                    break;
                case CUSTOMER:
                    File file = new File(getExternalStorageDirectory() + AppInfo.PHOTO_TEMP + StitchUtils.CUSTOMER_LOGO);
                    String customerLogoPath = file.getAbsolutePath();
                    if (!TextUtils.isEmpty(FileUtils.getSize(customerLogoPath))) {
                        jsonObject.put("logofile", customerLogoPath);
                        jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    }

                    AppLog.d(TAG, "customer logo file:" + customerLogoPath);
                    break;
                default:
                    String testLogoPath = new File(cacheDir, CACHE_LOGO).getAbsolutePath();
                    copyFile(new File(testLogoPath), context.getAssets().open(CACHE_LOGO));
                    jsonObject.put("logofile", testLogoPath);
                    jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    AppLog.d(TAG, "detu logo file:" + testLogoPath);
                    break;
            }
            String jsonString = jsonObject.toString();
            LogUtil.e(jsonString);
            long start = System.currentTimeMillis();
            int result = SzStitch.CaliExecByContent(jsonString);
            long end = System.currentTimeMillis();
            AppLog.i(TAG, "CaliExecByContent, take time : " + (end - start) + "ms");
            AppLog.i(TAG, "CaliExecByContent, result : " + result);

            File cacheImage = new File(cacheDir + "/" + PANO_JPG);
            imageEnhancement(cacheImage);//开启图片增强
            String newImagePath = getExternalStorageDirectory()
                    + AppInfo.DOWNLOAD_PATH_PHOTO + getFileNameWithRemark(tmp);
            File newImage = new File(newImagePath);
            copyFile(newImage, Files.newInputStream(cacheImage.toPath()));
            MediaRefresh.scanFileAsync(context, newImagePath);
            AppLog.d(TAG, "new pano image:" + newImage.getAbsolutePath());
            return result;
        } catch (JSONException | IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return -1;
    }

    public static int stitchExecHdr(final Context context,
                                    final String filePathA, final String filePathB,
                                    final String filePathC, final String filePathD,
                                    final String filePathCaliText,
                                    final String tmp) {
        if (tmp.contains("HDR") || tmp.contains("hdr")) {

        }


        if (null == filePathA) throw new AssertionError();
        if (null == filePathB) throw new AssertionError();
        if (null == filePathC) throw new AssertionError();
        if (null == filePathD) throw new AssertionError();
        String fileNameA = new File(filePathA).getName();
        String fileNameB = new File(filePathB).getName();
        String fileNameC = new File(filePathC).getName();
        String fileNameD = new File(filePathD).getName();
        String cacheDir = context.getCacheDir().getAbsolutePath();

        String cacheFilePathA = new File(cacheDir, fileNameA).getAbsolutePath();
        String cacheFilePathB = new File(cacheDir, fileNameB).getAbsolutePath();
        String cacheFilePathC = new File(cacheDir, fileNameC).getAbsolutePath();
        String cacheFilePathD = new File(cacheDir, fileNameD).getAbsolutePath();
        String cali_str = "";
        InputStream inputStream = null;
        try {
            inputStream = Files.newInputStream(Paths.get(filePathCaliText));
            cali_str = StitchUtils.getCaliTextOfExifByFile(inputStream);
            if (TextUtils.isEmpty(cali_str)) {
                AppToast.show(context, "标定参数为空！");
            }
            JSONArray jsonArray = new JSONArray();
            jsonArray.put(cacheFilePathA).put(cacheFilePathB).put(cacheFilePathC).put(cacheFilePathD);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("calistr", cali_str);
            jsonObject.put("accesskey", accessKey);
            jsonObject.put("cachefile", cacheDir + "/");
            jsonObject.put("img", jsonArray);
            jsonObject.put(KEY_WIDTH, spUtils.getInt(KEY_WIDTH, DEFAULT_WIDTH));
            jsonObject.put(KEY_HEIGHT, spUtils.getInt(KEY_HEIGHT, DEFAULT_HEIGHT));
            jsonObject.put(KEY_OPT, spUtils.getInt(KEY_OPT, DEFAULT_OPT));
            jsonObject.put(KEY_MULTI, spUtils.getInt(KEY_MULTI, spUtils.getInt(KEY_MULTI, DEFAULT_MULTI)));//是否去色差
            jsonObject.put(KEY_MUTILUV, spUtils.getInt(KEY_MUTILUV, DEFAULT_MUTILUV));
            int logoSize = spUtils.getInt(KEY_LOGO_SIZE, DEFAULT_LOGO_SIZE);
            int logoParam = spUtils.getInt(KEY_LOGO, DEFAULT_LOGO.ordinal());
            switch (LogoType.values()[logoParam]) {
                case NONE:
                    break;
                case IJOYER:
                    String iLogoPath = new File(cacheDir, IJOYER_LOGO_JPG).getAbsolutePath();
                    copyFile(new File(iLogoPath), context.getAssets().open(IJOYER_LOGO_JPG));
                    jsonObject.put("logofile", iLogoPath);
                    jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    AppLog.d(TAG, "ijoyer logo file:" + iLogoPath);
                    break;
                case CUSTOMER:
                    File file = new File(getExternalStorageDirectory() + AppInfo.PHOTO_TEMP + StitchUtils.CUSTOMER_LOGO);
                    String customerLogoPath = file.getAbsolutePath();
                    if (!TextUtils.isEmpty(FileUtils.getSize(customerLogoPath))) {
                        jsonObject.put("logofile", customerLogoPath);
                        jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    }

                    AppLog.d(TAG, "customer logo file:" + customerLogoPath);
                    break;
                default:
                    String testLogoPath = new File(cacheDir, CACHE_LOGO).getAbsolutePath();
                    copyFile(new File(testLogoPath), context.getAssets().open(CACHE_LOGO));
                    jsonObject.put("logofile", testLogoPath);
                    jsonObject.put(KEY_LOGO_SIZE, logoSize);
                    AppLog.d(TAG, "detu logo file:" + testLogoPath);
                    break;
            }
            String jsonString = jsonObject.toString();
            LogUtil.e(jsonString);
            LogUtils.file("开始拼接，拼接参数=" + jsonString);
            int result = SzStitch.CaliExecByContent(jsonString);

            LogUtils.file("拼接结束，拼接结果=" + result);

            File cacheImage = new File(cacheDir + "/" + PANO_JPG);
            imageEnhancement(cacheImage);//开启图片增强

            String newImagePath = getExternalStorageDirectory()
                    + AppInfo.DOWNLOAD_PATH_PHOTO + getFileNameWithRemark(tmp);
            File newImage = new File(newImagePath);
            copyFile(newImage, Files.newInputStream(cacheImage.toPath()));
            MediaRefresh.scanFileAsync(context, newImagePath);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                inputStream.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return -1;
    }

    public static String getFileNameWithRemark(String file){
        //把备注名塞进去
        String tempWithRemark = file;
        String groupName = SPKey.getRemarkGroup(file);
        if (!TextUtils.isEmpty(groupName)) {
            String remarkName = MMKV.defaultMMKV().getString(SPKey.REMARK_NAME_PREFIX + groupName, null);
            if (!TextUtils.isEmpty(remarkName)){
                tempWithRemark = "["+remarkName+"]"+file;
            }
        }
        return tempWithRemark;
    }

    //图片增强，替换原图位置
    public static boolean imageEnhancement(File newImage) {
        if (!SPKey.useImageEnhancement()) {
            return false; //跳过，不增强
        }
        String tag = "imageEnhancement（图像增强）：";

        LogUtil.d(tag + "原图地址：" + newImage.getAbsolutePath());
        if (!FileUtils.isFileExists(newImage)) {
            LogUtil.e(tag + "图片文件不存在");
            return false;
        }

        // 提前声明所有Mat对象，方便在finally中统一释放
        Mat source = null;
        Mat bilateral = null;
        Mat laplacian = null;
        Mat laplacianAbs = null;
        Mat sharp = null;
        Mat brightened = null;
        MatOfInt quality = null;

        // 读取成功后备份原图片
        String filePath = newImage.getAbsolutePath();
        String bakPath = PathUtils.getInternalAppCachePath() + "/" + newImage.getName() + ".bak";
        if (!FileUtils.copy(filePath, bakPath)) {
            LogUtil.e(tag + "图片备份失败，跳过增强：" + bakPath);
            return false;
        } else {
            LogUtil.d(tag + "备份图片地址：" + bakPath);
        }

        boolean result = false;
        try {
            source = Imgcodecs.imread(newImage.getAbsolutePath());
            if (source == null || source.empty()) {
                LogUtil.e(tag + "读取图片失败或图片为空");
                // 注意：这里读取失败也需要进行回滚操作
                FileUtils.delete(filePath);
                FileUtils.copy(bakPath, filePath);
                LogUtil.e(tag + "回滚图片");
                return false;
            }

            // 使用双边滤波进行保边降噪，保留边缘细节
            bilateral = new Mat();
            Imgproc.bilateralFilter(source, bilateral, 9, 75, 75);

            // 使用Laplacian增强图像的锐度，强度较轻，模拟0.5的锐化强度
            laplacian = new Mat(source.size(), CvType.CV_32F); // 使用32位浮点型以提高精度
            Imgproc.Laplacian(bilateral, laplacian, CvType.CV_32F, 3);

            // 将Laplacian转换为绝对值，避免负值影响
            laplacianAbs = new Mat();
            Core.convertScaleAbs(laplacian, laplacianAbs);

            // 锐化：利用Laplacian图像增强原图像的细节，锐化强度为0.5
            sharp = new Mat();
            Core.addWeighted(source, 1.1, laplacianAbs, -0.1, 0, sharp);  // 通过调整系数0.5进行锐化

            // 增加亮度：使用Core.add()添加亮度（例如增加10）
            brightened = new Mat();
            Core.add(sharp, new Scalar(10, 10, 10), brightened);  // 为RGB通道都加上10，调整亮度

            // 输出最后结果
            FileUtils.createFileByDeleteOldFile(new File(filePath));

            // 使用JPEG格式保存图片并设置质量参数
            quality = new MatOfInt(Imgcodecs.IMWRITE_JPEG_QUALITY, 97);
            result = Imgcodecs.imwrite(filePath, brightened, quality);
            LogUtil.d(tag + "增强后图片路径：" + filePath);

        } catch (Exception e) {
            LogUtil.e(tag + "图像增强失败：" + e);
            e.printStackTrace();
            result = false; // 确保异常发生时result为false
        } finally {
            if (source != null) source.release();
            if (bilateral != null) bilateral.release();
            if (laplacian != null) laplacian.release();
            if (laplacianAbs != null) laplacianAbs.release();
            if (sharp != null) sharp.release();
            if (brightened != null) brightened.release();
            if (quality != null) quality.release();
            LogUtil.d(tag + "释放所有Mat对象");
        }

        if (result) {
            FileUtils.delete(bakPath);
            LogUtil.d(tag + "图片增强成功");
        } else { //回滚
            FileUtils.delete(filePath);
            FileUtils.copy(bakPath, filePath);
            LogUtil.e(tag + "回滚图片");
        }
        // 成功处理后，备份文件也应该被删除
        if(FileUtils.isFileExists(bakPath)){
            FileUtils.delete(bakPath);
        }
        return result;
    }


    public static boolean copyFile(File fileCopyDest, InputStream inputStream) {
        long start = System.currentTimeMillis();
        if (inputStream == null) throw new AssertionError();
        if (fileCopyDest == null) throw new AssertionError();
        BufferedOutputStream bos = null;
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        try {
            bos = new BufferedOutputStream(new FileOutputStream(fileCopyDest));
            int length = 0;
            byte[] buffer = new byte[10240];
            while ((length = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, length);
            }
            AppLog.i(TAG, "copyFile success ：" + fileCopyDest.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
            AppLog.i(TAG, "copyFile fail ：" + fileCopyDest.getAbsolutePath());
            return false;
        } finally {
            try {
                if (bos != null) {
                    bos.close();
                }
                if (bis != null) {
                    bis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        long end = System.currentTimeMillis();
        AppLog.d(TAG, "copyFile take time : " + (end - start) + "ms");
        return true;
    }
}

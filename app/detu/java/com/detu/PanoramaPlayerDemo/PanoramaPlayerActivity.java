package com.detu.PanoramaPlayerDemo;
import android.Manifest;
import android.graphics.Color;
import android.graphics.PointF;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import com.detu.PanoramaPlayerDemo.utils.DTListDialog;
import com.detu.android_panoplayer.IPanoPlayerHotpotListener;
import com.detu.android_panoplayer.PanoPlayerImpl;
import com.detu.android_panoplayer.PanoPlayerUrl;
import com.detu.android_panoplayer.data.panoramas.Hotspot;
import com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView;
import com.detu.getmaskandweight.FileReadandWrite;
import com.detu.getmaskandweight.GetWeightAndMaskHelper;
import com.ijoyer.mobilecam.R;
import com.player.panoplayer.GLGesture;
import com.player.panoplayer.IPanoPlayerListener;
import com.player.panoplayer.IPanoPluginListener;
import com.player.panoplayer.enitity.EaseTypes;
import com.player.panoplayer.enitity.LiteDecor;
import com.player.panoplayer.enitity.PanoData;
import com.player.panoplayer.enitity.PanoDeviceId;
import com.player.panoplayer.enitity.PanoNodeImage;
import com.player.panoplayer.enitity.PanoNodePreview;
import com.player.panoplayer.enitity.PanoNodeView;
import com.player.panoplayer.enitity.PanoOptionKey;
import com.player.panoplayer.enitity.PanoPlayerError;
import com.player.panoplayer.enitity.PanoPlayerOption;
import com.player.panoplayer.enitity.PanoPlayerOptionType;
import com.player.panoplayer.enitity.PanoPlayerStatus;
import com.player.panoplayer.enitity.PanoPluginError;
import com.player.panoplayer.enitity.PanoPluginStatus;
import com.player.panoplayer.enitity.PanoResourceType;
import com.player.panoplayer.enitity.PanoViewMode;
import com.player.panoplayer.plugin.Plugin;
import com.player.panoplayer.plugin.VideoPlugin;
import com.player.panoplayer.view.DisplayType;
import com.player.panoplayer.view.LayoutParams;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
public class PanoramaPlayerActivity extends AppCompatActivity implements
        View.OnClickListener,
        IPanoPlayerHotpotListener,
        IPanoPlayerListener,
        IPanoPluginListener,
        GLGesture.ClickPanoViewListener {
    private static final String TAG = PanoramaPlayerActivity.class.getSimpleName();
    PanoPlayerSurfaceView ppSurfaceView;
    PanoPlayerImpl panoramaPlayer;
    PanoViewMode curViewMode = PanoViewMode.DEF;
    String M1Path = "";
    String F4PlusPath = "";
    String Calibration = "";
    String[] weightPath = new String[4];
    boolean isAutoPlay = false;
    boolean isReverse = true;
    boolean isPointSelecting = false;
    boolean isScenesBackMusic = true;
    private boolean mIsHwDecoder;
    private TextView tvFov;
    private String fovText;
    private Timer timer;
    Handler handler = new Handler();
    DTListDialog menuDialog;
    DTListDialog menuDialogMode;
    DTListDialog menuDialogLiteDecor;
    
    float[] curGestureData = {};
    boolean isReplay = false;
    String PLAYER_CONFIG = "<DetuVr>\n" +
            "   <settings init='pano1' initmode='default' enablevr='true' title=''/>\n" +
            "   <scenes>\n" +
            "       <scene name='pano1' title='' thumburl=''>\n" +
            "           <preview url='' />\n" +
            "           <image type='%s' url='%s' device='%d' isreplay_f4 = 'true'/>\n" +
            "           <view viewmode='default'  isptselect ='true' />\n" +
            "       </scene>\n" +
            "   </scenes>\n" +
            "</DetuVr>";
    static {
        System.loadLibrary("MaskAndWeight");
    }
    private static final boolean IS_TEST_LITE_DECOR = false;
    private static final boolean IS_PLAY_BY_XML = true;
    private void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setNavigationBarColor(Color.TRANSPARENT);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        actionBar.hide();
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        setContentView(R.layout.panoplayerdemo_player);
        hideActionBar();
        findViewById(R.id.btn_viewMode).setOnClickListener(this);
        findViewById(R.id.btn_chooseSwitch).setOnClickListener(this);
        findViewById(R.id.btn_replay).setOnClickListener(this);
        findViewById(R.id.btn_liteDecor).setOnClickListener(this);
        
        ppSurfaceView = findViewById(R.id.pp_surfaceView);
        
        ppSurfaceView.setOnClickPanoViewListener(this);
        tvFov = (TextView) findViewById(R.id.tv_fov);
        tvFov.setTextColor(Color.rgb(255, 0, 0));
        
        panoramaPlayer = ppSurfaceView.getRender();
        panoramaPlayer.setPanoPlayerListener(this);
        panoramaPlayer.setPanoPluginListener(this);
        panoramaPlayer.setHotpotListener(this);
        initBtnSwitch();
        initBtnModeChoose();
        initLiteDecorDialog();
        
        parsePlayRequest();
        timer = new Timer();
        new Thread() {
            @Override
            public void run() {
                super.run();
                timer.schedule(task, 1000, 1000 / 30);
            }
        }.start();
        ppSurfaceView.setGyroEnable(true);
        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE,}, 0);
    } 
    TimerTask task = new TimerTask() {
        @Override
        public void run() {
            ShowFov();
        }
    };
    private void ShowFov() {
        float fov = 0;
        if (panoramaPlayer != null) {
            fov = panoramaPlayer.getCurrentGesturedata()[0];
        }
        if (panoramaPlayer.getCurrentPanoViewMode() == PanoViewMode.FLAT) {
            fovText = "scale:" + fov;
        } else if (panoramaPlayer.getCurrentPanoViewMode() != PanoViewMode.ORIGINAL) {
            fovText = "fov:" + fov;
        }
        handler.post(new Runnable() {
            @Override
            public void run() {
                tvFov.setText(fovText);
            }
        });
    }
    private void initLiteDecorDialog() {
        menuDialogLiteDecor = new DTListDialog(this);
        menuDialogLiteDecor.setTitle("liteDecor").setItems(new String[]{"添加", "移动", "删除"},
                new DTListDialog.OnItemClickListener() {
                    @Override
                    public void onItemClick(DTListDialog dialog, View view, int position) {
                        switch (position) {
                            case 0:
                                ;
                                break;
                            case 1:
                                LayoutParams layoutParams = new LayoutParams();
                                layoutParams.ath = 15f * athIndex++;
                                layoutParams.atv = 0f;
                                panoramaPlayer.updateLiteDecorLayoutParamsSect("smart", layoutParams);
                                break;
                            case 2:
                                panoramaPlayer.deleteLiteDecor("smart");
                                break;
                            default:
                                break;
                        }
                    }
                });
    }
    private void initBtnModeChoose() {
        menuDialogMode = new DTListDialog(this);
        menuDialogMode.setTitle("模式选择").setItems(new String[]{"全景", "鱼眼", "2：1展开", "原始平面", "vr水平", "vr垂直", "小行星", "曲面", "3D"},
                new DTListDialog.OnItemClickListener() {
                    @Override
                    public void onItemClick(DTListDialog dialog, View view, int position) {
                        PanoViewMode viewMode = PanoViewMode.DEF;
                        switch (position) {
                            case 0:
                                viewMode = PanoViewMode.DEF; 
                                break;
                            case 1:
                                viewMode = PanoViewMode.FISHEYE; 
                                break;
                            case 2:
                                viewMode = PanoViewMode.FLAT; 
                                break;
                            case 3:
                                viewMode = PanoViewMode.ORIGINAL;
                                break;
                            case 4:
                                viewMode = PanoViewMode.VR_HORIZONTAL;
                                break;
                            case 5:
                                viewMode = PanoViewMode.VR_VERTICAL;
                                break;
                            case 6:
                                viewMode = PanoViewMode.LITTLEPLANET;
                                break;
                            case 7:
                                viewMode = PanoViewMode.SPHERE;
                                break;
                            case 8:
                                viewMode = PanoViewMode.SPACE3D;
                            default:
                                break;
                        }
                        panoramaPlayer.setAnimationViewMode(viewMode, 1, EaseTypes.LinearEaseOuts);
                        
                        menuDialogMode.dismiss();
                    }
                });
    }
    private void initBtnSwitch() {
        menuDialog = new DTListDialog(this);
        menuDialog.setTitle("开关选择").setItems(new String[]{"陀螺仪开关", "手势缩放开关", "手势移动开关", "陀螺仪手势共存开关", "自动播放开关", "翻转开关", "背景音乐开关", "点选开关"},
                new DTListDialog.OnItemClickListener() {
                    @Override
                    public void onItemClick(DTListDialog dialog, View view, int position) {
                        switch (position) {
                            case 0:
                                ppSurfaceView.setGyroEnable(!ppSurfaceView.getGyroEnable());
                                break;
                            case 1:
                                ppSurfaceView.setZoomEnable(!ppSurfaceView.getZoomEnable());
                                break;
                            case 2:
                                ppSurfaceView.setGestureEnable(!ppSurfaceView.getGestureEnable());
                                break;
                            case 3:
                                ppSurfaceView.setGyroModeShouldMove(!ppSurfaceView.getGyroModeShouldMove());
                                break;
                            case 4:
                                isAutoPlay = !isAutoPlay;
                                panoramaPlayer.setAutoRotate(isAutoPlay, -0.35f);
                                panoramaPlayer.setPauseTime(1);
                                break;
                            case 5:
                                panoramaPlayer.setReverse(isReverse);
                                isReverse = !isReverse;
                                break;
                            case 6:
                                if (isScenesBackMusic) {
                                    panoramaPlayer.pauseBackgroundMusic();
                                    isScenesBackMusic = !isScenesBackMusic;
                                } else {
                                    panoramaPlayer.startBackgroundMusic();
                                    isScenesBackMusic = !isScenesBackMusic;
                                }
                            case 7:
                                isPointSelecting = !panoramaPlayer.getCurrentPanoData().nodeView.isPointSelecting;
                                panoramaPlayer.setPointSelecting(isPointSelecting);
                                break;
                            default:
                                break;
                        }
                        menuDialog.dismiss();
                    }
                });
    }
    private void parsePlayRequest() {
        final int tag = getIntent().getIntExtra("playTag", 0);
        mIsHwDecoder = getIntent().getBooleanExtra("isHwDecoder", true);
        String paths = getIntent().getStringExtra("path");
        Log.e(TAG, "parsePlayRequest, tag:" + tag + ", path:" + paths);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        switch (tag) {
            case 10:
                testPlayPicture2_1();
                break;
            case 11:
                testPlayPictureCube();
                break;
            case 12:
                panoplayerurl.setXmlUrl("http://192.168.8.94:8092/picture/fisheyepic.xml");
                panoramaPlayer.playByXml(panoplayerurl);
                break;
            case 13:
                panoplayerurl.setXmlUrl("http://192.168.8.94:8092/picture/fish2eyepic.xml");
                panoramaPlayer.playByXml(panoplayerurl);
                break;
            case 14:
                panoplayerurl.setXmlUrl("http://192.168.8.94:8092/picture/1to1_3dpic.xml");
                panoramaPlayer.playByXml(panoplayerurl);
                break;
            case 15:
                testPlaySpace3D();
                break;
            case 16:
                panoplayerurl.setXmlUrl("http://192.168.8.106:8092/sphere.xml");
                panoramaPlayer.playByXml(panoplayerurl);
                break;
            case 20:
                testPlayVideo2_1();
                break;
            case 21:
                testPlayF4OrF4Plus();
                break;
            case 22:
                testPlayF4OrF4PlusLive();
                break;
            case 23:
                testPlayM1();
                break;
            case 24:
                panoplayerurl.setXmlUrl("http://192.168.8.94:8092/video/fisheyevideo.xml");
                panoramaPlayer.playByXml(panoplayerurl);
                break;
            case 25:
                panoplayerurl.setXmlUrl("http://192.168.8.94:8092/video/fish2eyevideo.xml");
                panoramaPlayer.playByXml(panoplayerurl);
                break;
            case 3:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请输入xml", Toast.LENGTH_LONG).show();
                    return;
                }
                if (!paths.contains("xml")) {
                    Toast.makeText(getApplicationContext(), "输入有误，请重新输入", Toast.LENGTH_LONG).show();
                    return;
                }
                panoplayerurl.setXmlUrl(paths);
                panoramaPlayer.playByXml(panoplayerurl);
                break;
            case 1:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择图片", Toast.LENGTH_LONG).show();
                    return;
                }
                testPlayLocalPicture(paths);
                break;
            case 2:
                if (paths == null || paths.equals("")) {
                    Toast.makeText(getApplicationContext(), "请选择视频", Toast.LENGTH_LONG).show();
                    return;
                }
                testPlayLocalVideo(paths);
                break;
        }
    }
    private void testPlayPicture2_1() {
        if (IS_PLAY_BY_XML) {
            String Path = "http://media.detu.com/@/79945993-153D-446B-0781-B18F69922537/2018-01-17/5a5f0e7d89875-1000x500.jpg";
            String url = String.format(PLAYER_CONFIG, "sphere", Path, 0);
            PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
            panoplayerurl.setXmlContent(url);
            panoramaPlayer.playByXml(panoplayerurl);
        } else {
            PanoData panoData = new PanoData();
            PanoNodePreview nodePreview = new PanoNodePreview();
            panoData.nodePreview = nodePreview;
            PanoNodeImage nodeImage = new PanoNodeImage();
            nodeImage.panoResourceType = PanoResourceType.SPHERE;
            nodeImage.panoDeviceId = PanoDeviceId.PanoDeviceId_2_1;
            nodeImage.calibration = "";
            String url = "http://media.detu.com/@/79945993-153D-446B-0781-B18F69922537/2018-01-17/5a5f0e7d89875-1000x500.jpg";
            nodeImage.urls = new String[]{url};
            panoData.nodeImage = nodeImage;
            PanoNodeView nodeView = new PanoNodeView();
            panoData.nodeView = nodeView;
            panoramaPlayer.play(panoData);
        }
    }
    private void testPlayPictureCube() {
        if (IS_PLAY_BY_XML) {
            PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
            panoplayerurl.setXmlUrl("http://192.168.8.94:8092/panographics.xml");
            panoramaPlayer.playByXml(panoplayerurl, null);
        } else {
            String path = "http://media.detu.com/panod3597eabd93c6cc91111a5bda2ac5754/oper/panofile_html_%s.jpg";
            PanoData panoData = new PanoData();
            PanoNodePreview nodePreview = new PanoNodePreview();
            nodePreview.url = path;
            panoData.nodePreview = nodePreview;
            PanoNodeImage nodeImage = new PanoNodeImage();
            nodeImage.panoResourceType = PanoResourceType.CUBE;
            nodeImage.panoDeviceId = PanoDeviceId.PanoDeviceId_UNKNOW;
            nodeImage.calibration = "";
            String url = "";
            nodeImage.urls = new String[]{url};
            panoData.nodeImage = nodeImage;
            PanoNodeView nodeView = new PanoNodeView();
            panoData.nodeView = nodeView;
            panoramaPlayer.play(panoData);
        }
    }
    private void testPlaySpace3D() {
        String PLAYER_CONFIG =
                "<DetuVr>\n" +
                        "   <settings init='pano1' initmode='default' enablevr='false' title=''/>\n" +
                        "   <scenes>\n" +
                        "       <scene name='pano1' title='' thumburl=''>\n" +
                        "           <preview url='' />\n" +
                        "           <image type='obj3D' url='%s' device='%d' biaoding='%s'/>\n" +
                        "           <view viewmode='space3D' isptselect ='true' zScale ='5'/>\n" +
                        "       </scene>\n" +
                        "   </scenes>\n" +
                        "</DetuVr>";
        String path = "/mnt/sdcard/space3D/detu_vision_texture.jpg";
        String biaoding = "/mnt/sdcard/space3D/detu_vision_obj.obj";
        String url = String.format(PLAYER_CONFIG, path, 0, biaoding);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        panoramaPlayer.playByXml(panoplayerurl, null);
    }
    private PanoPlayerUrl local_panoplayerurl;
    private void testPlayVideo2_1() {
        
        String path = "http://media.detu.com/@/41020711-1591-C3CD-78FA-FB2F67437049/2017-06-05/593590081a66b-2048x1024.m3u8";
        


        String url = String.format(PLAYER_CONFIG, "video", path, 0);
        local_panoplayerurl = new PanoPlayerUrl();
        local_panoplayerurl.setXmlContent(url);



        panoramaPlayer.playByXml(local_panoplayerurl, null);
    }
    private void testPlayLocalPicture(String paths) {
        String url = String.format(PLAYER_CONFIG, "sphere", paths, -1);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        panoramaPlayer.playByXml(panoplayerurl, null);
    }
    private void testPlayLocalVideo(String paths) {
        String url = String.format(PLAYER_CONFIG, "video", paths, -1);
        this.local_panoplayerurl = new PanoPlayerUrl();
        local_panoplayerurl.setXmlContent(url);
        panoramaPlayer.playByXml(local_panoplayerurl, null);
    }
    private void testPlayF4OrF4Plus() {
        String path = "http://192.168.8.94:8092/f4data/F4Plus000523_220101AA_00/f4p11.mp4|http://192.168.8.94:8092/f4data/F4Plus000523_220101AA_00/f4p12.mp4|http://192.168.8.94:8092/f4data/F4Plus000523_220101AA_00/f4p13.mp4|http://192.168.8.94:8092/f4data/F4Plus000523_220101AA_00/f4p14.mp4";
        String url = String.format(PLAYER_CONFIG, "video", path, 4001);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        List<PanoPlayerOption> options = new ArrayList<PanoPlayerOption>(1);
        PanoPlayerOption optionCodec = new PanoPlayerOption(PanoPlayerOptionType.OPT_CATEGORY_CODEC, PanoOptionKey.DETU_HW_DECODER, String.valueOf(mIsHwDecoder));
        options.add(optionCodec);
        panoramaPlayer.playByXml(panoplayerurl, options);
        
    }
    private void testPlayF4OrF4PlusLive() {
        String path = "rtsp://192.168.155.101/live|rtsp://192.168.155.102/live|rtsp://192.168.155.103/live|rtsp://192.168.155.104/live";
        String url = String.format(PLAYER_CONFIG, "video", path, 4001);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        List<PanoPlayerOption> options = new ArrayList<PanoPlayerOption>(1);
        PanoPlayerOption optionCodec = new PanoPlayerOption(PanoPlayerOptionType.OPT_CATEGORY_CODEC, PanoOptionKey.DETU_HW_DECODER, String.valueOf(mIsHwDecoder));
        options.add(optionCodec);
        PanoPlayerOption optionTcp = new PanoPlayerOption(PanoPlayerOptionType.OPT_CATEGORY_FORMAT, PanoOptionKey.RTSP_TRANSPORT, "tcp");
        options.add(optionTcp);
        
        
        panoramaPlayer.playByXml(panoplayerurl, options);
    }
    private void testPlayM1() {
        String path = "rtmp://192.168.8.74:1935/0B603C_1/1";
        
        String url = String.format(PLAYER_CONFIG, "video", path, 4003);
        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        List<PanoPlayerOption> options = new ArrayList<PanoPlayerOption>(1);
        PanoPlayerOption optionCodec = new PanoPlayerOption(PanoPlayerOptionType.OPT_CATEGORY_CODEC, PanoOptionKey.DETU_HW_DECODER, String.valueOf(mIsHwDecoder));
        options.add(optionCodec);
        panoramaPlayer.playByXml(panoplayerurl, options);
        
    }
    public void play(PanoResourceType resourceType, PanoDeviceId deviceId, String calibration, String[] urls, List<PanoPlayerOption> options) {
        play(resourceType, deviceId, calibration, PanoViewMode.ORIGINAL, urls, options);
    }
    public void play(PanoResourceType resourceType, PanoDeviceId deviceId, String calibration, PanoViewMode viewMode, String[] urls, List<PanoPlayerOption> options) {
        PanoData panoData = new PanoData();
        PanoNodeImage nodeImage = new PanoNodeImage();
        nodeImage.panoResourceType = resourceType;
        nodeImage.panoDeviceId = deviceId;
        nodeImage.calibration = calibration;
        nodeImage.urls = urls;
        panoData.nodeImage = nodeImage;
        PanoNodeView nodeView = new PanoNodeView();
        nodeView.viewMode = viewMode;
        panoData.nodeView = nodeView;
        if (options == null) {
            options = new ArrayList<PanoPlayerOption>(1);
        }
        PanoPlayerOption optionCodec = new PanoPlayerOption(PanoPlayerOptionType.OPT_CATEGORY_CODEC, PanoOptionKey.DETU_HW_DECODER, String.valueOf(mIsHwDecoder));
        options.add(optionCodec);
        panoramaPlayer.play(panoData, options);
    }
    int athIndex = 1;
    @Override
    public void onClick(View view) {
        if (panoramaPlayer != null && ppSurfaceView != null) {
            if (R.id.btn_viewMode == view.getId()) {
                menuDialogMode.show();
            } else if (R.id.btn_chooseSwitch == view.getId()) {
                menuDialog.show();
            } else if (R.id.btn_replay == view.getId()) {
                Plugin plugin = panoramaPlayer.getPlugin();
                if (plugin instanceof VideoPlugin) {
                    curViewMode = panoramaPlayer.getCurrentPanoViewMode();
                    curGestureData = panoramaPlayer.getCurrentGesturedata();
                    isReplay = true;
                    ((VideoPlugin) plugin).refresh();
                    
                    String paths = getIntent().getStringExtra("path");
                    testPlayLocalVideo(paths);
                }
            } else if (R.id.btn_liteDecor == view.getId()) {
                menuDialogLiteDecor.show();
            }
        }
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (ppSurfaceView != null) {
            ppSurfaceView.onDestroy();
        }
        if (task != null) {
            task.cancel();
        }
    }
    @Override
    public void onResume() {
        super.onResume();
        ppSurfaceView.onResume();

        // 延迟调用，确保播放器完全初始化，避免空指针异常
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                startVideoPluginSafely();
            }
        }, 200); // 延迟200ms

        isScenesBackMusic = true;

        panoramaPlayer.resumeAllBackgroundMusic();
        Log.e(TAG, "onResume");
    }

    /**
     * 安全地启动视频插件，避免空指针异常
     */
    private void startVideoPluginSafely() {
        if (panoramaPlayer != null) {
            Plugin plugin = panoramaPlayer.getPlugin();
            if (plugin instanceof VideoPlugin) {
                try {
                    ((VideoPlugin) plugin).start();
                    Log.d(TAG, "Video plugin started successfully");
                } catch (Exception e) {
                    Log.e(TAG, "Failed to start video plugin: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        ppSurfaceView.onPause();
        Plugin plugin = panoramaPlayer.getPlugin();
        if (plugin instanceof VideoPlugin) {
            ((VideoPlugin) plugin).pause();
        }
        panoramaPlayer.onGLSurfaceViewPause();
        panoramaPlayer.pauseAllHotMusic();
        panoramaPlayer.pauseAllBackgroundMusic();
        panoramaPlayer.onGLSurfaceViewPause();
        Log.e(TAG, "onPause");
    }
    @Override
    public void onPanoPlayerStatusChanged(PanoPlayerStatus status, String tip) {
        switch (status) {
            case LOADING:
                if (panoramaPlayer.getCurrentPanoData().nodeImage.panoDeviceId.deviceId > 4000) {
                    panoramaPlayer.setCurrentImageDataCali(Calibration);
                    panoramaPlayer.setWeight(weightPath);
                }
                if (IS_TEST_LITE_DECOR) {
                    
                    LiteDecor liteDecor = new LiteDecor();
                    liteDecor.tag = "smart";
                    liteDecor.imageUrl = "/mnt/sdcard/3.png";
                    LayoutParams layoutParams = new LayoutParams();
                    layoutParams.displayType = DisplayType.GL_DISTORTED;
                    layoutParams.ath = 0f;
                    layoutParams.atv = 0f;
                    layoutParams.width = 0.17f;
                    layoutParams.height = 0.17f;
                    liteDecor.layoutParams = layoutParams;
                    panoramaPlayer.addLiteDecor(liteDecor);
                }
                Log.e(TAG, "onPanoPlayerStatusChanged: loading!");
                break;
            case LOADED:
                if (isReplay) {
                    isReplay = false;
                    panoramaPlayer.setViewMode(curViewMode);
                    panoramaPlayer.setCurrentGestureData(curGestureData);
                }
                panoramaPlayer.setPointSelecting(false);
                Log.e(TAG, "onPanoPlayerStatusChanged: loaded!");
                break;
            case ERROR:
                PanoPlayerError error = PanoPlayerError.parseByString(tip);
                switch (error) {
                    case LACK_CALIBRATION:
                        Log.e(TAG, "onPanoPlayerError: lack calibration!");
                        break;
                    case PLAY_MANAGER_DATA_IS_EMPTY:
                        Log.e(TAG, "onPanoPlayerError: PLAY MANAGER DATA IS EMPTY");
                        break;
                    case SETTING_DATA_IS_EMPTY:
                        Log.e(TAG, "onPanoPlayerError: SETTING DATA IS EMPTY!");
                        break;
                    case PANORAMALIST_IS_EMPTY:
                        Log.e(TAG, "onPanoPlayerError: PANORAMALIST IS EMPTY!");
                        break;
                    case PLAY_URL_IS_EMPTY:
                        Log.e(TAG, "onPanoPlayerError: PLAY URL IS EMPTY!");
                        break;
                    case IMAGE_LOAD_ERROR:
                        Log.e(TAG, "onPanoPlayerError: IMAGE LOAD ERROR");
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }
    private Handler uiHandler = new Handler();
    @Override
    public void onPanoPlayerConfigLoaded(int deviceId) {
        Log.e(TAG, "onPanoPlayerConfigLoaded: loading!");
        if (deviceId > 4000) {
            GetWeightAndMaskHelper getWeightAndMaskHelper = new GetWeightAndMaskHelper();
            M1Path = "/mnt/sdcard/M1WeightPath";
            F4PlusPath = "/mnt/sdcard/F4PlusWeightPath";
            if (deviceId == PanoDeviceId.PanoDeviceId_SPHERE_DETU_M1.deviceId) {
                File file = new File(M1Path);
                if (!file.exists()) {
                    file.mkdir();
                }
                weightPath = new String[]{M1Path + "/wt0.jpg", M1Path + "/wt1.jpg", M1Path + "/wt2.jpg", M1Path + "/wt3.jpg"};
                String biaodingPath = M1Path + "/biaoding.txt";
                File[] fileWeight = new File[4];
                for (int i = 0; i < 4; i++) {
                    fileWeight[i] = new File(weightPath[i]);
                }
                File biaodingFile = new File(biaodingPath);
                if ((fileWeight[0].exists()) && (biaodingFile.exists())) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        Calibration = FileReadandWrite.readtxt(biaodingFile);
                    }
                } else {
                    GetWeightAndMaskHelper helper = new GetWeightAndMaskHelper();
                    Calibration = helper.GetWgetWeightAndMaskInfo(M1Path + "/pat.pts", new int[]{1024, 512}, weightPath);
                    FileReadandWrite.writetxt(biaodingPath, Calibration);
                }
            } else {
                File file = new File(F4PlusPath);
                if (!file.exists()) {
                    file.mkdir();
                }
                weightPath = new String[]{F4PlusPath + "/wt0.jpg", F4PlusPath + "/wt1.jpg", F4PlusPath + "/wt2.jpg", F4PlusPath + "/wt3.jpg"};
                String biaodingPath = F4PlusPath + "/biaoding.txt";
                File[] fileWeight = new File[4];
                for (int i = 0; i < 4; i++) {
                    fileWeight[i] = new File(weightPath[i]);
                }
                File biaodingfile = new File(biaodingPath);
                if ((fileWeight[0].exists()) && (biaodingfile.exists())) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        Calibration = FileReadandWrite.readtxt(biaodingfile);
                    }
                } else {
                    GetWeightAndMaskHelper helper = new GetWeightAndMaskHelper();
                    String path = "A:_4000_3000_-3_-465_3915_3915_2_203.729_55_90_2_0_-0.346621_0.214988_44.621_14.0211_0_0_0_0_B:_4000_3000_16_-474_3919_3919_2_202.197_145.849_91.6581_-2.06139_0_-0.256364_0.110774_66.2789_6.20713_0_0_0_0_C:_4000_3000_5_-427_3864_3864_2_199.69_54.8259_-90.61_-179.144_0_-0.270679_0.142519_57.8891_6.8586_0_0_0_0_D:_4000_3000_15_-467_3942_3942_2_207.991_-33.6985_89.0577_-0.285306_0_-0.330378_0.183777_40.8338_-25.4735_0_0_0_0_&&";
                    Calibration = helper.GetWgetWeightAndMaskInfo(path, new int[]{1024, 512}, weightPath);
                    FileReadandWrite.writetxt(biaodingPath, Calibration);
                }
            }
        }
        Log.e(TAG, "onPanoPlayerConfigLoaded: finish!");
    }
    @Override
    public void onPanoPluginStateChanged(PanoPluginStatus status, String tip) {
        switch (status) {
            case PREPARED:
                Log.e(TAG, "onPanoPluginStateChanged, PREPARED!");
                break;
            case PLAYING:
                Log.e(TAG, "onPanoPluginStateChanged, PLAYING!");
                break;
            case PAUSE:
                Log.e(TAG, "onPanoPluginStateChanged, Pause!");
                break;
            case STOP:
                Log.e(TAG, "onPanoPluginStateChanged, Stop!");
                break;
            case FINISH:
                Log.e(TAG, "onPanoPluginStateChanged, Finish!");
                if (panoramaPlayer.getCurrentPanoData().nodeImage.panoDeviceId.deviceId > 4000) {
                    Plugin plugin = panoramaPlayer.getPlugin();
                    if (plugin != null) {
                        plugin.refresh();
                    }
                    
                }
                break;
            case CODEC_SWITCH:
                Log.e(TAG, "onPanoPluginStateChanged, Switch Codec:" + tip);
                break;
            case ERROR:
                PanoPluginError error = PanoPluginError.parseByString(tip);
                switch (error) {
                    case NETWORK:
                        Log.e(TAG, "*onPanoPluginStateChanged, Network Error!");
                        uiHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                panoramaPlayer.playByXml(local_panoplayerurl, null);
                                Toast.makeText(PanoramaPlayerActivity.this, "网络问题111", Toast.LENGTH_SHORT).show();
                            }
                        });
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
    }
    @Override
    public void onPanoPluginProgressChanged(long currentTime, long bufferTime, long duration) {
        int progress = (int) (currentTime * 1.0 / duration * 100);
        Log.e(TAG, "onPanoPluginProgressChanged:" + progress);
    }
    @Override
    public void PanoPlayOnTapBeforeHotPot(Hotspot hotspot) {
        Log.e(TAG, "PanoPlay OnTap Before HotPot");
    }
    @Override
    public void PanoPlayOnTapAfterHotPot(Hotspot hotspot, String name) {
        Log.e(TAG, "PanoPlay OnTap After HotPot");
    }
    @Override
    public void onClickPanoView(MotionEvent motionEvent) {
        PointF pointF = panoramaPlayer.calDegByWinPoint(motionEvent.getX(), motionEvent.getY());
        double x = Math.toDegrees(pointF.x);
        if (x > 270) {
            x = x - 360 - 90;
        } else {
            x = x - 90;
        }
        double y = 90 - Math.toDegrees(pointF.y);
        Log.i(TAG, "经纬度坐标点:" + x + "\t" + y);
        Hotspot hotspot = new Hotspot();
        hotspot.ath = (float) x;
        hotspot.atv = (float) y;
        hotspot.name = "";
        hotspot.style = "hello";
        hotspot.eventtype = "pop";
        hotspot.stylearg = "{\"text\":\"文字\"}";
        
        
        
        
        
    }
    
}

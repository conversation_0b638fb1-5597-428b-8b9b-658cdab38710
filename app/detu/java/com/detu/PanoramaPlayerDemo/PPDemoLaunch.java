package com.detu.PanoramaPlayerDemo;
import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import com.ijoyer.mobilecam.R;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
public class PPDemoLaunch extends AppCompatActivity implements View.OnClickListener {
    private static final String TAG = PPDemoLaunch.class.getSimpleName();
    private Unbinder unbinder;
    @BindView(R.id.tv_codec)
    TextView tvCodec;
    @SuppressLint("UseSwitchCompatOrMaterialCode")
    @BindView(R.id.switch_decoder)
    Switch switch_decoder;
    @BindView(R.id.et_path)
    EditText et_xmlPath;
    private void initView() {
        findViewById(R.id.btn_picture_2_1).setOnClickListener(this);
        findViewById(R.id.btn_picture_cube).setOnClickListener(this);
        findViewById(R.id.btn_picture_fishEye).setOnClickListener(this);
        findViewById(R.id.btn_picture_fishEye2).setOnClickListener(this);
        findViewById(R.id.btn_picture_real3d).setOnClickListener(this);
        findViewById(R.id.btn_picture_space3d).setOnClickListener(this);
        findViewById(R.id.btn_picture_f4pic).setOnClickListener(this);
        switch_decoder.setOnCheckedChangeListener(new Switch.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                tvCodec.setText(isChecked ? "硬解" : "软解");
            }
        });
        findViewById(R.id.btn_video_2_1).setOnClickListener(this);
        findViewById(R.id.btn_video_f4s).setOnClickListener(this);
        findViewById(R.id.btn_m1).setOnClickListener(this);
        findViewById(R.id.btn_video_f4sLive).setOnClickListener(this);
        findViewById(R.id.btn_video_fishEye).setOnClickListener(this);
        findViewById(R.id.btn_video_fishEye2).setOnClickListener(this);
        findViewById(R.id.btn_localPicture).setOnClickListener(this);
        findViewById(R.id.btn_localVideo).setOnClickListener(this);
        findViewById(R.id.btn_linkPlay).setOnClickListener(this);
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        unbinder.unbind();
    }
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        
        super.onCreate(savedInstanceState);
        setContentView(R.layout.panoplayerdemo_launch);
        unbinder = ButterKnife.bind(this);
        this.initView();
        getPermissionToReadUserContacts();
    }
    private static final int CODE_REQUEST_SCANNER = 100;
    private static final int CODE_REQUEST_LINK = 101;
    private static final int CODE_REQUEST_ALBUM_FILE = 102;
    private static final int REQUEST_CODE_PERMISSION_CAMERA = 103;
    private static final int REQUEST_CODE_PERMISSION_STORAGE = 104;
    private static final int MESSAGE_WHAT_PARSE_IMAGE = 1;
    private static final int MESSAGE_WHAT_PARSE_IMAGE_RESULT = 2;
    private String mStrXmlPath = "";
    private int mIntPlayTag = 0;
    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_picture_2_1:
                mIntPlayTag = 10;
                break;
            case R.id.btn_picture_cube:
                mIntPlayTag = 11;
                break;
            case R.id.btn_picture_fishEye:
                mIntPlayTag = 12;
                break;
            case R.id.btn_picture_fishEye2:
                mIntPlayTag = 13;
                break;
            case R.id.btn_picture_real3d:
                mIntPlayTag = 14;
                break;
            case R.id.btn_picture_space3d:
                mIntPlayTag = 15;
                break;
            case R.id.btn_picture_f4pic:
                mIntPlayTag = 16;
                break;
            case R.id.btn_video_2_1:
                mIntPlayTag = 20;
                break;
            case R.id.btn_video_f4s:
                mIntPlayTag = 21;
                break;
            case R.id.btn_video_f4sLive:
                mIntPlayTag = 22;
                break;
            case R.id.btn_m1:
                mIntPlayTag = 23;
                break;
            case R.id.btn_video_fishEye:
                mIntPlayTag = 24;
                break;
            case R.id.btn_video_fishEye2:
                mIntPlayTag = 25;
                break;
            case R.id.btn_localPicture:
                getPicFromAlbum(true);
                mIntPlayTag = 1;
                break;
            case R.id.btn_localVideo:
                getPicFromAlbum(false);
                mIntPlayTag = 2;
                break;
            case R.id.btn_linkPlay:
                mIntPlayTag = 3;
                mStrXmlPath = et_xmlPath.getText().toString();
                mStrXmlPath = "http://www.detu.com/ajax/pano/xml/412780";
                break;
        }
        if (view.getId() != R.id.btn_localVideo && view.getId() != R.id.btn_localPicture) {
            Intent intent = new Intent(this, PanoramaPlayerActivity.class);
            intent.putExtra("playTag", mIntPlayTag);
            intent.putExtra("path", mStrXmlPath);
            intent.putExtra("isHwDecoder", switch_decoder.isChecked());
            startActivity(intent);
        }
    }
    private void getPicFromAlbum(boolean isImage) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            Intent intent = new Intent(Intent.ACTION_PICK, android.provider.MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
            intent.setDataAndType(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, isImage ? "image/*" : "video/*");
            startActivityForResult(intent, CODE_REQUEST_ALBUM_FILE);







        } else {
            Intent intentFromGallery = new Intent();
            intentFromGallery.setType(isImage ? "image/*" : "video/*"); 
            intentFromGallery.setAction(Intent.ACTION_GET_CONTENT);
            startActivityForResult(intentFromGallery, CODE_REQUEST_ALBUM_FILE);
        }
    }
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == CODE_REQUEST_ALBUM_FILE && resultCode == RESULT_OK && null != data) {
            final Uri selectedImage = data.getData();
            if (!TextUtils.isEmpty(selectedImage.getAuthority())) {
                Cursor cursor = getContentResolver().query(selectedImage,
                        new String[]{MediaStore.Images.Media.DATA}, null, null, null);
                if (null == cursor) {
                    Toast.makeText(getApplicationContext(), "图片没找到", Toast.LENGTH_LONG).show();
                    return;
                }
                cursor.moveToFirst();
                mStrXmlPath = cursor.getString(cursor.getColumnIndex(MediaStore.Images.Media.DATA));
                Log.e(TAG, "onActivityResult: " + mStrXmlPath);
                cursor.close();
                Intent intent = new Intent(this, PanoramaPlayerActivity.class);
                intent.putExtra("playTag", mIntPlayTag);
                intent.putExtra("path", mStrXmlPath);
                intent.putExtra("isHwDecoder", switch_decoder.isChecked());
                startActivity(intent);
            } else {
                mStrXmlPath = selectedImage.getPath();
            }
        }
    }
    private static final int WRITE_EXTERNAL_STORAGE_REQUEST = 1;
    
    public void getPermissionToReadUserContacts() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            
            if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.WRITE_EXTERNAL_STORAGE)) {
                Toast.makeText(this, "需要有存储权限才能正常工作", Toast.LENGTH_SHORT).show();
            }
            ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE},
                    WRITE_EXTERNAL_STORAGE_REQUEST);
        }
    }
    
    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        
        if (requestCode == WRITE_EXTERNAL_STORAGE_REQUEST) {
            if (grantResults.length == 2 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "存储权限获得", Toast.LENGTH_SHORT).show();
            } else if (grantResults.length == 2 && grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "存储权限获取失败", Toast.LENGTH_SHORT).show();
            } else if (grantResults.length == 2 && grantResults[1] == PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "获取摄像头权限成功", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "获得摄像头权限失败", Toast.LENGTH_SHORT).show();
            }
        } else {
            super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }
}
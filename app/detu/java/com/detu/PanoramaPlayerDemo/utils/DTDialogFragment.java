package com.detu.PanoramaPlayerDemo.utils;
import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import androidx.annotation.RequiresApi;
import androidx.annotation.StyleRes;
import androidx.fragment.app.DialogFragment;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import com.blankj.utilcode.util.SizeUtils;
import com.ijoyer.mobilecam.R;

public class DTDialogFragment extends DialogFragment {
    private DisplayMetrics mDisplayMetrics;
    static int style;
    private View mView;
    private float mWidthPercentage = 0; 
    private float mHeightPercentage = 0; 
    private int mGravity = Gravity.CENTER;
    private DialogEvent dialogEvent;
    public static final DTDialogFragment newInstance(@StyleRes int styleId) {
        DTDialogFragment dialogFragment = new DTDialogFragment();
        style = styleId;
        return dialogFragment;
    }
    public void setDialogEvent(DialogEvent dialogEvent) {
        this.dialogEvent = dialogEvent;
    }
    public void setGravity(int gravity) {
        this.mGravity = gravity;
    }
    public void setView(View view) {
        this.mView = view;
    }
    public void setWidthPercentage(float widthPercentage) {
        this.mWidthPercentage = widthPercentage;
    }
    public void setHeightPercentage(float heightPercentage) {
        this.mHeightPercentage = heightPercentage;
    }
    @SuppressWarnings("unchecked")
    public <T> T findViewById(int viewId) {
        if (mView == null) {
            return null;
        }
        return (T) mView.findViewById(viewId);
    }
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (style != 0) {
            setStyle(STYLE_NO_FRAME, style);
        } else {
            setStyle(STYLE_NO_FRAME, R.style.DtDialog);
        }
        mDisplayMetrics = getActivity().getResources().getDisplayMetrics();
    }
    @Override
    public void onActivityCreated(Bundle arg0) {
        Dialog dialog = getDialog();
        if (mView == null || dialog == null) {
            super.onActivityCreated(arg0);
            return;
        }
        dialog.setContentView(mView);
        Window window = dialog.getWindow();
        WindowManager.LayoutParams attributes = window.getAttributes();
        attributes.gravity = mGravity;
        
        window.setAttributes(attributes);
        super.onActivityCreated(arg0);
    }
    @Override
    public void onResume() {
        super.onResume();
        Window window = getDialog().getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
            
            window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        }
    }
    @Override
    public void onCancel(DialogInterface dialog) {
        super.onCancel(dialog);
        if (dialogEvent != null) {
            dialogEvent.onCancel();
        }
    }
    @Override
    public void onDismiss(DialogInterface dialog) {
        super.onDismiss(dialog);
        if (dialogEvent != null) {
            dialogEvent.onDismiss();
        }
    }
    public interface DialogEvent {
        void onCancel();
        void onDismiss();
    }
}

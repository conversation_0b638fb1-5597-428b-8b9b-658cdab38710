package com.detu.PanoramaPlayerDemo.utils;
import android.graphics.Bitmap;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class BitmapUtils {
    public static void saveBitmap(Bitmap bitmap, String saveAbsolutePath) {
        try {
            File filePic = new File(saveAbsolutePath);
            FileOutputStream fos = new FileOutputStream(filePic);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, fos);
            fos.flush();
            fos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

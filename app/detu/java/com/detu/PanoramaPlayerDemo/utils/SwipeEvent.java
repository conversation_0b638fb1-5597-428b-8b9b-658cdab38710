package com.detu.PanoramaPlayerDemo.utils;
import android.content.Context;
import android.hardware.input.InputManager;
import android.os.SystemClock;
import android.view.InputDevice;
import android.view.InputEvent;
import android.view.MotionEvent;
import androidx.annotation.NonNull;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
public class SwipeEvent {
    
    private static final int INJECT_INPUT_EVENT_MODE_ASYNC = 0; 
    
    private static final int INJECT_INPUT_EVENT_MODE_WAIT_FOR_RESULT = 1;  
    
    private static final int INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH = 2;  
    
    public static void makeSwipeDown(@NonNull Context context, int fromX, int fromY, int toX, int toY, int step) {
        InputManager inputManager = (InputManager) context.getSystemService(Context.INPUT_SERVICE);
        int y = fromY;
        long downTime = SystemClock.uptimeMillis();
        long eventTime = SystemClock.uptimeMillis();
        
        MotionEvent motionEvent = null;
        motionEvent = MotionEvent.obtain(downTime, eventTime, MotionEvent.ACTION_DOWN, fromX, fromY, 0);
        
        motionEvent.setSource(InputDevice.SOURCE_TOUCHSCREEN);
        
        invokeInjectInputEventMethod(inputManager, motionEvent, INJECT_INPUT_EVENT_MODE_WAIT_FOR_RESULT);
        
        int stepCount = Math.abs(fromY - toY) / step;
        for (int ix = 0; ix < stepCount; ix++) {
            if (fromY > toY) {
                y -= step;
            } else {
                y += step;
            }
            motionEvent = MotionEvent.obtain(downTime, eventTime, MotionEvent.ACTION_MOVE, fromX, y, 0);
            motionEvent.setSource(InputDevice.SOURCE_TOUCHSCREEN);
            
            invokeInjectInputEventMethod(inputManager, motionEvent, INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH);
            
        }
        
        
        motionEvent = MotionEvent.obtain(downTime, eventTime, MotionEvent.ACTION_UP, toX, y, 0);
        motionEvent.setSource(InputDevice.SOURCE_TOUCHSCREEN);
        invokeInjectInputEventMethod(inputManager, motionEvent, INJECT_INPUT_EVENT_MODE_WAIT_FOR_FINISH);
        
    }
    private static void invokeInjectInputEventMethod(InputManager inputManager, InputEvent event, int mode) {
        Class<?> clazz = null;
        Method injectInputEventMethod = null;
        Method recycleMethod = null;
        try {
            clazz = Class.forName("android.hardware.input.InputManager");
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        try {
            injectInputEventMethod = clazz.getMethod("injectInputEvent", InputEvent.class, int.class);
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
        try {
            injectInputEventMethod.invoke(inputManager, event, mode);
            
            recycleMethod = event.getClass().getMethod("recycle");
            
            recycleMethod.invoke(event);
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        }
    }
}

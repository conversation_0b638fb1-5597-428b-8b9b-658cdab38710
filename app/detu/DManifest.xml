<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.icatch.mobilecam">
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <application
        android:allowBackup="true"
        android:debuggable="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/Theme.AppCompat.DayNight"
        android:usesCleartextTraffic="true"
        tools:ignore="HardcodedDebugMode">
        <activity
            android:name="com.detu.szStitch.StitchDemo"
            android:icon="@drawable/app_icon"
            android:label="Stitch"
            android:theme="@style/Theme.AppCompat.DayNight.Dialog">
            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.MAIN" />-->
            <!--                <category android:name="android.intent.category.LAUNCHER" />-->
            <!--            </intent-filter>-->
        </activity>
        <activity
            android:name="com.detu.PanoramaPlayerDemo.PanoramaPlayerActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:icon="@drawable/app_icon"
            android:launchMode="singleTask" />
        <activity
            android:name="com.ijoyer.camera.activity.PanoramaPlayerActivity"
            android:configChanges="keyboard|orientation|screenSize"
            android:icon="@drawable/app_icon"
            android:label="Player"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat">
            <!--            <intent-filter>-->
            <!--                <action android:name="android.intent.action.MAIN" />-->
            <!--                <category android:name="android.intent.category.LAUNCHER" />-->
            <!--            </intent-filter>-->
        </activity>
        <activity
            android:name="com.detu.PanoramaPlayerDemo.PPDemoLaunch"
            android:icon="@drawable/app_icon"
            android:label="PPDemo"
            android:theme="@style/Theme.AppCompat.DayNight.Dialog">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <meta-data
            android:name="com.picovr.type"
            android:value="vr" />
        <!--设置Vierwe平台VR 模式-->
        <meta-data
            android:name="com.softwinner.vr.mode"
            android:value="vr" />
        <meta-data
            android:name="com.picovr.display.orientation"
            android:value="0" />
        <!--授权范围-->
        <meta-data
            android:name="pico_scope"
            android:value="get_user_info" />
        <meta-data
            android:name="com.pvr.instructionset"
            android:value="0" />
        <!--是否显示平台动画-->
        <meta-data
            android:name="platform_logo"
            android:value="0" />
        <meta-data
            android:name="platform_high"
            android:value="0" />
        <!--设置Vierwe平台msaa-->
        <meta-data
            android:name="MSAA"
            android:value="2" />
        <meta-data
            android:name="com.pvr.hmd.trackingmode"
            android:value="6dof" />
        <meta-data
            android:name="isPUI"
            android:value="0" />
        <meta-data
            android:name="pico_merchant_id"
            android:value="81" />
        <meta-data
            android:name="pico_app_id"
            android:value="5a189befeb3b33f7df101fbecffe4f98" />
        <meta-data
            android:name="pico_app_key"
            android:value="25ba00fb73343ff1ec32e1c152fff291" />
        <meta-data
            android:name="pico_pay_key"
            android:value="d828c5d3a1cc11e6b7fe008cfaf3d930" />
        <meta-data
            android:name="pico_redirectUrl"
            android:value="http://www.picovr.com" />
    </application>
</manifest>
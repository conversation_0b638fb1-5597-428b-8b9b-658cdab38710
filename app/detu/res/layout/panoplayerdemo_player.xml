<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView
        android:id="@+id/pp_surfaceView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView>
    <TextView
        android:id="@+id/tv_fov"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:textSize="@dimen/text_size_20" />
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="10dp"
        android:orientation="horizontal">
        <Button
            android:id="@+id/btn_viewMode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="模式选择" />
        <Button
            android:id="@+id/btn_chooseSwitch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择开关" />
        <Button
            android:id="@+id/btn_replay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="重播" />
        <Button
            android:id="@+id/btn_liteDecor"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="轻热点" />
    </LinearLayout>
</RelativeLayout>

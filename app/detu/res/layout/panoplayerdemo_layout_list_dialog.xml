<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginLeft="0dp"
    android:layout_marginRight="0dp"
    android:background="@drawable/inner_dialog_bg"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/full_transparent">
        <ImageButton
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/dp_10"
            android:background="@color/full_transparent"
            android:foreground="@color/full_transparent"
            android:padding="@dimen/dp_8"
            android:src="@drawable/ic_arrow_back_white_24dp" />
        <TextView
            android:id="@+id/tv_frame_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:background="@color/full_transparent"
            android:gravity="center"
            android:padding="15dp"
            android:text="@string/app_name"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:visibility="gone" />
    </RelativeLayout>
    <View
        android:id="@+id/view_separator"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@android:color/white" />
    <ListView
        android:id="@+id/lv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:cacheColorHint="@color/listitem_gray"
        android:dividerHeight="0.9dp" />
</LinearLayout>

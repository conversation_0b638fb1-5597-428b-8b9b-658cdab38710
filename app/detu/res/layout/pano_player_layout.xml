<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView
        android:id="@+id/pp_surfaceView"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent" />

    android:src="@drawable/ic_pause_white"

    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_centerInParent="true"
        android:adjustViewBounds="true"
        android:background="@drawable/shape_bg_half_grey_circle"
        android:padding="10dp"
        android:src="@drawable/ic_pause_white"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_edit_photo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/dp_10"
        android:background="@drawable/shape_popup_round_angle_grey_view"
        android:orientation="vertical"
        android:padding="@dimen/dp_10"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_contrast_ratio"
                android:textColor="@color/white" />

            <com.warkiz.widget.IndicatorSeekBar
                android:id="@+id/sb_contrast_ratio"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:isb_show_indicator="none"
                app:isb_show_thumb_text="false"
                app:isb_thumb_color="@color/colorPrimary"
                app:isb_thumb_size="15dp"
                app:isb_track_background_color="@color/text_line"
                app:isb_track_progress_color="@color/colorPrimary"
                app:isb_track_progress_size="6dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_exposure"
                android:textColor="@color/white" />

            <com.warkiz.widget.IndicatorSeekBar
                android:id="@+id/sb_exposure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:isb_show_indicator="none"
                app:isb_show_thumb_text="false"
                app:isb_thumb_color="@color/colorPrimary"
                app:isb_thumb_size="15dp"
                app:isb_track_background_color="@color/text_line"
                app:isb_track_progress_color="@color/colorPrimary"
                app:isb_track_progress_size="6dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_saturation"
                android:textColor="@color/white" />

            <com.warkiz.widget.IndicatorSeekBar
                android:id="@+id/sb_saturation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:isb_show_indicator="none"
                app:isb_show_thumb_text="false"
                app:isb_thumb_color="@color/colorPrimary"
                app:isb_thumb_size="15dp"
                app:isb_track_background_color="@color/text_line"
                app:isb_track_progress_color="@color/colorPrimary"
                app:isb_track_progress_size="6dp" />


        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/local_pb_bottom_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/item_height"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/dp_10"
        android:background="@drawable/shape_popup_round_angle_grey_view"
        android:orientation="horizontal"
        android:padding="@dimen/dp_10">

        <ImageButton
            android:id="@+id/btn_prev"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@drawable/selector_transparent2gray"
            android:src="@drawable/icon_left"
            android:text="上一张"
            android:textColor="@color/grayWhite"
            android:visibility="visible" />

        <ImageButton
            android:id="@+id/btn_next"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@drawable/selector_transparent2gray"
            android:src="@drawable/icon_right"
            android:text="下一张"
            android:textColor="@color/grayWhite"
            android:visibility="visible" />

        <Button
            android:id="@+id/btn_turn_up"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@drawable/selector_transparent2gray"
            android:text="@string/turn_up"
            android:textColor="@color/grayWhite"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_turn_down"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@drawable/selector_transparent2gray"
            android:text="@string/turn_down"
            android:textColor="@color/grayWhite"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_chooseSwitch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1.6"
            android:background="@drawable/selector_transparent2gray"
            android:text="@string/function"
            android:textColor="@color/grayWhite"
            android:visibility="visible" />

        <Button
            android:id="@+id/btn_viewMode"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1.8"
            android:background="@drawable/selector_transparent2gray"
            android:gravity="center"
            android:text="@string/vr"
            android:textColor="@color/grayWhite"
            android:visibility="visible" />

        <Button
            android:id="@+id/btn_replay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_5"
            android:layout_weight="1"
            android:background="@drawable/selector_transparent2gray"
            android:text="重播"
            android:textColor="@color/grayWhite"
            android:visibility="gone" />

        <ImageButton
            android:id="@+id/local_photo_pb_delete"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="0.6"
            android:background="@drawable/selector_transparent2gray"
            android:src="@drawable/ic_delete_white_24dp" />

        <TextView
            android:id="@+id/panorama_type_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:background="@drawable/selector_transparent2gray"
            android:gravity="center"
            android:text="@string/text_panorama"
            android:textColor="@color/white"
            android:visibility="gone" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/local_pb_top_layout"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:layout_marginTop="@dimen/dp_25"
        android:background="@color/full_transparent"
        android:orientation="horizontal">

        <ImageButton
            android:id="@+id/local_pb_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="@dimen/dp_10"
            android:background="@drawable/shape_bg_half_grey_circle"
            android:padding="@dimen/dp_8"
            android:src="@drawable/ic_arrow_back_white_24dp" />

        <TextView
            android:id="@+id/txt_record"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp_10"
            android:background="@drawable/shape_popup_round_angle_grey_view"
            android:padding="@dimen/dp_10"
            android:text="@string/record_screen"
            android:textColor="@color/white" />

        <TextView
            android:id="@+id/tv_edit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_toStartOf="@+id/txt_record"
            android:background="@drawable/shape_popup_round_angle_grey_view"
            android:padding="@dimen/dp_10"
            android:text="@string/edit"
            android:textColor="@color/white"
            android:visibility="gone" />


        <TextView
            android:id="@+id/tv_save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_toStartOf="@+id/tv_edit"
            android:background="@drawable/shape_popup_round_angle_grey_view"
            android:padding="@dimen/dp_10"
            android:text="@string/action_save"
            android:textColor="@color/white"
            android:visibility="gone" />

        <TextView
            android:id="@+id/tv_reduction"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="@dimen/dp_10"
            android:layout_toStartOf="@+id/tv_save"
            android:background="@drawable/shape_popup_round_angle_grey_view"
            android:padding="@dimen/dp_10"
            android:text="@string/action_reduction"
            android:textColor="@color/white"
            android:visibility="gone" />
    </RelativeLayout>

    <ImageView
        android:id="@+id/iv_record"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_below="@+id/local_pb_top_layout"
        android:layout_margin="@dimen/dp_10"
        android:adjustViewBounds="true" />

    <ImageView
        android:id="@+id/iv_test"
        android:layout_width="200dp"
        android:layout_height="100dp"
        android:layout_below="@+id/iv_record" />

</RelativeLayout>

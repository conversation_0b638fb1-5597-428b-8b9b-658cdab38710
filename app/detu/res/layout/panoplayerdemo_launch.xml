<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical"
    tools:context="com.detu.PanoramaPlayerDemo.PPDemoLaunch">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_5"
        android:background="@android:color/holo_blue_light">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:text="图片" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="fill_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp_5">
        <Button
            android:id="@+id/btn_picture_2_1"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="2_1" />
        <Button
            android:id="@+id/btn_picture_cube"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="cube" />
        <Button
            android:id="@+id/btn_picture_fishEye2"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="双鱼眼" />
        <Button
            android:id="@+id/btn_picture_fishEye"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="鱼眼" />
        <Button
            android:id="@+id/btn_picture_real3d"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="maxf8" />
        <Button
            android:id="@+id/btn_picture_space3d"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="3D" />
        <Button
            android:id="@+id/btn_picture_f4pic"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="f4pic" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp_5"
        android:layout_marginTop="@dimen/dp_40"
        android:background="@android:color/holo_blue_light"
        android:orientation="horizontal"
        android:padding="@dimen/dp_5">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:text="视频" />
        <TextView
            android:id="@+id/tv_codec"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="50dp"
            android:padding="8dp"
            android:text="硬解" />
        <Switch
            android:id="@+id/switch_decoder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true"
            android:textOff="软解"
            android:textOn="硬解"
            tools:ignore="UseSwitchCompatOrMaterialXml" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="fill_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/dp_5">
        <Button
            android:id="@+id/btn_video_2_1"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="2_1" />
        <Button
            android:id="@+id/btn_video_fishEye"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="鱼眼" />
        <Button
            android:id="@+id/btn_video_fishEye2"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="双鱼眼" />
        <Button
            android:id="@+id/btn_video_f4s"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="F4/F4PLUS" />
        <Button
            android:id="@+id/btn_video_f4sLive"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="F4/F4PLUS live" />
        <Button
            android:id="@+id/btn_m1"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp_72"
            android:layout_weight="1"
            android:text="m1" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_40"
        android:orientation="vertical"
        android:padding="@dimen/dp_5">
        <Button
            android:id="@+id/btn_localPicture"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="本地图片播放" />
        <Button
            android:id="@+id/btn_localVideo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="本地视频播放" />
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp_30"
        android:orientation="vertical"
        android:padding="@dimen/dp_5">
        <EditText
            android:id="@+id/et_path"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="请输入链接(xml)"
            android:text="http://172.27.35.1:8090/testxml.xml" />
        <Button
            android:id="@+id/btn_linkPlay"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="链接播放" />
    </LinearLayout>
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE resources [
    <!ENTITY license_app_name "MobileCam">
    <!ENTITY license_full_name "ICATCH TECHNOLOGY, INC.">
    <!ENTITY license_privacy_url "https://www.icatchtek.com/privacy">
    <!ENTITY license_address "19-1, Innovation 1st Road, Hsinchu Science Park, Taiwan 300">
    <!ENTITY license_contact_email "<EMAIL>">
    ]>
<resources>
    <string name="app_name" translatable="false">MobileCam</string>
    <string name="action_search">Search</string>
    <string name="action_setting">Setting</string>
    <string name="preview">Preview</string>
    <string name="capture">capture</string>
    <string name="mpb">Multiple Playback</string>
    <string name="spb">Single Playback</string>
    <string name="camera_setting">Setting</string>
    <string name="invalid_device">Please download correct APP</string>
    <string name="title_car_mode">Car Mode</string>
    <string name="setting_slow_mo">Slow-Motion Function</string>
    <string name="setting_on">ON</string>
    <string name="setting_off">OFF</string>
    <string name="setting_timelapse_set">Please turn off Timelapse to set this value</string>
    <string name="setting_car_mode_set">Please turn off Car Mode to set this value</string>
    <string name="setting_burst_set">Please turn off Burst Capture to set this value</string>
    <string name="setting_slow_motion_set">Please turn off Slow Motion to set this value</string>
    <string name="stream_title">Camera Preview</string>
    <string name="stream_set_complete">Set Success</string>
    <string name="stream_set_timer">Timing set</string>
    <string name="stream_set_timer_off">Off</string>
    <string name="stream_set_res_vid">Set Video Resolution</string>
    <string name="stream_set_res_photo">Shooting resolutionsetting</string>
    <string name="stream_set_error">Set Failed</string>
    <string name="ptp_connected">PTP Connected</string>
    <string name="stream_error_cannot_open_gallery">Disabled while capturing or recording</string>
    <string name="stream_error_capturing">Capturing cannot switch</string>
    <string name="stream_error_not_enough_space">Not enough space!</string>
    <string name="stream_error_recording">Recording cannot switch</string>
    <string name="stream_error_capturing_capture">Processing...</string>
    <string name="stream_capturing">Capturing...</string>
    <string name="stream_capture_failed">Capture failed, please retry</string>
    <string name="stream_wait_for_video">Please wait</string>
    <string name="stream_exit_wait">Please wait</string>
    <string name="stream_exit_recording">Press again to stop recording and exit</string>
    <string name="stream_exiting">Exiting...</string>
    <string name="stream_failed">Video stream lost, please get closer to the device and reconnect.</string>
    <string name="stream_reconnect">Reconnect</string>
    <string name="stream_zoom_wait">Setting...</string>
    <string name="setting">Setting</string>
    <string name="setting_yes">Yes</string>
    <string name="setting_no">No</string>
    <string name="setting_power_supply">Power frequency</string>
    <string name="setting_format">Format Camera</string>
    <string name="setting_format_confirm">Format Camera</string>
    <string name="setting_format_desc">Format the SD and all your data will be lost!</string>
    <string name="setting_formating">Formating...</string>
    <string name="setting_formatted">Format SD card</string>
    <string name="setting_capture_delay">Timelapse</string>
    <string name="setting_capture_delay_v2">Timelapse</string>
    <string name="setting_image_size">Image size</string>
    <string name="setting_video_size">Video size</string>
    <string name="title_burst">Continuous Shooting</string>
    <string name="setting_burst_interval">Continuous Shooting Interval</string>
    <string name="setting_cap_timescape_interval">Timelapse Interval</string>
    <string name="setting_cap_timescape_duration">Timelapse Duration</string>
    <string name="setting_vid_timescape_interval">Video Timelapse Interval</string>
    <string name="setting_vid_timescape_duration">Video Timelapse Duration</string>
    <string name="setting_datestamp">Date mark</string>
    <string name="setting_about">About</string>
    <string name="setting_app_version">App Version</string>
    <string name="setting_product_name">Model No</string>
    <string name="setting_firmware_version">Firmware version</string>
    <string name="title_awb">White Balance</string>
    <string name="dialog_no_sd">No SD card, please insert and try again</string>
    <string name="setting_no_sd">No SD card to format</string>
    <string name="dialog_refreshing">Refreshing Wi-Fi...</string>
    <string name="dialog_connect_failed">Connection failed, please retry.</string>
    <string name="dialog_failed">Operation failed!</string>
    <string name="dialog_capturing">Capturing</string>
    <string name="dialog_preview">Preview Loading...</string>
    <string name="dialog_connecting">Connecting...</string>
    <string name="dialog_connecting_to_cam">Connecting with camera...</string>
    <string name="dialog_reconnect">Reconnecting</string>
    <string name="dialog_configuring">Configuring, please wait...</string>
    <string name="dialog_invalid_ap">"Doesn't seem to be connecting to a camera, please select another Wi-Fi!"</string>
    <string name="dialog_deleting">Deleting...</string>
    <string name="dialog_delete_failed_single">Error while deleting</string>
    <string name="dialog_deleting_failed_multi">Error while deleting, $1$ items not deleted,please check whether the files are locked or not</string>
    <string name="dialog_downloading">Downloading $1$/$2$...</string>
    <string name="dialog_downloaded">"$2$ pictures downloaded to $3$"</string>
    <string name="dialog_downloaded_2">"$2$ items downloaded to $3$"</string>
    <string name="dialog_downloaded_skipped">"$2$ pictures downloaded.(Skipped $1$ videos and $4$ downloaded pictures)"</string>
    <string name="dialog_downloaded_skipped_2">"$2$ items downloaded.(Skipped $1$ downloaded items)"</string>
    <string name="dialog_downloaded_single">"Picture has been downloaded to $1$"</string>
    <string name="dialog_downloading_single">Downloading...</string>
    <string name="dialog_cancel_downloading_succeeded">"Succeeded to cancel downloading"</string>
    <string name="dialog_cancel_downloading_failed">"Failed to cancel downloading""</string>
    <string name="dialog_wifi_lost">Cannot connect to Wi-Fi, please retry.</string>
    <string name="dialog_btn_reconnect">Reconnect</string>
    <string name="dialog_btn_continue">Continue</string>
    <string name="dialog_btn_exit">Exit</string>
    <string name="dialog_not_enough_space">Handset does not have enough storage, please delete some files and restart.</string>
    <string name="dialog_recording_card_full">Warning: SD card full, video recording stopped.</string>
    <string name="dialog_sd_card_is_full">Warning: SD card full, cannot record or take pictures.</string>
    <string name="dialog_connect_lose_exit_page">Connection exception, please try to re-enter the page</string>
    <string name="dialog_card_error">SD card error!</string>
    <string name="dialog_card_not_exist">SD card is not inserted, cannot perform capture/recording.</string>
    <string name="dialog_card_inserted">SD card inserted</string>
    <string name="dialog_card_removed">SD card is removed</string>
    <string name="dialog_card_full">SD card is full</string>
    <string name="dialog_card_lose">SD card lost</string>
    <string name="dialog_timeout">Camera connection failed, please ensure the WIFI connection works.</string>
    <string name="ok">OK</string>
    <string name="go_to_permiss">go to open the permiss</string>
    <string name="gallery_view_video_not_supported">Video viewing not supported</string>
    <string name="gallery_download_video_not_supported">Video downloading not supported</string>
    <string name="gallery_no_image">No files to download</string>
    <string name="gallery_selection_count">$1$/$2$ Selected</string>
    <string name="gallery_delete_des">Confirm deleting $1$ files?</string>
    <string name="gallery_download_msg">Confirm download $1$ files?</string>
    <string name="gallery_download_with_vid_msg">"Total Files: $1$,Download Time: $2$ mins and $3$ seconds,Confirm download?"</string>
    <string name="gallery_download_pic_msg">Confirm download $1$ pictures?</string>
    <string name="gallery_download_msg_2">"Confirm download $1$ files?($2$ skipped videos)"</string>
    <string name="gallery_download">Download</string>
    <string name="gallery_delete">Delete</string>
    <string name="gallery_cancel">Cancel</string>
    <string name="gallery_no_file_selected">No file selected</string>
    <string name="gallery_share_wifi">Please select Wi-Fi</string>
    <string name="gallery_share_to">Share to...</string>
    <string name="gallery_cannot_view_video">Video viewing not supported</string>
    <string name="gallery_cannot_download_video">Cannot download video</string>
    <string name="image_delete_des">Confirm to delete this photo?</string>
    <string name="image_download_msg">Confirm download?</string>
    <string name="no_pics">No picture to view</string>
    <string name="low_battery">Low battery, please recharge the device.</string>
    <string name="unsupported_mode">The device does not support this mode of operation</string>
    <string name="pb_video">Video playback</string>
    <string name="camera_awake_success">Succeed to awake camera</string>
    <string name="camera_awake_failed">Failed to awake camera</string>
    <string name="dialog_delete_all">All delete</string>
    <string name="dialog_awake_all">All awake</string>
    <string name="camera_awake">Awake camera</string>
    <string name="camera_sleep">Sleep camera</string>
    <string name="camera_sleep_alert">Connect to camera will lose if sleep camera,comfirm to do?</string>
    <string name="wb_auto">Auto</string>
    <string name="wb_daylight">Sunlight</string>
    <string name="wb_cloudy">Cloudy</string>
    <string name="wb_fluorescent">Fluorescent</string>
    <string name="wb_incandescent">Incandescent</string>
    <string name="burst_off">OFF</string>
    <string name="burst_3">3 photos</string>
    <string name="burst_5">5 photos</string>
    <string name="burst_10">10 photos</string>
    <string name="burst_7">7 photos</string>
    <string name="burst_15">15 photos</string>
    <string name="burst_30">30 photos</string>
    <string name="burst_hs">Continuous</string>
    <string name="dateStamp_off">OFF</string>
    <string name="dateStamp_date">Date</string>
    <string name="dateStamp_date_and_time">Date and time</string>
    <string name="frequency_50HZ" translatable="false">50HZ</string>
    <string name="frequency_60HZ" translatable="false">60HZ</string>
    <string name="download_manager">Download Management</string>
    <string name="download_progress">Completed: $1$    Wait: $2$    Failed: $3$</string>
    <string name="downloading_quit">All task of downloading will be cancelled,are you sure?</string>
    <string name="check_wifi_policy">Please keep to always connecting Wi-Fi</string>
    <string name="setting_preview_cache_duration">Preview cache duration</string>
    <string name="setting_time_lapse_interval">Delay Timer</string>
    <string name="setting_time_lapse_interval_off">OFF</string>
    <string name="setting_time_lapse_interval_2s">2 Sec</string>
    <string name="setting_time_lapse_interval_5s">5 Sec</string>
    <string name="setting_time_lapse_interval_10s">10 Sec</string>
    <string name="setting_time_lapse_interval_20s">20 Sec</string>
    <string name="setting_time_lapse_interval_30s">30 Sec</string>
    <string name="setting_time_lapse_interval_1M">1 Min</string>
    <string name="setting_time_lapse_interval_5M">5 Min</string>
    <string name="setting_time_lapse_interval_10M">10 Min</string>
    <string name="setting_time_lapse_interval_30M">30 Min</string>
    <string name="setting_time_lapse_interval_1HR">60 Min</string>
    <string name="setting_time_lapse_duration">Timelapse</string>
    <string name="setting_time_lapse_duration_2M">2 Min</string>
    <string name="setting_time_lapse_duration_5M">5 Min</string>
    <string name="setting_time_lapse_duration_10M">10 Min</string>
    <string name="setting_time_lapse_duration_15M">15 Min</string>
    <string name="setting_time_lapse_duration_20M">20 Min</string>
    <string name="setting_time_lapse_duration_30M">30 Min</string>
    <string name="setting_time_lapse_duration_60M">60 Min</string>
    <string name="setting_time_lapse_duration_unlimit">Unlimited</string>
    <string name="capture_start">Start capturing...</string>
    <string name="capture_completed"> Capture completed...</string>
    <string name="title_timeLapse_mode">Timelapse mode</string>
    <string name="timeLapse_capture_mode">Timelapse photo mode</string>
    <string name="timeLapse_video_mode">Timelapse recording mode</string>
    <string name="timeLapse_not_allow">Operation failed,please check the interval(be not OFF)</string>
    <string name="timeLapse_stop">Timelapse stopped!</string>
    <string name="title_slow_motion">Slow motion</string>
    <string name="slowmotion">Slow-motion fuction</string>
    <string name="upside">Vertical Flip</string>
    <string name="action_processing">Processing...</string>
    <string name="message_reconnect">newwork is abnormal ,is reconnectting...</string>
    <string name="event_stream_abnormal">camera stream is abnormal!</string>
    <string name="not_support_preview">Camera preview do not supportted for this size,confirm it?</string>
    <string name="camera_wifi_configuration">Camera Wi-Fi configuration</string>
    <string name="camera_configuration_set">Set</string>
    <string name="password_limit">Password must be digit, and length must between 8 and 10</string>
    <string name="camera_wifi_name">Camera Wi-Fi name</string>
    <string name="camera_wifi_password">Camera Wi-Fi password</string>
    <string name="camera_name_limit">Wi-Fi Name length must not more  between 1 and 20</string>
    <string name="connect_control">Connect and Control</string>
    <string name="title_activity_local_photo_wall">Local photowall </string>
    <string name="title_activity_local_video_wall">Local videowall </string>
    <string name="download_complete_result">"Download completed,successed:$1$, failed:$2$"</string>
    <string name="no_files_found">No files found!</string>
    <string name="need_permition">No permition!</string>
    <string name="check_mac_fail">No permition in DEMO APP!  Please contact with your APP Supplier! </string>
    <string name="setting_updateFW_prompt">Do you want to update FW?</string>
    <string name="setting_update_fw">Update FW</string>
    <string name="setting_updatefw_title">Automatic update FW</string>
    <string name="setting_updatefw_start">Updating FW...</string>
    <string name="setting_updatefw_success">Successfully Updated FW</string>
    <string name="setting_updatefw_failed">Updated failed</string>
    <string name="setting_updatefw_closeAppInfo">FW update has completed, please restart your application</string>
    <string name="setting_updatefw_completedInfo">FW update has completed</string>
    <string name="setting_updatefw_failedInfo">Update FW failure, check to see if this feature is supported or if positive firmware is used.</string>
    <string name="setting_auto_download">Auto download</string>
    <string name="setting_audio_switch">Audio switch</string>
    <string name="setting_auto_download_size_limit">Limit of Auto download Size </string>
    <string name="setting_card_removed">No SD card to update FW</string>
    <string name="camera_pwd_setting">camera pwd setting</string>
    <string name="alert_pwd_needed">Please input password!</string>
    <string name="alert_uid_needed">Please input Uid!</string>
    <string name="alert_camera_name_needed">Please input camera name!</string>
    <string name="alert_uid_repeated">Uid has already exsited!</string>
    <string name="alert_no_camera_found">No camera found!</string>
    <string name="alert_is_capturing_or_recording">Failed! Camera is capturing or recording!</string>
    <string name="text_resolution_lowest">Stream resolution has been the lowest!</string>
    <string name="text_resolution_adjust">Adjusting stream resolution...</string>
    <string name="text_operation_success">Opertaion succeed</string>
    <string name="dialog_reconnect_failed">Failed to reconnecting!</string>
    <string name="text_operation_failed">Opertaion failed!</string>
    <string name="text_stream_launch_failed">Failed to launch stream!</string>
    <string name="text_camera_not_ready">Wait for connceting</string>
    <string name="text_camera_is_connecting">Is connceting...</string>
    <string name="text_camera_connected">Connceted</string>
    <string name="text_camera_connection_failed">Failed to connect</string>
    <string name="setting_title_stream">Choose stream</string>
    <string name="alert_old_pwd_verify_failed">Old password is not correct!</string>
    <string name="alert_not_same_password">New passwords are not the same!</string>
    <string name="button_check_current_wifi_list">Check current Wi-Fi</string>
    <string name="setting_title_choose_wifi">Choose Wi-Fi</string>
    <string name="camera_abnormal">Camera abnormal!</string>
    <string name="nearby_camera">Nearby camera</string>
    <string name="add_to">Add to</string>
    <string name="dialog_init_network">Initializing Network...</string>
    <string name="dialog_select_wifi">Select wifi!</string>
    <string name="title_activity_local_video" translatable="false">LocalVideoActivity</string>
    <string name="dialog_local_memory_not_enough">Local memory is not enough to download</string>
    <string name="title_activity_local_photo" translatable="false">LocalPhotoActivity</string>
    <string name="text_not_support_preview">Does not support preview</string>
    <string name="current_size_not_supported_sound_off">The current size is not supported on or off sound</string>
    <string name="title_activity_pb_local_video" translatable="false">PbLocalVideoActivity</string>
    <string name="title_local_media">Media on My Phone</string>
    <string name="title_video">Videos</string>
    <string name="title_photo">Photos</string>
    <string name="title_activity_preview" translatable="false">PreviewActivity</string>
    <string name="title_emergency_video">Emergency video</string>
    <string name="dummy_button">Dummy Button</string>
    <string name="dummy_content">DUMMY\nCONTENT</string>
    <string name="action_title_date_stamp">Date stamp</string>
    <string name="delay_capture">Delay capture</string>
    <string name="title_delay_capture">Delay capture</string>
    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="action_settings">Settings</string>
    <string name="action_back">back</string>
    <string name="title_audio_switch">Audio switch</string>
    <string name="title_photo_size">Photo size</string>
    <string name="title_video_size">Video size</string>
    <string name="menu_list">List</string>
    <!-- Example General settings -->
    <string-array name="pref_example_list_titles">
        <item>Always</item>
        <item>When possible</item>
        <item>Never</item>
    </string-array>
    <string-array name="pref_example_list_values">
        <item>1</item>
        <item>0</item>
        <item>-1</item>
    </string-array>
    <string-array name="pref_sync_frequency_titles">
        <item>15 minutes</item>
        <item>30 minutes</item>
        <item>1 hour</item>
        <item>3 hours</item>
        <item>6 hours</item>
        <item>Never</item>
    </string-array>
    <string-array name="pref_sync_frequency_values">
        <item>15</item>
        <item>30</item>
        <item>60</item>
        <item>180</item>
        <item>360</item>
        <item>-1</item>
    </string-array>
    <string name="text_password">Password</string>
    <string name="text_hint_pwd_needed">Please enter password</string>
    <string name="title_activity_multi_pb">MultiPb</string>
    <string name="title_activity_local_pb">LocalPb</string>
    <string name="title_setting">Setting</string>
    <string name="title_preview">Preview</string>
    <string name="action_save">Save</string>
    <string name="action_reduction">Reduction</string>
    <string name="title_activity_video_pb" translatable="false">VideoPbActivity</string>
    <string name="title_activity_photo_pb" translatable="false">PhotoPbActivity</string>
    <string name="text_sd_card_memory_shortage">sd card memory shortage!</string>
    <string name="title_activity_add_new_cam">Add New Camera</string>
    <string name="title_fragment_btpair_begin">Pairing</string>
    <string name="title_fragment_btpair_wifisetup">Wi-Fi setup</string>
    <string name="title_fragment_btpair_completed">Completed</string>
    <string name="text_btpair">Bluetooth Pair</string>
    <string name="text_ready_pair">Make sure your camera is ready for pairing and type the right PIN.</string>
    <string name="text_btpair_try_again">Try Again</string>
    <string name="text_btpair_search_camera">Search</string>
    <string name="text_btpair_search_ble">Search BLE</string>
    <string name="text_btpair_setup">Setup</string>
    <string name="text_btpair_skip">SKIP</string>
    <string name="text_btpair_next">Next</string>
    <string name="text_btpair_done">Done</string>
    <string name="text_btpair_already_connect_cam">When you already connect to camera by Wi-Fi.</string>
    <string name="text_btpair_connect_wifi">Wi-Fi Connect</string>
    <string name="text_btpair_connect_cam">Connect to Your Camera</string>
    <string name="text_btpair_support_bt">For new camera, and the camera support Bluetooth.</string>
    <string name="text_connect_camera_by_usb">When you already connect to camera by USB.</string>
    <string name="text_add_camaera_usb">USB Connect</string>
    <string name="text_btpair_connect_operationsteps">Please click on the Done button to enter preview</string>
    <string name="text_btpair_setup_camwifi">Pair camera success, you can setup camera Wi-Fi or just click Setup to keep default setting.</string>
    <string name="text_add_new_camera">Add Camera</string>
    <string name="text_disconnect">Disconnect</string>
    <string name="text_connected">Connected</string>
    <string name="text_exception_info">Sorry, the program exception, will want to quit...</string>
    <string name="text_init_failed">Initialization failed</string>
    <string name="gallery_share_in_process">Share in process...</string>
    <string name="gallery_exiting_share">Exiting Share</string>
    <string name="gallery_test_content">Test content</string>
    <string name="text_rich_media_management_center">Rich Media Management Center</string>
    <string name="text_return">back</string>
    <string name="text_ble_devices">BLE Devices</string>
    <string name="text_classic_bluetooth_devices">Classic Bluetooth Devices</string>
    <string name="app_exception">APP  meets exception!</string>
    <string name="text_reconnect_timeout">Reconnect timeout</string>
    <string name="message_reconnect_timeout">Cannot connect to the camera Wi-Fi...</string>
    <string name="title_activity_panorama_photo_pb">PanoramaPhotoPbActivity</string>
    <string name="title_activity_panorama_video_pb">PanoramaVideoPbActivity</string>
    <string name="video_format_not_support">Failed to play because of size is not supported!</string>
    <string name="setting_live_switch">Youtube live</string>
    <string name="setting_live_address">Live address</string>
    <string name="input_live_url">Input live url</string>
    <string name="wait">Wait...</string>
    <string name="action_input_ip">Input ip</string>
    <string name="setting_enable_wifi_hotspot">Enable Wifi Hotspot</string>
    <string name="message_download_failed">Download failed</string>
    <string name="message_download_to">Downloaded to $1$</string>
    <string name="message_failed_to_stop_living_publish">Failed to stop living publish!</string>
    <string name="message_succeed_to_stop_living_publish">Succeed to stop living publish!</string>
    <string name="message_failed_to_start_living_publish">Failed to start living publish!</string>
    <string name="message_succeed_to_start_living_publish">Succeed to start living publish!</string>
    <string name="message_setup_false">Setup false!</string>
    <string name="message_connecting">Connecting...</string>
    <string name="message_btpair_search">Search...</string>
    <string name="message_loading">Loading...</string>
    <string name="message_binding">Binding...</string>
    <string name="message_binding_failed">create bounded failed.</string>
    <string name="message_binding_exception">create bounded exception</string>
    <string name="message_failed_to_connect_wifi">failed to connect wifi.</string>
    <string name="message_failed_to_connect_camera">failed to connect camera.</string>
    <string name="message_failed_to_connect_please_tyragain">connect failed,please tryagain!</string>
    <string name="message_ble_service_discovered">ble service discovered.</string>
    <string name="message_no_ble_service_discovered">ble no service discovered.</string>
    <string name="message_ble_device_connected">ble device connected.</string>
    <string name="message_ble_device_not_connected_please_tryagain.">ble device not connected, please tryagain.</string>
    <string name="message_failed_to_bounded">failed to bounded.</string>
    <string name="message_bounded_is_ok">Bounded is ok</string>
    <string name="facebook_app_id">295583287549917</string>
    <string name="fb_login_protocol_scheme">fb295583287549917</string>
    <!--<string name="facebook_app_id">***************</string>-->
    <string name="facebook_start_live">Strat facebook live</string>
    <string name="facebook_end_live">End facebook live</string>
    <string name="facebook_page_setting">Facebook Page setting</string>
    <string name="facebook_input_page_name">Please enter the facebook home page</string>
    <string name="can_not_be_empty">Can not be empty</string>
    <string name="start_youtube_live">Start YouTube live</string>
    <string name="google_account">Google account</string>
    <string name="end_youtube_live">End YouTube live</string>
    <string name="signed_in">Signed in</string>
    <string name="signing_in">Signing in...</string>
    <string name="signed_out">Signed out</string>
    <string name="sign_out">Sign Out</string>
    <string name="disconnect">Disconnect</string>
    <string name="refresh_token">GetToken</string>
    <string name="desc_google_icon">Google Logo</string>
    <string name="title_text">Google Sign-In\nQuickstart</string>
    <string name="auth_code_fmt">Auth Code: %s</string>
    <string name="no_content">No files</string>
    <string name="open_preview_failed">Open preview failed!</string>
    <string name="message_please_stop_live">please stop live!</string>
    <string name="message_start_live">start live</string>
    <string name="message_go_back_and_start_YouTube_live">Please go back and start YouTube live!</string>
    <string name="message_login_cancel">Login cancel</string>
    <string name="message_login_error">Login error</string>
    <string name="message_refreshAccessToken_IOException">refreshAccessToken IOException</string>
    <string name="message_click_disconnect_and_relogin">Failed to get accessToken , Please enter the google account click disconnect and re-login</string>
    <string name="message_login_to_google_account">You are not logged in, please login to google account</string>
    <string name="message_failed_to_Youtube_live_OAuth2AccessToken_is_null">Failed to Youtube live,OAuth2AccessToken is null</string>
    <string name="message_failed_to_Youtube_live_pushUrl_is_null">Failed to Youtube live,pushUrl is null</string>
    <string name="message_failed_to_start_publish_streaming">Failed to start publish streaming</string>
    <string name="message_failed_to_YouTube_live_shareUrl_is_null">Failed to YouTube live,shareUrl is null</string>
    <string name="stop_live_hint">Please stop live!</string>
    <string name="stop_other_live_hint">Please stop other live!</string>
    <string name="setting_type_other">Other Settings</string>
    <string name="setting_type_custom">Custom Settings</string>
    <string name="title_activity_pv_param_setting">PvParamSetting</string>
    <string name="title_activity_usbpreview">USBPreview</string>
    <string name="loading">loading...</string>
    <string name="text_panorama">全景</string>
    <string name="text_asteroid">小行星</string>
    <string name="text_vr">VR</string>
    <string name="text_stablization">Stablization</string>
    <string name="text_usb_permission_has_been_denied">USB permission has been denied, unable to connect camera.</string>
    <string name="text_usb_device_detected">USB device detected</string>
    <string name="text_usb_device_not_detected">USB device not detected</string>
    <string name="text_usb_device_disconnected">USB device has been disconnected</string>
    <string name="non_360_picture_not_support_switch">The current picture is not a 360 image and does not support switching.</string>
    <string name="permission_is_denied_info">The necessary permissions are denied, the application can not be used normally!</string>
    <string name="current_size_not_support_image_stabilization">The current size is not support image stabilization</string>
    <string name="setting_title_exposure_compensation">EC</string>
    <string name="setting_title_image_stabilization">Image stabilization</string>
    <string name="setting_title_video_file_length">Recording time</string>
    <string name="setting_title_video_file_length_v2">Recording time</string>
    <string name="text_camera_has_been_registered">Camera $1$ has been registered.</string>
    <string name="text_please_connect_camera">Please connect camera $1$.</string>
    <string name="title_warning">Warning</string>
    <string name="text_selected">Selected($1$)</string>
    <string name="setting_updatefw_chec_sum_failed">FW update check sum error!</string>
    <string name="setting_updatefw_upgrade_file_not_exist">The upgrade file $1$ does not exist, please put the file to this path:$2$.</string>
    <string name="text_file_length_unlimited">Unlimited</string>
    <string name="time_minutes"> MIN</string>
    <string name="text_agree">Agree</string>
    <string name="text_disagree">Disagree</string>
    <string name="title_privacy_policy1">Privacy policy and user agreement</string>
    <string name="title_privacy_policy2">Privacy policy and user agreement</string>
    <string name="content_privacy_policy_1">Please read carefully and fully understand the terms and conditions, including but not limited to: in order to provide you with basic services, we need to collect your device information, operation log and other personal information. You can read the </string>
    <string name="content_privacy_policy_2">Privacy Policy and User Agreement</string>
    <string name="content_privacy_policy_3"> for more information.If you agree, please click "Agree" to enjoy our services.</string>
    <string name="cancel_all">Cancel All</string>
    <string name="download_cancel_all_tips">Do you want to cancel all file downloads?</string>
    <string name="dialog_card_removed_and_back">SD卡已移除，将退出视频播放。</string>
    <string name="dialog_card_removed_and_back_photo_pb">SD已移除，将退出图片预览</string>
    <string name="setting_title_screen_saver">Screen saver</string>
    <string name="setting_title_auto_power_off">Auto power off</string>
    <string name="setting_title_power_on_auto_record">Auto video recording</string>
    <string name="setting_title_fast_motion_movie">Fast motion movie</string>
    <string name="setting_title_wind_noise_reduction">Noise reduction</string>
    <string name="setting_title_camera_switch">Camera Switch</string>
    <string name="setting_camera_front">Front Camera</string>
    <string name="setting_camera_back">Back Camera</string>
    <string name="setting_storage_location">Download file storage location</string>
    <string name="setting_internal_storage">Internal storage</string>
    <string name="setting_sd_card_storage">SD card</string>
    <string name="off">OFF</string>
    <string name="help">Help</string>
    <string name="dialog_timeout_2">Unable to connect the camera successfully! </string>
    <string name="wifi_or_password_cannot_be_empty">Wifi name or password cannot be empty.</string>
    <string name="wifi_hotspot_open_failed_info">The Wifi hotspot fails to be turned on.You need to manually turn it on and set the Wifi hotspot name: $1$ and password: $2$. After the setting is completed, please restart and connect.</string>
    <string name="request_camera_permission_warn_info">Versions of Android 9.0 and above require camera permissions to invoke external UVC cameras. Don`t worry, we won`t call the phone`s camera.</string>
    <string name="camera_permission_is_denied_info">Camera permissions were denied, so the UVC camera could not be called!</string>
    <string name="please_set_the_correct_resolution_and_fps">Please set the correct resolution and fps</string>
    <string name="video_frame_rate">Frame rate</string>
    <string name="video_resolution">Video resolution</string>
    <string name="turn_on_location_information_tips">requires location information to get the current WiFi name.</string>
    <string name="restore_factory">Reset</string>
    <string name="A6_ROTATE_MOTOR_STATE">Rotate State</string>
    <string name="A6_ROTATE_SHOT_TIMES">Shot times</string>
    <string name="a6_rotate_off">(0) rotate off</string>
    <string name="a6_rotate_start">(1) rotate start</string>
    <string name="a6_rotate_end">(2) rotate end</string>
    <string name="a6_shot_1st" translatable="false">(0) 1st</string>
    <string name="a6_shot_2nd" translatable="false">(1) 2nd</string>
    <string name="a6_shot_3rd" translatable="false">(2) 3rd</string>
    <string name="a6_shot_4th" translatable="false">(3) 4th</string>
    <string name="faq">FAQ</string>
    <string name="member_center">Member center</string>
    <string name="featured">Featured</string>
    <string name="title_album">Album</string>
    <string name="title_shot">Shooting</string>
    <string name="ijoyer_setting">Settings</string>
    <string name="connect_cam_wifi">  connect WIFI hotspot-Connect Now ▶</string>
    <string name="use_wifi">WIFI connection (Wireless connection)</string>
    <string name="use_wifi_message1">○ Turn on the camera and switch to the WIFI hotspot;\n
    ○ Turn on your smartphone and go to the WIFI settings, select the "IJOYER" on your smartphone WIFI list and click on it to paring your phone and your camera;\n
    ○ Enter the default password: 1234567890 to confirm pairing;</string>
    <string name="use_wifi_message2">○ After successful pairing, you return to the APP page to control the camera.</string>
    <string name="go_to_connect">Go to connect</string>
    <string name="phone">Phone</string>
    <string name="camera">Camera</string>
    <string name="no_device_to_be_found">No devices to be found connected to WIFI hotspot of IJOYER camera</string>
    <string name="camera_info">Camera information</string>
    <string name="complementary_ground_logo">Complementary ground LOGO</string>
    <string name="set_complementary_ground_logo">Set</string>
    <string name="params">HDR Setting</string>
    <string name="screen_recording_setting">Video Setting</string>
    <string name="clear_cache">Clear Cache</string>
    <string name="software_version">Software version</string>
    <string name="user_agreement">User agreement</string>
    <string name="privacy_statement">Privacy statement</string>
    <string name="about_ijoyer">About IJOYER</string>
    <string name="auto_download_photos">Auto Download Photos</string>
    <string name="use_multi_thread_mode">Multi-Threaded Mode</string>
    <string name="hdr_compression_mode">HDR-Compression Mode</string>
    <string name="use_image_enhancement">Image Enhancement</string>
    <string name="use_multi_thread_mode_desc">Opening can significantly increase speed, but if it crash, try closing it</string>
    <string name="hdr_compression_mode_desc">Turning it off will improve picture quality. If the app crashes during stitching, please try turning it on.</string>
    <string name="use_image_enhancement_desc">If black spots appear, please try turning off this option</string>
    <string name="stitching_params">Stitching params(debug)</string>
    <string name="ijoyer_connect_message1">○ IJOYER panoramic camera supports one-click connection, and the cameras that finish the connection pairing will be saved for this list.</string>
    <string name="ijoyer_connect_message2">○ If you delete a paired camera, you need to reconnect it with one click.</string>
    <string name="complementary_ground_logo_setting">Complementary ground LOGO setting</string>
    <string name="note_complementary_ground_logo_setting">Note: The purpose of the complementary ground logo is to cover the camera tripodin the panorama when stitching images</string>
    <string name="no_logo">No LOGO</string>
    <string name="customize_logo">Customize LOGO</string>
    <string name="logo_size">LOGO size</string>
    <string name="stitching_parameter_settings">Stitching parameter settings</string>
    <string name="image_resolution">Image resolution</string>
    <string name="optimization">Optimization</string>
    <string name="remove_chromatic_aberration">Remove Chromatic\nAberration</string>
    <string name="strengthen_chromatic_aberration_fusion">Strengthen Chromatic</string>
    <string name="screen_recording_settings">Screen recording settings</string>
    <string name="note_screen">
Tip: This function is used to adjust the definition of binocular video. Please set it according to the performance of your phone. Ultra HD can be set for high-end flagship models. (Default: HD)</string>
    <string name="fluent">Fluent</string>
    <string name="hd">HD</string>
    <string name="fhd">FHD</string>
    <string name="is_claen">Clear the cache?</string>
    <string name="introduce">IJOYER was established in the first year of VR,we are devoted to solving all the supporting development and applications related to panoramic photography and sharing display. IJOYER has reached strategic cooperation and development agreements with many upstream chip manufacturers of panoramic cameras. At present, our main products include 360-degree panoramic camera, panoramic sports camera, panoramic driving recorder,etc.IJOYER panoramic photography platform adopts the international advanced panoramic display core technology KRPANO, and cooperates with the whole set of panoramic roaming editing and display system developed by ourselves to provide users with value-added experience of panoramic camera products and sharing platform. We will always adhere to the business philosophy of "quality, casting brand", so that panoramic photography into thousands of households.</string>
    <string name="introduce_and_help">About us</string>
    <string name="about_coder">Contact us</string>
    <string name="introduce_about_coder">Contact information\nCompany: Zhongshan Zhuodi Electronic Technology Co.,Ltd\nAddress: Room 325,3/F, Block 2, Fanhua Cultural and Creative Park, Zhongshan Daily Printing Center,No.121,Qi wan Road North, East District, Zhongshan, Guangdong Province.\nTel: 0760-********\nQQ:*********\nE-mail:<EMAIL>\nCompany website: www.ijoyer.com\nWeChat official account: ijoyer360\nCustomer service QQ group: ********** ,*********\nWorking hours: Monday to Friday 9: 00--17: 30 (GMT+8)\n</string>
    <string name="processing">Processing</string>
    <string name="take_photo1">The first photo time consuming:</string>
    <string name="take_photo2">The second photo time consuming:</string>
    <string name="take_photo3">The third photo time consuming:</string>
    <string name="take_photo4">The fourth photo time consuming:</string>
    <string name="take_photo_finish">Rotating photo shooting completed!</string>
    <string name="stitching_completed">Stitching completed,</string>
    <string name="time_consuming">Time consuming</string>
    <string name="the">the</string>
    <string name="photo">photo:</string>
    <string name="function">Function</string>
    <string name="vr">VR</string>
    <string name="record_screen">ScreenRecording</string>
    <string name="record_screen_end">End ScreenRecording</string>
    <string name="edit">edit</string>
    <string name="function_switch">Function switch button</string>
    <string name="function_on">● On</string>
    <string name="function_off">○ Off</string>
    <string name="gyro">Gyro\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="gesture_zoom">Gesture Zoom\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="gesture_movement">Gesture Movement\t\t\t\t\t\t\t\t</string>
    <string name="gyro_gesture_coexistence">Gyro Gesture Coexistence\t</string>
    <string name="auto_play">Auto Play\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="flip">Flip\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="background_music">Background Music\t\t\t\t\t\t\t\t</string>
    <string name="tap">Tap\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="mode_switching">Mode switching</string>
    <string name="mode_switch1">Panoramic View\t\t\t\t\t\t</string>
    <string name="mode_switch2">Fisheye\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="mode_switch3">Asteroid\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="mode_switch4">Curve\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="mode_switch5">3D\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t</string>
    <string name="mode_switch6">VR Horizontal\t\t\t\t\t\t\t\t</string>
    <string name="mode_switch7">VR Vertical\t\t\t\t\t\t\t\t\t\t</string>
    <string name="mode_switch8">2:1 unfolding\t\t\t\t\t\t\t\t</string>
    <string name="mode_switch9">Original plane\t\t\t\t\t\t\t</string>
    <string name="mode_switch1_data">Panoramic View</string>
    <string name="mode_switch2_data">FishEye</string>
    <string name="mode_switch3_data">Asteroid</string>
    <string name="mode_switch4_data">Curve</string>
    <string name="mode_switch5_data">3D</string>
    <string name="mode_switch6_data">VR Horizontal</string>
    <string name="mode_switch7_data">VR Vertical</string>
    <string name="mode_switch8_data">2:1 unfolding</string>
    <string name="mode_switch9_data">Original plane</string>
    <string name="screen_recording_start">Screen recording start</string>
    <string name="screen_recording_completed">Screen Recording completed</string>
    <string name="restore">Restore factory settings and all your settings will be restored to the default.</string>
    <string name="ijoyer_title">IJOYER  Panoramic camera</string>
    <string name="no_more_file">no more file</string>
    <string name="ijoyer2" translatable="false">IJOYER</string>
    <string name="reminder">Reminder</string>
    <string name="exit_app">Are you sure you want to exit App?</string>
    <string name="title_find">Find</string>
    <string name="title_mine">Mine</string>
    <string name="agreement_and_privacy">User Agreement And Privacy Policy</string>
    <string name="welcome">Welcome to the IJOYER panoramic camera! Please carefully read and understand the user agreement and privacy policy before you use the IJOYER panoramic camera. If you agree, please click "agree" to start accepting our services.</string>
    <string name="read">Read</string>
    <string name="user_agreement_data">《User Agreement》</string>
    <string name="privacy_policy_data">《Privacy Policy》</string>
    <string name="and">and</string>
    <string name="agree">Agree</string>
    <string name="disagree">Disagree and exit app>></string>

    <string name="str_saturation">saturation</string>
    <string name="str_contrast_ratio">contrast_ratio</string>
    <string name="str_exposure">exposure</string>

    <string name="app_license1" translatable="false">&license_app_name; "LICENSE AGREEMENT\n"
"\n"
"PLEASE READ THE FOLLOWING TERMS AND CONDITIONS CAREFULLY BEFORE DOWNLOADING, INSTALLING OR USING THE &license_app_name; APPLICATION (THE \" &license_app_name; APP \"). THE TERMS AND CONDITIONS OF THIS &license_app_name; APP LICENSE AGREEMENT (\"AGREEMENT\") GOVERN USE OF THE &license_app_name; APP UNLESS YOU AND &license_full_name; (\"&license_full_name;\") HAVE EXECUTED A SEPARATE AGREEMENT GOVERNING USE OF THE &license_app_name; APP. THIS APP MAY SEND &license_full_name; CERTAIN INFORMATION AS DESCRIBED BELOW AND THIS INFORMATION IS GOVERNED BY &license_full_name;'S PRIVACY POLICY. BY DOWNLOADING, INSTALLING OR USING THE &license_app_name; APP, YOU HAVE INDICATED THAT YOU UNDERSTAND AND AGREE TO (1) THIS AGREEMENT AND (2) &license_full_name;'S PRIVACY POLICY.\n"
"\n"
"&license_full_name; is willing to license the &license_app_name; APP to you only upon the condition that you accept all the terms contained in this Agreement and in the &license_full_name; Privacy Policy (available at &license_privacy_url;). If you are accepting the terms of this Agreement on behalf of a company or other legal entity, you represent and warrant that you have the authority to bind that company or other legal entity to the terms of this Agreement, and, in such event, \"you\" and \"your\" will refer to that company or other legal entity. If you do not accept all the terms of this Agreement, then &license_full_name; is unwilling to license the &license_app_name; APP to you, and you may not download, install or use the &license_app_name; APP.\n"
            "\n"
            "1. License and Restrictions. Subject to your compliance with the terms and conditions of this Agreement, &license_full_name; grants you a non-exclusive and non-transferable license to download and install one (1) copy of the &license_app_name; APP on any mobile device or computer that you own or control, solely for your own personal use. You may not: (i) copy, modify or distribute the &license_app_name; APP for any purpose; (ii) transfer, sublicense, lease, lend, rent or otherwise distribute the &license_app_name; APP to any third party; (iii) decompile, reverse-engineer, disassemble, or create derivative works of the &license_app_name; APP; (iv) make the functionality of the &license_app_name; APP available to multiple users through any means; or (v) use the &license_app_name; APP in any unlawful manner, for any unlawful purpose, or in any manner inconsistent with this Agreement. &license_full_name; reserves all rights in and to the &license_app_name; APP not expressly granted to you under this Agreement.\n"
            "\n"
            "2. Ownership. The &license_app_name; APP is protected by copyright, trademark, and other laws of the United States and foreign countries. Except as expressly provided in this Agreement, &license_full_name; (or its licensors) exclusively owns all right, title and interest in and to the &license_app_name; APP, including all associated intellectual property rights. You may not remove, alter or obscure any copyright, trademark, service mark or other proprietary rights notices incorporated in or accompanying the &license_app_name; APP. You acknowledge and agree that any feedback, comments or suggestions you may provide regarding the &license_app_name; APP (\"Feedback\") will be the sole and exclusive property of &license_full_name; and you hereby irrevocably assign to &license_full_name; all of your right, title and interest in and to all Feedback.\n"
            "\n"
            "3. Data Collection. \n"
            "  a. To facilitate product support, product development and improvement as well as other services to you and other &license_full_name; customers, we have provide a method to collect detail connection log between camera and &license_app_name; APP, and the log file will save in the external storage in your smart phone. By the default setting, this method is disable and users need to enable it manually and do NOT collect any privacy data、 NOT work in system services、Not send the log files to anywhere automatically. Other description is in our Privacy Policy. (available at: &license_privacy_url;). \n"
            "\n"
            "  b. Regarding AdSense Policy : \n"
            "    Third party vendors, including Google, use cookies to serve ads based on a user’s prior visits to our APP/Sites. Google’s use of the DoubleClick cookie enables it and its partners to serve ads to users based on their visit to our app/sites and/or other app/sites on the internet. You may opt out of the use of the DoubleClick cookie for app-based/interest-based advertising by visiting Ad Settings. (Alternatively, you can opt out of a third-party vendor’s use of cookies for interest-based advertising by visiting about ads.info ). \n"
            "\n"
            "4. Term and Control. \n"
            " a. Term. \n"
            "   The license granted under this Agreement remains in effect until terminated in accordance with this Agreement. You may terminate the license at any time by destroying all copies of the &license_app_name; APP in your possession or control. The license granted under this Agreement will automatically terminate, with or without notice from &license_full_name;, if you breach any term of this Agreement. Upon termination, you must delete all copies of the &license_app_name; APP in your possession or control.\n"
            " b. Control:\n"
            "   Here's more information about what permissions the app need to be allowed to execute:\n"
            " 	Access External Storage: Access any video files in the local storage media cabinet on your cell phone/device.\n"
            " 	Wi-Fi: Enable and use Wi-Fi connection between your camera device and cell phone.\n"
            " 	Location: Enable and get active WiFi name(SSID). Since Android 8.1, it's necessary for apps which want th determince the active WiFi name(SSID). The SSID may be \"unknown ssid\", if there is no network currently connected or if the caller has insufficient \"Location\" permissions to access the SSID. Please search or reference: https://developer.android.com/about/versions/pie/android-9.0-changes-all \n"
            " 	Mobile data (LTE / 4G Network): Using Mobile data (Internet) can share photos/videos.\n"
            "\n"
            "5. Disclaimers. You understand and agree that the &license_app_name; APP is provided to you \"AS IS\" and on an \"AS AVAILABLE\" basis. Without limiting the foregoing, &license_full_name; EXPLICITLY DISCLAIMS ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT, AND ANY WARRANTIES ARISING OUT OF COURSE OF DEALING OR USAGE OF TRADE. &license_full_name; makes no warranty that the &license_app_name; APP will meet your requirements or be available on an uninterrupted, secure, or error-free basis.\n"
            "\n"
            "6. Limitation of Liability. &license_full_name; 'S TOTAL LIABILITY TO YOU FROM ALL CAUSES OF ACTION AND UNDER ALL THEORIES OF LIABILITY WILL BE LIMITED TO THE AMOUNT YOU PAID FOR THE &license_app_name; APP AND IN NO EVENT WILL IT EXCEED $1. &license_full_name; WILL NOT BE LIABLE TO YOU FOR ANY INCIDENTAL, SPECIAL, CONSEQUENTIAL OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE &license_app_name; APP, WHETHER BASED ON WARRANTY, CONTRACT, TORT (INCLUDING NEGLIGENCE) OR ANY OTHER LEGAL THEORY, WHETHER OR NOT &license_full_name; HAS BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGE, AND EVEN IF A REMEDY SET FORTH HEREIN IS FOUND TO HAVE FAILED OF ITS ESSENTIAL PURPOSE.\n"
            "\n"
            "7. U.S. Government End Users. The &license_app_name; APP and any related documentation are \"commercial items\" as that term is defined in FAR 2.101, consisting of \"commercial computer application\" and \"commercial computer application documentation,\" respectively, as such terms are used in FAR 12.212 and DFARS 227.7202. If the &license_app_name; APP and any related documentation are being acquired by or on behalf of the U.S. Government, then, as provided in FAR 12.212 and DFARS 227.7202-1 through 227.7202-4, as applicable, the U.S. Government's rights in the &license_app_name; APP and any such documentation will be only those specified in this Agreement.\n"
            "\n"
            "8. Export Control. You agree to comply fully with all U.S. and foreign export laws and regulations to ensure that neither the &license_app_name; APP nor any technical data related thereto nor any direct product thereof is exported or re-exported directly or indirectly in violation of, or used for any purposes prohibited by, such laws and regulations. By using the &license_app_name; APP, you represent and warrant that: (i) you are not located in a country that is subject to a U.S. or any other governments embargo, or that has been designated by the U.S. or any other governments as a \"terrorist supporting\" country; and (ii) you are not listed on any U.S. or any other governments’ list of prohibited or restricted parties.\n"
            "\n"
            "9. General. This Agreement will be governed by and construed in accordance with the laws of Taiwan, without regard to or application of conflict of laws rules or principles. The United Nations Convention on Contracts for the International Sale of Goods will not apply. You may not assign or transfer this Agreement or any rights granted hereunder, by operation of law or otherwise, without &license_full_name; 's prior written consent, and any attempt by you to do so, without such consent, will be void. Except as expressly set forth in this Agreement, the exercise by either party of any of its remedies under this Agreement will be without prejudice to its other remedies under this Agreement or otherwise. All notices or approvals required or permitted under this Agreement will be in writing and delivered by confirmed facsimile transmission, by overnight delivery service, by email or by certified mail, and in each instance will be deemed given upon receipt. All notices or approvals will be sent to such other address as may be specified by either party to the other in accordance with this section. The failure by either party to enforce any provision of this Agreement will not constitute a waiver of future enforcement of that or any other provision. &license_full_name; reserves the right to modify this Agreement and will give you reasonable notice of any changes. Your continued use of the &license_app_name; APP after such notice will indicate your consent to any such changes. If any provision of this Agreement is held to be unenforceable or invalid, that provision will be enforced to the maximum extent possible, and the other provisions will remain in full force and effect. This Agreement and the &license_full_name; Privacy Policy is the complete and exclusive understanding and agreement between the parties regarding its subject matter, and supersedes all proposals, understandings or communications between the parties, oral or written, regarding its subject matter, unless you and &license_full_name; have executed a separate agreement.\n"
            "\n"
            "10. Contact Information. If you have any questions regarding this Agreement or the &license_app_name; APP, you may contact &license_full_name; at &license_contact_email; or at: &license_address; \n";
</string>

    <string name="app_license2" translatable="false">&license_app_name; "LICENSE AGREEMENT\n"
"\n"
"PLEASE READ THE FOLLOWING TERMS AND CONDITIONS CAREFULLY BEFORE DOWNLOADING, INSTALLING OR USING THE &license_app_name; APPLICATION (THE \" &license_app_name; APP \"). THE TERMS AND CONDITIONS OF THIS &license_app_name; APP LICENSE AGREEMENT (\"AGREEMENT\") GOVERN USE OF THE &license_app_name; APP UNLESS YOU AND &license_full_name; (\"&license_full_name;\") HAVE EXECUTED A SEPARATE AGREEMENT GOVERNING USE OF THE &license_app_name; APP. THIS APP MAY SEND &license_full_name; CERTAIN INFORMATION AS DESCRIBED BELOW AND THIS INFORMATION IS GOVERNED BY &license_full_name;'S PRIVACY POLICY. BY DOWNLOADING, INSTALLING OR USING THE &license_app_name; APP, YOU HAVE INDICATED THAT YOU UNDERSTAND AND AGREE TO (1) THIS AGREEMENT AND (2) &license_full_name;'S PRIVACY POLICY.\n"
"\n"
"&license_full_name; is willing to license the &license_app_name; APP to you only upon the condition that you accept all the terms contained in this Agreement and in the &license_full_name; Privacy Policy (available at &license_privacy_url;). If you are accepting the terms of this Agreement on behalf of a company or other legal entity, you represent and warrant that you have the authority to bind that company or other legal entity to the terms of this Agreement, and, in such event, \"you\" and \"your\" will refer to that company or other legal entity. If you do not accept all the terms of this Agreement, then &license_full_name; is unwilling to license the &license_app_name; APP to you, and you may not download, install or use the &license_app_name; APP.\n"
            "\n"
            "1. License and Restrictions. Subject to your compliance with the terms and conditions of this Agreement, &license_full_name; grants you a non-exclusive and non-transferable license to download and install one (1) copy of the &license_app_name; APP on any mobile device or computer that you own or control, solely for your own personal use. You may not: (i) copy, modify or distribute the &license_app_name; APP for any purpose; (ii) transfer, sublicense, lease, lend, rent or otherwise distribute the &license_app_name; APP to any third party; (iii) decompile, reverse-engineer, disassemble, or create derivative works of the &license_app_name; APP; (iv) make the functionality of the &license_app_name; APP available to multiple users through any means; or (v) use the &license_app_name; APP in any unlawful manner, for any unlawful purpose, or in any manner inconsistent with this Agreement. &license_full_name; reserves all rights in and to the &license_app_name; APP not expressly granted to you under this Agreement.\n"
            "\n"
            "2. Ownership. The &license_app_name; APP is protected by copyright, trademark, and other laws of the United States and foreign countries. Except as expressly provided in this Agreement, &license_full_name; (or its licensors) exclusively owns all right, title and interest in and to the &license_app_name; APP, including all associated intellectual property rights. You may not remove, alter or obscure any copyright, trademark, service mark or other proprietary rights notices incorporated in or accompanying the &license_app_name; APP. You acknowledge and agree that any feedback, comments or suggestions you may provide regarding the &license_app_name; APP (\"Feedback\") will be the sole and exclusive property of &license_full_name; and you hereby irrevocably assign to &license_full_name; all of your right, title and interest in and to all Feedback.\n"
            "\n"
            "3. Data Collection. \n"
            "  a. To facilitate product support, product development and improvement as well as other services to you and other &license_full_name; customers, we have provide a method to collect detail connection log between camera and &license_app_name; APP, and the log file will save in the external storage in your smart phone. By the default setting, this method is disable and users need to enable it manually and do NOT collect any privacy data、 NOT work in system services、Not send the log files to anywhere automatically. Other description is in our Privacy Policy. (available at: &license_privacy_url;). \n"
            "\n"
            "  b. Regarding AdSense Policy : \n"
            "    Third party vendors, including Google, use cookies to serve ads based on a user’s prior visits to our APP/Sites. Google’s use of the DoubleClick cookie enables it and its partners to serve ads to users based on their visit to our app/sites and/or other app/sites on the internet. You may opt out of the use of the DoubleClick cookie for app-based/interest-based advertising by visiting Ad Settings. (Alternatively, you can opt out of a third-party vendor’s use of cookies for interest-based advertising by visiting about ads.info ). \n"
            "\n"
            "4. Term and Control. \n"
            " a. Term. \n"
            "   The license granted under this Agreement remains in effect until terminated in accordance with this Agreement. You may terminate the license at any time by destroying all copies of the &license_app_name; APP in your possession or control. The license granted under this Agreement will automatically terminate, with or without notice from &license_full_name;, if you breach any term of this Agreement. Upon termination, you must delete all copies of the &license_app_name; APP in your possession or control.\n"
            " b. Control:\n"
            "   Here's more information about what permissions the app need to be allowed to execute:\n"
            " 	Access External Storage: Access any video files in the local storage media cabinet on your cell phone/device.\n"
            " 	Wi-Fi: Enable and use Wi-Fi connection between your camera device and cell phone.\n"
            " 	Location: Enable and get active WiFi name(SSID). Since Android 8.1, it's necessary for apps which want th determince the active WiFi name(SSID). The SSID may be \"unknown ssid\", if there is no network currently connected or if the caller has insufficient \"Location\" permissions to access the SSID. Please search or reference: https://developer.android.com/about/versions/pie/android-9.0-changes-all \n"
            " 	Mobile data (LTE / 4G Network): Using Mobile data (Internet) can share photos/videos.\n"
            "\n"
            "5. Disclaimers. You understand and agree that the &license_app_name; APP is provided to you \"AS IS\" and on an \"AS AVAILABLE\" basis. Without limiting the foregoing, &license_full_name; EXPLICITLY DISCLAIMS ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, QUIET ENJOYMENT OR NON-INFRINGEMENT, AND ANY WARRANTIES ARISING OUT OF COURSE OF DEALING OR USAGE OF TRADE. &license_full_name; makes no warranty that the &license_app_name; APP will meet your requirements or be available on an uninterrupted, secure, or error-free basis.\n"
            "\n"
            "6. Limitation of Liability. &license_full_name; 'S TOTAL LIABILITY TO YOU FROM ALL CAUSES OF ACTION AND UNDER ALL THEORIES OF LIABILITY WILL BE LIMITED TO THE AMOUNT YOU PAID FOR THE &license_app_name; APP AND IN NO EVENT WILL IT EXCEED $1. &license_full_name; WILL NOT BE LIABLE TO YOU FOR ANY INCIDENTAL, SPECIAL, CONSEQUENTIAL OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE &license_app_name; APP, WHETHER BASED ON WARRANTY, CONTRACT, TORT (INCLUDING NEGLIGENCE) OR ANY OTHER LEGAL THEORY, WHETHER OR NOT &license_full_name; HAS BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGE, AND EVEN IF A REMEDY SET FORTH HEREIN IS FOUND TO HAVE FAILED OF ITS ESSENTIAL PURPOSE.\n"
            "\n"
            "7. U.S. Government End Users. The &license_app_name; APP and any related documentation are \"commercial items\" as that term is defined in FAR 2.101, consisting of \"commercial computer application\" and \"commercial computer application documentation,\" respectively, as such terms are used in FAR 12.212 and DFARS 227.7202. If the &license_app_name; APP and any related documentation are being acquired by or on behalf of the U.S. Government, then, as provided in FAR 12.212 and DFARS 227.7202-1 through 227.7202-4, as applicable, the U.S. Government's rights in the &license_app_name; APP and any such documentation will be only those specified in this Agreement.\n"
            "\n"
            "8. Export Control. You agree to comply fully with all U.S. and foreign export laws and regulations to ensure that neither the &license_app_name; APP nor any technical data related thereto nor any direct product thereof is exported or re-exported directly or indirectly in violation of, or used for any purposes prohibited by, such laws and regulations. By using the &license_app_name; APP, you represent and warrant that: (i) you are not located in a country that is subject to a U.S. or any other governments embargo, or that has been designated by the U.S. or any other governments as a \"terrorist supporting\" country; and (ii) you are not listed on any U.S. or any other governments’ list of prohibited or restricted parties.\n"
            "\n"
            "9. General. This Agreement will be governed by and construed in accordance with the laws of Taiwan, without regard to or application of conflict of laws rules or principles. The United Nations Convention on Contracts for the International Sale of Goods will not apply. You may not assign or transfer this Agreement or any rights granted hereunder, by operation of law or otherwise, without &license_full_name; 's prior written consent, and any attempt by you to do so, without such consent, will be void. Except as expressly set forth in this Agreement, the exercise by either party of any of its remedies under this Agreement will be without prejudice to its other remedies under this Agreement or otherwise. All notices or approvals required or permitted under this Agreement will be in writing and delivered by confirmed facsimile transmission, by overnight delivery service, by email or by certified mail, and in each instance will be deemed given upon receipt. All notices or approvals will be sent to such other address as may be specified by either party to the other in accordance with this section. The failure by either party to enforce any provision of this Agreement will not constitute a waiver of future enforcement of that or any other provision. &license_full_name; reserves the right to modify this Agreement and will give you reasonable notice of any changes. Your continued use of the &license_app_name; APP after such notice will indicate your consent to any such changes. If any provision of this Agreement is held to be unenforceable or invalid, that provision will be enforced to the maximum extent possible, and the other provisions will remain in full force and effect. This Agreement and the &license_full_name; Privacy Policy is the complete and exclusive understanding and agreement between the parties regarding its subject matter, and supersedes all proposals, understandings or communications between the parties, oral or written, regarding its subject matter, unless you and &license_full_name; have executed a separate agreement.\n"
            "\n"
            "10. Contact Information. If you have any questions regarding this Agreement or the &license_app_name; APP, you may contact &license_full_name; at &license_contact_email; or at: &license_address; \n";
</string>

    <string name="paired_camera">Paired camera</string>
    <string name="str_custom">custom</string>
    <string name="hdr_title">Use HDR</string>
    <string name="hdr_tip">Use tip: compared with ordinary images, HDR can provide more dynamic range and image details, can better reflect the visual effect in the real environment. But joining together for a long time. </string>
    <string name="remember_me">Remember my choice</string>
    <string name="hdr_no">Dont Using HDR</string>
    <string name="hdr_yes">Using HDR</string>
    <string name="hdr_str0">Popup choose every time</string>


</resources>



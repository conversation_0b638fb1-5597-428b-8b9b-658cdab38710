<resources>
    <!--
         Declare custom theme attributes that allow changing which styles are
         used for button bars depending on the API level.
         ?android:attr/buttonBarStyle is new as of API 11 so this is
         necessary to support previous API levels.
    -->
    <declare-styleable name="RoundAngleImageView">
        <attr name="roundWidth" format="dimension" />
        <attr name="roundHeight" format="dimension" />
    </declare-styleable>
    <!-- Declare custom theme attributes that allow changing which styles are
         used for button bars depending on the API level.
         ?android:attr/buttonBarStyle is new as of API 11 so this is
         necessary to support previous API levels. -->
    <declare-styleable name="ButtonBarContainerTheme">
        <attr name="metaButtonBarStyle" format="reference" />
        <attr name="metaButtonBarButtonStyle" format="reference" />
    </declare-styleable>
    <declare-styleable name="ProgressWheel">
        <attr name="text" format="string" />
        <attr name="textColor" format="color" />
        <attr name="textSize" format="dimension" />
        <attr name="barColor" format="color" />
        <attr name="rimColor" format="color" />
        <attr name="rimWidth" format="dimension" />
        <attr name="spinSpeed" format="dimension" />
        <attr name="delayMillis" format="integer" />
        <attr name="circleColor" format="color" />
        <attr name="pw_radius" format="dimension" />
        <attr name="barWidth" format="dimension" />
        <attr name="barLengthP" format="dimension" />
        <attr name="contourColor" format="color"/>
        <attr name="contourSize" format="dimension"/>
    </declare-styleable>
    <declare-styleable name="NumberProgressBar">
        <attr name="progress_current" format="integer"/>
        <attr name="progress_max" format="integer"/>
        <attr name="progress_unreached_color" format="color"/>
        <attr name="progress_reached_color" format="color"/>
        <attr name="progress_reached_bar_height" format="dimension"/>
        <attr name="progress_unreached_bar_height" format="dimension"/>
        <attr name="progress_text_size" format="dimension"/>
        <attr name="progress_text_color" format="color"/>
        <attr name="progress_text_offset" format="dimension"/>
        <attr name="progress_text_visibility" format="enum">
            <enum name="visible" value="0"/>
            <enum name="invisible" value="1"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="Themes">
        <attr name="numberProgressBarStyle" format="reference"/>
    </declare-styleable>
</resources>

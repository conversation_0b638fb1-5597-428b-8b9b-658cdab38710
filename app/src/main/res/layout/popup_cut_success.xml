<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@drawable/shape_ffffff_10dp"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:textSize="20sp"
        android:textColor="@color/colorPrimary"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:text="温馨提示"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <TextView
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:gravity="center"
        android:text="操作成功，编辑页面是否切换新的视频"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:textSize="16sp"
            android:textColor="#333"
            android:gravity="center"
            android:background="@drawable/shape_eeeeee_start_bottom_10dp"
            android:text="取消"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/tv_sure"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:gravity="center"
            android:background="@drawable/shape_primary_end_bottom_10dp"
            android:text="确定"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="match_parent"/>

    </LinearLayout>

</LinearLayout>
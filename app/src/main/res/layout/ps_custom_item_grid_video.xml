<?xml version="1.0" encoding="utf-8"?>
<com.luck.picture.lib.widget.SquareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/ivPicture"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tvCheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_margin="8dp"
        android:background="@drawable/ps_checkbox_selector"
        android:gravity="center"
        android:textColor="@color/ps_color_white"
        android:textSize="12sp" />

    <View
        android:id="@+id/btnCheck"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_alignParentEnd="true"
        android:background="@color/ps_color_transparent" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tv_media_remark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/ps_gif_tag"
        android:text="备注名"
        android:layout_alignParentBottom="true"
        android:textColor="@color/ps_color_white"
        android:ellipsize="end"
        android:textSize="11sp"
        android:visibility="gone"
        tools:visibility="visible" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tv_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/ps_ic_shadow_bg"
        android:drawableLeft="@drawable/ps_ic_video"
        android:drawablePadding="5dp"
        android:gravity="center_vertical"
        android:paddingLeft="5dp"
        android:text="00:00"
        android:layout_above="@id/tv_media_remark"
        android:textColor="@color/ps_color_white"
        android:textSize="11sp" />

</com.luck.picture.lib.widget.SquareRelativeLayout>
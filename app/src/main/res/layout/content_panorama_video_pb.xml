<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:ProgressWheel="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context=".ui.activity.LocalVideoPbActivity">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <SurfaceView
                android:id="@+id/m_surfaceView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true" />
        </RelativeLayout>
        <LinearLayout
            android:id="@+id/video_pb_top_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginTop="@dimen/dp_25"
            android:background="@color/half_transparent_grey"
            android:orientation="horizontal">
            <ImageButton
                android:id="@+id/video_pb_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:background="@drawable/selector_transparent2gray"
                android:gravity="center"
                android:padding="15dp"
                android:src="@drawable/ic_arrow_back_white_24dp" />
            <TextView
                android:id="@+id/video_pb_video_name"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:layout_toRightOf="@id/video_pb_back"
                android:layout_weight="1"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:singleLine="true"
                android:text=""
                android:textColor="@color/grayWhite"
                android:textSize="16sp" />
            <ImageButton
                android:id="@+id/delete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_toLeftOf="@+id/download"
                android:background="@drawable/selector_transparent2gray"
                android:padding="15dp"
                android:src="@drawable/ic_delete_white_24dp"
                android:visibility="gone" />
            <ImageButton
                android:id="@+id/download"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:background="@drawable/selector_transparent2gray"
                android:padding="15dp"
                android:src="@drawable/ic_file_download_white_24dp" />
            <ImageButton
                android:id="@+id/more_btn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@drawable/selector_transparent2gray"
                android:padding="10dp"
                android:src="@drawable/ic_more_vert_white_24dp" />
        </LinearLayout>
        <LinearLayout
            android:id="@+id/more_setting_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@color/half_transparent_grey"
            android:orientation="vertical"
            android:visibility="gone">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="更多设置"
                    android:textColor="@color/white"
                    android:textSize="@dimen/second_title_size" />
                <ImageButton
                    android:id="@+id/cancel_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/selector_transparent2gray"
                    android:padding="14dp"
                    android:src="@drawable/ic_clear_white_24dp" />
            </RelativeLayout>
            <TextView
                android:id="@+id/delete_txv"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:layout_centerVertical="true"
                android:layout_marginLeft="20dp"
                android:gravity="center_vertical"
                android:text="@string/gallery_delete"
                android:textColor="@color/grayWhite"
                android:textSize="@dimen/second_title_size" />
            <RelativeLayout
                android:id="@+id/eis_layout"
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:visibility="visible">
                <TextView
                    android:id="@+id/eis_txv"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="20dp"
                    android:gravity="center_vertical"
                    android:text="@string/text_stablization"
                    android:textColor="@color/grayWhite"
                    android:textSize="@dimen/second_title_size" />
                <Switch
                    android:id="@+id/eis_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:layout_marginRight="20dp"
                    android:checked="false" />
            </RelativeLayout>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/video_pb_bottom_layout"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:layout_alignParentBottom="true"
            android:background="@color/half_transparent_grey"
            android:orientation="vertical">
            <SeekBar
                android:id="@+id/video_pb_seekBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:indeterminate="false"
                android:maxHeight="3dp"
                android:progressDrawable="@drawable/po_seekbar_02"
                android:theme="@drawable/seekbar_thumb"
                android:thumb="@drawable/seekbar_thumb" />
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/video_pb_seekBar"
                android:layout_gravity="center">
                <ImageButton
                    android:id="@+id/video_pb_play_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="10dp"
                    android:background="@color/full_transparent"
                    android:src="@drawable/ic_play_arrow_white_36dp" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="10dp"
                    android:layout_toRightOf="@+id/video_pb_play_btn"
                    android:orientation="horizontal">
                    <TextView
                        android:id="@+id/video_pb_time_lapsed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="00:00"
                        android:textColor="@color/greyish_white"
                        android:textSize="14sp" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="/"
                        android:textColor="@color/greyish_white"
                        android:textSize="14sp" />
                    <TextView
                        android:id="@+id/video_pb_time_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center"
                        android:text="00:00"
                        android:textColor="@color/greyish_white"
                        android:textSize="14sp" />
                </LinearLayout>
                <ImageButton
                    android:id="@+id/panorama_type_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:background="@color/full_transparent"
                    android:src="@drawable/panorama"
                    android:text="@string/text_panorama"
                    android:textColor="@color/white" />
            </RelativeLayout>
        </LinearLayout>
        <com.icatch.mobilecam.ui.ExtendComponent.ProgressWheel
            android:id="@+id/video_pb_spinner"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:visibility="gone"
            ProgressWheel:barColor="#0097D6"
            ProgressWheel:barLengthP="100dp"
            ProgressWheel:barWidth="5dp"
            ProgressWheel:contourColor="#330097D6"
            ProgressWheel:rimColor="#330097D6"
            ProgressWheel:rimWidth="10dp"
            ProgressWheel:text="0%"
            ProgressWheel:textColor="@android:color/white"
            ProgressWheel:textSize="14sp" />
    </RelativeLayout>
</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:background="@drawable/shape_ffffff_10dp"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_title"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="20dp"
        android:text="请选择视频的速度"
        android:textSize="20sp"
        android:textColor="@color/colorPrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>


<!--    <com.warkiz.widget.IndicatorSeekBar-->
<!--        app:layout_constraintTop_toBottomOf="@+id/tv_title"-->
<!--        app:layout_constraintBottom_toTopOf="@+id/ll_bottom"-->
<!--        android:id="@+id/sb_single"-->
<!--        app:isb_track_progress_size="6dp"-->
<!--        app:isb_thumb_size="15dp"-->
<!--        app:isb_thumb_color="@color/colorPrimary"-->
<!--        app:isb_track_progress_color="@color/colorPrimary"-->
<!--        app:isb_track_background_color="@color/text_line"-->
<!--        app:isb_show_thumb_text="false"-->
<!--        app:isb_tick_texts_array="@array/speed"-->
<!--        app:isb_thumb_text_color="@color/colorPrimary"-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="wrap_content"-->
<!--        app:isb_max="50"-->
<!--        app:isb_min="-50"-->
<!--        app:isb_progress_value_float="true"-->
<!--        app:isb_only_thumb_draggable="true"-->
<!--        app:isb_show_indicator="circular_bubble"-->
<!--        app:isb_show_tick_texts="true"-->
<!--        app:isb_ticks_count="2"-->
<!--        />-->



    <LinearLayout
        app:layout_constraintTop_toTopOf="@+id/sb_single"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@+id/sb_single"
        android:layout_width="match_parent"
        android:layout_height="20dp">

        <View
            android:layout_weight="2.5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="14dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="14dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="14dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="14dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="14dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>
        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>
        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="8dp"/>

        <View
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>


        <View
            android:background="@color/colorLine"
            android:layout_width="1dp"
            android:layout_height="14dp"/>
        <View
            android:layout_weight="2.5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

    </LinearLayout>

    app:isb_indicator_color="@color/colorPrimary"
    app:isb_tick_marks_color="@color/colorPrimary"
    app:isb_thumb_text_color="@color/colorPrimary"
    app:isb_thumb_color="@color/colorPrimary"
    app:isb_track_background_color="@color/text_line"
    app:isb_track_progress_color="@color/colorPrimary"
    app:isb_tick_texts_color="@color/colorPrimary"
    <com.warkiz.widget.IndicatorSeekBar
        app:isb_show_thumb_text="false"
        android:id="@+id/sb_single"
        app:isb_indicator_color="@color/full_transparent"
        app:isb_tick_marks_color="@color/full_transparent"
        app:isb_thumb_text_color="@color/full_transparent"
        app:isb_thumb_color="@color/colorPrimary"
        app:isb_track_background_color="@color/full_transparent"
        app:isb_track_progress_color="@color/full_transparent"
        app:isb_tick_texts_color="@color/full_transparent"
        app:isb_progress_value_float="true"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:isb_progress="1"
        app:isb_max="5"
        app:isb_min="0"
        app:isb_only_thumb_draggable="true"
        app:isb_show_indicator="circular_bubble"
        app:isb_show_tick_texts="false"/>

    app:isb_ticks_count="2"

    <LinearLayout
        android:layout_marginTop="3dp"
        app:layout_constraintTop_toBottomOf="@+id/sb_single"
        android:layout_width="match_parent"
        android:layout_height="30dp">

        <View
            android:layout_weight="0.5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="0.5x"
            android:textColor="#999"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:layout_weight="5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="1.0x"
            android:textColor="#999"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:layout_weight="5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="2.0x"
            android:textColor="#999"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:layout_weight="5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="4.0x"
            android:textColor="#999"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:layout_weight="5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="10x"
            android:textColor="#999"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:layout_weight="5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:text="20x"
            android:textColor="#999"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"/>

        <View
            android:layout_weight="0.5"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>



    </LinearLayout>


    <LinearLayout
        android:id="@+id/ll_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:textSize="16sp"
            android:textColor="#333"
            android:gravity="center"
            android:background="@drawable/shape_eeeeee_start_bottom_10dp"
            android:text="取消"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/tv_sure"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:gravity="center"
            android:background="@drawable/shape_primary_end_bottom_10dp"
            android:text="确定"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="match_parent"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/icons"
    android:fitsSystemWindows="true"
    android:orientation="vertical"
    tools:context=".ui.activity.LaunchActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        android:elevation="0dp"
        android:theme="@style/FullScreenTheme.AppBarOverlay"
        app:elevation="0dp">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/FullScreenTheme.PopupOverlay" />
    </com.google.android.material.appbar.AppBarLayout>
    <!--    <include layout="@layout/app_bar_layout" />-->
    <include layout="@layout/content_launch" />
</LinearLayout>

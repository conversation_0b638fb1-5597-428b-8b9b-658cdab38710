<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:background="@drawable/shape_ffffff_10dp"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_title"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="20dp"
        android:text="新增的相片"
        android:textSize="20sp"
        android:textColor="@color/colorPrimary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <androidx.recyclerview.widget.RecyclerView
        app:layout_constraintBottom_toTopOf="@+id/ll_bottom"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        android:layout_marginTop="10dp"
        android:id="@+id/rv_file"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>



    <LinearLayout
        android:id="@+id/ll_bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_cancel"
            android:textSize="16sp"
            android:textColor="#333"
            android:gravity="center"
            android:background="@drawable/shape_eeeeee_start_bottom_10dp"
            android:text="取消"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/tv_sure"
            android:textSize="16sp"
            android:textColor="@color/white"
            android:gravity="center"
            android:background="@drawable/shape_primary_end_bottom_10dp"
            android:text="清空"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="match_parent"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
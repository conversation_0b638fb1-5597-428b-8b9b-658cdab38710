<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="20dp"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_white_16dp"
        android:orientation="vertical"
        android:paddingLeft="20dp"
        android:paddingTop="20dp"
        android:paddingRight="20dp"
        android:paddingBottom="20dp">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="权限申请"
            android:textColor="@color/theme_color"
            android:textSize="18sp" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:textColor="@color/black"
            android:gravity="center"
            android:textIsSelectable="true"
            android:text="使用当前功能需要申请\n定位权限、录音权限、读写文件权限" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_left"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:layout_weight="1"
                android:background="@drawable/shape_theme_line_12dp"
                android:gravity="center"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="拒绝"
                android:textColor="@color/theme_color"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_right"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_weight="1"
                android:background="@drawable/shape_theme_12dp"
                android:gravity="center"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:text="允许"
                android:textColor="@color/white"
                android:textSize="16sp" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>
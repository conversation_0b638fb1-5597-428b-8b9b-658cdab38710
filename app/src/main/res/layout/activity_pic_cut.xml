<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_pic"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:adjustViewBounds="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>


    <LinearLayout
        app:layout_constraintBottom_toBottomOf="parent"
        android:id="@+id/ll_edit_photo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="@dimen/dp_10"
        android:background="@drawable/shape_popup_round_angle_grey_view"
        android:orientation="vertical"
        android:padding="@dimen/dp_10">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_contrast_ratio"
                android:textColor="@color/white" />

            <com.warkiz.widget.IndicatorSeekBar
                android:id="@+id/sb_contrast_ratio"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:isb_show_indicator="none"
                app:isb_show_thumb_text="false"
                app:isb_thumb_color="@color/colorPrimary"
                app:isb_thumb_size="15dp"
                app:isb_track_background_color="@color/text_line"
                app:isb_track_progress_color="@color/colorPrimary"
                app:isb_track_progress_size="6dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_exposure"
                android:textColor="@color/white" />

            <com.warkiz.widget.IndicatorSeekBar
                android:id="@+id/sb_exposure"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:isb_show_indicator="none"
                app:isb_show_thumb_text="false"
                app:isb_thumb_color="@color/colorPrimary"
                app:isb_thumb_size="15dp"
                app:isb_track_background_color="@color/text_line"
                app:isb_track_progress_color="@color/colorPrimary"
                app:isb_track_progress_size="6dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/str_saturation"
                android:textColor="@color/white" />

            <com.warkiz.widget.IndicatorSeekBar
                android:id="@+id/sb_saturation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                app:isb_show_indicator="none"
                app:isb_show_thumb_text="false"
                app:isb_thumb_color="@color/colorPrimary"
                app:isb_thumb_size="15dp"
                app:isb_track_background_color="@color/text_line"
                app:isb_track_progress_color="@color/colorPrimary"
                app:isb_track_progress_size="6dp" />

        </LinearLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
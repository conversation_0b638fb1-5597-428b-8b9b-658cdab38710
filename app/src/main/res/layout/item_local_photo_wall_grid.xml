<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/local_video_item_selector"
    android:padding="2dp">
    <ImageView
        android:id="@+id/local_photo_wall_grid_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:scaleType="centerCrop"
        android:src="@drawable/pictures_no" />
    <ImageView
        android:id="@+id/video_sign"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:src="@drawable/videooverlay" />
    <ImageView
        android:id="@+id/local_photo_wall_grid_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:src="@drawable/ic_check_box_blank_grey" />
    <ImageView
        android:id="@+id/is_panorama"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:src="@drawable/ic_panorama_18dp" />
</RelativeLayout>
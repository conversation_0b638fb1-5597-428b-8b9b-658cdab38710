<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="5dp"
    android:layout_marginTop="3dp"
    android:layout_marginEnd="5dp"
    android:gravity="center"
    android:orientation="vertical">


    <!--    <com.shehuan.niv.NiceImageView-->
    <!--        app:corner_radius="5dp"-->
    <!--        app:layout_constraintTop_toTopOf="parent"-->
    <!--        android:layout_width="match_parent"-->
    <!--        app:layout_constraintStart_toStartOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        android:layout_height="0dp"-->
    <!--        android:adjustViewBounds="true"-->
    <!--        android:scaleType="centerCrop"-->
    <!--        app:layout_constraintDimensionRatio="w,1:1" />-->

    <LinearLayout
        android:id="@+id/ll_no_filter"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:adjustViewBounds="true"
        android:scaleType="centerCrop"
        android:visibility="visible"
        app:layout_constraintDimensionRatio="w,1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <androidx.cardview.widget.CardView
            android:layout_margin="2dp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:cardBackgroundColor="#ccc"
            app:cardCornerRadius="5dp"
            app:layout_constraintDimensionRatio="w,1:1"
           >

            <ImageView
                android:id="@+id/iv_pic"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop"
                android:src="@drawable/icon_filter_no" />


        </androidx.cardview.widget.CardView>

    </LinearLayout>




    <ImageView
        android:id="@+id/iv_select"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="2dp"
        android:src="@drawable/icon_select"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="3dp"
        android:textColor="#ccc"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="@+id/ll_no_filter"
        app:layout_constraintStart_toStartOf="@+id/ll_no_filter"
        app:layout_constraintTop_toBottomOf="@+id/ll_no_filter" />


</androidx.constraintlayout.widget.ConstraintLayout>
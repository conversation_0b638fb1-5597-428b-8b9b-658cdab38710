<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:cardCornerRadius="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">


    <LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_gravity="center_horizontal"
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/hdr_title"
            android:textColor="@color/colorPrimary"
            android:textSize="20sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_tips"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="@string/hdr_tip"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <LinearLayout
            android:id="@+id/ll_select"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/iv_select"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginStart="10dp"
                android:padding="6dp"
                android:src="@drawable/select_hdr"
                android:textSize="12sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_tips" />

            <TextView
                android:layout_gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/remember_me"
                app:layout_constraintBottom_toBottomOf="@+id/iv_select"
                app:layout_constraintStart_toEndOf="@+id/iv_select"
                app:layout_constraintTop_toTopOf="@+id/iv_select" />

        </LinearLayout>



        <LinearLayout
            android:layout_marginTop="10dp"
            android:id="@+id/ll_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_select">

            <TextView
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:id="@+id/tv_cancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_f3f3f3_start_bottom_10dp"
                android:gravity="center"
                android:text="@string/hdr_no"
                android:textColor="#333"
                android:textSize="16sp" />

            <TextView
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:id="@+id/tv_cancel_no_tip"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/shape_cccccc_10dp"
                android:gravity="center"
                android:text="每次提醒"
                android:textColor="#333"
                android:textSize="16sp"
                android:visibility="gone" />

            <TextView
                android:paddingTop="14dp"
                android:paddingBottom="14dp"
                android:id="@+id/tv_sure"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@drawable/shape_primary_end_bottom_10dp"
                android:gravity="center"
                android:text="@string/hdr_yes"
                android:textColor="@color/white"
                android:textSize="16sp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="@color/black"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <com.google.android.exoplayer2.ui.PlayerView
        android:background="@color/black"
        android:id="@+id/video_player"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:layout_marginTop="60dp"
        app:layout_goneMarginEnd="20dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_toStartOf="@+id/txt_record"
        android:id="@+id/tv_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/dp_10"
        android:background="@drawable/shape_popup_round_angle_grey_view"
        android:padding="@dimen/dp_10"
        android:text="@string/edit"
        android:textColor="@color/white" />

    <ImageButton
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginStart="20dp"
        android:layout_marginTop="60dp"
        android:id="@+id/local_pb_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:background="@drawable/shape_bg_half_grey_circle"
        android:padding="@dimen/dp_8"
        android:src="@drawable/ic_arrow_back_white_24dp" />


</androidx.constraintlayout.widget.ConstraintLayout>
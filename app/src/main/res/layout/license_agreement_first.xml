<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/round_angle_white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:paddingTop="@dimen/dp_10">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/dp_5"
            android:gravity="center"
            android:text="@string/agreement_and_privacy"
            android:textColor="@color/black"
            android:textSize="@dimen/first_title_size"
            android:textStyle="bold" />
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="@dimen/dp_5"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@color/gray" />
        <LinearLayout

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:orientation="vertical">
            <TextView
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/welcome"
                android:textColor="@color/black"
                android:textSize="@dimen/dp_15" />


            <LinearLayout
                android:gravity="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:clickable="true"
                    android:text="@string/read"
                    android:textColor="@color/primary_text"
                    android:textSize="14dp" />

                <TextView
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:id="@+id/tv_agreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:clickable="true"
                    android:text="@string/user_agreement_data"
                    android:textColor="@color/blue_grey_500"
                    android:textSize="14dp" />

                <TextView
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:clickable="true"
                    android:text="@string/and"
                    android:textColor="@color/primary_text"
                    android:textSize="14dp" />

                <TextView
                    android:layout_marginTop="15dp"
                    android:layout_marginBottom="15dp"
                    android:id="@+id/tv_private"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:clickable="true"
                    android:text="@string/privacy_policy_data"
                    android:textColor="@color/blue_grey_500"
                    android:textSize="14dp" />


            </LinearLayout>

        </LinearLayout>

        <com.ijoyer.camera.widget.PressCircleButton
            android:id="@+id/ok"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_40"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp_15"
            android:layout_marginBottom="@dimen/dp_5"
            android:background="@drawable/circle_button_bg"
            android:gravity="center"
            android:text="@string/agree"
            android:textColor="@color/grayWhite"
            android:textSize="@dimen/dp_15" />
        <TextView
            android:layout_marginBottom="5dp"
            android:id="@+id/cancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_5"
            android:clickable="true"
            android:gravity="center"
            android:text="@string/disagree"
            android:textColor="@color/blue_grey_500"
            android:textSize="@dimen/dp_15" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

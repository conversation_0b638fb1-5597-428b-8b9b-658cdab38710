<FrameLayout
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ffffff"
        >
        <LinearLayout
            android:id="@+id/layout01"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_alignParentTop="true"
            android:layout_margin="10dp">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="筛选文件"
                android:textSize="@dimen/first_title_size"
                android:textColor="@color/text"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="时间段"
                android:textSize="@dimen/second_title_size"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="40dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/min_time_edt"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/shape_bg_c"
                    android:gravity="center"
                    android:textColor="#000000"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="到"
                    android:layout_margin="10dp"/>
                <TextView
                    android:id="@+id/max_time_edt"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/shape_bg_c"
                    android:textColor="#000000"
                    android:gravity="center" />
            </LinearLayout>
            <GridView
                android:id="@+id/time_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:numColumns="3"
                android:scrollbars="none"
                android:horizontalSpacing="2dp"
                android:verticalSpacing="2dp"
                android:listSelector="@android:color/transparent"
                />
            <TextView
                android:id="@+id/sensors_type_txv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="摄像头类型"
                android:visibility="gone"/>
            <GridView
                android:id="@+id/camera_type_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:numColumns="3"
                android:scrollbars="none"
                android:horizontalSpacing="2dp"
                android:verticalSpacing="2dp"
                android:listSelector="@android:color/transparent"
                android:visibility="gone"/>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/filter_layout"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_below="@+id/layout01"
            android:background="#ffffff"
            android:layout_marginTop="20dp"
            android:orientation="vertical">
            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="#cccccc" />
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">
                <TextView
                    android:id="@+id/filter_reset"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:textColor="@color/primary_text"
                    android:text="重置" />
                <TextView
                    android:id="@+id/filter_sure"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@color/primary"
                    android:gravity="center"
                    android:text="确定"
                    android:textColor="#ffffff" />
            </LinearLayout>
        </LinearLayout>
    </RelativeLayout>
</FrameLayout>
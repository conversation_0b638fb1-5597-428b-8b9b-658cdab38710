<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:visibility="gone"
        android:textSize="50sp"
        android:textColor="@color/white"
        android:id="@+id/tv_progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_marginTop="10dp"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="460dp"
        android:background="#333">


        <TextView
            android:id="@+id/tv_filter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="滤镜"
            android:textColor="#cccccc"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginTop="12dp"
            android:background="#cccccc"
            app:layout_constraintTop_toBottomOf="@+id/tv_filter" />

        <ImageView
            android:id="@+id/iv_sure"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="10dp"
            android:padding="5dp"
            android:src="@drawable/icon_sure"
            app:layout_constraintBottom_toBottomOf="@+id/tv_filter"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_filter" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_filter"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            app:layout_constraintBottom_toTopOf="@+id/isb_progress"
            app:layout_constraintTop_toBottomOf="@+id/view"
            app:layout_constraintVertical_bias="0.0"
            tools:layout_editor_absoluteX="0dp" />

        <TextView
            android:id="@+id/tv_progress_str"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:text="效果比例"
            android:textColor="#ccc"
            app:layout_constraintBottom_toBottomOf="@+id/isb_progress"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/isb_progress" />

        <com.warkiz.widget.IndicatorSeekBar
            android:id="@+id/isb_progress"
            android:layout_width="0dp"
            android:layout_height="20dp"
            android:layout_gravity="center"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:layout_marginBottom="40dp"
            android:gravity="center"
            android:paddingTop="5dp"
            app:isb_max="100"
            app:isb_show_indicator="none"
            app:isb_show_tick_marks_type="divider"
            app:isb_show_tick_texts="false"
            app:isb_thumb_drawable="@drawable/icon_thumb_white_circle"
            app:isb_thumb_size="15dp"
            app:isb_tick_marks_color="@color/transparent"
            app:isb_tick_texts_color="#333"
            app:isb_track_background_color="#33000000"
            app:isb_track_background_size="10dp"
            app:isb_track_progress_color="#ccc"
            app:isb_track_progress_size="10dp"
            app:isb_track_rounded_corners="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tv_progress_str" />


    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>
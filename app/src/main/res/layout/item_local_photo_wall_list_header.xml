<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical"
    tools:showIn="@layout/fragment_multi_pb_photo">
    <TextView
        android:id="@+id/photo_wall_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="left"
        android:paddingStart="4dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:textColor="@color/secondary_text"
        android:textSize="@dimen/first_title_size" />
</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/charcoal_gray">
    <RelativeLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="fill_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary">
        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:background="@drawable/selector_primary"
            android:padding="10dp"
            android:src="@drawable/ic_arrow_back_white_24dp" />
        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/back_btn"
            android:layout_centerVertical="true"
            android:text="@string/title_fragment_btpair_begin"
            android:textColor="@color/icons"
            android:textSize="@dimen/navigation_size" />
        <ImageButton
            android:id="@+id/refresh_btn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:padding="10dp"
            android:background="@drawable/selector_primary"
            android:src="@drawable/ic_refresh_white_24dp"
            />
    </RelativeLayout>
    <TextView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:text="@string/text_ready_pair"
        style="@style/customTextviewStyleLight"/>
    <ListView
        android:id="@+id/choose_blutTooth_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_margin="10dp"
        android:background="@color/dark_gray"
        android:layout_weight="1" >
    </ListView>
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:weightSum="1"
        android:padding="20dp"
        android:visibility="gone"
        >
        <Button
            android:id="@+id/button_bluetooth_search"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="0.5"
            android:text="@string/text_btpair_search_camera"
            style="@style/customButtonStyle01"
            android:visibility="gone"
            />
        <Button
            android:id="@+id/button_ble_search"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="0.5"
            android:text="@string/text_btpair_search_ble"
            style="@style/customButtonStyle01"
            android:visibility="gone"
            />
    </LinearLayout>
</LinearLayout>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/charcoal_gray"
    android:orientation="vertical">
    <RelativeLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary">
        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:background="@drawable/selector_primary"
            android:padding="10dp"
            android:src="@drawable/ic_arrow_back_white_24dp" />
        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/back_btn"
            android:layout_centerVertical="true"
            android:text="@string/title_activity_add_new_cam"
            android:textColor="@color/icons"
            android:textSize="@dimen/navigation_size" />
        <TextView
            android:id="@+id/save"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="@string/text_btpair_done"
            android:textColor="@color/primary_light"
            android:padding="10dp"
            android:background="@drawable/selector_primary"
            android:textSize="@dimen/first_title_size"
            android:visibility="gone"/>
    </RelativeLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="@string/text_btpair_already_connect_cam"
            style="@style/customTextviewStyleLight"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="1">
            <Button
                android:id="@+id/wifi_connect_camera"
                style="@style/customButtonStyle02"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:text="@string/text_btpair_connect_wifi" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="@string/text_connect_camera_by_usb"
            style="@style/customTextviewStyleLight"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="1">
            <Button
                android:id="@+id/usb_connect_camera"
                style="@style/customButtonStyle02"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:text="@string/text_add_camaera_usb" />
        </LinearLayout>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="@string/text_btpair_support_bt"
            style="@style/customTextviewStyleLight"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="1">
            <Button
                android:id="@+id/bt_pair"
                style="@style/customButtonStyle02"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:text="@string/text_btpair" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>

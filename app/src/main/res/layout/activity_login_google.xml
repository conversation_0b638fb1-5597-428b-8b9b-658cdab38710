<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/blue_grey_700"
    android:orientation="vertical"
    android:weightSum="4"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="3"
        android:gravity="center_horizontal"
        android:orientation="vertical">
        <ImageView
            android:id="@+id/google_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginBottom="10dp"
            android:layout_marginTop="100dp"
            android:contentDescription="@string/desc_google_icon"
            android:src="@drawable/googleg_color" />
        <TextView
            android:id="@+id/title_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/title_text"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="36sp" />
        <TextView
            android:id="@+id/status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/signed_out"
            android:textColor="@android:color/white"
            android:textSize="14sp" />
        <TextView
            android:id="@+id/detail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fadeScrollbars="true"
            android:gravity="center"
            android:maxLines="5"
            android:padding="10dp"
            android:scrollbars="vertical"
            android:textColor="@android:color/white"
            android:textSize="14sp" />
        <Button
            android:id="@+id/button_optional_action"
            style="@style/Base.Widget.AppCompat.Button.Borderless.Colored"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:text="Optional Action"
            tools:visibility="visible" />
    </LinearLayout>
    <RelativeLayout
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/blue_grey_900">
        <com.google.android.gms.common.SignInButton
            android:id="@+id/sign_in_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="visible"
            tools:visibility="gone" />
        <LinearLayout
            android:id="@+id/sign_out_and_disconnect"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:visibility="gone"
            tools:visibility="visible">
            <Button
                android:id="@+id/sign_out_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/sign_out"
                android:theme="@style/ThemeOverlay.MyDarkButton" />
            <Button
                android:id="@+id/disconnect_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/disconnect"
                android:theme="@style/ThemeOverlay.MyDarkButton" />
            <Button
                android:id="@+id/refresh_token_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/refresh_token"
                android:theme="@style/ThemeOverlay.MyDarkButton"
                android:visibility="gone"/>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>

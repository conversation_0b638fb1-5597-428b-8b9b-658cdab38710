<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/setupMainMenu"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_above="@id/bottomBar"
    android:layout_centerInParent="true"
    android:layout_gravity="center"
    android:layout_marginLeft="@dimen/dp_10"
    android:layout_marginTop="@dimen/dp_25"
    android:layout_marginRight="@dimen/dp_10"
    android:background="@drawable/shape_popup_round_angle_grey_view"
    android:gravity="center"
    android:orientation="vertical"
    android:visibility="gone"
    tools:showIn="@layout/content_panorama_preview">
    <ListView
        android:id="@+id/setup_menu_listView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true"
        android:divider="@color/divider"
        android:dividerHeight="0.5dp" />
</RelativeLayout>

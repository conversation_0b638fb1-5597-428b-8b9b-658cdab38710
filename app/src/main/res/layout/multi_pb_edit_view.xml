<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/edit_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/item_height"
    android:background="@color/primary_dark"
    android:orientation="horizontal"
    android:padding="10dp"
    android:visibility="gone">
    <ImageButton
        android:id="@+id/action_select"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:padding="6dp"
        android:src="@drawable/ic_select_all_white_24dp"
        android:visibility="gone" />
    <ImageButton
        android:id="@+id/action_delete"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:padding="6dp"
        android:src="@drawable/ic_delete_white_24dp" />
    <ImageButton
        android:id="@+id/action_download"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:padding="6dp"
        android:src="@drawable/ic_file_download_white_24dp" />
    <TextView
        android:id="@+id/info_selected_num"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@color/primary"
        android:gravity="center"
        android:text="Selected(0)"
        android:textColor="@color/white"
        android:visibility="gone" />
</LinearLayout>

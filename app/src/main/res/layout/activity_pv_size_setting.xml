<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary"
        android:theme="@style/FullScreenTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:popupTheme="@style/FullScreenTheme.PopupOverlay"/>
    </com.google.android.material.appbar.AppBarLayout>
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/space_10"
            android:orientation="vertical">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_20"
                android:text="@string/please_set_the_correct_resolution_and_fps"
                android:textSize="@dimen/text_size_18"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_20"
                android:text="@string/video_resolution"
                android:textSize="@dimen/text_size_14"/>
            <RadioGroup
                android:id="@+id/video_size_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <!--<RadioButton-->
                    <!--android:id="@+id/video_size_1920_960"-->
                    <!--android:layout_width="match_parent"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:checked="true"-->
                    <!--android:text="1920 x 960"/>-->
                <!--<RadioButton-->
                    <!--android:id="@+id/video_size_1920_1080"-->
                    <!--android:layout_width="match_parent"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:text="1920 x 1080"/>-->
                <!--<RadioButton-->
                    <!--android:id="@+id/video_size_2k"-->
                    <!--android:layout_width="match_parent"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:text="2.7K"/>-->
                <!--<RadioButton-->
                    <!--android:id="@+id/video_size_4k"-->
                    <!--android:layout_width="match_parent"-->
                    <!--android:layout_height="wrap_content"-->
                    <!--android:text="4K"/>-->
            </RadioGroup>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_10"
                android:text="@string/video_frame_rate"
                android:textSize="@dimen/text_size_14"/>
            <RadioGroup
                android:id="@+id/video_fps_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <RadioButton
                    android:id="@+id/video_fps_30"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="30 fps"/>
                <RadioButton
                    android:id="@+id/video_fps_15"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="15 fps"/>
                <RadioButton
                    android:id="@+id/video_fps_10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="10 fps"/>
            </RadioGroup>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_10"
                android:text="Video codec"
                android:textSize="@dimen/text_size_14"/>
            <RadioGroup
                android:id="@+id/video_codec_group"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <RadioButton
                    android:id="@+id/video_codec_h264"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="H264"/>
                <RadioButton
                    android:id="@+id/video_codec_mjpg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="MJPG"/>
            </RadioGroup>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_20"
                android:gravity="center"
                android:weightSum="2">
                <Button
                    android:id="@+id/start_pv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="start Pv"/>
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dip"
        android:text="SSID"
        android:textSize="18sp"
        android:gravity="left"
        />
    <EditText
        android:id="@+id/wifi_ssid"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_marginLeft="20dip"
        android:layout_marginRight="20dip"
        android:gravity="fill_horizontal"
        android:maxLength="20"
        android:textSize="16sp"
        android:scrollHorizontally="true" />
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dip"
        android:text="Password"
        android:gravity="left"
        android:textSize="18sp"
        />
    <EditText
        android:id="@+id/wifi_password"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_marginLeft="20dip"
        android:layout_marginRight="20dip"
        android:scrollHorizontally="true"
        android:gravity="fill_horizontal"
        android:textSize="16sp"
        />
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ListView
        android:id="@+id/downloadStatus"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_below="@+id/message"
        android:background="@color/white"
        android:dividerHeight="0.5dp"
        android:divider="@color/divider">
    </ListView>
    <TextView
        android:id="@+id/message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/title"
        android:text="test"
        android:textColor="@color/primary_light"
        android:textSize="15sp"
        android:padding="10dp"
        android:background="@color/primary_dark"/>
</LinearLayout>

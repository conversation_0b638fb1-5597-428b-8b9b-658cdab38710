<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:background="@drawable/shape_popup_round_angle_grey_view"
    android:gravity="center"
    android:orientation="vertical">
    <ProgressBar
        android:id="@+id/pb_load"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:layout_centerInParent="true"
        android:indeterminateDrawable="@drawable/rotate_progressbar" />
    <TextView
        android:id="@+id/tv_load_dialog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:text="@string/loading"
        android:textColor="#f0f0f0"
        android:textSize="14sp" />
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto" android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    android:background="@color/primary"
    tools:context="com.icatch.mobilecam.ui.activity.LaunchActivity">
    <LinearLayout
        android:id="@+id/launch_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <ListView
            android:id="@+id/cam_slot_listview"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="3"
            android:padding="16dp"
            android:choiceMode="singleChoice"
            android:dividerHeight="5dp"
            android:orientation="vertical">
        </ListView>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="2"
            android:orientation="vertical">
            <TextView
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:background="@color/primary_dark"
                android:gravity="center_horizontal"
                android:text="@string/title_local_media"
                android:textColor="@color/white"
                android:textSize="20sp" />
            <LinearLayout
                android:layout_width="fill_parent"
                android:layout_height="fill_parent"
                android:layout_margin="10dp"
                android:baselineAligned="false"
                android:orientation="horizontal"
                android:weightSum="2">
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="fill_parent"
                    android:layout_marginRight="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/round_angle_view">
                    <com.icatch.mobilecam.ui.ExtendComponent.RoundAngleImageView
                        android:id="@+id/local_photo"
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/local_default_thumbnail"
                        app:roundHeight="15dp"
                        app:roundWidth="15dp" />
                    <TextView
                        android:id="@+id/no_local_photos"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="@string/no_files_found"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:visibility="gone" />
                    <TextView
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:gravity="center_horizontal"
                        android:text="@string/title_photo"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </RelativeLayout>
                <RelativeLayout
                    android:layout_width="fill_parent"
                    android:layout_height="fill_parent"
                    android:layout_marginLeft="10dp"
                    android:layout_weight="1"
                    android:background="@drawable/round_angle_view">
                    <com.icatch.mobilecam.ui.ExtendComponent.RoundAngleImageView
                        android:id="@+id/local_video"
                        android:layout_width="fill_parent"
                        android:layout_height="fill_parent"
                        android:scaleType="centerCrop"
                        android:src="@drawable/local_default_thumbnail"
                        app:roundHeight="15dp"
                        app:roundWidth="15dp" />
                    <TextView
                        android:id="@+id/no_local_videos"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:text="@string/no_files_found"
                        android:textColor="@color/white"
                        android:textSize="15sp"
                        android:visibility="gone" />
                    <TextView
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:gravity="center_horizontal"
                        android:text="@string/title_video"
                        android:textColor="@color/white"
                        android:textSize="18sp" />
                </RelativeLayout>
            </LinearLayout>
        </LinearLayout>
    </LinearLayout>
    <FrameLayout
        android:id="@+id/launch_setting_frame"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:visibility="gone"
        >
    </FrameLayout>
</RelativeLayout>

<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_centerVertical="true"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:padding="10dp">
    <TextView
        android:id="@+id/bluetooth_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="bluetooth device"
        android:textColor="@color/lightblue"
        android:textSize="20sp" />
    <TextView
        android:id="@+id/bluetooth_mac"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_below="@+id/bluetooth_name"
        android:layout_marginTop="10dp"
        android:text="bluetooth mac"
        android:textColor="@color/black"
        android:textSize="15sp" />
    <TextView
        android:id="@+id/bluetooth_connect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_below="@+id/bluetooth_name"
        android:layout_marginTop="10dp"
        android:text="Unbinded"
        android:textColor="@color/red"
        android:textSize="15sp" />
</RelativeLayout>

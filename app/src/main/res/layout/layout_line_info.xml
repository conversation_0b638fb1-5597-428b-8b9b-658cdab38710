<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_marginTop="2dp"
        android:layout_marginRight="8dp"
        android:layout_marginBottom="5dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1"
            android:alpha="0.5"
            android:background="#4DB6AC"
            android:gravity="start" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">


            <TextView
                android:id="@+id/tv_line_info_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:text="name"
                android:textColor="@color/secondary_text"
                android:textSize="12sp" />

            <com.qmuiteam.qmui.layout.QMUIMediumTextView
                android:id="@+id/tv_line_info_remark"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:text="备注"
                android:textColor="#333333"
                android:textSize="12sp" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="vertical">


            <TextView
                android:id="@+id/tv_line_info_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:text="time"
                android:textColor="@color/secondary_text"
                android:textSize="12sp" />
            <TextView
                android:id="@+id/tv_line_info_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:text="size"
                android:textColor="@color/secondary_text"
                android:textSize="12sp" />



            <com.qmuiteam.qmui.layout.QMUIMediumTextView
                android:id="@+id/tv_line_info_is_download"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="8dp"
                android:paddingRight="8dp"
                android:visibility="gone"
                android:text="已下载"
                android:textColor="#333333"
                android:textSize="12sp" />

        </LinearLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1"
            android:alpha="0.5"
            android:background="#4DB6AC"
            android:gravity="end" />
    </LinearLayout>
</LinearLayout>

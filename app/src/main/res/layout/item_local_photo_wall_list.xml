<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/greyish_white"
    android:orientation="vertical">

    <include
        android:id="@+id/local_photo_wall_header_layout"
        layout="@layout/item_local_photo_wall_list_header" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/selector_white"
        android:orientation="horizontal">

        <FrameLayout
            android:layout_width="@dimen/dp_30"
            android:layout_height="@dimen/dp_60"
            android:layout_gravity="center"
            android:paddingStart="@dimen/dp_5">

            <TextView
                android:id="@+id/quarter_0"
                android:layout_width="@dimen/dp_18"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|center_horizontal"
                android:layout_marginBottom="0dp"
                android:gravity="bottom|center_horizontal"
                android:paddingBottom="0dp"
                android:textColor="@color/grayWhite"
                android:visibility="gone" />

            <TextView
                android:id="@+id/quarter_1"
                android:layout_width="@dimen/dp_18"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:textColor="@color/grayWhite"
                android:visibility="gone" />

            <TextView
                android:id="@+id/quarter_2"
                android:layout_width="@dimen/dp_18"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:textColor="@color/grayWhite"
                android:visibility="gone" />

            <TextView
                android:id="@+id/quarter_3"
                android:layout_width="@dimen/dp_18"
                android:layout_height="wrap_content"
                android:layout_gravity="top|center_horizontal"
                android:layout_marginTop="0dp"
                android:gravity="top|center_horizontal"
                android:paddingTop="0dp"
                android:textColor="@color/grayWhite"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/quarter_none"
                android:layout_width="@dimen/dp_25"
                android:layout_height="20dp"
                android:layout_gravity="center"
                android:background="@color/greyish_white"
                android:gravity="center"
                android:scaleType="center" />
        </FrameLayout>

        <FrameLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/local_photo_thumbnail_list"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_margin="5dp"
                android:scaleType="centerCrop"
                android:src="@drawable/pictures_no" />

            <ImageView
                android:id="@+id/video_sign"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_gravity="center"
                android:src="@drawable/videooverlay" />

            <ImageView
                android:id="@+id/is_panorama"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="left|bottom"
                android:src="@drawable/ic_panorama_18dp"
                android:visibility="gone" />
        </FrameLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/local_photo_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="6dp"
                android:text="photo name"
                android:textColor="@color/primary_text"
                android:textSize="@dimen/first_title_size" />

            <TextView
                android:id="@+id/local_photo_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="6dp"
                android:text="photo name"
                android:textColor="@color/primary_text"
                android:textSize="@dimen/first_title_size" />

            <TextView
                android:id="@+id/local_video_duration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/local_video_duration"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="4dp"
                android:text="00:00"
                android:textColor="@color/secondary_text"
                android:textSize="@dimen/second_title_size"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/local_video_duration"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/local_photo_size"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="12.5M"
                    android:textColor="@color/secondary_text"
                    android:textSize="@dimen/second_title_size" />

                <TextView
                    android:id="@+id/local_photo_date"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="10dp"
                    android:text="2015-09-02 11:33"
                    android:textColor="@color/secondary_text"
                    android:textSize="@dimen/second_title_size" />
            </LinearLayout>
        </LinearLayout>

        <ImageView
            android:id="@+id/local_photo_wall_list_edit"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/dp_5"
            android:paddingEnd="@dimen/dp_10"
            android:src="@drawable/ic_check_box_blank_grey" />

        <ImageView
            android:id="@+id/local_photo_wall_list_edit_remark_name"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/dp_5"
            android:paddingEnd="@dimen/dp_10"
            android:src="@drawable/ic_edit_24" />

        <ImageView
            android:id="@+id/local_photo_wall_list_share"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/dp_5"
            android:paddingEnd="@dimen/dp_10"
            android:src="@drawable/ic_share_black_24dp" />
    </LinearLayout>
</LinearLayout>

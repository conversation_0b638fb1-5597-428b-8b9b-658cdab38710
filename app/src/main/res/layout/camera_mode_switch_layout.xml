<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/half_transparent_grey"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingStart="@dimen/dp_10"
    android:paddingEnd="@dimen/dp_10"
    android:paddingBottom="@dimen/dp_10">
    <RadioGroup
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <RadioButton
            android:id="@+id/video_radio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/selector_radio_video"
            android:button="@null"
            android:visibility="visible" />
        <RadioButton
            android:id="@+id/capture_radio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/selector_radio_capture"
            android:button="@null"
            android:checked="true" />
        <RadioButton
            android:id="@+id/timeLapse_radio"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp_10"
            android:background="@drawable/selector_radio_timelapse"
            android:button="@null"
            android:visibility="gone" />
    </RadioGroup>
</LinearLayout>

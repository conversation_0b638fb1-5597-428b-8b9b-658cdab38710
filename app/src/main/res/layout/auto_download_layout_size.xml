<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/full_transparent"
    android:gravity="center_vertical"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_10"
        android:minHeight="@dimen/dp_40"
        android:paddingTop="@dimen/dp_5"
        android:paddingBottom="@dimen/dp_5">
        <TextView
            android:id="@+id/item_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/setting_auto_download_size_limit"
            android:textColor="@color/primary_text"
            android:textSize="@dimen/first_title_size" />
        <TextView
            android:id="@+id/download_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/item_text"
            android:text="1.0GB"
            android:textColor="@color/secondary_text"
            android:textSize="@dimen/second_title_size" />
    </RelativeLayout>
</LinearLayout>

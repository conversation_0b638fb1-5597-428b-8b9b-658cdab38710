<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/edit_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/item_height"
    android:background="@color/primary_dark"
    android:orientation="horizontal"
    android:padding="10dp"
    android:visibility="gone"
    tools:showIn="@layout/content_local_multi_pb">
    <ImageButton
        android:id="@+id/action_select"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:padding="6dp"
        android:src="@drawable/ic_select_all_white_24dp"
        android:visibility="gone" />
    <TextView
        android:id="@+id/stitch_batch"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:gravity="center"
        android:text="批量拼接"
        android:textColor="@color/white" />
    <ImageButton
        android:id="@+id/action_delete"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:padding="6dp"
        android:src="@drawable/ic_delete_white_24dp" />
    <ImageButton
        android:id="@+id/action_share"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:padding="6dp"
        android:src="@drawable/ic_share_white_24dp"
        android:visibility="gone" />
    <TextView
        android:id="@+id/info_selected_num"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:background="@drawable/selector_primary"
        android:gravity="center"
        android:text="Selected(0)"
        android:textColor="@color/white"
        android:visibility="gone" />
</LinearLayout>

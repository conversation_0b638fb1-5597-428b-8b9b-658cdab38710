<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/full_transparent"
    android:gravity="center_vertical"
    android:orientation="vertical">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_10"
        android:minHeight="@dimen/dp_40"
        android:paddingTop="@dimen/dp_5"
        android:paddingBottom="@dimen/dp_5">
        <TextView
            android:id="@+id/item_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:text="name"
            android:textColor="@color/primary_text"
            android:textSize="@dimen/first_title_size" />
        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/switchCompat"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="10dp"
            android:gravity="center" />
    </RelativeLayout>
</LinearLayout>

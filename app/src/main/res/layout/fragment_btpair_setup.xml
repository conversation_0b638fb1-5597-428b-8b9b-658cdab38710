<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/charcoal_gray">
    <RelativeLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="fill_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary">
        <ImageButton
            android:id="@+id/back_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerVertical="true"
            android:background="@drawable/selector_primary"
            android:padding="10dp"
            android:src="@drawable/ic_arrow_back_white_24dp" />
        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toRightOf="@+id/back_btn"
            android:layout_centerVertical="true"
            android:text="@string/title_fragment_btpair_wifisetup"
            android:textColor="@color/icons"
            android:textSize="@dimen/navigation_size" />
        <TextView
            android:id="@+id/skip_txv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:text="@string/text_btpair_skip"
            android:textColor="@color/primary_light"
            android:padding="10dp"
            android:background="@drawable/selector_primary"
            android:textSize="@dimen/first_title_size"
            />
    </RelativeLayout>
    <TextView
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        android:text="@string/text_btpair_setup_camwifi"
        style="@style/customTextviewStyleLight"/>
    <EditText
        android:id="@+id/bt_wifisetup_camera_ssid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:inputType="textCapWords"
        android:hint="Ssid"
        android:textColorHint="@color/white"
        android:textColor="@color/white"
        android:maxLength="20"/>
    <EditText
        android:id="@+id/bt_wifisetup_camera_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:ems="10"
        android:inputType="number"
        android:hint="Password"
        android:textColorHint="@color/white"
        android:textColor="@color/white"
        android:maxLength="10"
        />
    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:weightSum="1" >
        <Button
            android:id="@+id/bt_wifisetup"
            android:layout_width="0dp"
            android:layout_weight="0.5"
            android:text="@string/text_btpair_setup"
            style="@style/customButtonStyle02"
            />
    </LinearLayout>
</LinearLayout>

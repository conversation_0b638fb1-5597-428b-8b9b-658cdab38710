<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">
    <TextView
        android:id="@+id/photo_wall_header1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="left"
        android:paddingStart="4dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:textColor="@color/secondary_text"
        android:textSize="@dimen/first_title_size"
        tools:ignore="RtlHardcoded,RtlSymmetry" />
</RelativeLayout>

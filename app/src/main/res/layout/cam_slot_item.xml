<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/round_angle_view"
    android:orientation="horizontal" >
    <LinearLayout
        android:id="@+id/slot_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:orientation="vertical" >
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal" >
            <ImageView
                android:id="@+id/slot_connect_sign"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="5dp"
                android:src="@drawable/ic_add_circle_24dp" />
            <TextView
                android:id="@+id/slot_connect_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="5dp"
                android:gravity="center_vertical"
                android:text="Disconnected"
                android:textColor="@color/greyish_white"
                android:textSize="15sp" />
        </LinearLayout>
        <TextView
            android:id="@+id/slot_camera_name"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="top"
            android:layout_marginLeft="5dp"
            android:text="WDV8000_FDG"
            android:textColor="@color/white"
            android:textSize="18sp" />
    </LinearLayout>
    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_gravity="center_vertical"
        android:layout_weight="1">
        <com.icatch.mobilecam.ui.ExtendComponent.RoundAngleImageView
            android:id="@+id/slotPhoto"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="5dp"
            android:background="#1d1d1d"
            android:scaleType="centerCrop"
            app:roundHeight="15dp"
            app:roundWidth="15dp" />
        <ImageView
            android:id="@+id/delete_camera"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentTop="true"
            android:background="@drawable/ic_remove_circle_red_24dp" />
</RelativeLayout>
</LinearLayout>

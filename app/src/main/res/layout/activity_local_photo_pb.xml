<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    app:layout_behavior="@string/appbar_scrolling_view_behavior"
    tools:context=".ui.activity.LocalPhotoPbActivity">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <SurfaceView
            android:id="@+id/m_surfaceView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:visibility="visible" />
        <com.icatch.mobilecam.ui.ExtendComponent.HackyViewPager
            android:id="@+id/viewpager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerInParent="true">
        </com.icatch.mobilecam.ui.ExtendComponent.HackyViewPager>
        <ImageButton
            android:id="@+id/do_previous"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:background="@drawable/ic_chevron_left_48dp"
            android:visibility="gone" />
        <ImageButton
            android:id="@+id/do_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@drawable/ic_chevron_right_48dp"
            android:visibility="gone" />
        <LinearLayout
            android:id="@+id/local_pb_bottom_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_margin="@dimen/dp_10"
            android:background="@drawable/shape_popup_round_angle_grey_view"
            android:orientation="horizontal"
            android:padding="@dimen/dp_10">
            <ImageButton
                android:id="@+id/local_photo_pb_delete"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="@drawable/selector_transparent2gray"
                android:src="@drawable/ic_delete_white_24dp" />
            <ImageButton
                android:id="@+id/local_photo_pb_share"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="@drawable/selector_transparent2gray"
                android:src="@drawable/ic_share_white_24dp"
                android:visibility="gone" />
            <ImageButton
                android:id="@+id/local_photo_pb_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_transparent2gray"
                android:src="@drawable/ic_info_white_36dp"
                android:visibility="gone" />
            <TextView
                android:id="@+id/panorama_type_btn"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:background="@drawable/selector_transparent2gray"
                android:gravity="center"
                android:text="@string/text_panorama"
                android:textColor="@color/white" />
        </LinearLayout>
        <RelativeLayout
            android:id="@+id/local_pb_top_layout"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:layout_alignParentTop="true"
            android:layout_marginTop="@dimen/dp_25"
            android:background="@color/full_transparent"
            android:orientation="horizontal">
            <ImageButton
                android:id="@+id/local_pb_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="@dimen/dp_10"
                android:background="@drawable/shape_bg_half_grey_circle"
                android:padding="@dimen/dp_8"
                android:src="@drawable/ic_arrow_back_white_24dp" />
            <TextView
                android:id="@+id/pb_index_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center_vertical"
                android:background="@drawable/shape_bg_half_grey_circle"
                android:padding="@dimen/dp_8"
                android:textColor="@color/grayWhite"
                android:textSize="18sp"
                android:visibility="gone" />
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>

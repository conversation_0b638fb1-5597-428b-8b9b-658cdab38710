<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="80dp">

    <TextView
        android:visibility="gone"
        android:id="@+id/tv_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:paddingStart="10dp"
        android:layout_gravity="center_vertical"
        android:textColor="@color/secondary_text"
        android:textSize="@dimen/first_title_size" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_item"
        android:visibility="visible"
        android:layout_width="match_parent"
        android:layout_height="70dp">


        <ImageView
            android:scaleType="centerCrop"
            android:id="@+id/iv_bg"
            android:layout_marginStart="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:adjustViewBounds="true"
            android:layout_width="60dp"
            android:layout_height="60dp"/>

        <TextView
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            app:layout_constraintBottom_toTopOf="@+id/tv_file_time"
            app:layout_constraintEnd_toStartOf="@+id/tv_check"
            android:textSize="16sp"
            android:textColor="#333"
            android:id="@+id/tv_name"
            app:layout_constraintStart_toEndOf="@+id/iv_bg"
            app:layout_constraintTop_toTopOf="@+id/iv_bg"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>

        <TextView
            android:layout_marginStart="10dp"
            android:layout_marginEnd="10dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_name"
            app:layout_constraintEnd_toStartOf="@+id/tv_check"
            android:textSize="14sp"
            android:textColor="#666"
            android:id="@+id/tv_file_time"
            app:layout_constraintStart_toEndOf="@+id/iv_bg"
            app:layout_constraintBottom_toBottomOf="@+id/iv_bg"
            android:layout_width="0dp"
            android:layout_height="wrap_content"/>


        <TextView
            android:layout_marginEnd="10dp"
            android:textColor="#ccc"
            android:text="1"
            android:gravity="center"
            android:id="@+id/tv_check"
            android:adjustViewBounds="true"
            android:background="@drawable/shape_video_bg_ccc"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="30dp"
            android:layout_height="30dp"/>

        <View
            android:background="#ccc"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="1px"/>


    </androidx.constraintlayout.widget.ConstraintLayout>


</LinearLayout>
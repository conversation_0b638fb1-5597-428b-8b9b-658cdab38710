<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/full_transparent"
    android:gravity="center_vertical"
    android:orientation="vertical">
    <RelativeLayout
        android:visibility="visible"
        android:id="@+id/rl_default_item"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp_10"
        android:minHeight="@dimen/dp_40"
        android:paddingTop="@dimen/dp_5"
        android:paddingBottom="@dimen/dp_5">
        <TextView
            android:id="@+id/item_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:text="Title"
            android:textColor="@color/primary_text"
            android:textSize="@dimen/first_title_size" />
        <TextView
            android:id="@+id/item_value"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/item_arrow"
            android:gravity="center"
            android:text="value"
            android:textColor="@color/secondary_text"
            android:textSize="@dimen/second_title_size" />
        <ImageView
            android:id="@+id/item_arrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@drawable/pv_option_arrow" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:padding="10dp"
        android:visibility="gone"
        android:id="@+id/cl_have_tip_item"
        android:layout_width="match_parent"
        android:layout_height="60dp">

        <TextView
            app:layout_constraintBottom_toTopOf="@+id/tv_tip"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:textColor="@color/primary_text"
            android:textSize="16sp"/>

        <TextView
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintStart_toStartOf="@+id/tv_title"
            android:id="@+id/tv_tip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:gravity="center"
            android:textColor="#999"
            android:textSize="10sp"/>


        <com.kyleduo.switchbutton.SwitchButton
            app:kswThumbColor="#2F4195"
            android:layout_marginEnd="5dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:id="@+id/sw"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="com.icatch.mobilecam.ui.activity.RemoteMultiPbActivity">
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/FullScreenTheme.AppBarOverlay">
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary"
            app:contentInsetStart="0dp"
            app:layout_collapseMode="pin"
            app:popupTheme="@style/FullScreenTheme.PopupOverlay">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/camera"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_18" />
        </androidx.appcompat.widget.Toolbar>
        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="35dp"
            android:layout_gravity="center_vertical"
            android:background="@drawable/actionbar_border"
            app:tabGravity="fill"
            app:tabIndicatorColor="@color/cambridge_blue"
            app:tabIndicatorHeight="3dp"
            app:tabMode="fixed"
            app:tabSelectedTextColor="@color/white"
            app:tabTextAppearance="@style/MyTabLayoutTextAppearanceInverse" />
    </com.google.android.material.appbar.AppBarLayout>
    <include layout="@layout/content_multi_pb" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>

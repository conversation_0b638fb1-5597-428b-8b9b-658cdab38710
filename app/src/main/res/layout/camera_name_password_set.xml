<?xml version="1.0" encoding="utf-8"?>  
<LinearLayout  
  xmlns:android="http://schemas.android.com/apk/res/android"  
  android:orientation="vertical"  
  android:layout_width="fill_parent"  
  android:layout_height="fill_parent">  
<TextView
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"  
    android:layout_marginLeft="20dip"  
    android:text="@string/camera_wifi_name"  
    android:gravity="left"  
/>
    <EditText
        android:id="@+id/camera_name"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_marginLeft="20dip"
        android:layout_marginRight="20dip"
        android:gravity="fill_horizontal"
        android:maxLength="20"
        android:scrollHorizontally="true" />
    <TextView
    android:layout_width="wrap_content"  
    android:layout_height="wrap_content"  
    android:layout_marginLeft="20dip"  
    android:text="@string/camera_wifi_password"  
    android:gravity="left"  
/>  
<EditText   
    android:id="@+id/wifi_password"  
    android:layout_width="fill_parent"  
    android:layout_height="fill_parent"  
    android:layout_marginLeft="20dip"  
    android:layout_marginRight="20dip"  
    android:scrollHorizontally="true"  
    android:gravity="fill_horizontal"  
    android:inputType="numberPassword"
    android:maxLength="10"
/>  
</LinearLayout>  

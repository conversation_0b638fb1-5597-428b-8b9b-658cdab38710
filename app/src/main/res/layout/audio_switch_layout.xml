<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_white">
    <TextView
        android:id="@+id/item_text"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/item_height"
        android:textColor="@color/primary_text"
        android:textSize="@dimen/first_title_size"
        android:layout_centerVertical="true"
        android:text="@string/setting_audio_switch"
        android:layout_marginLeft="10dp"
        android:gravity="center"
        />
    <CheckBox
        android:id="@+id/switcher"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_centerVertical="true"
        android:button="@drawable/selector_switch_btn"
        android:layout_margin="10dp"
        />
</RelativeLayout>

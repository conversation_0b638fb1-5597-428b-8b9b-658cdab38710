<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/local_video_item_selector"
    android:padding="2dp">

    <ImageView
        android:id="@+id/local_photo_wall_grid_item"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:scaleType="centerCrop"
        android:src="@drawable/pictures_no" />

    <ImageView
        android:id="@+id/video_sign"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:src="@drawable/videooverlay" />

    <ImageView
        android:id="@+id/local_photo_wall_grid_edit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true"
        android:src="@drawable/ic_check_box_blank_grey" />

    <ImageView
        android:id="@+id/is_panorama"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:src="@drawable/ic_panorama_18dp" />

    <com.qmuiteam.qmui.layout.QMUILinearLayout
        android:id="@+id/llUnDownload"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:gravity="center"
        android:background="#99333333"
        android:paddingHorizontal="5dp"
        android:paddingVertical="2dp"
        android:visibility="gone"
        app:qmui_radius="11dp"
        tools:visibility="visible">

        <View
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginTop="2dp"
            android:background="@drawable/ic_file_download_grey_600_24dp"
            android:backgroundTint="@color/white" />

        <com.qmuiteam.qmui.layout.QMUITextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="未下载"
            android:textSize="12sp"
            android:textColor="@color/white" />
    </com.qmuiteam.qmui.layout.QMUILinearLayout>
</RelativeLayout>
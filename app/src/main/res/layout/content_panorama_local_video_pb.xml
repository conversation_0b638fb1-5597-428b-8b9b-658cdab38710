<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:ProgressWheel="http://schemas.android.com/apk/res-auto"
                xmlns:app="http://schemas.android.com/apk/res-auto"
                xmlns:tools="http://schemas.android.com/tools"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"
                tools:context="com.icatch.mobilecam.ui.activity.LocalVideoPbActivity"
    >
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        >
        <TextView
            android:id="@+id/codec_info_txv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:padding="20dp"
            android:background="@color/half_transparent_grey"
            android:textColor="@color/primary_light"
            android:visibility="gone"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >
            <SurfaceView
                android:id="@+id/m_surfaceView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"/>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/local_pb_top_layout"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:layout_gravity="top"
            android:background="@color/half_transparent_grey"
            android:visibility="visible">
            <ImageButton
                android:id="@+id/local_pb_back"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentLeft="true"
                android:layout_centerVertical="true"
                android:background="@drawable/selector_transparent2gray"
                android:padding="10dp"
                android:src="@drawable/ic_arrow_back_white_24dp"/>
            <TextView
                android:id="@+id/local_pb_video_name"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerVertical="true"
                android:gravity="center_vertical"
                android:layout_toRightOf="@+id/local_pb_back"
                android:layout_toLeftOf="@+id/more_btn"
                android:padding="10dp"
                android:text="20151245_1245.mp4"
                android:textColor="@color/grayWhite"
                android:ellipsize="end"
                android:maxLines="1"
                android:textSize="@dimen/first_title_size"/>
            <ImageButton
                android:id="@+id/more_btn"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:background="@drawable/selector_transparent2gray"
                android:padding="10dp"
                android:src="@drawable/ic_more_vert_white_24dp"/>
        </RelativeLayout>
        <LinearLayout
            android:id="@+id/more_setting_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:orientation="vertical"
            android:background="@color/half_transparent_grey"
            android:visibility="gone"
            >
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:text="更多设置"
                    android:textSize="@dimen/second_title_size"
                    android:textColor="@color/white"/>
                <ImageButton
                    android:id="@+id/cancel_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/selector_transparent2gray"
                    android:padding="14dp"
                    android:src="@drawable/ic_clear_white_24dp"/>
            </RelativeLayout>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="40dp">
                <TextView
                    android:id="@+id/eis_txv"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_centerVertical="true"
                    android:gravity="center_vertical"
                    android:text="防抖"
                    android:textColor="@color/grayWhite"
                    android:layout_marginLeft="20dp"
                    android:textSize="@dimen/second_title_size"/>
                <Switch
                    android:id="@+id/eis_switch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="20dp"
                    android:checked="false"
                    />
            </RelativeLayout>
        </LinearLayout>
        <LinearLayout
            android:id="@+id/local_pb_bottom_layout"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="@color/half_transparent_grey"
            android:orientation="vertical"
            android:layout_alignParentBottom="true"
            >
            <SeekBar
                android:id="@+id/local_pb_seekBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:indeterminate="false"
                android:maxHeight="3dp"
                android:progressDrawable="@drawable/po_seekbar_02"
                android:theme="@drawable/seekbar_thumb"
                android:thumb="@drawable/seekbar_thumb"/>
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@+id/local_pb_seekBar"
                android:layout_gravity="center">
                <ImageButton
                    android:id="@+id/local_pb_play_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="10dp"
                    android:background="@color/full_transparent"
                    android:src="@drawable/ic_play_arrow_white_36dp"/>
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:layout_marginLeft="10dp"
                    android:layout_toRightOf="@+id/local_pb_play_btn"
                    >
                    <TextView
                        android:id="@+id/local_pb_time_lapsed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="00:00"
                        android:textColor="@color/greyish_white"
                        android:textSize="14sp"/>
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:text="/"
                        android:textColor="@color/greyish_white"
                        android:textSize="14sp"/>
                    <TextView
                        android:id="@+id/local_pb_time_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:layout_gravity="center"
                        android:text="00:00"
                        android:textColor="@color/greyish_white"
                        android:textSize="14sp"/>
                </LinearLayout>
                <ImageButton
                    android:id="@+id/panorama_type_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:text="@string/text_panorama"
                    android:layout_alignParentRight="true"
                    android:layout_marginRight="10dp"
                    android:textColor="@color/white"
                    android:background="@color/full_transparent"
                    android:src="@drawable/panorama"
                    />
            </RelativeLayout>
        </LinearLayout>
        <com.icatch.mobilecam.ui.ExtendComponent.ProgressWheel
            android:id="@+id/local_pb_spinner"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:visibility="gone"
            ProgressWheel:barColor="#0097D6"
            ProgressWheel:barLengthP="100dp"
            ProgressWheel:barWidth="5dp"
            ProgressWheel:contourColor="#330097D6"
            ProgressWheel:rimColor="#330097D6"
            ProgressWheel:rimWidth="10dp"
            ProgressWheel:text="0%"
            ProgressWheel:textColor="@android:color/white"
            ProgressWheel:textSize="14sp"/>
        <com.icatch.mobilecam.ui.ExtendComponent.ZoomView
            android:id="@+id/zoom_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/local_pb_bottom_layout"
            android:visibility="gone"
            />
    </RelativeLayout>
</RelativeLayout>

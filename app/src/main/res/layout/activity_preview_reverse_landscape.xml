<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.icatch.mobilecam.ui.activity.PreviewActivity">
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black"
        tools:context="com.icatch.mobilecam.ui.activity.PreviewActivity"
        tools:showIn="@layout/activity_preview">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <SurfaceView
                android:id="@+id/preview"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true" />
            <TextView
                android:id="@+id/not_support_preview_txv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_margin="10dp"
                android:rotation="90"
                android:text="@string/text_not_support_preview"
                android:textColor="@color/secondary_text"
                android:textSize="@dimen/navigation_size"
                android:visibility="gone" />
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/status_bar1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true">
            <ImageView
                android:id="@+id/battery_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_margin="10dp"
                android:background="@drawable/ic_battery_full_green_24dp"
                android:rotation="90" />
            <ImageView
                android:id="@+id/wifi_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@+id/battery_status"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:background="@drawable/ic_signal_wifi_1_bar_24dp"
                android:rotation="90" />
            <RelativeLayout
                android:id="@+id/image_size_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerInParent="true"
                android:layout_margin="10dp"
                android:visibility="gone">
                <TextView
                    android:id="@+id/image_size_txv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentTop="true"
                    android:layout_centerInParent="true"
                    android:rotation="90"
                    android:text="4M"
                    android:textColor="@color/white"
                    android:textSize="@dimen/first_title_size" />
                <TextView
                    android:id="@+id/separator1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/image_size_txv"
                    android:layout_centerInParent="true"
                    android:layout_marginTop="3dp"
                    android:layout_marginBottom="3dp"
                    android:rotation="90"
                    android:text="/"
                    android:textColor="@color/white"
                    android:textSize="@dimen/first_title_size" />
                <TextView
                    android:id="@+id/remain_capture_count_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/separator1"
                    android:layout_centerInParent="true"
                    android:rotation="90"
                    android:text="123"
                    android:textColor="@color/white"
                    android:textSize="@dimen/first_title_size" />
            </RelativeLayout>
            <RelativeLayout
                android:id="@+id/video_size_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerInParent="true"
                android:layout_margin="10dp"
                android:visibility="gone">
                <TextView
                    android:id="@+id/video_size_txv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:rotation="90"
                    android:text="FHD 30"
                    android:textColor="@color/white"
                    android:textSize="@dimen/first_title_size" />
                <TextView
                    android:id="@+id/separator2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/video_size_txv"
                    android:layout_centerInParent="true"
                    android:layout_marginLeft="3dp"
                    android:layout_marginRight="3dp"
                    android:rotation="90"
                    android:text="/"
                    android:textColor="@color/white"
                    android:textSize="@dimen/first_title_size" />
                <TextView
                    android:id="@+id/remain_recording_time_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/separator2"
                    android:layout_centerInParent="true"
                    android:rotation="90"
                    android:text="40:10"
                    android:textColor="@color/white"
                    android:textSize="@dimen/first_title_size"
                    tools:text="40:10" />
            </RelativeLayout>
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/status_bar2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@+id/status_bar1"
            android:layout_alignParentTop="true"
            android:visibility="visible">
            <ImageView
                android:id="@+id/wb_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:background="@drawable/awb_auto"
                android:rotation="90"
                android:visibility="gone" />
            <ImageView
                android:id="@+id/burst_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/wb_status"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:background="@drawable/continuous_shot_1"
                android:rotation="90"
                android:visibility="gone" />
            <ImageView
                android:id="@+id/timelapse_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/burst_status"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:background="@drawable/flag_timelapse_video"
                android:rotation="90"
                android:visibility="gone" />
            <ImageView
                android:id="@+id/slow_motion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/timelapse_mode"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:background="@drawable/slow_motion"
                android:rotation="90"
                android:visibility="gone" />
            <ImageView
                android:id="@+id/car_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/slow_motion"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:background="@drawable/te_car_mode"
                android:rotation="90"
                android:visibility="gone" />
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/status_bar3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@+id/status_bar2"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:visibility="visible">
            <TextView
                android:id="@+id/recording_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:rotation="90"
                android:text="00:00:00"
                android:textColor="@color/white"
                android:textSize="@dimen/space_16"
                android:visibility="gone"
                tools:text="00:00:00" />
        </RelativeLayout>
        <ImageView
            android:id="@+id/auto_download_imageView"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_alignStart="@+id/status_bar2"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="10dp"
            android:rotation="90"
            android:scaleType="centerCrop" />
        <RelativeLayout
            android:id="@+id/delay_capture_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@+id/status_bar1"
            android:layout_alignParentBottom="true"
            android:layout_marginEnd="10dp"
            android:visibility="gone">
            <ImageView
                android:id="@+id/delay_capture_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerVertical="true"
                android:background="@drawable/capture_delay_btn"
                android:rotation="90"
                android:scaleType="fitCenter"
                android:visibility="visible" />
            <TextView
                android:id="@+id/delay_capture_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignTop="@+id/delay_capture_iv"
                android:layout_centerVertical="true"
                android:layout_marginTop="5dp"
                android:rotation="90"
                android:textColor="@color/white"
                android:textSize="@dimen/space_16"
                android:visibility="visible" />
        </RelativeLayout>
        <com.icatch.mobilecam.ui.ExtendComponent.ZoomView
            android:id="@+id/zoom_view"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignEnd="@+id/bottomBar"
            android:rotation="90"
            android:visibility="gone" />
        <RelativeLayout
            android:id="@+id/bottomBar"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentStart="true"
            android:layout_margin="@dimen/dp_10"
            android:background="@drawable/shape_popup_round_angle_grey_view"
            android:orientation="vertical"
            android:padding="@dimen/dp_10">
            <ImageButton
                android:id="@+id/multi_pb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="@dimen/dp_5"
                android:layout_marginTop="@dimen/dp_10"
                android:background="@drawable/ic_image"
                android:rotation="90"
                android:scaleType="fitCenter" />
            <!-- Capture -->
            <ImageButton
                android:id="@+id/panorama_type_btn"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_above="@id/doCapture"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="@dimen/dp_20"
                android:background="@color/full_transparent"
                android:rotation="90"
                android:scaleType="fitCenter"
                android:src="@drawable/panorama" />
            <ImageButton
                android:id="@+id/doCapture"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:background="@drawable/video_start"
                android:rotation="90"
                android:scaleType="fitCenter" />
            <ImageButton
                android:id="@+id/pv_mode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@id/doCapture"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/dp_20"
                android:background="@drawable/video_on"
                android:rotation="90"
                android:scaleType="fitCenter" />
            <ImageButton
                android:id="@+id/pv_setting"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginEnd="@dimen/dp_5"
                android:layout_marginBottom="@dimen/dp_10"
                android:background="@drawable/option_pv"
                android:rotation="90"
                android:scaleType="fitCenter" />
        </RelativeLayout>
        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignStart="@+id/auto_download_imageView"
            android:layout_alignEnd="@+id/bottomBar"
            android:layout_alignParentStart="true"
            android:layout_alignParentBottom="true"
            android:layout_margin="10dp"
            android:visibility="gone">
            <!-- Mode switch toggle -->
            <ImageButton
                android:id="@+id/stillToggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:background="@drawable/camera_off"
                android:rotation="90" />
            <ImageButton
                android:id="@+id/videoToggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignEnd="@+id/stillToggle"
                android:layout_marginBottom="25dp"
                android:background="@drawable/video_toggle_btn_on"
                android:rotation="90"
                android:visibility="gone" />
            <ImageButton
                android:id="@+id/timeLapseToggle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignStart="@id/stillToggle"
                android:layout_marginTop="25dp"
                android:background="@drawable/time_lapse_off"
                android:rotation="90"
                android:visibility="gone" />
        </RelativeLayout>
        <RelativeLayout
            android:id="@+id/setupMainMenu"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignBottom="@id/bottomBar"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:layout_margin="@dimen/dp_10"
            android:background="@drawable/shape_popup_round_angle_grey_view"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">
            <ListView
                android:id="@+id/setup_menu_listView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:divider="@color/divider"
                android:dividerHeight="0.5dp"
                />
        </RelativeLayout>
    </RelativeLayout>
</RelativeLayout>

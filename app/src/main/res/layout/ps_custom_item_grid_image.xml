<?xml version="1.0" encoding="utf-8"?>
<com.luck.picture.lib.widget.SquareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#4d000000">

    <ImageView
        android:id="@+id/ivPicture"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tvCheck"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/ps_demo_custom_selector"
        android:gravity="center"
        android:textColor="@color/ps_color_white"
        android:textSize="9sp" />

    <View
        android:id="@+id/btnCheck"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_centerInParent="true"
        android:background="@color/ps_color_transparent" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tv_media_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:background="@drawable/ps_gif_tag"
        android:text="@string/ps_gif_tag"
        android:textColor="@color/ps_color_white"
        android:textSize="11sp"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/ivEditor"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_margin="3dp"
        android:src="@drawable/ps_ic_editor"
        android:visibility="gone" />

    <com.luck.picture.lib.widget.MediumBoldTextView
        android:id="@+id/tv_media_remark"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/ps_gif_tag"
        android:text="备注名"
        android:textColor="@color/ps_color_white"
        android:ellipsize="end"
        android:textSize="11sp"
        android:visibility="gone"
        tools:visibility="visible" />
</com.luck.picture.lib.widget.SquareRelativeLayout>
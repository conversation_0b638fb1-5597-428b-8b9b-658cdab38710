<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:padding="10dp"
    android:layout_height="match_parent">

    <TextView
        android:layout_width="wrap_content"
        android:text="照片名称"
        android:textSize="18sp"
        android:textColor="#333333"
        android:layout_marginTop="10dp"
        android:textStyle="bold"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/tvTitle"
        android:layout_height="wrap_content"/>

    <com.qmuiteam.qmui.layout.QMUIMediumTextView
        android:layout_width="wrap_content"
        android:text="完成"
        android:textSize="16sp"
        android:textColor="#0A64F5"
        android:padding="5dp"
        android:layout_marginEnd="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@id/tvTitle"
        android:id="@+id/tvFinish"
        android:layout_height="wrap_content"/>

    <com.qmuiteam.qmui.layout.QMUIFrameLayout
        android:id="@+id/flEdit"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#f2f2f2"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        android:padding="10dp"
        android:layout_marginTop="20dp"
        app:qmui_radius="10dp">

        <EditText
            android:id="@+id/etRemark"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:maxLength="20"
            android:gravity="top|left"
            android:hint="请输入..."
            android:background="@color/empty" />

    </com.qmuiteam.qmui.layout.QMUIFrameLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/rv"
        android:layout_marginTop="10dp"
        app:layout_constraintHeight_max="200dp"
        app:layout_constraintTop_toBottomOf="@id/flEdit" />
</androidx.constraintlayout.widget.ConstraintLayout>
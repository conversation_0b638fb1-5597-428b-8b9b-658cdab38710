<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent">
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/least_title_size"
            android:layout_marginRight="@dimen/least_title_size"
            android:orientation="vertical" >
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:id="@+id/imageView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:srcCompat="@drawable/setup_user_guide" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <ImageView
                    android:id="@+id/wifi_supported_ip"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:srcCompat="@drawable/wifi_supported_ip" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_1" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_2" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:id="@+id/demovideotextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="https://youtu.be/V_gPlCbHaNI"
                    android:autoLink="web" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_3" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_4" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_5" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_6" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_7" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_8" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:id="@+id/FBtextView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="https://business.facebook.com/ismartdvuser/"
                    android:autoLink="web" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_8_2" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_9" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="https://youtu.be/GSnqVBMUWkk"
                    android:autoLink="web" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent" >
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_10" />
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent" >
                <LinearLayout
                    android:id = "@+id/relativeLayout1"
                    android:layout_width = "fill_parent"
                    android:layout_height = "wrap_content" >
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height = "match_parent"
                    android:layout_weight = "1"
                    android:text = "@string/setting_app_version" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height = "match_parent"
                    android:layout_weight = "1"
                    android:text = "@string/gallery_download" />
                </LinearLayout>
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent" >
                <LinearLayout
                    android:layout_width = "fill_parent"
                    android:layout_height = "wrap_content" >
                    <TextView
                        android:id="@+id/btndown12"
                    android:layout_width="match_parent"
                    android:layout_height = "match_parent"
                    android:layout_weight = "3"
                    android:text = "iSport Cam 1.2" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height = "match_parent"
                    android:layout_weight = "1"
                    android:text = "https://play.google.com/store/apps/details?id=com.icatch.wificam.isportcam"
                    android:autoLink="web" />
                </LinearLayout>
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent" >
                <LinearLayout
                    android:layout_width = "fill_parent"
                    android:layout_height = "wrap_content">
                <TextView
                    android:id="@+id/btndown14"
                    android:layout_width="match_parent"
                    android:layout_height = "match_parent"
                    android:layout_weight = "3"
                    android:text = "iSmart DV OLD version" />
                <TextView
                    android:id="@+id/txturl14"
                    android:layout_width="match_parent"
                    android:layout_height = "match_parent"
                    android:layout_weight = "1"
                    android:text = "https://drive.google.com/drive/folders/1GgynPf8nXXKaDsDEys3bD0RtRaEy5R6O?usp=sharing"
                    android:autoLink="web" />
                </LinearLayout>
            </TableRow>
            <TableRow
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:text="@string/launch_help_qa_end" />
            </TableRow>
        </LinearLayout>
    </ScrollView>
</LinearLayout>

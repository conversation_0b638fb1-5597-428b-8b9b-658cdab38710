<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".LaunchActivity">
    <item
        android:id="@+id/action_done"
        android:orderInCategory="100"
        android:title="@string/text_btpair_skip"
        android:visible="false"
        app:showAsAction="always" />
    <item
        android:id="@+id/action_refresh"
        android:icon="@drawable/ic_refresh_white_24dp"
        android:orderInCategory="100"
        android:title="@string/text_btpair_skip"
        app:showAsAction="always" />
    <item
        android:id="@+id/action_search"
        android:orderInCategory="100"
        android:title="@string/action_search"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_input_ip"
        android:orderInCategory="100"
        android:title="@string/action_input_ip"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_about"
        android:orderInCategory="100"
        android:title="@string/setting_about"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_license"
        android:orderInCategory="100"
        android:title="@string/title_privacy_policy2"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_help"
        android:orderInCategory="100"
        android:title="@string/help"
        app:showAsAction="never" />
</menu>

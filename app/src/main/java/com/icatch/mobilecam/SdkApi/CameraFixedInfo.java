package com.icatch.mobilecam.SdkApi;
import com.icatch.mobilecam.Log.AppLog;
import com.icatchtek.control.customer.ICatchCameraInfo;
import com.icatchtek.reliant.customer.exception.IchInvalidSessionException;
import com.ijoyer.camera.utils.LogUtil;

public class CameraFixedInfo {
	private final String tag = "CameraFixedInfo";
	private ICatchCameraInfo cameraInfo;
	public CameraFixedInfo(ICatchCameraInfo cameraInfo) {
		this.cameraInfo = cameraInfo;
	}
	public String getCameraName() {
		AppLog.i(tag, "begin getCameraName");
		String name = "";
		try {
			name = cameraInfo.getCameraProductName();
		} catch (IchInvalidSessionException e) {
			e.printStackTrace();
		}catch (Exception e){
			e.printStackTrace();
			LogUtil.e("获取相机产品名称失败");
		}
		AppLog.i(tag, "end getCameraName name =" + name);
		return name;
	}
	public String getCameraVersion() {
		AppLog.i(tag, "begin getCameraVersion");
		String version = "";
		try {
			version = cameraInfo.getCameraFWVersion();
		} catch (IchInvalidSessionException e) {
			AppLog.e(tag, "IchInvalidSessionException");
			e.printStackTrace();
		}
		AppLog.i(tag, "end getCameraVersion version =" + version);
		return version;
	}
}

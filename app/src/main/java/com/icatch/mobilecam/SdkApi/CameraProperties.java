package com.icatch.mobilecam.SdkApi;

import com.icatch.mobilecam.DataConvert.BurstConvert;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatchtek.control.customer.ICatchCameraControl;
import com.icatchtek.control.customer.ICatchCameraProperty;
import com.icatchtek.control.customer.exception.IchCameraModeException;
import com.icatchtek.control.customer.exception.IchDevicePropException;
import com.icatchtek.control.customer.exception.IchNoSDCardException;
import com.icatchtek.control.customer.type.ICatchCamLightFrequency;
import com.icatchtek.control.customer.type.ICatchCamMode;
import com.icatchtek.reliant.customer.exception.IchDeviceException;
import com.icatchtek.reliant.customer.exception.IchInvalidSessionException;
import com.icatchtek.reliant.customer.exception.IchSocketException;
import com.icatchtek.reliant.customer.type.ICatchCodec;
import com.icatchtek.reliant.customer.type.ICatchVideoFormat;
import com.ijoyer.camera.utils.LogUtil;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

//相机连接方法，这里目前很多都是直接在主线程操作的，极大可能触发ANR，但是又不好改，容易整个改崩
public class CameraProperties {
    private final String TAG = "CameraProperties";
    private List<Integer> function;
    private List<Integer> modeList;
    private ICatchCameraProperty cameraProperty;
    private ICatchCameraControl cameraAction;
    private List<ICatchVideoFormat> resolutionList;

    public CameraProperties(ICatchCameraProperty cameraProperty, ICatchCameraControl cameraAction) {
        this.cameraAction = cameraAction;
        this.cameraProperty = cameraProperty;
    }

    public List<String> getSupportedImageSizes() {
        List<String> list = null;
        try {
            list = cameraProperty.getSupportedImageSizes();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedImageSizes error:" + e);
        }

        AppLog.i(TAG, "end getSupportedImageSizes list.size =" + list.size());
        if (list != null) {
            for (int ii = 0; ii < list.size(); ii++) {
                AppLog.i(TAG, "image size ii=" + ii + " size=" + list.get(ii));
            }
        }
        return list;
    }

    public List<String> getSupportedVideoSizes() {
        AppLog.i(TAG, "begin getSupportedVideoSizes");
        List<String> list = null;
        try {
            list = cameraProperty.getSupportedVideoSizes();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedVideoSizes error:" + e);
        }

        AppLog.i(TAG, "begin getSupportedVideoSizes size =" + list.size());
        return list;
    }

    public List<Integer> getSupportedWhiteBalances() {
        AppLog.i(TAG, "begin getSupportedWhiteBalances");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedWhiteBalances();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedWhiteBalances error:" + e);
        }

        AppLog.i(TAG, "end getSupportedWhiteBalances list.size() =" + list.size());
        return list;
    }

    public List<Integer> getSupportedCaptureDelays() {
        AppLog.i(TAG, "begin getSupportedCaptureDelays");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedCaptureDelays();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedCaptureDelays error:" + e);
        }

        AppLog.i(TAG, "end getSupportedCaptureDelays list.size() =" + list.size());
        for (Integer temp : list) {
            AppLog.i(TAG, "end getSupportedCaptureDelays list value=" + temp);
        }
        return list;
    }

    public List<Integer> getSupportedLightFrequencys() {
        AppLog.i(TAG, "begin getSupportedLightFrequencys");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedLightFrequencies();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedLightFrequencys error:" + e);
        }

        if (list == null){
            return Collections.emptyList();
        }
        for (int ii = 0; ii < list.size(); ii++) {
            if (list.get(ii) == ICatchCamLightFrequency.ICH_CAM_LIGHT_FREQUENCY_AUTO) {
                list.remove(ii);
            }
        }
        AppLog.i(TAG, "end getSupportedLightFrequencys list.size() =" + list.size());
        return list;
    }

    public boolean setImageSize(String value) {
        AppLog.i(TAG, "begin setImageSize set value =" + value);
        boolean retVal = false;
        try {
            retVal = cameraProperty.setImageSize(value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setImageSize error:" + e);
        }

        AppLog.i(TAG, "end setImageSize retVal=" + retVal);
        return retVal;
    }

    public boolean setVideoSize(String value) {
        AppLog.i(TAG, "begin setVideoSize set value =" + value);
        boolean retVal = false;
        try {
            retVal = cameraProperty.setVideoSize(value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setVideoSize error:" + e);
        }

        AppLog.i(TAG, "end setVideoSize retVal=" + retVal);
        return retVal;
    }

    public boolean setWhiteBalance(int value) {
        AppLog.i(TAG, "begin setWhiteBalanceset value =" + value);
        boolean retVal = false;
        if (value < 0 || value == 0xff) {
            return false;
        }
        try {
            retVal = cameraProperty.setWhiteBalance(value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setWhiteBalance error:" + e);
        }

        AppLog.i(TAG, "end setWhiteBalance retVal=" + retVal);
        return retVal;
    }

    public boolean setLightFrequency(int value) {
        AppLog.i(TAG, "begin setLightFrequency set value =" + value);
        boolean retVal = false;
        if (value < 0 || value == 0xff) {
            return false;
        }
        try {
            retVal = cameraProperty.setLightFrequency(value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setLightFrequency error:" + e);
        }

        AppLog.i(TAG, "end setLightFrequency retVal=" + retVal);
        return retVal;
    }

    public String getCurrentImageSize() {
        AppLog.i(TAG, "begin getCurrentImageSize");
        String value = "unknown";
        try {
            value = cameraProperty.getCurrentImageSize();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentImageSize error:" + e);
        }

        AppLog.i(TAG, "end getCurrentImageSize value =" + value);
        return value;
    }

    public String getCurrentVideoSize() {
        AppLog.i(TAG, "begin getCurrentVideoSize");
        String value = "unknown";
        try {
            value = cameraProperty.getCurrentVideoSize();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentVideoSize error:" + e);
        }

        AppLog.i(TAG, "end getCurrentVideoSize value =" + value);
        return value;
    }

    public int getCurrentWhiteBalance() {
        AppLog.i(TAG, "begin getCurrentWhiteBalance");
        int value = 0xff;
        try {
            value = cameraProperty.getCurrentWhiteBalance();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentWhiteBalance error:" + e);
        }

        AppLog.i(TAG, "end getCurrentWhiteBalance retvalue =" + value);
        return value;
    }

    public int getCurrentLightFrequency() {
        AppLog.i(TAG, "begin getCurrentLightFrequency");
        int value = 0xff;
        try {
            value = cameraProperty.getCurrentLightFrequency();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentLightFrequency error:" + e);
        }

        AppLog.i(TAG, "end getCurrentLightFrequency value =" + value);
        return value;
    }

    public boolean setCaptureDelay(int value) {
        AppLog.i(TAG, "begin setCaptureDelay set value =" + value);
        boolean retVal = false;
        try {
            AppLog.i(TAG, "start setCaptureDelay ");
            retVal = cameraProperty.setCaptureDelay(value);
            AppLog.i(TAG, "end setCaptureDelay ");
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCaptureDelay error:" + e);
        }

        AppLog.i(TAG, "end setCaptureDelay retVal =" + retVal);
        return retVal;
    }

    public int getCurrentCaptureDelay() {
        AppLog.i(TAG, "begin getCurrentCaptureDelay");
        int retVal = 0;
        try {
            retVal = cameraProperty.getCurrentCaptureDelay();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentCaptureDelay error:" + e);
        }

        AppLog.i(TAG, "end getCurrentCaptureDelay retVal =" + retVal);
        return retVal;
    }

    public int getCurrentDateStamp() {
        AppLog.i(TAG, "begin getCurrentDateStampType");
        int retValue = 0;
        try {
            retValue = cameraProperty.getCurrentDateStamp();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentDateStamp error:" + e);
        }
        AppLog.i(TAG, "getCurrentDateStampType retValue =" + retValue);
        return retValue;
    }

    public boolean setDateStamp(int dateStamp) {
        AppLog.i(TAG, "begin setDateStampType set value = " + dateStamp);
        Boolean retValue = false;
        try {
            retValue = cameraProperty.setDateStamp(dateStamp);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setDateStamp error:" + e);
        }
        AppLog.i(TAG, "end setDateStampType retValue =" + retValue);
        return retValue;
    }

    public List<Integer> getDateStampList() {
        AppLog.i(TAG, "begin getDateStampList");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedDateStamps();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getDateStampList error:" + e);
        }
        AppLog.i(TAG, "end getDateStampList list.size ==" + list.size());
        return list;
    }

    public List<Integer> getSupportFuction() {
        AppLog.i(TAG, "begin getSupportFuction");
        List<Integer> fuction = null;
        try {
            //获取相机功能列表
            fuction = cameraProperty.getSupportedProperties();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportFuction error:" + e);
        }
        AppLog.i(TAG, "end getSupportFuction fuction=" + fuction);
        return fuction;
    }

    public int getCurrentBurstNum() {
        AppLog.i(TAG, "begin getCurrentBurstNum");
        int number = 0xff;
        try {
            number = cameraProperty.getCurrentBurstNumber();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentBurstNum error:" + e);
        }
        AppLog.i(TAG, "getCurrentBurstNum num =" + number);
        return number;
    }

    public int getCurrentAppBurstNum() {
        AppLog.i(TAG, "begin getCurrentAppBurstNum");
        int number = 0xff;
        try {
            number = cameraProperty.getCurrentBurstNumber();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentAppBurstNum error:" + e);
        }
        number = BurstConvert.getInstance().getBurstConverFromFw(number);
        AppLog.i(TAG, "getCurrentAppBurstNum num =" + number);
        return number;
    }

    public boolean setCurrentBurst(int burstNum) {
        AppLog.i(TAG, "begin setCurrentBurst set value = " + burstNum);
        if (burstNum < 0 || burstNum == 0xff) {
            return false;
        }
        boolean retValue = false;
        try {
            retValue = cameraProperty.setBurstNumber(burstNum);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCurrentBurst error:" + e);
        }
        AppLog.i(TAG, "end setCurrentBurst retValue =" + retValue);
        return retValue;
    }

    public int getRemainImageNum() {
        AppLog.i(TAG, "begin getRemainImageNum");
        int num = 0;
        try {
            num = cameraAction.getFreeSpaceInImages();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
            num = -1;
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
            num = -2;
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
            num = -3;
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
            num = -4;
        } catch (IchNoSDCardException e) {
            AppLog.e(TAG, "IchNoSDCardException");
            e.printStackTrace();
            num = -5;
        } catch (Exception e) {
            LogUtil.e("getRemainImageNum error:" + e);
            num = -6;
        }
        AppLog.i(TAG, "end getRemainImageNum num =" + num);
        return num;
    }

    public int getRecordingRemainTime() {
        AppLog.i(TAG, "begin getRecordingRemainTimeInt");
        int recordingTime = 0;
        try {
            recordingTime = cameraAction.getRemainRecordingTime();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
            recordingTime= -1;
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
            recordingTime = -2;
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
            recordingTime = -3;
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
            recordingTime = -4;
        } catch (IchNoSDCardException e) {
            e.printStackTrace();
            recordingTime = -5;
        } catch (Exception e) {
            LogUtil.e("getRecordingRemainTime error:" + e);
            recordingTime = -6;
        }
        AppLog.i(TAG, "end getRecordingRemainTimeInt recordingTime =" + recordingTime);
        return recordingTime;
    }

    /**
     * 返回sd 卡是否存在，null 获取sd 卡存在失败
     * @return
     */
    public Boolean isSDCardExist() {
        AppLog.i(TAG, "begin isSDCardExist");
        Boolean isReady = null;
        try {
            // TODO: 2022/6/18 更换相机连接有闪退问题
            isReady = cameraAction.isSDCardExist();
            return isReady;
        } catch (IchSocketException e) {
            AppLog.e(TAG, "isSDCardExist:IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "isSDCardExist:IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "isSDCardExist:IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDeviceException e) {
            AppLog.e(TAG, "isSDCardExist:IchDeviceException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("获取SD 卡报错");
            e.printStackTrace();
        } finally {
            AppLog.i(TAG, "end isSDCardExist isReady =" + isReady);
        }
        return isReady;
    }

    public int getBatteryElectric() {
        AppLog.i(TAG, "start getBatteryElectric");
        int electric = 0;
        try {
            electric = cameraAction.getCurrentBatteryLevel();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getBatteryElectric error:" + e);
        }
        AppLog.i(TAG, "end getBatteryElectric electric =" + electric);
        return electric;
    }

    public boolean supportVideoPlayback() {
        AppLog.i(TAG, "begin hasVideoPlaybackFuction");
        boolean retValue = false;
        try {
            retValue = cameraAction.supportVideoPlayback();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (IchNoSDCardException e) {
            AppLog.e(TAG, "IchNoSDCardException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("supportVideoPlayback error:" + e);
        }
        AppLog.i(TAG, "hasVideoPlaybackFuction retValue =" + retValue);
        return retValue;
    }

    public boolean cameraModeSupport(int mode) {
        AppLog.i(TAG, "begin cameraModeSupport  mode=" + mode);
        Boolean retValue = false;
        if (modeList == null) {
            modeList = getSupportedModes();
        }
        if (modeList.contains(mode)) {
            retValue = true;
        }
        AppLog.i(TAG, "end cameraModeSupport retValue =" + retValue);
        return retValue;
    }

    public String getCameraMacAddress() {
        AppLog.i(TAG, "begin getCameraMacAddress macAddress macAddress ");
        String macAddress = cameraAction.getMacAddress();
        AppLog.i(TAG, "end getCameraMacAddress macAddress =" + macAddress);
        return macAddress;
    }

    public boolean hasFunction(int fuc) {
        AppLog.i(TAG, "begin hasFuction query fuction = " + fuc);
        if (function == null) {
            function = getSupportFuction();
        }
        boolean retValue = function != null && function.contains(fuc);
        AppLog.i(TAG, "end hasFuction retValue =" + retValue);
        return retValue;
    }

    public List<Integer> getSupportedDateStamps() {
        AppLog.i(TAG, "begin getsupportedDateStamps");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedDateStamps();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedDateStamps error:" + e);
        }
        AppLog.i(TAG, "end getsupportedDateStamps list=" + list);
        return list;
    }

    public List<Integer> getsupportedBurstNums() {
        AppLog.i(TAG, "begin getsupportedBurstNums");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedBurstNumbers();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getsupportedBurstNums error:" + e);
        }
        AppLog.i(TAG, "end getsupportedBurstNums list.size() =" + (list == null ? 0 : list.size()));
        if (list != null && !list.isEmpty()) {
            for (int size : list) {
                AppLog.i(TAG, "end getsupportedBurstNums size=" + size);
            }
        }
        return list;
    }

    public List<Integer> getSupportedFrequencies() {
        AppLog.i(TAG, "begin getSupportedFrequencies");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedLightFrequencies();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedFrequencies error:" + e);
        }
        AppLog.i(TAG, "end getSupportedFrequencies list.size() =" + list.size());
        return list;
    }

    public List<Integer> getSupportedModes() {
        AppLog.i(TAG, "begin getSupportedModes");
        List<Integer> list = null;
        if (hasFunction(PropertyId.A6_ROTATE_SHOT_TIMES) && hasFunction(PropertyId.A6_ROTATE_MOTOR_STATE)) {
            list = new LinkedList<>();
            list.add(ICatchCamMode.ICH_CAM_MODE_CAMERA);
            list.add(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE);
            list.add(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE_VIDEO_OFF);
            if (CameraUtils.isA6Max()){
                list.add(ICatchCamMode.ICH_CAM_MODE_VIDEO);
            }
        } else {
            try {
                list = cameraAction.getSupportedModes();
            } catch (IchSocketException e) {
                AppLog.e(TAG, "IchSocketException");
                e.printStackTrace();
            } catch (IchCameraModeException e) {
                AppLog.e(TAG, "IchCameraModeException");
                e.printStackTrace();
            } catch (IchInvalidSessionException e) {
                AppLog.e(TAG, "IchInvalidSessionException");
                e.printStackTrace();
            }
        }
        AppLog.i(TAG, "end getSupportedModes list =" + list);
        return list;
    }

    public List<Integer> getSupportedTimeLapseDurations() {
        AppLog.i(TAG, "begin getSupportedTimeLapseDurations");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedTimeLapseDurations();
        } catch (IchSocketException e) {
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedTimeLapseDurations error:" + e);
        }
        for (int ii = 0; ii < list.size(); ii++) {
            AppLog.i(TAG, "list.get(ii) =" + list.get(ii));
        }
        AppLog.i(TAG, "end getSupportedTimeLapseDurations list =" + list.size());
        return list;
    }

    public List<Integer> getSupportedTimeLapseIntervals() {
        AppLog.i(TAG, "begin getSupportedTimeLapseIntervals");
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedTimeLapseIntervals();
        } catch (IchSocketException e) {
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedTimeLapseIntervals error:" + e);
        }
        for (int ii = 0; ii < list.size(); ii++) {
            AppLog.i(TAG, "list.get(ii) =" + list.get(ii));
        }
        AppLog.i(TAG, "end getSupportedTimeLapseIntervals list =" + list.size());
        return list;
    }

    public boolean setTimeLapseDuration(int timeDuration) {
        AppLog.i(TAG, "begin setTimeLapseDuration videoDuration =" + timeDuration);
        boolean retVal = false;
        if (timeDuration < 0 || timeDuration == 0xff) {
            return false;
        }
        try {
            retVal = cameraProperty.setTimeLapseDuration(timeDuration);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setTimeLapseDuration error:" + e);
        }
        AppLog.i(TAG, "end setTimeLapseDuration retVal=" + retVal);
        return retVal;
    }

    public int getCurrentTimeLapseDuration() {
        AppLog.i(TAG, "begin getCurrentTimeLapseDuration");
        int retVal = 0xff;
        try {
            retVal = cameraProperty.getCurrentTimeLapseDuration();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentTimeLapseDuration error:" + e);
        }
        AppLog.i(TAG, "end getCurrentTimeLapseDuration retVal=" + retVal);
        return retVal;
    }

    public boolean setTimeLapseInterval(int timeInterval) {
        AppLog.i(TAG, "begin setTimeLapseInterval videoDuration =" + timeInterval);
        boolean retVal = false;
        try {
            retVal = cameraProperty.setTimeLapseInterval(timeInterval);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("IchDevicePropException error:" + e);
        }
        AppLog.i(TAG, "end setTimeLapseInterval retVal=" + retVal);
        return retVal;
    }

    public int getCurrentTimeLapseInterval() {
        AppLog.i(TAG, "begin getCurrentTimeLapseInterval");
        int retVal = 0xff;
        try {
            retVal = cameraProperty.getCurrentTimeLapseInterval();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentTimeLapseInterval error:" + e);
        }
        AppLog.i(TAG, "end getCurrentTimeLapseInterval retVal=" + retVal);
        return retVal;
    }

    public float getMaxZoomRatio() {
        AppLog.i(TAG, "start getMaxZoomRatio");
        float retValue = 0;
        try {
            retValue = cameraProperty.getMaxZoomRatio() / 10.0f;
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getMaxZoomRatio error:" + e);
        }
        AppLog.i(TAG, "end getMaxZoomRatio retValue =" + retValue);
        return retValue;
    }

    public float getCurrentZoomRatio() {
        AppLog.i(TAG, "start getCurrentZoomRatio");
        float retValue = 0;
        try {
            retValue = cameraProperty.getCurrentZoomRatio() / 10.0f;
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentZoomRatio error:" + e);
        }
        AppLog.i(TAG, "end getCurrentZoomRatio retValue =" + retValue);
        return retValue;
    }

    public int getCurrentUpsideDown() {
        AppLog.i(TAG, "start getCurrentUpsideDown");
        int retValue = 0;
        try {
            retValue = cameraProperty.getCurrentUpsideDown();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentUpsideDown error:" + e);
        }
        AppLog.i(TAG, "end getCurrentUpsideDown retValue =" + retValue);
        return retValue;
    }

    public boolean setUpsideDown(int upside) {
        AppLog.i(TAG, "start setUpsideDown upside = " + upside);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setUpsideDown(upside);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setUpsideDown error:" + e);
        }
        AppLog.i(TAG, "end setUpsideDown retValue =" + retValue);
        return retValue;
    }

    public int getCurrentSlowMotion() {
        AppLog.i(TAG, "start getCurrentSlowMotion");
        int retValue = 0;
        try {
            retValue = cameraProperty.getCurrentSlowMotion();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentSlowMotion error:" + e);
        }
        AppLog.i(TAG, "end getCurrentSlowMotion retValue =" + retValue);
        return retValue;
    }

    public boolean setSlowMotion(int slowMotion) {
        AppLog.i(TAG, "start setSlowMotion slowMotion = " + slowMotion);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setSlowMotion(slowMotion);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setSlowMotion error:" + e);
        }
        AppLog.i(TAG, "end setSlowMotion retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraDate() {
        long time = System.currentTimeMillis();
        Date date = new Date(time);
        SimpleDateFormat myFmt = new SimpleDateFormat("yyyyMMdd HHmmss");
        String tempDate = myFmt.format(date);
        tempDate = tempDate.replaceAll(" ", "T");
        tempDate = tempDate + ".0";
        AppLog.i(TAG, "start setCameraDate date = " + tempDate);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.CAMERA_DATE, tempDate);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraDate error:" + e);
        }
        AppLog.i(TAG, "end setCameraDate retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraDateTimeZone() {
        long time = System.currentTimeMillis();
        Date date = new Date(time);
        SimpleDateFormat myFmt = new SimpleDateFormat("Z");
        String tempZone = myFmt.format(date);
        List<String> stringList = Arrays.asList("Victure/AC920", "Victure/AC940", "Crosstour/CT9900", "Niceboy", "Model : YDOL3", "Action Cam", "Action Cam - 078", "Action Cam - 258", "Action Cam - 278", "Action Cam - 317", "Action Cam - 386", "Action Cam - 458", "Action Camera", "V40");
        CameraFixedInfo cameraFixedInfo = CameraManager.getInstance().getCurCamera().getCameraFixedInfo();
        if (cameraFixedInfo != null) {
            String curCameraName = cameraFixedInfo.getCameraName();
            if (curCameraName != null && stringList.contains(curCameraName)) {
                AppLog.i(TAG, "start setCameraDateTimeZone contains curCameraName:" + curCameraName);
                return false;
            }
        }
        AppLog.i(TAG, "start setCameraDateTimeZone date = " + tempZone);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.CAMERA_DATE_TIMEZONE, tempZone);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraDateTimeZone error:" + e);
        }
        AppLog.i(TAG, "end setCameraDateTimeZone retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraEssidName(String ssidName) {
        AppLog.i(TAG, "start setCameraEssidName date = " + ssidName);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.ESSID_NAME, ssidName);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraEssidName error:" + e);
        }
        AppLog.i(TAG, "end setCameraEssidName retValue =" + retValue);
        return retValue;
    }

    public String getCameraEssidName() {
        AppLog.i(TAG, "start getCameraEssidName");
        String retValue = null;
        try {
            retValue = cameraProperty.getCurrentStringPropertyValue(PropertyId.ESSID_NAME);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("getCameraEssidName error:" + e);
        }
        AppLog.i(TAG, "end getCameraEssidName retValue =" + retValue);
        return retValue;
    }

    public String getCameraEssidPassword() {
        AppLog.i(TAG, "start getCameraEssidPassword");
        String retValue = null;
        try {
            retValue = cameraProperty.getCurrentStringPropertyValue(PropertyId.ESSID_PASSWORD);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("getCameraEssidPassword error:" + e);
        }
        AppLog.i(TAG, "end getCameraEssidPassword retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraEssidPassword(String ssidPassword) {
        AppLog.i(TAG, "start setStringPropertyValue date = " + ssidPassword);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.ESSID_PASSWORD, ssidPassword);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraEssidPassword error:" + e);
        }

        AppLog.i(TAG, "end setCameraSsid retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraSsid(String ssid) {
        AppLog.i(TAG, "start setCameraSsid date = " + ssid);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.CAMERA_ESSID, ssid);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraSsid error:" + e);
        }
        AppLog.i(TAG, "end setCameraSsid retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraName(String cameraName) {
        AppLog.i(TAG, "start setStringPropertyValue cameraName = " + cameraName);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.CAMERA_NAME, cameraName);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraName error:" + e);
        }
        AppLog.i(TAG, "end setStringPropertyValue retValue =" + retValue);
        return retValue;
    }

    public String getCameraName() {
        AppLog.i(TAG, "start getCameraName");
        String retValue = null;
        try {
            retValue = cameraProperty.getCurrentStringPropertyValue(PropertyId.CAMERA_NAME);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("getCameraName error:" + e);
        }
        AppLog.i(TAG, "end getCameraName retValue =" + retValue);
        return retValue;
    }

    public String getCameraName(ICatchCameraProperty cameraConfiguration1) {
        AppLog.i(TAG, "start getCameraName");
        String retValue = null;
        try {
            retValue = cameraConfiguration1.getCurrentStringPropertyValue(PropertyId.CAMERA_NAME);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCameraName error:" + e);
        }
        AppLog.i(TAG, "end getCameraName retValue =" + retValue);
        return retValue;
    }

    public String getCameraPasswordNew() {
        AppLog.i(TAG, "start getCameraPassword");
        String retValue = null;
        try {
            retValue = cameraProperty.getCurrentStringPropertyValue(PropertyId.CAMERA_PASSWORD_NEW);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("getCameraPasswordNew error:" + e);
        }
        AppLog.i(TAG, "end getCameraPassword retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraPasswordNew(String cameraNamePassword) {
        AppLog.i(TAG, "start setCameraPasswordNew cameraName = " + cameraNamePassword);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.CAMERA_PASSWORD_NEW, cameraNamePassword);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraPasswordNew error:" + e);
        }
        AppLog.i(TAG, "end setCameraPasswordNew retValue =" + retValue);
        return retValue;
    }

    public String getCameraSsid() {
        AppLog.i(TAG, "start getCameraSsid date = ");
        String retValue = null;
        try {
            retValue = cameraProperty.getCurrentStringPropertyValue(PropertyId.CAMERA_ESSID);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("getCameraSsid error:" + e);
        }
        AppLog.i(TAG, "end getCameraSsid retValue =" + retValue);
        return retValue;
    }

    public boolean setCameraPassword(String password) {
        AppLog.i(TAG, "start setCameraPassword date = " + password);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.CAMERA_PASSWORD, password);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCameraPassword error:" + e);
        }

        AppLog.i(TAG, "end setCameraPassword retValue =" + retValue);
        return retValue;
    }

    public String getCameraPassword() {
        AppLog.i(TAG, "start getCameraPassword date = ");
        String retValue = null;
        try {
            retValue = cameraProperty.getCurrentStringPropertyValue(PropertyId.CAMERA_PASSWORD);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("getCameraPassword error:" + e);
        }
        AppLog.i(TAG, "end getCameraPassword retValue =" + retValue);
        return retValue;
    }

    public boolean setCaptureDelayMode(int value) {
        AppLog.i(TAG, "start setCaptureDelayMode value = " + value);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setPropertyValue(PropertyId.CAPTURE_DELAY_MODE, value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setCaptureDelayMode error:" + e);
        }

        AppLog.i(TAG, "end setCaptureDelayMode retValue =" + retValue);
        return retValue;
    }

    public int getVideoRecordingTime() {
        AppLog.i(TAG, "start getRecordingTime");
        int retValue = 0;
        try {
            retValue = cameraProperty.getCurrentPropertyValue(PropertyId.VIDEO_RECORDING_TIME);
        } catch (Exception e) {
            AppLog.e(TAG, "Exception e:" + e.getClass().getSimpleName());
            e.printStackTrace();
        }
        AppLog.i(TAG, "end getRecordingTime retValue =" + retValue);
        return retValue;
    }

    public boolean setServiceEssid(String value) {
        AppLog.i(TAG, "start setServiceEssid value = " + value);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.SERVICE_ESSID, value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setServiceEssid error:" + e);
        }

        AppLog.i(TAG, "end setServiceEssid retValue =" + retValue);
        return retValue;
    }

    public boolean setServicePassword(String value) {
        AppLog.i(TAG, "start setServicePassword value = " + value);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(PropertyId.SERVICE_PASSWORD, value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setServicePassword error:" + e);
        }

        AppLog.i(TAG, "end setServicePassword retValue =" + retValue);
        return retValue;
    }

    public boolean notifyFwToShareMode(int value) {
        AppLog.i(TAG, "start notifyFwToShareMode value = " + value);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setPropertyValue(PropertyId.NOTIFY_FW_TO_SHARE_MODE, value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("notifyFwToShareMode error:" + e);
        }

        AppLog.i(TAG, "end notifyFwToShareMode retValue =" + retValue);
        return retValue;
    }

    public List<Integer> getSupportedPropertyValues(int propertyId) {
        AppLog.i(TAG, "begin getSupportedPropertyValues propertyId =" + propertyId);
        List<Integer> list = null;
        try {
            list = cameraProperty.getSupportedPropertyValues(propertyId);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getSupportedPropertyValues error:" + e);
        }

        AppLog.i(TAG, "end getSupportedPropertyValues list.size() =" + list.size());
        return list;
    }

    public int getCurrentPropertyValue(int propertyId) {
        AppLog.i(TAG, "start getCurrentPropertyValue propertyId = " + propertyId);
        int retValue = 0;
        try {
            retValue = cameraProperty.getCurrentPropertyValue(propertyId);
        } catch (Exception e) {
            AppLog.e(TAG, "Exception e:" + e.getClass().getSimpleName());
            e.printStackTrace();
            retValue = 3;
        }
        AppLog.i(TAG, "end getCurrentPropertyValue retValue =" + retValue);
//        LogUtil.d("getCurrentPropertyValue:"+propertyId+":retValue="+retValue);
        return retValue;
    }

    public String getCurrentStringPropertyValue(int propertyId) {
        AppLog.i(TAG, "start getCurrentStringPropertyValue propertyId = " + propertyId);
        String retValue = null;
        try {
            retValue = cameraProperty.getCurrentStringPropertyValue(propertyId);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("getCurrentStringPropertyValue error：" + propertyId + "," + e);
        }
        AppLog.i(TAG, "end getCurrentStringPropertyValue retValue =" + retValue);
        return retValue;
    }

    public boolean setPropertyValue(int propertyId, int value) {
        AppLog.i(TAG, "start setPropertyValue propertyId=" + propertyId + " value=" + value);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setPropertyValue(propertyId, value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
            LogUtil.e("setPropertyValue error：" + propertyId + "," + e);
        }
        AppLog.i(TAG, "end setPropertyValue retValue =" + retValue);
        return retValue;
    }

    public boolean setStringPropertyValue(int propertyId, String value) {
        AppLog.i(TAG, "start setStringPropertyValue propertyId=" + propertyId + " value=[" + value + "]");
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStringPropertyValue(propertyId, value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setStringPropertyValue error:" + e);
        }

        AppLog.i(TAG, "end setStringPropertyValue retValue =" + retValue);
        return retValue;
    }

    public int getVideoSizeFlow() {
        AppLog.i(TAG, "start getVideoSizeFlow");
        int retValue = 0;
        try {
            retValue = cameraProperty.getCurrentPropertyValue(PropertyId.VIDEO_SIZE_FLOW);
        } catch (Exception e) {
            AppLog.e(TAG, "Exception e:" + e.getClass().getSimpleName());
            e.printStackTrace();
        }
        AppLog.i(TAG, "end getVideoSizeFlow retValue =" + retValue);
        return retValue;
    }

    public boolean notifyCameraConnectChnage(int value) {
        AppLog.i(TAG, "start notifyCameraConnectChnage value = " + value);
        boolean retValue = false;
        try {
            retValue = cameraProperty.setPropertyValue(PropertyId.CAMERA_CONNECT_CHANGE, value);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("notifyCameraConnectChnage error:" + e);
        }

        AppLog.i(TAG, "end notifyCameraConnectChnage retValue =" + retValue);
        return retValue;
    }

    public List<ICatchVideoFormat> getResolutionList() {
        AppLog.i(TAG, "start getResolution");
        if (resolutionList == null) {
            try {
                resolutionList = cameraProperty.getSupportedStreamingInfos();
            } catch (Exception e) {
                AppLog.e(TAG, "Exception e:" + e.getMessage());
                e.printStackTrace();
            }
        }
        AppLog.i(TAG, "end getResolution retList=" + resolutionList);
        for (ICatchVideoFormat temp : resolutionList) {
            AppLog.i(TAG, "end getResolution format=" + temp);
        }
        return resolutionList;
    }

    public List<ICatchVideoFormat> getResolutionList(int codecType) {
        AppLog.i(TAG, "start getResolution");
        if (resolutionList == null) {
            try {
                resolutionList = cameraProperty.getSupportedStreamingInfos();
            } catch (Exception e) {
                AppLog.e(TAG, "Exception e:" + e.getMessage());
                e.printStackTrace();
            }
        }
        AppLog.i(TAG, "end getResolution retList=" + resolutionList);
        List<ICatchVideoFormat> tempList = new LinkedList<>();
        for (ICatchVideoFormat temp : resolutionList) {
            if (temp.getCodec() == codecType) {
                tempList.add(temp);
            }
            AppLog.i(TAG, "end getResolution format=" + temp);
        }
        return tempList;
    }

    public String getFWDefaultResolution() {
        AppLog.i(TAG, "start getFWDefaultResolution");
        String resolution = null;
        ICatchVideoFormat retValue = null;
        try {
            retValue = cameraProperty.getCurrentStreamingInfo();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getFWDefaultResolution error:" + e);
        }

        if (retValue != null) {
            if (retValue.getCodec() == ICatchCodec.ICH_CODEC_H264) {
                resolution = "H264?" + "W=" + retValue.getVideoW() + "&H=" + retValue.getVideoH() + "&BR=" + retValue.getBitrate() + "&";
            } else if (retValue.getCodec() == ICatchCodec.ICH_CODEC_JPEG) {
                resolution = "MJPG?" + "W=" + retValue.getVideoW() + "&H=" + retValue.getVideoH() + "&BR=" + retValue.getBitrate() + "&";
            }
        }
        AppLog.i(TAG, "end getFWDefaultResolution");
        return resolution;
    }

    public boolean setStreamingInfo(ICatchVideoFormat iCatchVideoFormat) {
        AppLog.i(TAG, "start setStreamingInfo");
        boolean retValue = false;
        try {
            retValue = cameraProperty.setStreamingInfo(iCatchVideoFormat);
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("setStreamingInfo error:" + e);
        }

        AppLog.i(TAG, "end setStreamingInfo");
        return retValue;
    }

    public String getCurrentStreamInfo() {
        AppLog.i(TAG, "start getCurrentStreamInfo cameraProperty=" + cameraProperty);
        ICatchVideoFormat retValue = null;
        String bestResolution = null;
        try {
            retValue = cameraProperty.getCurrentStreamingInfo();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getCurrentStreamInfo error:" + e);
        }

        AppLog.i(TAG, "end getCurrentStreamInfo retValue = " + retValue);
        if (retValue == null) {
            return null;
        }
        if (hasFunction(0xd7ae)) {
            if (retValue.getCodec() == ICatchCodec.ICH_CODEC_H264) {
                bestResolution = "H264?" + "W=" + retValue.getVideoW() + "&H=" + retValue.getVideoH() + "&BR=" + retValue.getBitrate() + "&FPS=" + retValue.getFrameRate() + "&";
            } else if (retValue.getCodec() == ICatchCodec.ICH_CODEC_JPEG) {
                bestResolution = "MJPG?" + "W=" + retValue.getVideoW() + "&H=" + retValue.getVideoH() + "&BR=" + retValue.getBitrate() + "&FPS=" + retValue.getFrameRate() + "&";
            }
        } else {
            if (retValue.getCodec() == ICatchCodec.ICH_CODEC_H264) {
                bestResolution = "H264?" + "W=" + retValue.getVideoW() + "&H=" + retValue.getVideoH() + "&BR=" + retValue.getBitrate();
            } else if (retValue.getCodec() == ICatchCodec.ICH_CODEC_JPEG) {
                bestResolution = "MJPG?" + "W=" + retValue.getVideoW() + "&H=" + retValue.getVideoH() + "&BR=" + retValue.getBitrate();
            }
        }
        AppLog.i(TAG, "end getCurrentStreamInfo bestResolution =" + bestResolution);
        return bestResolution;
    }

    public int getPreviewCacheTime() {
        AppLog.i(TAG, "start getPreviewCacheTime");
        int retValue = 0;
        try {
            retValue = cameraProperty.getPreviewCacheTime();
        } catch (IchSocketException e) {
            AppLog.e(TAG, "IchSocketException");
            e.printStackTrace();
        } catch (IchCameraModeException e) {
            AppLog.e(TAG, "IchCameraModeException");
            e.printStackTrace();
        } catch (IchInvalidSessionException e) {
            AppLog.e(TAG, "IchInvalidSessionException");
            e.printStackTrace();
        } catch (IchDevicePropException e) {
            AppLog.e(TAG, "IchDevicePropException");
            e.printStackTrace();
        } catch (Exception e) {
            LogUtil.e("getPreviewCacheTime error:" + e);
        }

        AppLog.i(TAG, "end getPreviewCacheTime retValue =" + retValue);
        return retValue;
    }

    public boolean isSupportPreview() {
        AppLog.i(TAG, "start getRecordingTime");
        int retValue = 0;
        boolean isSupport = false;
        try {
            retValue = cameraProperty.getCurrentPropertyValue(PropertyId.SUPPORT_PREVIEW);
        } catch (Exception e) {
            AppLog.e(TAG, "Exception:" + e.getClass().getSimpleName());
            e.printStackTrace();
        }
        AppLog.i(TAG, "end getRecordingTime retValue =" + retValue);
        if (retValue == 0) {
            isSupport = false;
        } else {
            isSupport = true;
        }
        return isSupport;
    }

    public int getNumberOfSensors() {
        AppLog.i(TAG, "start getNumberOfSensors");
        int retValue = 1;
        try {
            retValue = cameraProperty.getNumberOfSensors();
        } catch (Exception e) {
            AppLog.e(TAG, "Exception:" + e.getClass().getSimpleName());
            e.printStackTrace();
        }
        AppLog.i(TAG, "end getNumberOfSensors retValue =" + retValue);
        return retValue;
    }

    public boolean checkCameraCapabilities(int featureID) {
        AppLog.i(TAG, "start checkCameraCapabilities featureID:" + featureID);
        boolean retValue = false;
        try {
            retValue = cameraProperty.checkCameraCapabilities(featureID);
        } catch (Exception e) {
            AppLog.e(TAG, "Exception:" + e.getClass().getSimpleName());
            e.printStackTrace();
        }
        AppLog.i(TAG, "end checkCameraCapabilities retValue =" + retValue);
        return retValue;
    }
}

package com.icatch.mobilecam.Presenter;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Handler;
import android.util.Log;
import android.view.View;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.blankj.utilcode.util.LogUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.Interface.BasePresenter;
import com.icatch.mobilecam.SdkApi.FileOperation;
import com.icatch.mobilecam.bean.CheckSingleCameraPhotoBean;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.entity.MultiPbFileResult;
import com.icatch.mobilecam.data.entity.MultiPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Interface.MultiPbFragmentView;
import com.icatch.mobilecam.ui.RemoteFileHelper;
import com.icatch.mobilecam.ui.activity.PhotoPbActivity;
import com.icatch.mobilecam.ui.activity.VideoPbActivity;
import com.icatch.mobilecam.ui.adapter.MultiPbRecyclerViewAdapter;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.VideoListUtil;
import com.icatch.mobilecam.utils.imageloader.ImageLoaderConfig;
import com.ijoyer.mobilecam.R;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class MultiPbFragmentPresenter extends BasePresenter {
    private String TAG = MultiPbFragmentPresenter.class.getSimpleName();
    private MultiPbFragmentView multiPbPhotoView;
    private MultiPbRecyclerViewAdapter recyclerViewAdapter;
    private Activity activity;
    private OperationMode curOperationMode = OperationMode.MODE_BROWSE;
    private List<MultiPbItemInfo> pbItemInfoList = new LinkedList<>();
    private FileOperation fileOperation = CameraManager.getInstance().getCurCamera().getFileOperation();
    private Handler handler;
    public FileType fileType;
    private int fileTotalNum;
    PhotoWallLayoutType curLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
    private boolean needGetFileNumRemote = true;
    private int curIndex = 1;
    private int maxNum = 15;
    private boolean isMore = true;
    private boolean supportSegmentedLoading = false;
    private Fragment fragment;

    public MultiPbFragmentPresenter(Activity activity, FileType fileType) {
        super(activity);
        this.activity = activity;
        handler = new Handler();
        this.fileType = fileType;
        MyCamera curCamera = CameraManager.getInstance().getCurCamera();
        String cameraName = curCamera.getCameraFixedInfo().getCameraName();
        if (fileType == FileType.FILE_VIDEO && CameraUtils.isA6Max(cameraName)){
            curLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
        } else if ("A6".equals(cameraName) || CameraUtils.isA6SOrA8(cameraName)) {
            curLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_GRID;
        } else {
            curLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
        }
    }

    public Boolean isCurBrowseMode() {
        return curOperationMode == OperationMode.MODE_BROWSE;
    }

    public int getItemInfoSize() {
        return pbItemInfoList.size();
    }

    public void setView(MultiPbFragmentView pbFragmentView) {
        this.multiPbPhotoView = pbFragmentView;
        initCfg();
    }

    public void setFragment(Fragment fragment) {
        this.fragment = fragment;
    }

    public void resetCurIndex() {
        curIndex = 1;
    }

    public void resetAdapter() {
        recyclerViewAdapter = null;
    }

    public synchronized List<MultiPbItemInfo> getRemotePhotoInfoList() {
        if (supportSegmentedLoading) {
            fileTotalNum = RemoteFileHelper.getInstance().getFileCount(fileOperation, fileType);
            MultiPbFileResult multiPbFileResult = RemoteFileHelper.getInstance().getRemoteFile(fileOperation, fileType, fileTotalNum, curIndex);
            curIndex = multiPbFileResult.getLastIndex();
            isMore = multiPbFileResult.isMore();
            return multiPbFileResult.getFileList();
        } else {
            List<MultiPbItemInfo> fileList = RemoteFileHelper.getInstance().getRemoteFile(fileOperation, fileType);
            if (fileList == null) return new ArrayList<>();
            if (isSingleCamera() && !(fileType == FileType.FILE_VIDEO && CameraUtils.isA6Max())) {//这里需要剔除一下A6Max的视频
                HashMap<String, ArrayList<CheckSingleCameraPhotoBean>> map = new HashMap<>();
                for (int i = 0; i < fileList.size(); i++) {
                    CheckSingleCameraPhotoBean checkBean = new CheckSingleCameraPhotoBean();
                    String fileName = fileList.get(i).iCatchFile.getFileName();
                    if (!fileName.contains("_")){
                        //兼容一下
                        LogUtils.file("从远程相机中读到不合规的文件名，跳过："+fileName);
                        continue;
                    }
                    String groupName = fileName.substring(0, fileName.lastIndexOf("_"));
                    checkBean.fileName = fileName;
                    checkBean.groupName = groupName;
                    checkBean.filePath=fileList.get(i).iCatchFile.getFilePath();
                    checkBean.iCatchFile=fileList.get(i).iCatchFile;

                    ArrayList<CheckSingleCameraPhotoBean> checkList = map.get(groupName);
                    if (checkList == null) {
                        ArrayList<CheckSingleCameraPhotoBean> newList = new ArrayList<>();
                        newList.add(checkBean);
                        map.put(groupName, newList);
                    } else {
                        checkList.add(checkBean);
                    }
                }

                Iterator<Map.Entry<String, ArrayList<CheckSingleCameraPhotoBean>>> iterator = map.entrySet().iterator();
                ArrayList<String> strings = new ArrayList<>();
                while (iterator.hasNext()) {
                    Map.Entry<String, ArrayList<CheckSingleCameraPhotoBean>> entry = iterator.next();
                    ArrayList<CheckSingleCameraPhotoBean> checkList = entry.getValue();
                    if (checkList.size() == 4 || checkList.size() == 12) {
                        if(checkList.size() == 4) {
                            for (CheckSingleCameraPhotoBean bean : checkList) {
                                strings.add(bean.fileName);
                            }
                        }else{
//                            //HDR图片每一组 只要第一张
//                            for(int i=0;i<checkList.size();i=i+3){
//                                strings.add(checkList.get(i).fileName);
//                            }
                            //HDR图片每一组 只要包含A.那一张张
                            for(int i=0;i<checkList.size();i++){
                                if(checkList.get(i).fileName.contains("A.")){
                                    strings.add(checkList.get(i).fileName);
                                }
                            }
                        }
                    } else {
//                        iterator.remove();
                    }
                }

                ArrayList<MultiPbItemInfo> result = new ArrayList<>();
                for (MultiPbItemInfo file : fileList) {
                    for (String fileName : strings) {
                        if (file.getFileName().equals(fileName)) {
                            if(map.get(file.getFileName().substring(0, fileName.lastIndexOf("_")))!=null && map.get(file.getFileName().substring(0, fileName.lastIndexOf("_"))).size()==12){
                                ArrayList<CheckSingleCameraPhotoBean> checkList =map.get(file.getFileName().substring(0, fileName.lastIndexOf("_")));
                                for(CheckSingleCameraPhotoBean bean : checkList){
                                    if(bean.fileName.contains(file.getFileName().substring(0, fileName.lastIndexOf("_")+2))){
                                        if(file.hrdFilePath==null){
                                            file.hrdFilePath=new ArrayList<>();
                                        }
                                        file.hrdFilePath.add(bean);
                                    }
                                }
                            }
                            result.add(file);
                        }
                    }
                }
                fileList.clear();
                fileList.addAll(result);
            }
            return fileList;
        }
    }

    private boolean isSingleCamera() {
        String cameraName = CameraUtils.getCurCamera();
        return "A6".equals(cameraName) || CameraUtils.isA6SOrA8(cameraName);
    }


    public synchronized void loadMoreFile() {
        if (recyclerViewAdapter == null) {
            return;
        }
        recyclerViewAdapter.setLoadState(recyclerViewAdapter.LOADING);
        AppLog.d(TAG, "loadMoreFile current list size:" + pbItemInfoList.size());
        if (!supportSegmentedLoading) {
            recyclerViewAdapter.setLoadState(recyclerViewAdapter.LOADING_END);
            return;
        }
        if (isMore) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    List<MultiPbItemInfo> tempList = getRemotePhotoInfoList();
                    if (tempList != null && tempList.size() > 0) {
                        pbItemInfoList.addAll(tempList);
                    }
                    RemoteFileHelper.getInstance().setLocalFileList(pbItemInfoList, fileType);
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            recyclerViewAdapter.setLoadState(recyclerViewAdapter.LOADING_COMPLETE);
                        }
                    });
                }
            }).start();
        } else {
            recyclerViewAdapter.setLoadState(recyclerViewAdapter.LOADING_END);
        }
    }

    public void loadPhotoWall() {
        MyProgressDialog.showProgressDialog(activity, R.string.message_loading);
        new Thread(() -> {
            supportSegmentedLoading = RemoteFileHelper.getInstance().isSupportSegmentedLoading();
            if (supportSegmentedLoading) {
                fileTotalNum = RemoteFileHelper.getInstance().getFileCount(fileOperation, fileType);
                pbItemInfoList.clear();
                List<MultiPbItemInfo> temp = RemoteFileHelper.getInstance().getLocalFileList(fileType);
                if (fileTotalNum > 0 && temp != null && temp.size() > 0) {
                    pbItemInfoList.addAll(temp);
                } else if (fileTotalNum > 0) {
                    resetCurIndex();
                    List tempList = getRemotePhotoInfoList();
                    if (tempList != null && tempList.size() > 0) {
                        pbItemInfoList.addAll(tempList);
                    }
                    RemoteFileHelper.getInstance().setLocalFileList(pbItemInfoList, fileType);
                }
                AppLog.d(TAG, "pbItemInfoList=" + pbItemInfoList);
                if (fileTotalNum <= 0 || pbItemInfoList.size() <= 0) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            multiPbPhotoView.setRecyclerViewVisibility(View.GONE);
                            multiPbPhotoView.setNoContentTxvVisibility(View.VISIBLE);
                        }
                    });
                } else {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            multiPbPhotoView.setNoContentTxvVisibility(View.GONE);
                            multiPbPhotoView.setRecyclerViewVisibility(View.VISIBLE);
                            setAdapter();
                            MyProgressDialog.closeProgressDialog();
                        }
                    });
                }
            } else {
                pbItemInfoList.clear();
                List<MultiPbItemInfo> temp = RemoteFileHelper.getInstance().getLocalFileList(fileType);
                if (temp != null) {
                    pbItemInfoList.addAll(temp);
                } else {
                    resetCurIndex();
                    List tempList = getRemotePhotoInfoList();
                    if (tempList != null && tempList.size() > 0) {
                        pbItemInfoList.addAll(tempList);
                    }
                    RemoteFileHelper.getInstance().setLocalFileList(pbItemInfoList, fileType);
                }
                AppLog.d(TAG, "pbItemInfoList=" + pbItemInfoList);
                if (pbItemInfoList.size() <= 0) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            multiPbPhotoView.setRecyclerViewVisibility(View.GONE);
                            multiPbPhotoView.setNoContentTxvVisibility(View.VISIBLE);
                        }
                    });
                } else {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            multiPbPhotoView.setNoContentTxvVisibility(View.GONE);
                            multiPbPhotoView.setRecyclerViewVisibility(View.VISIBLE);
                            setAdapter();
                            MyProgressDialog.closeProgressDialog();

                            if (remoteMultiPbPresenter == null) {
                                remoteMultiPbPresenter = new RemoteMultiPbPresenter(activity);
                            }
                            remoteMultiPbPresenter.downZdInfo(remoteMultiPbPresenter);
                        }
                    });
                }
            }
        }).start();
    }

    public RemoteMultiPbPresenter remoteMultiPbPresenter;



    public void setAdapter() {
        curOperationMode = OperationMode.MODE_BROWSE;
        if (pbItemInfoList == null || pbItemInfoList.size() < 0) {
            return;
        }
        List<LocalPbItemInfo> photoInfoList = VideoListUtil.getPhotoInfoList(fileType, PanoramaApp.getContext());
        //确定相机文件是否下载过
        if (photoInfoList!=null && photoInfoList.size()>0) {
            for (int i = 0; i < photoInfoList.size(); i++) {
                for (int j = 0; j < pbItemInfoList.size(); j++) {
                    String fileName = "";
                    if (pbItemInfoList.get(j).iCatchFile.getFileName().length() > 15) {
                        fileName = pbItemInfoList.get(j).iCatchFile.getFileName().substring(0, 15);
                        if (photoInfoList.get(i).file.getName().contains(fileName)) {
                            pbItemInfoList.get(j).isDownload = true;
                            pbItemInfoList.get(j).fileLocal = photoInfoList.get(i).file;
                        }
                    }
                }
            }
        }

        if (recyclerViewAdapter != null) {
            recyclerViewAdapter.notifyDataSetChanged();
        } else {
            recyclerViewAdapter = new MultiPbRecyclerViewAdapter(activity, pbItemInfoList, fileType);
            setLayoutType(curLayoutType);
            multiPbPhotoView.setRecyclerViewAdapter(recyclerViewAdapter);
        }
    }

    public void refreshAdapter() {
        curOperationMode = OperationMode.MODE_BROWSE;
        if (pbItemInfoList == null || pbItemInfoList.size() < 0) {
            return;
        }
        if (recyclerViewAdapter != null) {
            recyclerViewAdapter.notifyDataSetChanged();
        } else {
            recyclerViewAdapter = new MultiPbRecyclerViewAdapter(activity, pbItemInfoList, fileType);
            setLayoutType(curLayoutType);
            multiPbPhotoView.setRecyclerViewAdapter(recyclerViewAdapter);
        }
    }

    public void refreshPhotoWall() {
        Log.i(TAG, "refreshPhotoWall");
        if (pbItemInfoList == null || pbItemInfoList.size() <= 0) {
            multiPbPhotoView.setRecyclerViewVisibility(View.GONE);
            multiPbPhotoView.setNoContentTxvVisibility(View.VISIBLE);
        } else {
            multiPbPhotoView.setNoContentTxvVisibility(View.GONE);
            refreshAdapter();
        }
    }

    public void setLayoutType(PhotoWallLayoutType layoutType) {
        if (recyclerViewAdapter == null) {
            return;
        }
        if (layoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
            recyclerViewAdapter.setCurViewType(MultiPbRecyclerViewAdapter.TYPE_LIST);
            multiPbPhotoView.setRecyclerViewLayoutManager(new LinearLayoutManager(activity));
        } else if (layoutType == PhotoWallLayoutType.PREVIEW_TYPE_GRID) {
            recyclerViewAdapter.setCurViewType(MultiPbRecyclerViewAdapter.TYPE_GRID);
            multiPbPhotoView.setRecyclerViewLayoutManager(new GridLayoutManager(activity, 4));
        } else if (layoutType == PhotoWallLayoutType.PREVIEW_TYPE_QUICK_LIST) {
            recyclerViewAdapter.setCurViewType(MultiPbRecyclerViewAdapter.TYPE_QUICK_LIST);
            multiPbPhotoView.setRecyclerViewLayoutManager(new LinearLayoutManager(activity));
        }
        multiPbPhotoView.setRecyclerViewAdapter(recyclerViewAdapter);
    }

    public void changePreviewType(PhotoWallLayoutType layoutType) {
        curLayoutType = layoutType;
        setLayoutType(curLayoutType);
    }

    public void enterEditMode(int position) {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            curOperationMode = OperationMode.MODE_EDIT;
            multiPbPhotoView.notifyChangeMultiPbMode(curOperationMode);
            recyclerViewAdapter.setOperationMode(curOperationMode);
            int[] pos0123 = recyclerViewAdapter.getPanoramaQuarterPos0123(position);
            if (pos0123.length == 4 && recyclerViewAdapter.isHasQuarter0123(position)) {
                for (int ix : pos0123) {
                    recyclerViewAdapter.changeCheckBoxState(ix);
                }
            } else {
                recyclerViewAdapter.changeCheckBoxState(position);
            }
            multiPbPhotoView.setPhotoSelectNumText(recyclerViewAdapter.getSelectedCount());
            AppLog.i(TAG, "gridViewSelectOrCancelOnce curOperationMode=" + curOperationMode);
        }
    }

    public void quitEditMode() {
        if (curOperationMode == OperationMode.MODE_EDIT) {
            curOperationMode = OperationMode.MODE_BROWSE;
            multiPbPhotoView.notifyChangeMultiPbMode(curOperationMode);
            recyclerViewAdapter.quitEditMode();
        }
    }

    public synchronized void itemClick(final int position) {
        AppLog.i(TAG, "listViewSelectOrCancelOnce positon=" + position);
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            AppLog.i(TAG, "listViewSelectOrCancelOnce curOperationMode=" + curOperationMode);
            if (fileType == FileType.FILE_PHOTO) {
                Intent intent = new Intent();
                intent.putExtra("curFilePosition", position);
                intent.putExtra("fileType", fileType.ordinal());
                intent.setClass(activity, PhotoPbActivity.class);
                fragment.startActivityForResult(intent, 1000);
            } else {
                MyProgressDialog.showProgressDialog(activity, R.string.wait);
                MyCamera myCamera = CameraManager.getInstance().getCurCamera();
                if (myCamera != null) {
                    myCamera.setLoadThumbnail(false);
                }
                stopLoad();
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        Intent intent = new Intent();
                        intent.putExtra("curFilePosition", position);
                        intent.putExtra("fileType", fileType.ordinal());
                        intent.setClass(activity, VideoPbActivity.class);
                        fragment.startActivityForResult(intent, 1000);
                        MyProgressDialog.closeProgressDialog();
                    }
                }, 1500);
            }
        } else {
            int[] pos0123 = recyclerViewAdapter.getPanoramaQuarterPos0123(position);
            if (pos0123.length == 4 && recyclerViewAdapter.isHasQuarter0123(position)) {
                for (int ix : pos0123) {
                    recyclerViewAdapter.changeCheckBoxState(ix);
                }
            } else {
                recyclerViewAdapter.changeCheckBoxState(position);
            }
            multiPbPhotoView.setPhotoSelectNumText(recyclerViewAdapter.getSelectedCount());
        }
    }

    public void selectOrCancelAll(boolean isSelectAll) {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            return;
        }
        int selectNum;
        if (isSelectAll) {
            recyclerViewAdapter.selectAllItems();
            selectNum = recyclerViewAdapter.getSelectedCount();
        } else {
            recyclerViewAdapter.cancelAllSelections();
            selectNum = recyclerViewAdapter.getSelectedCount();
        }
        multiPbPhotoView.setPhotoSelectNumText(selectNum);
    }

    public List<MultiPbItemInfo> getSelectedList() {
        return recyclerViewAdapter.getCheckedItemsList();
    }

    public void emptyFileList() {
        RemoteFileHelper.getInstance().clearFileList(fileType);
    }

    public void deleteFile() {
        List<MultiPbItemInfo> list = null;
        list = getSelectedList();
        if (list == null || list.size() <= 0) {
            AppLog.d(TAG, "asytaskList size=" + list.size());
            MyToast.show(activity, R.string.gallery_no_file_selected);
        } else {
            CharSequence what = activity.getResources().getString(R.string.gallery_delete_des).replace("$1$", String.valueOf(list.size()));
            AlertDialog.Builder builder = new AlertDialog.Builder(activity);
            builder.setCancelable(false);
            builder.setMessage(what);
            builder.setPositiveButton(activity.getResources().getString(R.string.gallery_cancel), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });
            final List<MultiPbItemInfo> finalList = list;
            final FileType finalFileType = fileType;
            builder.setNegativeButton(activity.getResources().getString(R.string.gallery_delete), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    MyProgressDialog.showProgressDialog(activity, R.string.dialog_deleting);
                    quitEditMode();
                    new Thread(new DeleteFileThread(finalList, finalFileType)).start();
                }
            });
            builder.create().show();
        }
    }

    public void stopLoad() {
        ImageLoaderConfig.stopLoad();
    }

    private class DeleteFileThread implements Runnable {
        private List<MultiPbItemInfo> fileList;
        private List<MultiPbItemInfo> deleteSucceedList;
        private Handler handler;
        private FileOperation fileOperation;
        private FileType fileType;

        public DeleteFileThread(List<MultiPbItemInfo> fileList, FileType fileType) {
            this.fileList = fileList;
            this.handler = new Handler();
            this.fileOperation = CameraManager.getInstance().getCurCamera().getFileOperation();
            this.fileType = fileType;
        }

        @Override
        public void run() {
            AppLog.d(TAG, "DeleteThread");
            deleteSucceedList = new LinkedList<MultiPbItemInfo>();
            for (MultiPbItemInfo tempFile : fileList) {
                AppLog.d(TAG, "deleteFile f.getFileHandle =" + tempFile.getFileHandle());
                if (fileOperation.deleteFile(tempFile.iCatchFile)) {
                    deleteSucceedList.add(tempFile);
                }
                if(tempFile.hrdFilePath!=null && tempFile.hrdFilePath.size()>0){
                    for(CheckSingleCameraPhotoBean hdrFile : tempFile.hrdFilePath){
                        if(hdrFile!=null && hdrFile.iCatchFile!=null){
                            fileOperation.deleteFile(hdrFile.iCatchFile);
                        }
                    }
                }
            }
            handler.post(new Runnable() {
                @Override
                public void run() {
                    MyProgressDialog.closeProgressDialog();
                    if (deleteSucceedList.size() > 0) {
                        pbItemInfoList.removeAll(deleteSucceedList);
                        RemoteFileHelper.getInstance().setLocalFileList(pbItemInfoList, fileType);
                    }
                    if (supportSegmentedLoading) {
                        resetCurIndex();
                        RemoteFileHelper.getInstance().clearFileList(fileType);
                        loadPhotoWall();
                    } else {
                        refreshPhotoWall();
                    }
                    curOperationMode = OperationMode.MODE_BROWSE;
                    multiPbPhotoView.notifyChangeMultiPbMode(curOperationMode);
                }
            });
        }
    }

    public interface OnGetListCompleteListener {
        void onGetFileListComplete();
    }
}

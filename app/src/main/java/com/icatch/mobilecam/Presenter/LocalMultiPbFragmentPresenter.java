package com.icatch.mobilecam.Presenter;

import android.app.Activity;
import android.content.Intent;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;

import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.Presenter.Interface.BasePresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.Interface.LocalMultiPbFragmentView;
import com.icatch.mobilecam.ui.activity.LocalPhotoPbActivity;
import com.icatch.mobilecam.ui.activity.LocalVideoPbActivity;
import com.icatch.mobilecam.ui.adapter.LocalMultiPbWallGridAdapter;
import com.icatch.mobilecam.ui.adapter.LocalMultiPbWallListAdapter;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.PanoramaTools;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.fileutils.MFileTools;
import com.ijoyer.camera.activity.PanoramaPlayerActivity;
import com.ijoyer.camera.activity.RecordPlayActivity;
import com.ijoyer.mobilecam.R;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LocalMultiPbFragmentPresenter extends BasePresenter {
    private String TAG = LocalMultiPbFragmentPresenter.class.getSimpleName();
    private LocalMultiPbFragmentView multiPbPhotoView;
    private LocalMultiPbWallListAdapter photoWallListAdapter;
    private LocalMultiPbWallGridAdapter photoWallGridAdapter;
    private Activity activity;
    private static int section = 1;
    private Map<String, Integer> sectionMap = new HashMap<String, Integer>();
    private OperationMode curOperationMode = OperationMode.MODE_BROWSE;
    private List<LocalPbItemInfo> pbItemInfoList;
    private Handler handler;
    private FileType fileType = FileType.FILE_PHOTO;

    public LocalMultiPbFragmentPresenter(Activity activity, FileType fileType) {
        super(activity);
        this.activity = activity;
        handler = new Handler();
        this.fileType = fileType;
    }

    public void setView(LocalMultiPbFragmentView localPhotoWallView) {
        this.multiPbPhotoView = localPhotoWallView;
        initCfg();
    }

    public List<LocalPbItemInfo> getPhotoInfoList(FileType fileType) {
        String fileDate;
        String rootPath = StorageUtil.getRootPath(activity);
        final List<LocalPbItemInfo> photoList = new ArrayList<LocalPbItemInfo>();
        List<File> fileList;
        if (fileType == FileType.FILE_PHOTO) {
            String filePath = rootPath + AppInfo.DOWNLOAD_PATH_PHOTO;
            fileList = MFileTools.getPhotosOrderByDate(filePath);
        } else {
            String filePath = rootPath + AppInfo.DOWNLOAD_PATH_VIDEO;
            fileList = MFileTools.getVideosOrderByDate(filePath);
        }
        if (fileList == null || fileList.size() <= 0) {
            return null;
        }
        AppLog.i(TAG, "fileList size=" + fileList.size());
        for (int ii = 0; ii < fileList.size(); ii++) {
            long time = fileList.get(ii).lastModified();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            fileDate = format.format(new Date(time));
            Integer savedSection = sectionMap.get(fileDate);
            if (savedSection == null) {
                sectionMap.put(fileDate, section);
                LocalPbItemInfo mGridItem =
                        new LocalPbItemInfo(fileList.get(ii), section, PanoramaTools.isPanorama(fileList.get(ii).getPath()), ii);
                photoList.add(mGridItem);
                section++;
            } else {
                LocalPbItemInfo mGridItem = new LocalPbItemInfo(fileList.get(ii), savedSection,
                        PanoramaTools.isPanorama(fileList.get(ii).getPath()), ii);
                photoList.add(mGridItem);
            }
        }


        try {
            if (photoList.size() != 0) {
                for (int ii = 0; ii < photoList.size(); ii++) {
                    LocalPbItemInfo localPbItemInfo = photoList.get(ii);
                    String[] fileName = localPbItemInfo.file.getName().split("\\.");
                    if (fileName != null && fileName.length > 0) {
                        //20230221
                        if ((fileName[0].length() == 15 || fileName[0].length() == 17 || fileName[0].length() == 18) && FileUtils.getFileLastModified(localPbItemInfo.file) > 1676967051044L) {
                            FileUtils.delete(photoList.get(ii).file);
                            photoList.remove(ii);
                            ii--;
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (fileType == FileType.FILE_PHOTO) {
            GlobalInfo.getInstance().setLocalPhotoList(photoList);
        } else {
            GlobalInfo.getInstance().setLocalVideoList(photoList);
        }
        return photoList;
    }

    /**
     * oldPath 和 newPath必须是新旧文件的绝对路径
     */
    private File renameFile(String oldPath, String newPath) {
        if (TextUtils.isEmpty(oldPath)) {
            return null;
        }

        if (TextUtils.isEmpty(newPath)) {
            return null;
        }
        File oldFile = new File(oldPath);
        File newFile = new File(newPath);
        boolean b = oldFile.renameTo(newFile);
        File file2 = new File(newPath);
        return file2;
    }

    public void loadPhotoWall() {
        MyProgressDialog.showProgressDialog(activity, R.string.message_loading);
        new Thread(new Runnable() {
            @Override
            public void run() {
                pbItemInfoList = getPhotoInfoList(fileType);
                if (pbItemInfoList == null || pbItemInfoList.size() <= 0) {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            multiPbPhotoView.setGridViewVisibility(View.GONE);
                            multiPbPhotoView.setListViewVisibility(View.GONE);
                            multiPbPhotoView.setNoContentTxvVisibility(View.VISIBLE);
                        }
                    });
                } else {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            multiPbPhotoView.setNoContentTxvVisibility(View.GONE);
                            setAdapter();
                        }
                    });
                }
            }
        }).start();
    }

    public void setAdapter() {
        curOperationMode = OperationMode.MODE_BROWSE;
        if (pbItemInfoList != null && pbItemInfoList.size() > 0) {
            String fileDate = pbItemInfoList.get(0).getFileDate();
            AppLog.d(TAG, "fileDate=" + fileDate);
        }
        int curWidth = 0;
        if (AppInfo.photoWallLayoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
            multiPbPhotoView.setGridViewVisibility(View.GONE);
            multiPbPhotoView.setListViewVisibility(View.VISIBLE);
            photoWallListAdapter = new LocalMultiPbWallListAdapter(activity, pbItemInfoList, fileType);
            multiPbPhotoView.setListViewAdapter(photoWallListAdapter);
        } else {
            multiPbPhotoView.setGridViewVisibility(View.VISIBLE);
            multiPbPhotoView.setListViewVisibility(View.GONE);
            AppLog.d(TAG, "width=" + curWidth);
            photoWallGridAdapter = (new LocalMultiPbWallGridAdapter(activity, pbItemInfoList, FileType.FILE_PHOTO));
            multiPbPhotoView.setGridViewAdapter(photoWallGridAdapter);
        }
    }

    public void refreshPhotoWall() {
        AppLog.d(TAG, "refreshPhotoWall layoutType=" + AppInfo.photoWallLayoutType);
        pbItemInfoList = getPhotoInfoList(fileType);
        if (pbItemInfoList == null || pbItemInfoList.size() <= 0) {
            multiPbPhotoView.setGridViewVisibility(View.GONE);
            multiPbPhotoView.setListViewVisibility(View.GONE);
            multiPbPhotoView.setNoContentTxvVisibility(View.VISIBLE);
        } else {
            multiPbPhotoView.setNoContentTxvVisibility(View.GONE);
            setAdapter();
        }
    }

    public void changePreviewType() {
        if (AppInfo.photoWallLayoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
            AppInfo.photoWallLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_GRID;
        } else {
            AppInfo.photoWallLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
        }
        loadPhotoWall();
    }

    public void listViewEnterEditMode(int position) {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            curOperationMode = OperationMode.MODE_EDIT;
            multiPbPhotoView.notifyChangeMultiPbMode(curOperationMode);
            photoWallListAdapter.setOperationMode(curOperationMode);
            int[] arr = pbItemInfoList.get(position).getPanoramaQuarterPos0123();
            if (arr.length == 4 && arr != null) {
                for (int i = 0; i < arr.length; i++) {
                    photoWallListAdapter.changeSelectionState(arr[i]);
                }
            } else {
                photoWallListAdapter.changeSelectionState(position);
            }
            multiPbPhotoView.setPhotoSelectNumText(photoWallListAdapter.getSelectedCount());
            AppLog.i(TAG, "gridViewSelectOrCancelOnce curOperationMode=" + curOperationMode);
        }
    }

    public void gridViewEnterEditMode(int position) {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            curOperationMode = OperationMode.MODE_EDIT;
            multiPbPhotoView.notifyChangeMultiPbMode(curOperationMode);
            photoWallGridAdapter.changeCheckBoxState(position, curOperationMode);
            multiPbPhotoView.setPhotoSelectNumText(photoWallGridAdapter.getSelectedCount());
            AppLog.i(TAG, "gridViewSelectOrCancelOnce curOperationMode=" + curOperationMode);
        }
    }

    public void quitEditMode() {
        if (curOperationMode == OperationMode.MODE_EDIT) {
            curOperationMode = OperationMode.MODE_BROWSE;
            multiPbPhotoView.notifyChangeMultiPbMode(curOperationMode);
            if (AppInfo.photoWallLayoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
                photoWallListAdapter.quitEditMode();
            } else {
                photoWallGridAdapter.quitEditMode();
            }
        }
    }

    private ExecutorService executorService;

    private class StitchThread implements Runnable {
        @Override
        public void run() {
            long start = System.currentTimeMillis();
            Pattern pattern = Pattern.compile("_[0-3]\\.");
            Matcher matcher = pattern.matcher(curFilePath);
            String filePathA = matcher.replaceFirst("_" + 3 + ".");
            String filePathB = matcher.replaceFirst("_" + 2 + ".");
            String filePathC = matcher.replaceFirst("_" + 1 + ".");
            String filePathD = matcher.replaceFirst("_" + 0 + ".");
            AppLog.d(TAG, "filePathA:" + filePathA);
            AppLog.d(TAG, "filePathB:" + filePathB);
            AppLog.d(TAG, "filePathC:" + filePathC);
            AppLog.d(TAG, "filePathD:" + filePathD);
            String curFileName = pbItemInfoList.get(curPosition).getFileName();
            matcher = pattern.matcher(curFileName);
            String tmp = matcher.replaceFirst("_" + "PANO" + ".");
            AppLog.d(TAG, "new pano file name:" + tmp);
            int result = StitchUtils.stitchExec(activity, filePathA, filePathB, filePathC, filePathD, tmp);
            handler.post(() -> {
                MyProgressDialog.closeProgressDialog();
                refreshPhotoWall();
                long end = System.currentTimeMillis();
                if (0 == result) {
                    AppToast.show(activity, PanoramaApp.getContext().getString(R.string.stitching_completed)
                            + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                } else {
                    AppDialog.showDialogWarn(activity, "拼接失败，代码：" + result);
                }
            });
        }
    }

    private int curPosition;
    private String curFilePath;

    public void listViewSelectOrCancelOnce(int position) {
        AppLog.i(TAG, "listViewSelectOrCancelOnce position=" + position + " AppInfo.photoWallPreviewType=" + AppInfo.photoWallLayoutType);
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            AppLog.i(TAG, "listViewSelectOrCancelOnce curOperationMode=" + curOperationMode);
            boolean isMatchesRecord = pbItemInfoList.get(position).getFileName().matches(StitchUtils.REGEX_RECORD);
            String filePath = null;
            if (position >= 0 && position < pbItemInfoList.size()){
                filePath = pbItemInfoList.get(position).getFilePath();
            }
            if (fileType == FileType.FILE_PHOTO) {
                curPosition = position;
                curFilePath = filePath;
                boolean isPanoramaEx = PanoramaTools.isPanorama(curFilePath);
                boolean isMatches = pbItemInfoList.get(position).getFileName().matches(StitchUtils.REGEX);
                boolean isExists01234 = pbItemInfoList.get(position).isQuarterAllExists();
                if (isMatches && isExists01234) {
                    AlertDialog.Builder builder = new AlertDialog.Builder(activity, R.style.MyAlertDialog);
                    builder.setMessage("该组图片为IJOYER全景相机拍摄，可以进行拼接，否则直接打开！");
                    builder.setCancelable(false);
                    int finalPosition = position;
                    builder.setNegativeButton(android.R.string.cancel, ((dialogInterface, i) -> {
                        Intent intent = new Intent();
                        intent.putExtra("curFilePosition", finalPosition);
                        intent.setClass(activity, LocalPhotoPbActivity.class);
                        activity.startActivity(intent);
                    }));
                    builder.setPositiveButton(R.string.ok, (dialogInterface, i) -> {
                        MyProgressDialog.showProgressDialog(activity, "正在拼接...");
                        executorService = Executors.newSingleThreadExecutor();
                        executorService.submit(new StitchThread(), null);
                    });
                    builder.show();
                    return;
                } else if (isPanoramaEx) {
                    Intent intent = new Intent();
                    AppLog.d(TAG, "Panorama File path: " + curFilePath);
                    intent.putExtra("path", curFilePath);
                    intent.putExtra("curFilePosition", position);
                    intent.putExtra("isShowPhotoEdit", true);
                    intent.setClass(activity, PanoramaPlayerActivity.class);
                    activity.startActivity(intent);
                    return;
                }
            }else if (CameraUtils.handleA6MaxVideo(activity,filePath)){
                //A6Max
                LogUtils.d(TAG,"A6Max 视频");
            }else if (fileType == FileType.FILE_VIDEO && !isMatchesRecord) {
                Intent intent = new Intent();
                intent.putExtra("path", filePath);
                intent.putExtra("playTag", 2);


                List<LocalPbItemInfo> localVideoList = GlobalInfo.getInstance().getLocalVideoList();
                List<LocalPbItemInfo> localVideoListNoRecord = new ArrayList<>();
                for (int i = 0; i < localVideoList.size(); i++) {
                    if (!localVideoList.get(i).file.getName().contains("RECORD")) {
                        localVideoListNoRecord.add(localVideoList.get(i));
                    }
                }

                for (int i = 0; i < localVideoListNoRecord.size(); i++) {
                    if (localVideoListNoRecord.get(i).file.getName().equals(pbItemInfoList.get(position).file.getName())) {
                        position = i;
                    }
                }

                intent.putExtra("curFilePosition", position);
                intent.setClass(activity, PanoramaPlayerActivity.class);
                activity.startActivity(intent);
                return;
            }
            Intent intent = new Intent();
            if (fileType == FileType.FILE_PHOTO) {
                intent.putExtra("curFilePosition", position);
                intent.setClass(activity, LocalPhotoPbActivity.class);
            } else {
                String videoPath = pbItemInfoList.get(position).getFilePath();
                intent.putExtra("path", videoPath);
                intent.setClass(activity, RecordPlayActivity.class);
            }
            activity.startActivity(intent);
        } else {
            int[] arr = pbItemInfoList.get(position).getPanoramaQuarterPos0123();
            if (arr.length == 4 && arr != null) {
                for (int i = 0; i < arr.length; i++) {
                    photoWallListAdapter.changeSelectionState(arr[i]);
                }
            } else {
                photoWallListAdapter.changeSelectionState(position);
            }
            multiPbPhotoView.setPhotoSelectNumText(photoWallListAdapter.getSelectedCount());
        }
    }

    public void gridViewSelectOrCancelOnce(int position) {
        AppLog.i(TAG, "gridViewSelectOrCancelOnce positon=" + position + " AppInfo.photoWallPreviewType=" + AppInfo.photoWallLayoutType);
        String videoPath = pbItemInfoList.get(position).getFilePath();
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            if (fileType == FileType.FILE_PHOTO) {
                Intent intent = new Intent();
                intent.putExtra("curFilePosition", position);
                intent.setClass(activity, LocalPhotoPbActivity.class);
                activity.startActivity(intent);
            } else {
                Intent intent = new Intent();
                intent.putExtra("curFilePath", videoPath);
                intent.putExtra("curFilePosition", position);
                intent.setClass(activity, LocalVideoPbActivity.class);
                activity.startActivity(intent);
            }
        } else {
            photoWallGridAdapter.changeCheckBoxState(position, curOperationMode);
            multiPbPhotoView.setPhotoSelectNumText(photoWallGridAdapter.getSelectedCount());
        }
    }

    public void selectOrCancelAll(boolean isSelectAll) {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            return;
        }
        int selectNum;
        if (isSelectAll) {
            if (AppInfo.photoWallLayoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
                photoWallListAdapter.selectAllItems();
                selectNum = photoWallListAdapter.getSelectedCount();
            } else {
                photoWallGridAdapter.selectAllItems();
                selectNum = photoWallGridAdapter.getSelectedCount();
            }
            multiPbPhotoView.setPhotoSelectNumText(selectNum);
        } else {
            if (AppInfo.photoWallLayoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
                photoWallListAdapter.cancelAllSelections();
                selectNum = photoWallListAdapter.getSelectedCount();
            } else {
                photoWallGridAdapter.cancelAllSelections();
                selectNum = photoWallGridAdapter.getSelectedCount();
            }
            multiPbPhotoView.setPhotoSelectNumText(selectNum);
        }
    }

    public List<LocalPbItemInfo> getSelectedList() {
        if (AppInfo.photoWallLayoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
            return photoWallListAdapter.getSelectedList();
        } else {
            return photoWallGridAdapter.getCheckedItemsList();
        }
    }
}

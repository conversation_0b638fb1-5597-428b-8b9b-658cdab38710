package com.icatch.mobilecam.Presenter;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.view.View;
import android.widget.Toast;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.blankj.utilcode.util.FileUtils;
import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Listener.OnStatusChangedListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.Presenter.Interface.BasePresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Fragment.LocalMultiPbFragment;
import com.icatch.mobilecam.ui.Interface.LocalMultiPbView;
import com.icatch.mobilecam.ui.adapter.ViewPagerAdapter;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.ijoyer.mobilecam.R;
import com.permissionx.guolindev.PermissionX;

import java.io.File;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LocalMultiPbPresenter extends BasePresenter {
    private static final String TAG = LocalMultiPbPresenter.class.getSimpleName();
    private LocalMultiPbView multiPbView;
    private Activity activity;
    LocalMultiPbFragment multiPbPhotoFragment;
    LocalMultiPbFragment multiPbVideoFragment;
    public OperationMode curOperationMode = OperationMode.MODE_BROWSE;
    ViewPagerAdapter adapter;
    private boolean curSelectAll = false;

    public LocalMultiPbPresenter(Activity activity) {
        super(activity);
        this.activity = activity;
        Intent intent = activity.getIntent();
        AppInfo.currentViewpagerPosition = intent.getIntExtra("CUR_POSITION", 0);
    }

    public void setView(LocalMultiPbView multiPbView) {
        this.multiPbView = multiPbView;
        initCfg();
    }

    public void loadViewPager() {
        initViewpager();
    }

    public void reset() {
        AppInfo.photoWallLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
        AppInfo.currentViewpagerPosition = 0;
        AppInfo.curVisibleItem = 0;
    }

    private void initViewpager() {
        if (multiPbPhotoFragment == null) {
            multiPbPhotoFragment = LocalMultiPbFragment.newInstance(FileType.FILE_PHOTO.ordinal());
        }
        multiPbPhotoFragment.setOperationListener(new OnStatusChangedListener() {
            @Override
            public void onChangeOperationMode(OperationMode operationMode) {
                curOperationMode = operationMode;
                if (curOperationMode == OperationMode.MODE_BROWSE) {
                    multiPbView.setViewPagerScanScroll(true);
                    multiPbView.setTabLayoutClickable(true);
                    multiPbView.setSelectBtnVisibility(View.GONE);
                    multiPbView.setSelectNumTextVisibility(View.GONE);
                    multiPbView.setEditLayoutVisibility(View.GONE);
                    multiPbView.setSelectBtnIcon(R.drawable.ic_select_all_white_24dp);
                    curSelectAll = false;
                    AppLog.d(TAG, "multiPbPhotoFragment quit EditMode");
                } else {
                    multiPbView.setViewPagerScanScroll(false);
                    multiPbView.setTabLayoutClickable(false);
                    multiPbView.setSelectBtnVisibility(View.VISIBLE);
                    multiPbView.setSelectNumTextVisibility(View.VISIBLE);
                    multiPbView.setEditLayoutVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onSelectedItemsCountChanged(int SelectedNum) {
                String temp = activity.getString(R.string.text_selected).replace("$1$", String.valueOf(SelectedNum));
                ;
                multiPbView.setSelectNumText(temp);
            }
        });
        if (multiPbVideoFragment == null) {
            multiPbVideoFragment = LocalMultiPbFragment.newInstance(FileType.FILE_VIDEO.ordinal());
        }
        multiPbVideoFragment.setOperationListener(new OnStatusChangedListener() {
            @Override
            public void onChangeOperationMode(OperationMode operationMode) {
                curOperationMode = operationMode;
                if (curOperationMode == OperationMode.MODE_BROWSE) {
                    multiPbView.setViewPagerScanScroll(true);
                    multiPbView.setTabLayoutClickable(true);
                    multiPbView.setSelectBtnVisibility(View.GONE);
                    multiPbView.setSelectNumTextVisibility(View.GONE);
                    multiPbView.setEditLayoutVisibility(View.GONE);
                    multiPbView.setSelectBtnIcon(R.drawable.ic_select_all_white_24dp);
                    curSelectAll = false;
                    AppLog.d(TAG, "multiPbVideoFragment quit EditMode");
                } else {
                    multiPbView.setViewPagerScanScroll(false);
                    multiPbView.setTabLayoutClickable(false);
                    multiPbView.setSelectBtnVisibility(View.VISIBLE);
                    multiPbView.setSelectNumTextVisibility(View.VISIBLE);
                    multiPbView.setEditLayoutVisibility(View.VISIBLE);
                }
            }

            @Override
            public void onSelectedItemsCountChanged(int SelectedNum) {
                String temp = activity.getString(R.string.text_selected).replace("$1$", String.valueOf(SelectedNum));
                multiPbView.setSelectNumText(temp);
            }
        });
        FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
        adapter = new ViewPagerAdapter(manager);
        adapter.addFragment(multiPbPhotoFragment, activity.getResources().getString(R.string.title_photo));
        adapter.addFragment(multiPbVideoFragment, activity.getResources().getString(R.string.title_video));
        multiPbView.setViewPageAdapter(adapter);
        multiPbView.setViewPageCurrentItem(AppInfo.currentViewpagerPosition);
    }

    public void updateViewpagerStatus(int arg0) {
        AppLog.d(TAG, "updateViewpagerStatus arg0=" + arg0);
        AppInfo.currentViewpagerPosition = arg0;
    }

    public void changePreviewType() {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            clealAsytaskList();
            if (AppInfo.photoWallLayoutType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
                AppInfo.photoWallLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_GRID;
                multiPbView.setMenuPhotoWallTypeIcon(R.drawable.ic_view_grid_white_24dp);
            } else {
                AppInfo.photoWallLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
                multiPbView.setMenuPhotoWallTypeIcon(R.drawable.ic_view_list_white_24dp);
            }
            multiPbPhotoFragment.changePreviewType();
            multiPbVideoFragment.changePreviewType();
            AppLog.d(TAG, " changePreviewType AppInfo.photoWallPreviewType");
        }
    }

    public void reBack() {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            activity.finish();
        } else if (curOperationMode == OperationMode.MODE_EDIT) {
            curOperationMode = OperationMode.MODE_BROWSE;
            if (AppInfo.currentViewpagerPosition == 0) {
                multiPbPhotoFragment.quitEditMode();
            } else if (AppInfo.currentViewpagerPosition == 1) {
                multiPbVideoFragment.quitEditMode();
            }
        }
    }

    public void selectOrCancel() {
        if (curSelectAll) {
            multiPbView.setSelectBtnIcon(R.drawable.ic_select_all_white_24dp);
            curSelectAll = false;
        } else {
            multiPbView.setSelectBtnIcon(R.drawable.ic_unselected_white_24dp);
            curSelectAll = true;
        }
        if (AppInfo.currentViewpagerPosition == 0) {
            multiPbPhotoFragment.selectOrCancelAll(curSelectAll);
        } else if (AppInfo.currentViewpagerPosition == 1) {
            multiPbVideoFragment.selectOrCancelAll(curSelectAll);
        }
    }

    public void stitchBatch() {
        List<LocalPbItemInfo> list = null;
        if (AppInfo.currentViewpagerPosition == 0) {
            list = multiPbPhotoFragment.getSelectedList();
            if (list == null || list.size() <= 0) {
                MyToast.show(activity, R.string.gallery_no_file_selected);
            } else {
                HashSet<String> hashSet = new HashSet<>();
                for (int i = 0; i < list.size(); i++) {
                    if (list.get(i).isPanoramaQuarter && list.get(i).isQuarterAllExists()) {
                        hashSet.add(list.get(i).panoramaQuarterFront);
                    }
                }
                if (0 == hashSet.size()) {
                    MyToast.show(activity, "至少选择一组全景图！");
                    return;
                }
                CharSequence what = "已选择了$1$组全景图片，确定要进行批量拼接吗？".replace("$1$", String.valueOf(hashSet.size()));
                AlertDialog.Builder builder = new AlertDialog.Builder(activity, R.style.MyAlertDialog);
                builder.setCancelable(false);
                builder.setMessage(what);
                builder.setPositiveButton(activity.getResources().getString(R.string.gallery_cancel), (dialog, which) -> dialog.dismiss());
                final List<LocalPbItemInfo> finalList = list;
                builder.setNegativeButton(activity.getResources().getString(R.string.ok), (dialog, which) -> {
                    MyProgressDialog.showProgressDialog(activity, "正在批量拼接...");
                    stitchCount = 0;
                    new Thread(new StitchBatchThread(finalList, FileType.FILE_PHOTO)).start();
                });
                builder.create().show();
            }
        }
    }

    static int stitchCount = 0;

    class StitchBatchThread implements Runnable {
        private List<LocalPbItemInfo> fileList;
        private Handler handler;
        private FileType fileType;

        public StitchBatchThread(List<LocalPbItemInfo> fileList, FileType fileType) {
            this.fileList = fileList;
            this.handler = new Handler();
            this.fileType = fileType;
        }

        private final Pattern pattern = Pattern.compile("_[0-3]\\.");

        @Override
        public void run() {
            HashSet<String> hashSet = new HashSet<>();
            for (LocalPbItemInfo tempFile : this.fileList) {
                if (tempFile.isPanoramaQuarter && tempFile.isQuarterAllExists()) {
                    Matcher matcher = pattern.matcher(tempFile.getFilePath());
                    String curFilePath = matcher.replaceFirst("_0.");
                    hashSet.add(curFilePath);
                }
            }
            handler.post(() -> multiPbPhotoFragment.quitEditMode());
            Iterator iterator = hashSet.iterator();
            while (iterator.hasNext()) {
                long start = System.currentTimeMillis();
                String path = (String) iterator.next();
                Matcher matcher = pattern.matcher(path);
                String filePathA = matcher.replaceFirst("_" + 3 + ".");
                String filePathB = matcher.replaceFirst("_" + 2 + ".");
                String filePathC = matcher.replaceFirst("_" + 1 + ".");
                String filePathD = matcher.replaceFirst("_" + 0 + ".");
                AppLog.d(TAG, "filePathA:" + filePathA);
                AppLog.d(TAG, "filePathB:" + filePathB);
                AppLog.d(TAG, "filePathC:" + filePathC);
                AppLog.d(TAG, "filePathD:" + filePathD);
                matcher = pattern.matcher(new File(path).getName());
                String tmp = matcher.replaceFirst("_" + "PANO" + ".");
                AppLog.d(TAG, "new pano file name:" + tmp);
                int result = StitchUtils.stitchExec(activity, filePathA, filePathB, filePathC, filePathD, tmp);
                handler.post(() -> {
                    long end = System.currentTimeMillis();
                    if (0 == result) {
                        multiPbPhotoFragment.refreshPhotoWall();
                        AppToast.show(activity, PanoramaApp.getContext().getString(R.string.the)
                                + ++stitchCount + PanoramaApp.getContext().getString(R.string.time_consuming)
                                + tmp + PanoramaApp.getContext().getString(R.string.stitching_completed)
                                + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                    } else {
                        AppDialog.showDialogWarn(activity, "StitchBatchThread：" + result);
                    }
                });
            }
            handler.post(() -> {
                MyProgressDialog.closeProgressDialog();
            });
        }
    }

    public void delete() {
        List<LocalPbItemInfo> list = null;
        FileType fileType = FileType.FILE_PHOTO;
        AppLog.d(TAG, "delete AppInfo.currentViewpagerPosition=" + AppInfo.currentViewpagerPosition);
        if (AppInfo.currentViewpagerPosition == 0) {
            list = multiPbPhotoFragment.getSelectedList();
            fileType = FileType.FILE_PHOTO;
        } else if (AppInfo.currentViewpagerPosition == 1) {
            list = multiPbVideoFragment.getSelectedList();
            fileType = FileType.FILE_VIDEO;
        }
        if (list == null || list.size() <= 0) {
            AppLog.d(TAG, "asytaskList size=" + list.size());
            MyToast.show(activity, R.string.gallery_no_file_selected);
        } else {
            CharSequence what = activity.getResources().getString(R.string.gallery_delete_des).replace("$1$", String.valueOf(list.size()));
            AlertDialog.Builder builder = new AlertDialog.Builder(activity, R.style.MyAlertDialog);
            builder.setCancelable(false);
            builder.setMessage(what);
            builder.setPositiveButton(activity.getResources().getString(R.string.gallery_cancel), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });
            final List<LocalPbItemInfo> finalList = list;
            final FileType finalFileType = fileType;
            builder.setNegativeButton(activity.getResources().getString(R.string.gallery_delete), new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    try {
                        if (Build.VERSION.SDK_INT >= 30) {
                            PermissionX.init((FragmentActivity) activity).permissions(Manifest.permission.MANAGE_EXTERNAL_STORAGE)
                                    .onExplainRequestReason((scope, deniedList) -> {
                                        scope.showRequestReasonDialog(deniedList, "IJOYER需要您同意以下授权才能正常使用", "同意", "拒绝");
                                    })
                                    .request(((allGranted, grantedList, deniedList) -> {
                                        if (allGranted) {
                                            MyProgressDialog.showProgressDialog(activity, R.string.dialog_deleting);
                                            new Thread(new DeleteFileThread(finalList, finalFileType)).start();
                                        }
                                    }));
                        } else {
                            MyProgressDialog.showProgressDialog(activity, R.string.dialog_deleting);
                            new Thread(new DeleteFileThread(finalList, finalFileType)).start();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
            builder.create().show();
        }
    }

    class DeleteFileThread implements Runnable {
        private List<LocalPbItemInfo> fileList;
        private List<LocalPbItemInfo> deleteFailedList;
        private List<LocalPbItemInfo> deleteSucceedList;
        private Handler handler;
        private FileType fileType;

        public DeleteFileThread(List<LocalPbItemInfo> fileList, FileType fileType) {
            this.fileList = fileList;
            this.handler = new Handler();
            this.fileType = fileType;
        }

        @Override
        public void run() {
            AppLog.d(TAG, "DeleteThread");
            if (deleteFailedList == null) {
                deleteFailedList = new LinkedList<LocalPbItemInfo>();
            } else {
                deleteFailedList.clear();
            }
            if (deleteSucceedList == null) {
                deleteSucceedList = new LinkedList<LocalPbItemInfo>();
            } else {
                deleteSucceedList.clear();
            }
            for (LocalPbItemInfo tempFile : fileList) {
                File file = tempFile.file;
                if (FileUtils.delete(file) == false) {
                    deleteFailedList.add(tempFile);
                } else {
                    deleteSucceedList.add(tempFile);
                }
            }
            //删除成功后，通知相册刷新
            notifyAlbumRefresh(deleteSucceedList);

            clealAsytaskList();
            handler.post(new Runnable() {
                @Override
                public void run() {
                    MyProgressDialog.closeProgressDialog();
                    curOperationMode = OperationMode.MODE_BROWSE;
                    if (AppInfo.currentViewpagerPosition == 0) {
                        multiPbPhotoFragment.quitEditMode();
                        multiPbPhotoFragment.refreshPhotoWall();
                    } else if (AppInfo.currentViewpagerPosition == 1) {
                        multiPbVideoFragment.quitEditMode();
                        multiPbVideoFragment.refreshPhotoWall();
                    }
                }
            });
        }
    }

    private void notifyAlbumRefresh(List<LocalPbItemInfo> list) {
        list.forEach(localPbItemInfo -> {
            Uri contentUri = Uri.fromFile(localPbItemInfo.file);
            Intent mediaScanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, contentUri);
            PanoramaApp.getContext().sendBroadcast(mediaScanIntent);
            AppLog.d(TAG, "通知相册刷新：" + localPbItemInfo.getFilePath());
        });
    }

    public void clealAsytaskList() {
        multiPbPhotoFragment.clealAsytaskList();
        multiPbVideoFragment.clealAsytaskList();
    }
}

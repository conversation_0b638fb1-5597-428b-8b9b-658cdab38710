package com.icatch.mobilecam.Presenter;

import android.app.Activity;
import android.os.Handler;
import android.view.View;
import android.widget.LinearLayout;

import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.icatch.mobilecam.Function.CameraAction.PbDownloadManager;
import com.icatch.mobilecam.Function.SDKEvent;
import com.icatch.mobilecam.Listener.OnStatusChangedListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.Interface.BasePresenter;
import com.icatch.mobilecam.SdkApi.CameraProperties;
import com.icatch.mobilecam.bean.CheckSingleCameraPhotoBean;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.data.SystemInfo.SystemInfo;
import com.icatch.mobilecam.data.entity.MultiPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Fragment.BaseMultiPbFragment;
import com.icatch.mobilecam.ui.Fragment.RemoteMultiPbFragment;
import com.icatch.mobilecam.ui.Interface.MultiPbView;
import com.icatch.mobilecam.ui.RemoteFileHelper;
import com.icatch.mobilecam.ui.adapter.ViewPagerAdapter;
import com.icatch.mobilecam.utils.FileFilter;
import com.icatch.mobilecam.utils.imageloader.ImageLoaderConfig;
import com.icatchtek.control.customer.type.ICatchCamFeatureID;
import com.icatchtek.reliant.customer.type.ICatchFile;
import com.ijoyer.mobilecam.R;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class RemoteMultiPbPresenter extends BasePresenter {
    private static final String TAG = RemoteMultiPbPresenter.class.getSimpleName();
    private MultiPbView multiPbView;
    private Activity activity;
    private List<BaseMultiPbFragment> fragments;
    public OperationMode curOperationMode = OperationMode.MODE_BROWSE;
    ViewPagerAdapter adapter;
    private boolean curSelectAll = false;
    PhotoWallLayoutType curLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
    Handler handler = new Handler();
    private CameraProperties cameraProperties;
    private LinearLayout multiPbEditLayout;
    private MyCamera camera;
    public PbDownloadManager downloadManager;

    public RemoteMultiPbPresenter(Activity activity) {
        super(activity);
        this.activity = activity;
        camera = CameraManager.getInstance().getCurCamera();
        if (camera != null) {
            cameraProperties = camera.getCameraProperties();
        }
    }

    public RemoteMultiPbPresenter(Activity activity, LinearLayout multiPbEditLayout) {
        super(activity);
        this.activity = activity;
        this.multiPbEditLayout = multiPbEditLayout;
        camera = CameraManager.getInstance().getCurCamera();
        if (camera != null) {
            cameraProperties = camera.getCameraProperties();
        }
    }

    public void setView(MultiPbView multiPbView) {
        this.multiPbView = multiPbView;
        initCfg();
    }

    public void loadViewPager() {
        RemoteFileHelper.getInstance().initSupportCapabilities();
        initViewpager();
        initEditLayout();
    }

    public void initEditLayout() {
        boolean isSupportSegmentedLoading = RemoteFileHelper.getInstance().isSupportSegmentedLoading();
        multiPbView.setSelectBtnVisibility(isSupportSegmentedLoading ? View.GONE : View.VISIBLE);
        multiPbView.setSelectNumTextVisibility(isSupportSegmentedLoading ? View.GONE : View.VISIBLE);
    }

    public void reset() {
        AppInfo.currentViewpagerPosition = 0;
        AppInfo.curVisibleItem = 0;
        RemoteFileHelper.getInstance().setFileFilter(null);
        if (cameraProperties != null
                && cameraProperties.hasFunction(PropertyId.DEFALUT_TO_PREVIEW)
                && cameraProperties.checkCameraCapabilities(ICatchCamFeatureID.ICH_CAM_APP_DEFAULT_TO_PLAYBACK)) {
            camera.disconnect();
        }
        boolean isNav = activity.getIntent().getBooleanExtra("navigation", false);
        if (isNav) camera.disconnect();
    }

    private OnStatusChangedListener onStatusChangedListener = new OnStatusChangedListener() {
        @Override
        public void onChangeOperationMode(OperationMode operationMode) {
            curOperationMode = operationMode;
            if (curOperationMode == OperationMode.MODE_BROWSE) {
                multiPbView.setViewPagerScanScroll(true);
                multiPbView.setTabLayoutClickable(true);
                multiPbView.setEditLayoutVisibility(View.GONE);
                multiPbView.setSelectBtnIcon(R.drawable.ic_select_all_white_24dp);
                curSelectAll = false;
                AppLog.d(TAG, "multiPbPhotoFragment quit EditMode");
            } else {
                multiPbView.setViewPagerScanScroll(false);
                multiPbView.setTabLayoutClickable(false);
                multiPbView.setEditLayoutVisibility(View.VISIBLE);
                //批量下载


//                boolean isHdr = HdrUtils.checkIsHdr();
//                int hdrData = SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 1);
//
//                //底部批量下载按钮
//                if ((hdrData == 2 || hdrData == 0) && CameraManager.getInstance().getCurCamera() != null && "A6S".equals(CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraName())){
//                    multiPbEditLayout.findViewById(R.id.action_download).setVisibility(View.GONE);
//                } else {
//                    multiPbEditLayout.findViewById(R.id.action_download).setVisibility(View.VISIBLE);
//                }

                multiPbEditLayout.findViewById(R.id.action_download).setVisibility(View.VISIBLE);

            }
        }

        @Override
        public void onSelectedItemsCountChanged(int SelectedNum) {
            String temp = activity.getString(R.string.text_selected).replace("$1$", String.valueOf(SelectedNum));
            multiPbView.setSelectNumText(temp);
        }
    };

    private void initViewpager() {
        if (fragments == null) {
            fragments = new ArrayList<>();
        } else {
            fragments.clear();
        }
        FragmentManager manager = ((FragmentActivity) activity).getSupportFragmentManager();
        adapter = new ViewPagerAdapter(manager);
        BaseMultiPbFragment multiPbPhotoFragment = RemoteMultiPbFragment.newInstance(FileType.FILE_PHOTO.ordinal());
        multiPbPhotoFragment.setOperationListener(onStatusChangedListener);
        fragments.add(multiPbPhotoFragment);
        adapter.addFragment(multiPbPhotoFragment, activity.getResources().getString(R.string.title_photo));

        //视频Tab
        BaseMultiPbFragment multiPbVideoFragment = RemoteMultiPbFragment.newInstance(FileType.FILE_VIDEO.ordinal());
        multiPbVideoFragment.setOperationListener(onStatusChangedListener);
        fragments.add(multiPbVideoFragment);
        adapter.addFragment(multiPbVideoFragment, activity.getResources().getString(R.string.title_video));

        multiPbView.setViewPageAdapter(adapter);
        multiPbView.setViewPageCurrentItem(AppInfo.currentViewpagerPosition);
    }

    public void updateViewpagerStatus(int arg0) {
        AppLog.d(TAG, "updateViewpagerStatus arg0=" + arg0);
        AppInfo.currentViewpagerPosition = arg0;
    }

    public void changePreviewType(PhotoWallLayoutType layoutType) {
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            curLayoutType = layoutType;
            if (fragments != null) {
                for (BaseMultiPbFragment fragment : fragments) {
                    fragment.changePreviewType(layoutType);
                }
            }
            AppLog.d(TAG, " changePreviewType AppInfo.photoWallPreviewType");
        } else {
            MyToast.show(activity, "编辑中，无法切换");
        }
    }

    public void reBack() {
        AppLog.i(TAG, "reback curOperationMode:" + curOperationMode);
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            MyProgressDialog.showProgressDialog(activity, R.string.wait);
            MyCamera camera = CameraManager.getInstance().getCurCamera();
            if (camera != null) {
                camera.setLoadThumbnail(false);
            }
            ImageLoaderConfig.stopLoad();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    MyProgressDialog.closeProgressDialog();
                    activity.finish();
                }
            }, 2000);
        } else if (curOperationMode == OperationMode.MODE_EDIT) {
            curOperationMode = OperationMode.MODE_BROWSE;
            int index = multiPbView.getViewPageIndex();
            BaseMultiPbFragment fragment = fragments.get(index);
            if (fragment != null) {
                fragment.quitEditMode();
            }
        }
    }

    public void reBack5Second() {
        AppLog.i(TAG, "reback curOperationMode:" + curOperationMode);
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            MyProgressDialog.showProgressDialog(activity, R.string.wait);
            MyCamera camera = CameraManager.getInstance().getCurCamera();
            if (camera != null) {
                camera.setLoadThumbnail(false);
            }
            ImageLoaderConfig.stopLoad();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    MyProgressDialog.closeProgressDialog();
                    activity.finish();
                }
            }, 5000);
        } else if (curOperationMode == OperationMode.MODE_EDIT) {
            curOperationMode = OperationMode.MODE_BROWSE;
            int index = multiPbView.getViewPageIndex();
            BaseMultiPbFragment fragment = fragments.get(index);
            if (fragment != null) {
                fragment.quitEditMode();
            }
        }
    }

    public void reBackNoActivity() {
        AppLog.i(TAG, "reback curOperationMode:" + curOperationMode);
        if (curOperationMode == OperationMode.MODE_BROWSE) {
            MyProgressDialog.showProgressDialog(activity, R.string.wait);
            MyCamera camera = CameraManager.getInstance().getCurCamera();
            if (camera != null) {
                camera.setLoadThumbnail(false);
            }
            ImageLoaderConfig.stopLoad();
//            handler.postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    MyProgressDialog.closeProgressDialog();
//                }
//            }, 1500);
        } else if (curOperationMode == OperationMode.MODE_EDIT) {
            curOperationMode = OperationMode.MODE_BROWSE;
            int index = multiPbView.getViewPageIndex();
            BaseMultiPbFragment fragment = fragments.get(index);
            if (fragment != null) {
                fragment.quitEditMode();
            }
        }
    }

    public void selectOrCancel() {
        if (curSelectAll) {
            multiPbView.setSelectBtnIcon(R.drawable.ic_select_all_white_24dp);
            curSelectAll = false;
        } else {
            multiPbView.setSelectBtnIcon(R.drawable.ic_unselected_white_24dp);
            curSelectAll = true;
        }
        if (fragments != null && fragments.size() > 0) {
            int index = multiPbView.getViewPageIndex();
            BaseMultiPbFragment fragment = fragments.get(index);
            if (fragment != null) {
                fragment.selectOrCancelAll(curSelectAll);
            }
        }
    }

    public void delete() {
        AppLog.d(TAG, "delete AppInfo.currentViewpagerPosition=" + AppInfo.currentViewpagerPosition);
        if (fragments != null && fragments.size() > 0) {
            BaseMultiPbFragment fragment = fragments.get(multiPbView.getViewPageIndex());
            if (fragment != null) {
                fragment.deleteFile();
            }
        }
    }

    public void download() {
        List<MultiPbItemInfo> list = null;
        LinkedList<ICatchFile> linkedList = new LinkedList<>();
        long fileSizeTotal = 0;
        AppLog.d(TAG, "delete currentViewpagerPosition=" + AppInfo.currentViewpagerPosition);
        if (fragments != null && fragments.size() > 0) {
            BaseMultiPbFragment fragment = fragments.get(multiPbView.getViewPageIndex());
            if (fragment != null) {
                list = fragment.getSelectedList();
            }
        }
        if (list == null || list.size() <= 0) {
            AppLog.d(TAG, "asytaskList size=" + list.size());
            MyToast.show(activity, R.string.gallery_no_file_selected);
        } else {
            for (MultiPbItemInfo temp : list) {
                if (temp.hrdFilePath != null && temp.hrdFilePath.size() > 0) {
                    for (CheckSingleCameraPhotoBean bean : temp.hrdFilePath) {
                        linkedList.add(bean.iCatchFile);
                        fileSizeTotal += bean.iCatchFile.getFileSize();
                    }
                } else {
                    linkedList.add(temp.iCatchFile);
                    fileSizeTotal += temp.getFileSizeInteger();
                }
            }
            if (SystemInfo.getSDFreeSize(activity) < fileSizeTotal) {
                MyToast.show(activity, R.string.text_sd_card_memory_shortage);
            } else {
                quitEditMode();
                PbDownloadManager downloadManager = new PbDownloadManager(activity, linkedList,activity);
                downloadManager.show();
            }
        }
    }

    public void downZdInfo(RemoteMultiPbPresenter remoteMultiPbPresenter) {
        if (downloadManager == null) {
            downloadManager = new PbDownloadManager(activity);
        }
        downloadManager.downloadZd(remoteMultiPbPresenter);
    }

    private void quitEditMode() {
        curOperationMode = OperationMode.MODE_BROWSE;
        if (fragments != null && fragments.size() > 0) {
            BaseMultiPbFragment fragment = fragments.get(multiPbView.getViewPageIndex());
            if (fragment != null) {
                fragment.quitEditMode();
            }
        }
    }

    private void reloadFileList() {
        RemoteFileHelper.getInstance().clearAllFileList();
        if (fragments != null && fragments.size() > 0) {
            BaseMultiPbFragment fragment = fragments.get(multiPbView.getViewPageIndex());
            if (fragment != null) {
                fragment.loadPhotoWall();
            }
        }
    }

    public void setFileFilter(FileFilter fileFilter) {
        RemoteFileHelper.getInstance().setFileFilter(fileFilter);
        reloadFileList();
    }

    public void setSdCardEventListener() {
        GlobalInfo.getInstance().setOnEventListener(new GlobalInfo.OnEventListener() {
            @Override
            public void eventListener(int sdkEventId) {
                switch (sdkEventId) {
                    case SDKEvent.EVENT_SDCARD_REMOVED:
                        MyToast.show(activity, R.string.dialog_card_removed);
                        reloadFileList();
                        break;
                    case SDKEvent.EVENT_SDCARD_INSERT:
                        MyToast.show(activity, R.string.dialog_card_inserted);
                        reloadFileList();
                        break;
                }
            }
        });
    }
}

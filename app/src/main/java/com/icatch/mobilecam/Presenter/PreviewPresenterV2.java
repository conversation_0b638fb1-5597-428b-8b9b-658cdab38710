package com.icatch.mobilecam.Presenter;

import static com.icatch.mobilecam.Application.PanoramaApp.getInstance;
import static com.icatch.mobilecam.data.Mode.PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.media.MediaPlayer;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Toast;

import com.blankj.utilcode.util.FileIOUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.detu.android_panoplayer.PanoPlayerImpl;
import com.detu.android_panoplayer.PanoPlayerUrl;
import com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView;
import com.detu.libszstitch.SzStitch;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.DataConvert.StreamInfoConvert;
import com.icatch.mobilecam.Function.BaseProrertys;
import com.icatch.mobilecam.Function.CameraAction.PbDownloadManager;
import com.icatch.mobilecam.Function.CameraAction.PhotoCapture;
import com.icatch.mobilecam.Function.CameraAction.ZoomInOut;
import com.icatch.mobilecam.Function.SDKEvent;
import com.icatch.mobilecam.Function.Setting.OptionSetting;
import com.icatch.mobilecam.Function.Setting.UIDisplaySource;
import com.icatch.mobilecam.Function.ThumbnailGetting.ThumbnailOperation;
import com.icatch.mobilecam.Function.streaming.CameraStreaming;
import com.icatch.mobilecam.Listener.OnSettingCompleteListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.CameraType;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.Interface.BasePresenter;
import com.icatch.mobilecam.SdkApi.CameraAction;
import com.icatch.mobilecam.SdkApi.CameraProperties;
import com.icatch.mobilecam.SdkApi.CameraState;
import com.icatch.mobilecam.SdkApi.FileOperation;
import com.icatch.mobilecam.SdkApi.PanoramaPreviewPlayback;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.CustomException.NullPointerException;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Message.AppMessage;
import com.icatch.mobilecam.data.Mode.PreviewMode;
import com.icatch.mobilecam.data.Mode.TouchMode;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.data.entity.CameraSlot;
import com.icatch.mobilecam.data.entity.SettingMenu;
import com.icatch.mobilecam.data.entity.StreamInfo;
import com.icatch.mobilecam.data.type.A6RotateMotorState;
import com.icatch.mobilecam.data.type.A6RotateShotTimes;
import com.icatch.mobilecam.data.type.SlowMotion;
import com.icatch.mobilecam.data.type.TimeLapseInterval;
import com.icatch.mobilecam.data.type.TimeLapseMode;
import com.icatch.mobilecam.data.type.Tristate;
import com.icatch.mobilecam.data.type.Upside;
import com.icatch.mobilecam.db.CameraSlotSQLite;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Interface.PreviewView;
import com.icatch.mobilecam.ui.adapter.SettingListAdapter;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.utils.BitmapTools;
import com.icatch.mobilecam.utils.ConvertTools;
import com.icatch.mobilecam.utils.PanoramaTools;
import com.icatch.mobilecam.utils.QRCode;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.fileutils.FileOper;
import com.icatch.mobilecam.utils.fileutils.FileTools;
import com.icatchtek.control.customer.type.ICatchCamDateStamp;
import com.icatchtek.control.customer.type.ICatchCamEventID;
import com.icatchtek.control.customer.type.ICatchCamMode;
import com.icatchtek.control.customer.type.ICatchCamPreviewMode;
import com.icatchtek.control.customer.type.ICatchCamProperty;
import com.icatchtek.pancam.customer.ICatchPancamConfig;
import com.icatchtek.pancam.customer.exception.IchGLSurfaceNotSetException;
import com.icatchtek.pancam.customer.surface.ICatchSurfaceContext;
import com.icatchtek.pancam.customer.type.ICatchGLPanoramaType;
import com.icatchtek.pancam.customer.type.ICatchGLPoint;
import com.icatchtek.pancam.customer.type.ICatchGLSurfaceType;
import com.icatchtek.reliant.customer.type.ICatchFile;
import com.icatchtek.reliant.customer.type.ICatchH264StreamParam;
import com.icatchtek.reliant.customer.type.ICatchJPEGStreamParam;
import com.icatchtek.reliant.customer.type.ICatchStreamParam;
import com.ijoyer.mobilecam.R;
import com.player.panoplayer.IPanoPlayerListener;
import com.player.panoplayer.enitity.PanoOptionKey;
import com.player.panoplayer.enitity.PanoPlayerOption;
import com.player.panoplayer.enitity.PanoPlayerOptionType;
import com.player.panoplayer.enitity.PanoPlayerStatus;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class PreviewPresenterV2 extends BasePresenter implements SensorEventListener {
    private static final String TAG = "PanoramaPreviewPresenter";
    private final static float MIN_ZOOM = 0.4f;
    private final static float MAX_ZOOM = 2.2f;
    private final static float FIXED_OUTSIDE_DISTANCE = 3.0f;
    private final static float FIXED_INSIDE_DISTANCE = 0.5f;
    public PanoramaPreviewPlayback panoramaPreviewPlayback;
    private TouchMode touchMode = TouchMode.NONE;
    private float mPreviousY;
    private float mPreviousX;
    private float beforeLenght;
    private float afterLenght;
    private float currentZoomRate = MAX_ZOOM;
    private SensorManager sensorManager;
    private Sensor gyroscopeSensor;
    private MediaPlayer videoCaptureStartBeep;
    private MediaPlayer modeSwitchBeep;
    private MediaPlayer stillCaptureStartBeep;
    private MediaPlayer continuousCaptureBeep;
    private Activity activity;
    private PreviewView previewView;
    private CameraProperties cameraProperties;
    private CameraAction cameraAction;
    private CameraState cameraState;
    private FileOperation fileOperation;
    private BaseProrertys baseProrertys;
    private MyCamera curCamera;
    private PreviewHandler previewHandler;
    private SDKEvent sdkEvent;
    private int curAppStateMode = PreviewMode.APP_STATE_NONE_MODE;
    private Timer videoCaptureButtomChangeTimer;
    public boolean videoCaptureButtomChangeFlag = true;
    private Timer recordingLapseTimeTimer;
    private int lapseTime = 0;
    private List<SettingMenu> settingMenuList;
    private SettingListAdapter settingListAdapter;
    private boolean allowClickButtoms = true;
    private int currentSettingMenuMode;
    private WifiSSReceiver wifiSSReceiver;
    private long lastCilckTime = 0;
    private long lastRecodeTime;
    private int curICatchMode;
    private ICatchSurfaceContext iCatchSurfaceContext;
    private boolean hasInitSurface = false;
    private ZoomInOut zoomInOut;
    private int curVideoWidth = 1920;
    private int curVideoHeight = 960;
    private int curVideoFps = 30;
    private String curCodecType = "H264";
    public CameraStreaming cameraStreaming;
    private int curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE;
    boolean isDelEvent = false;
    public static boolean isA6Camera = false;
    private PanoPlayerImpl mPanoPlayer;
    private PanoPlayerSurfaceView mSurfaceHolder;
    float[] curGestureData = {};

    public PreviewPresenterV2(Activity activity) {
        super(activity);
        this.activity = activity;
    }

    public void setView(PreviewView previewView) {
        this.previewView = previewView;
        initCfg();
        initData();
    }

    public void initData() {
        curCamera = CameraManager.getInstance().getCurCamera();
        panoramaPreviewPlayback = curCamera.getPanoramaPreviewPlayback();
        cameraStreaming = new CameraStreaming(panoramaPreviewPlayback);
        cameraProperties = curCamera.getCameraProperties();
        cameraAction = curCamera.getCameraAction();
        cameraState = curCamera.getCameraState();
        fileOperation = curCamera.getFileOperation();
        baseProrertys = curCamera.getBaseProrertys();
        zoomInOut = new ZoomInOut();
        videoCaptureStartBeep = MediaPlayer.create(activity, R.raw.camera_timer);
        stillCaptureStartBeep = MediaPlayer.create(activity, R.raw.captureshutter);
        continuousCaptureBeep = MediaPlayer.create(activity, R.raw.captureburst);
        modeSwitchBeep = MediaPlayer.create(activity, R.raw.focusbeep);
        previewHandler = new PreviewHandler();
        sdkEvent = new SDKEvent(previewHandler);
        if (cameraProperties.hasFunction(PropertyId.CAPTURE_DELAY_MODE)) {
            int delay = cameraProperties.getCurrentPropertyValue(PropertyId.CAPTURE_DELAY_MODE);
            AppLog.i(TAG, "PropertyId.CAPTURE_DELAY_MODE, value = " + delay);
            cameraProperties.setCaptureDelayMode(1);
            AppLog.i(TAG, "PropertyId.CAPTURE_DELAY_MODE, value = " + delay);
        }
        if (curCamera.getCameraType() == CameraType.USB_CAMERA) {
            Intent intent = activity.getIntent();
            curVideoWidth = intent.getIntExtra("videoWidth", 1920);
            curVideoHeight = intent.getIntExtra("videoHeight", 960);
            curVideoFps = intent.getIntExtra("videoFps", 30);
            curCodecType = intent.getStringExtra("videoCodec");
            if (curCodecType == null) {
                curCodecType = "H264";
            }
            AppLog.d(TAG, "initData videoWidth=" + curVideoWidth + " videoHeight=" + curVideoHeight + " videoFps=" + curVideoFps + " curCodecType=" +
                    curCodecType);
        }
        AppLog.i(TAG, "cameraProperties.getMaxZoomRatio() =" + cameraProperties.getMaxZoomRatio());
        isA6Camera = cameraProperties.hasFunction(PropertyId.A6_ROTATE_MOTOR_STATE) && cameraProperties.hasFunction(PropertyId.A6_ROTATE_SHOT_TIMES);

    }

    public void initStatus() {
        int batteryLevel = cameraProperties.getBatteryElectric();
        int resId = ThumbnailOperation.getBatteryLevelIcon(batteryLevel);
        if (resId > 0) {
            previewView.setBatteryIcon(resId);
            if (batteryLevel < 20) {
            }
        }
        Boolean isSDCardExist = cameraProperties.isSDCardExist();
        if (isSDCardExist == null) {
            AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
        } else if (Boolean.FALSE.equals(isSDCardExist)){
            AppDialog.showDialogWarn(activity, R.string.dialog_card_lose);
        }
        IntentFilter wifiSSFilter = new IntentFilter(WifiManager.RSSI_CHANGED_ACTION);
        wifiSSReceiver = new WifiSSReceiver();
        activity.registerReceiver(wifiSSReceiver, wifiSSFilter);
    }

    public void changeCameraMode(final int previewMode, final int ichVideoPreviewMode) {
        AppLog.i(TAG, "start changeCameraMode ichVideoPreviewMode=" + ichVideoPreviewMode);
        AppLog.i(TAG, "start changeCameraMode previewMode=" + previewMode + "  hasInitSurface=" + hasInitSurface);
        curICatchMode = ichVideoPreviewMode;
        MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
        new Thread(new Runnable() {
            @Override
            public void run() {
                cameraAction.changePreviewMode(ichVideoPreviewMode);
//                startPreview();
                initSurface();
                previewHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        curAppStateMode = previewMode;
                        createUIByMode(curAppStateMode);
                        previewView.dismissPopupWindow();
//                        MyProgressDialog.closeProgressDialog();
                    }
                });
            }
        }).start();

    }

    public void redrawSurface() {
        if (curCamera.isStreamReady && !AppInfo.enableRender) {
            int width = previewView.getSurfaceViewWidth();
            int height = previewView.getSurfaceViewHeight();
            AppLog.i(TAG, "SurfaceViewWidth=" + width + " SurfaceViewHeight=" + height);
            if (width > 0 || height > 0) {
                cameraStreaming.setViewParam(width, height);
                cameraStreaming.setSurfaceViewArea();
            }
        }
    }

    public void startOrStopCapture() {
        final int duration = videoCaptureStartBeep.getDuration();
        if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null){
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                return;
            }
            if (Boolean.FALSE.equals(isSDCardExist)) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                return;
            }
            int recordRemainTime = cameraProperties.getRecordingRemainTime();
            if (recordRemainTime <= 0) {
                LogUtils.file(this.getClass().getSimpleName() + "startOrStopCapture-(curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) getRecordingRemainTime()=" + recordRemainTime);
                String hint;
                if (recordRemainTime < 0) {
                    hint = getInstance().getString(R.string.dialog_connect_lose_exit_page);
                }else{
                    hint = getInstance().getString(R.string.dialog_sd_card_is_full);
                    hint += "(" + recordRemainTime + ")";
                }
                AppDialog.showDialogWarn(activity, hint);
                return;
            }
//            if (cameraProperties.getRecordingRemainTime() <= 0) {
//                AppDialog.showDialogWarn(activity, R.string.dialog_sd_card_is_full);
//                return;
//            }
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    videoCaptureStartBeep.start();
                    AppLog.d(TAG, "duration:" + duration);
                    try {
                        Thread.sleep(duration);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    lastRecodeTime = System.currentTimeMillis();
                    final boolean ret = cameraAction.startMovieRecord();
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            if (ret) {
                                AppLog.i(TAG, "startRecordingLapseTimeTimer(0)");
                                curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
                                startVideoCaptureButtomChangeTimer();
                                startRecordingLapseTimeTimer(0);
                            } else {
                                MyToast.show(activity, R.string.text_operation_failed);
                            }
                        }
                    });
                }
            }).start();
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE) {
            if (System.currentTimeMillis() - lastRecodeTime < 2000) {
                return;
            }
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    final boolean ret = cameraAction.stopVideoCapture();
                    videoCaptureStartBeep.start();
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            if (ret) {
                                curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                                stopVideoCaptureButtomChangeTimer();
                                stopRecordingLapseTimeTimer();
                                previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));
                            } else {
                                MyToast.show(activity, R.string.text_operation_failed);
                            }
                        }
                    });
                }
            }).start();
        } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.hideZoomView();
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null){
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                return;
            }
            if (Boolean.FALSE.equals(isSDCardExist)) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                return;
            }
            if (!checkRemainImageNum("startOrStopCapture-(curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW)")) {
//                AppDialog.showDialogWarn(activity, R.string.dialog_sd_card_is_full);
                return;
            }
            curAppStateMode = PreviewMode.APP_STATE_STILL_CAPTURE;
            startPhotoCapture();
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW) {
            //缩时摄影
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null){
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                return;
            }
            if (Boolean.FALSE.equals(isSDCardExist)) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                return;
            }
            if (!checkRemainImageNum("startOrStopCapture-(curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW)")) {
//                AppDialog.showDialogWarn(activity, R.string.dialog_sd_card_is_full);
                return;
            }
            if (cameraProperties.getCurrentTimeLapseInterval() == TimeLapseInterval.TIME_LAPSE_INTERVAL_OFF) {
                AppDialog.showDialogWarn(activity, R.string.timeLapse_not_allow);
                return;
            }
            continuousCaptureBeep.start();
            if (!cameraAction.startTimeLapse()) {
                AppLog.e(TAG, "failed to start startTimeLapse");
                return;
            }
            previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn_off);
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE;
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            AppLog.d(TAG, "curMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE");
            if (cameraAction.stopTimeLapse() == false) {
                AppLog.e(TAG, "failed to stopTimeLapse");
                return;
            }
            stopRecordingLapseTimeTimer();
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW;
        } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            AppLog.d(TAG, "curMode == PreviewMode.APP_STATE_TIMELAPSE_PREVIEW_VIDEO");
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (isSDCardExist == null){
                AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
                return;
            }
            if (Boolean.FALSE.equals(isSDCardExist)) {
                AppDialog.showDialogWarn(activity, R.string.dialog_card_not_exist);
                return;
            }
            if (!checkRemainImageNum("startOrStopCapture-(curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW)")) {
//                AppDialog.showDialogWarn(activity, R.string.dialog_sd_card_is_full);
                return;
            }
            if (cameraProperties.getCurrentTimeLapseInterval() == TimeLapseInterval.TIME_LAPSE_INTERVAL_OFF) {
                AppLog.d(TAG, "time lapse is not allowed because of timelapse interval is OFF");
                AppDialog.showDialogWarn(activity, R.string.timeLapse_not_allow);
                return;
            }
            videoCaptureStartBeep.start();
            if (cameraAction.startTimeLapse() == false) {
                AppLog.e(TAG, "failed to start startTimeLapse");
                return;
            }
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(0);
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
            AppLog.d(TAG, "curMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE");
            videoCaptureStartBeep.start();
            if (cameraAction.stopTimeLapse() == false) {
                AppLog.e(TAG, "failed to stopTimeLapse");
                return;
            }
            stopVideoCaptureButtomChangeTimer();
            stopRecordingLapseTimeTimer();
            curAppStateMode = APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
        }
        AppLog.d(TAG, "end processing for responsing captureBtn clicking");
    }

    private boolean checkRemainImageNum(String errLog){
        int recordRemainTime = cameraProperties.getRemainImageNum();
        if (recordRemainTime < 1) {
            LogUtils.file(this.getClass().getSimpleName() + errLog + " checkRemainImageNum()=" + recordRemainTime);
            String hint;
            if (recordRemainTime < 0) {
                hint = getInstance().getString(R.string.dialog_connect_lose_exit_page);
            }else{
                hint = getInstance().getString(R.string.dialog_sd_card_is_full);
                hint += "(" + recordRemainTime + ")";
            }
            AppDialog.showDialogWarn(activity, hint);
            return false;
        }else{
            return true;
        }
    }

    public void createUIByMode(int appStateMode) {
        AppLog.i(TAG, "start createUIByMode previewMode=" + appStateMode);
        if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO)) {
            if (appStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW || appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE) {
                previewView.setPvModeBtnBackgroundResource(R.drawable.video_toggle_btn_on);
            }
        }
        if (appStateMode == PreviewMode.APP_STATE_STILL_PREVIEW || appStateMode == PreviewMode.APP_STATE_STILL_CAPTURE) {
            previewView.setPvModeBtnBackgroundResource(R.drawable.capture_toggle_btn_on);
        }
        if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE)) {
            if (appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE ||
                    appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW ||
                    appStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE ||
                    appStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                previewView.setPvModeBtnBackgroundResource(R.drawable.timelapse_toggle_btn_on);
            }
        }
        if (isA6Camera) {
            previewView.setPvModeBtnVisibility(View.GONE);
        }
        if (appStateMode == PreviewMode.APP_STATE_STILL_CAPTURE ||
                appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE ||
                appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW ||
                appStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
        } else if (appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE ||
                appStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE ||
                appStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW ||
                appStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
        }
        if (baseProrertys.getCaptureDelay().needDisplayByMode(appStateMode) && !isA6Camera) {
            previewView.setDelayCaptureLayoutVisibility(View.VISIBLE);
            previewView.setDelayCaptureTextTime(baseProrertys.getCaptureDelay().getCurrentUiStringInPreview());
        } else {
            previewView.setDelayCaptureLayoutVisibility(View.GONE);
        }
        if (baseProrertys.getImageSize().needDisplayByMode(appStateMode)) {
            previewView.setImageSizeLayoutVisibility(View.VISIBLE);
            previewView.setImageSizeInfo(baseProrertys.getImageSize().getCurrentUiStringInPreview());
            previewView.setRemainCaptureCount(new Integer(cameraProperties.getRemainImageNum()).toString());
        } else {
            previewView.setImageSizeLayoutVisibility(View.GONE);
        }
        if (baseProrertys.getVideoSize().needDisplayByMode(appStateMode)) {
            previewView.setVideoSizeLayoutVisibility(View.VISIBLE);
            previewView.setVideoSizeInfo(baseProrertys.getVideoSize().getCurrentUiStringInPreview());
            previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));
        } else {
            previewView.setVideoSizeLayoutVisibility(View.GONE);
        }
        if (baseProrertys.getBurst().needDisplayByMode(appStateMode)) {
            previewView.setBurstStatusVisibility(View.VISIBLE);
            try {
                previewView.setBurstStatusIcon(baseProrertys.getBurst().getCurrentIcon());
            } catch (NullPointerException e) {
                e.printStackTrace();
            }
        } else {
            previewView.setBurstStatusVisibility(View.GONE);
        }
        if (baseProrertys.getWhiteBalance().needDisplayByMode(appStateMode)) {
            previewView.setWbStatusVisibility(View.VISIBLE);
            try {
                previewView.setWbStatusIcon(baseProrertys.getWhiteBalance().getCurrentIcon());
            } catch (NullPointerException e) {
                e.printStackTrace();
            }
        } else {
            previewView.setWbStatusVisibility(View.GONE);
        }
        if (baseProrertys.getUpside().needDisplayByMode(appStateMode) && cameraProperties.getCurrentUpsideDown() == Upside.UPSIDE_ON) {
            previewView.setUpsideVisibility(View.VISIBLE);
        } else {
            previewView.setUpsideVisibility(View.GONE);
        }
        if (baseProrertys.getSlowMotion().needDisplayByMode(appStateMode) && cameraProperties.getCurrentSlowMotion() == SlowMotion.SLOW_MOTION_ON) {
            previewView.setSlowMotionVisibility(View.VISIBLE);
        } else {
            previewView.setSlowMotionVisibility(View.GONE);
        }
        if (baseProrertys.getTimeLapseMode().needDisplayByMode(appStateMode)) {
            previewView.setTimeLapseModeVisibility(View.VISIBLE);
            try {
                previewView.setTimeLapseModeIcon(baseProrertys.getTimeLapseMode().getCurrentIcon());
            } catch (NullPointerException e) {
                e.printStackTrace();
            }
        } else {
            previewView.setTimeLapseModeVisibility(View.GONE);
        }
    }

    public void initPreview() {
        AppLog.i(TAG, "initPreview curMode=" + curAppStateMode);
        GlobalInfo.getInstance().setOnEventListener(new GlobalInfo.OnEventListener() {
            @Override
            public void eventListener(int sdkEventId) {
                switch (sdkEventId) {
                    case SDKEvent.EVENT_SDCARD_REMOVED:
                        MyToast.show(activity, R.string.dialog_card_removed);
                        if (baseProrertys.getImageSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainCaptureCount("0");
                        } else if (baseProrertys.getVideoSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(0));
                        }
                        break;
                    case SDKEvent.EVENT_SDCARD_INSERT:
                        MyToast.show(activity, R.string.dialog_card_inserted);
                        if (baseProrertys.getImageSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainCaptureCount(String.valueOf(cameraProperties.getRemainImageNum()));
                        } else if (baseProrertys.getVideoSize().needDisplayByMode(curAppStateMode)) {
                            previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));
                        }
                        break;
                }
            }
        });
        previewView.setMinZoomRate(1.0f);
        previewView.setMaxZoomRate(cameraProperties.getMaxZoomRatio() * 1.0f);
        previewView.updateZoomViewProgress(cameraProperties.getCurrentZoomRatio());
        int iCatchMode = cameraAction.getCurrentCameraMode();
        if (cameraState.isMovieRecording()) {
            AppLog.i(TAG, "camera is recording...");
            curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(cameraProperties.getVideoRecordingTime());
        } else if (cameraState.isTimeLapseVideoOn()) {
            AppLog.i(TAG, "camera is TimeLapseVideoOn...");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(cameraProperties.getVideoRecordingTime());
        } else if (cameraState.isTimeLapseStillOn()) {
            AppLog.i(TAG, "camera is TimeLapseStillOn...");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE;
            startVideoCaptureButtomChangeTimer();
            startRecordingLapseTimeTimer(cameraProperties.getVideoRecordingTime());
        } else if (curAppStateMode == PreviewMode.APP_STATE_NONE_MODE) {
            if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_CAMERA)) {
                curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE;
            } else if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO)) {
                curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
            } else if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE)) {
                curAppStateMode = APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
            } else {
                curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
            }
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == PreviewMode.APP_STATE_VIDEO_PREVIEW");
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
        } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == PreviewMode.APP_STATE_TIMELAPSE_PREVIEW_VIDEO");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == PreviewMode.APP_STATE_TIMELAPSE_PREVIEW_STILL");
            curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE;
        } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            AppLog.i(TAG, "initPreview curMode == ICH_STILL_PREVIEW_MODE");
            changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE);
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE;
        } else {
            curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
            curICatchMode = ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE;
        }
        cameraAction.changePreviewMode(curICatchMode);
        createUIByMode(curAppStateMode);
    }

    public void startVideoCaptureButtomChangeTimer() {
        AppLog.d(TAG, "startVideoCaptureButtomChangeTimer videoCaptureButtomChangeTimer=" + videoCaptureButtomChangeTimer);
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                if (videoCaptureButtomChangeFlag) {
                    videoCaptureButtomChangeFlag = false;
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                                previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
                            }
                        }
                    });
                } else {
                    videoCaptureButtomChangeFlag = true;
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                                previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_off);
                            }
                        }
                    });
                }
            }
        };
        videoCaptureButtomChangeTimer = new Timer(true);
        videoCaptureButtomChangeTimer.schedule(task, 0, 1000);
    }

    public void stopVideoCaptureButtomChangeTimer() {
        AppLog.d(TAG, "stopVideoCaptureButtomChangeTimer videoCaptureButtomChangeTimer=" + videoCaptureButtomChangeTimer);
        if (videoCaptureButtomChangeTimer != null) {
            videoCaptureButtomChangeTimer.cancel();
        }
        previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
    }

    private void startRecordingLapseTimeTimer(int startTime) {
        if (cameraProperties.hasFunction(PropertyId.VIDEO_RECORDING_TIME) == false) {
            return;
        }
        AppLog.i(TAG, "startRecordingLapseTimeTimer curMode=" + curAppStateMode);
        if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE || curAppStateMode == PreviewMode
                .APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            AppLog.i(TAG, "startRecordingLapseTimeTimer");
            if (recordingLapseTimeTimer != null) {
                recordingLapseTimeTimer.cancel();
            }
            lapseTime = startTime;
            recordingLapseTimeTimer = new Timer(true);
            previewView.setRecordingTimeVisibility(View.VISIBLE);
            TimerTask timerTask = new TimerTask() {
                @Override
                public void run() {
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            previewView.setRecordingTime(ConvertTools.secondsToHours(lapseTime++));
                        }
                    });
                }
            };
            recordingLapseTimeTimer.schedule(timerTask, 0, 1000);
        }
    }

    private void stopRecordingLapseTimeTimer() {
        if (recordingLapseTimeTimer != null) {
            recordingLapseTimeTimer.cancel();
        }
        previewView.setRecordingTime("00:00:00");
        previewView.setRecordingTimeVisibility(View.GONE);
    }

    public void changePreviewMode(int previewMode) {
        AppLog.d(TAG, "changePreviewMode previewMode=" + previewMode);
        AppLog.d(TAG, "changePreviewMode curAppStateMode=" + curAppStateMode);
        long timeInterval = System.currentTimeMillis() - lastCilckTime;
        AppLog.d(TAG, "repeat click: timeInterval=" + timeInterval);
        if (System.currentTimeMillis() - lastCilckTime < 2000) {
            AppLog.d(TAG, "repeat click: timeInterval < 2000");
            return;
        } else {
            lastCilckTime = System.currentTimeMillis();
        }
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        modeSwitchBeep.start();
        if (previewMode == PreviewMode.APP_STATE_VIDEO_MODE) {
            if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW ||
                    curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW ||
                    curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                stopPreview();
                changeCameraMode(PreviewMode.APP_STATE_VIDEO_PREVIEW, ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE);
            }
        } else if (previewMode == PreviewMode.APP_STATE_STILL_MODE) {
            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW ||
                    curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW ||
                    curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                stopPreview();
                changeCameraMode(PreviewMode.APP_STATE_STILL_PREVIEW, ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE);
            }
        } else if (previewMode == PreviewMode.APP_STATE_TIMELAPSE_MODE) {
            if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW || curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
                stopPreview();
                if (curCamera.timeLapsePreviewMode == TimeLapseMode.TIME_LAPSE_MODE_VIDEO) {
                    changeCameraMode(PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE);
                } else {
                    changeCameraMode(PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE);
                }
            }
        }
    }

    private long photoCaptureStartTime, photoCaptureEndTime;
    private static boolean isWaitA6Rotate = true;
    private static int countTimes = 0;

    private void startPhotoCapture() {
        previewView.setCaptureBtnEnAbility(false);
        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn_off);
        PhotoCapture photoCapture = new PhotoCapture();
        if (isA6Camera) {
            MyProgressDialog.showProgressDialog(activity, PanoramaApp.getContext().getString(R.string.processing));
            countTimes = 0;
            isWaitA6Rotate = true;
            photoCapture.addOnStopPreviewListener(() -> stopPreview());
            new Thread(() -> {
                cameraProperties.setPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, A6RotateMotorState.ROTATE_START);
                int motorState;
                for (; ; ) {
                    motorState = cameraProperties.getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                    if (motorState == A6RotateMotorState.ROTATE_END) {
                        photoCaptureStartTime = System.currentTimeMillis();
                        photoCapture.startCapture();
                        break;
                    }
                }
            }).start();
            return;
        }
        if (cameraProperties.hasFunction(PropertyId.CAPTURE_DELAY_MODE)) {
            photoCapture.addOnStopPreviewListener(new PhotoCapture.OnStopPreviewListener() {
                @Override
                public void onStop() {
                    if (!cameraProperties.hasFunction(0xd704)) {
                        stopPreview();
                    }
                }
            });
            photoCapture.setOnCaptureCompletedListener(new PhotoCapture.OnCaptureCompletedListener() {
                @Override
                public void onCompleted() {
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            previewView.setCaptureBtnEnAbility(true);
                        }
                    });
                }
            });
            photoCapture.startCapture();
        } else {
            stillCaptureStartBeep.start();
            if (!cameraProperties.hasFunction(0xd704)) {
                stopPreview();
            }
            MyProgressDialog.showProgressDialog(activity, R.string.dialog_capturing);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    final boolean ret = cameraAction.capturePhoto();
                    previewHandler.post(new Runnable() {
                        @Override
                        public void run() {
                            if (!ret) {
                                MyToast.show(activity, R.string.text_operation_failed);
                                curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                            }
                            previewView.setCaptureBtnEnAbility(true);
                            MyProgressDialog.closeProgressDialog();
                        }
                    });
                }
            }).start();
        }
    }

    public synchronized boolean disconnectCamera() {
        if (curCamera != null) {
            GlobalInfo.getInstance().delEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_REMOVED);
            GlobalInfo.getInstance().delEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_IN);
            GlobalInfo.getInstance().delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
            GlobalInfo.getInstance().delete();
            return curCamera.disconnect();
        } else {
            return false;
        }
    }

    public void delConnectFailureListener() {
    }

    public void unregisterWifiSSReceiver() {
        if (wifiSSReceiver != null) {
            activity.unregisterReceiver(wifiSSReceiver);
            wifiSSReceiver = null;
        }
    }

    public void zoomIn() {
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return;
        }
        zoomInOut.zoomIn();
        previewView.updateZoomViewProgress(cameraProperties.getCurrentZoomRatio());
    }

    public void zoomOut() {
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return;
        }
        zoomInOut.zoomOut();
        previewView.updateZoomViewProgress(cameraProperties.getCurrentZoomRatio());
    }

    public void zoomBySeekBar() {
        zoomInOut.addZoomCompletedListener(new ZoomInOut.ZoomCompletedListener() {
            @Override
            public void onCompleted(final float currentZoomRate) {
                previewHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                        AppLog.i(TAG, "addZoomCompletedListener currentZoomRate =" + currentZoomRate);
                        previewView.updateZoomViewProgress(currentZoomRate);
                    }
                });
            }
        });
        zoomInOut.startZoomInOutThread(this);
        MyProgressDialog.showProgressDialog(activity, null);
    }

    public void showZoomView() {
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE || curAppStateMode == PreviewMode
                .APP_STATE_TIMELAPSE_VIDEO_CAPTURE || (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_DATE_STAMP) == true && ICatchCamDateStamp
                .ICH_CAM_DATE_STAMP_OFF != cameraProperties.getCurrentDateStamp())) {
            return;
        }
        previewView.showZoomView();
    }

    public float getMaxZoomRate() {
        return previewView.getZoomViewMaxZoomRate();
    }

    public float getZoomViewProgress() {
        AppLog.d(TAG, "getZoomViewProgress value=" + previewView.getZoomViewProgress());
        return previewView.getZoomViewProgress();
    }

    public void showSettingDialog(int position) {
        OptionSetting optionSetting = new OptionSetting(this);
        if (settingMenuList != null && settingMenuList.size() > 0) {
            optionSetting.addSettingCompleteListener(new OnSettingCompleteListener() {
                @Override
                public void onOptionSettingComplete() {
                    AppLog.d(TAG, "onOptionSettingComplete");
                    settingMenuList = UIDisplaySource.getInstance().getList(currentSettingMenuMode, curCamera);
                    settingListAdapter.notifyDataSetChanged();
                }

                @Override
                public void settingVideoSizeComplete() {
                    AppLog.d(TAG, "settingVideoSizeComplete curAppStateMode=" + curAppStateMode);
//                    这里刷新下相机的尺寸
                }

                @Override
                public void onResetDoCaptureBtn(int timeLapseMode) {
                    if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_STILL) {
                        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                    } else if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_VIDEO) {
                        previewView.setCaptureBtnBackgroundResource(R.drawable.video_recording_btn_on);
                    }
                }

                @Override
                public void settingTimeLapseModeComplete(int timeLapseMode) {
                    if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_STILL) {
                        if (cameraAction.changePreviewMode(ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE)) {
                            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE;
                            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW;
                            baseProrertys.getTimeLapseStillInterval().initTimeLapseInterval();
                        }
                    } else if (timeLapseMode == TimeLapseMode.TIME_LAPSE_MODE_VIDEO) {
                        if (cameraAction.changePreviewMode(ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE)) {
                            curICatchMode = ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE;
                            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                            baseProrertys.getTimeLapseVideoInterval().initTimeLapseInterval();
                        }
                    }
                }
            });
            optionSetting.showSettingDialog(settingMenuList.get(position).name, activity);
        }
    }

    public void showPvModePopupWindow() {
        AppLog.d(TAG, "showPvModePopupWindow curAppStateMode=" + curAppStateMode);
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        previewView.showPopupWindow(curAppStateMode);
        previewView.setCaptureRadioBtnVisibility(cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_CAMERA) ? View.VISIBLE : View.GONE);
        previewView.setVideoRadioBtnVisibility(cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO) ? View.VISIBLE : View.GONE);
        previewView.setTimeLapseRadioBtnVisibility(cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE) ? View.VISIBLE : View.GONE);
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.setCaptureRadioBtnChecked(true);
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            previewView.setVideoRadioBtnChecked(true);
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            previewView.setTimeLapseRadioChecked(true);
        }
    }

    private class PreviewHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            Tristate ret = Tristate.FALSE;
            switch (msg.what) {
                case SDKEvent.EVENT_BATTERY_ELETRIC_CHANGED:
                    AppLog.i(TAG, "receive EVENT_BATTERY_ELETRIC_CHANGED power =" + msg.arg1);
                    int batteryLevel = msg.arg1;
                    int resId = ThumbnailOperation.getBatteryLevelIcon(batteryLevel);
                    if (resId > 0) {
                        previewView.setBatteryIcon(resId);
                        if (batteryLevel < 20) {
                        }
                    }
                    break;
                case SDKEvent.EVENT_CONNECTION_FAILURE:
                    AppLog.i(TAG, "receive EVENT_CONNECTION_FAILURE");
                    stopPreview();
                    delEvent();
                    disconnectCamera();
                    break;
                case SDKEvent.EVENT_SD_CARD_FULL:
                    AppLog.i(TAG, "receive EVENT_SD_CARD_FULL");
                    AppDialog.showDialogWarn(activity, R.string.dialog_card_full);
                    break;
                case SDKEvent.EVENT_VIDEO_OFF://only receive if fw request to stopMPreview video recording
                    AppLog.i(TAG, "receive EVENT_VIDEO_OFF:curAppStateMode=" + curAppStateMode);
                    if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                        if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE) {
                            curAppStateMode = PreviewMode.APP_STATE_VIDEO_PREVIEW;
                        } else {
                            curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                        }
                        stopVideoCaptureButtomChangeTimer();
                        stopRecordingLapseTimeTimer();
                        previewView.setRemainRecordingTimeText(ConvertTools.secondsToMinuteOrHours(cameraProperties.getRecordingRemainTime()));
                    }
                    break;
                case SDKEvent.EVENT_VIDEO_ON:
                    AppLog.i(TAG, "receive EVENT_VIDEO_ON:curAppStateMode =" + curAppStateMode);
                    if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
                        curAppStateMode = PreviewMode.APP_STATE_VIDEO_CAPTURE;
                        startVideoCaptureButtomChangeTimer();
                        startRecordingLapseTimeTimer(0);
                    } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                        curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE;
                        startVideoCaptureButtomChangeTimer();
                        startRecordingLapseTimeTimer(0);
                    }
                    break;
                case SDKEvent.EVENT_CAPTURE_START:
                    AppLog.i(TAG, "receive EVENT_CAPTURE_START:curAppStateMode=" + curAppStateMode);
                    if (curAppStateMode != PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
                        return;
                    }
                    continuousCaptureBeep.start();
                    MyToast.show(activity, R.string.capture_start);
                    break;
                case SDKEvent.EVENT_ROTATE_CAPTURE_COMPLETED:
                    MyToast.show(activity, "A6旋转拍照完成！！！！");
                    break;
                case SDKEvent.EVENT_CAPTURE_COMPLETED:
                    AppLog.i(TAG, "receive EVENT_CAPTURE_COMPLETED:curAppStateMode=" + curAppStateMode);
                    if (isA6Camera && curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE) {
                        countTimes++;
                        photoCaptureEndTime = System.currentTimeMillis();
                        if (countTimes == 1) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo1) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                        } else if (countTimes == 2) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo2) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                        } else if (countTimes == 3) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo3) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                        } else if (countTimes == 4) {
                            AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo4) + (photoCaptureEndTime - photoCaptureStartTime) + "ms", Toast.LENGTH_LONG);
                        }
                        curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                        new Thread(() -> {
//                            startPreview();
                            initSurface();
                            final String remainImageNum = String.valueOf(cameraProperties.getRemainImageNum());
                            previewHandler.post(() -> previewView.setRemainCaptureCount(remainImageNum));
                            previewHandler.postDelayed(() -> {
                                int motorState, shotTimes;
                                motorState = cameraProperties.getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                                shotTimes = cameraProperties.getCurrentPropertyValue(PropertyId.A6_ROTATE_SHOT_TIMES);
                                if (A6RotateMotorState.ROTATE_OFF == motorState && A6RotateShotTimes.SHOT_4TH == shotTimes) {
                                    previewView.setCaptureBtnEnAbility(true);
                                    previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                                    MyProgressDialog.closeProgressDialog();
                                    AppToast.show(activity, PanoramaApp.getContext().getString(R.string.take_photo_finish));
                                    isWaitA6Rotate = false;
                                }
                            }, 8000);
                        }).start();
                        int motorState = cameraProperties.getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                        int shotTimes = cameraProperties.getCurrentPropertyValue(PropertyId.A6_ROTATE_SHOT_TIMES);
                        if (A6RotateMotorState.ROTATE_OFF == motorState && A6RotateShotTimes.SHOT_4TH == shotTimes) {
                        } else {
                            new Thread(() -> {
                                int motorState1;
                                for (; ; ) {
                                    motorState1 = cameraProperties.getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                                    if (A6RotateMotorState.ROTATE_END == motorState1) {
                                        previewView.hideZoomView();
                                        curAppStateMode = PreviewMode.APP_STATE_STILL_CAPTURE;
                                        PhotoCapture photoCapture = new PhotoCapture();
                                        photoCapture.addOnStopPreviewListener(() -> stopPreview());
                                        photoCaptureStartTime = System.currentTimeMillis();
                                        photoCapture.startCapture();
                                        break;
                                    }
                                    if (!isWaitA6Rotate) {
                                        break;
                                    }
                                }
                            }).start();
                        }
                        return;
                    } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE) {
                        curAppStateMode = PreviewMode.APP_STATE_STILL_PREVIEW;
                        MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                if (!cameraProperties.hasFunction(0xd704)) {
//                                    startPreview();
                                    initSurface();
                                }
                                final String remainImageNum = String.valueOf(cameraProperties.getRemainImageNum());
                                previewHandler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        previewView.setCaptureBtnEnAbility(true);
                                        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                                        previewView.setRemainCaptureCount(remainImageNum);
                                        MyProgressDialog.closeProgressDialog();
                                    }
                                });
                            }
                        }).start();
                        return;
                    }
                    if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
                        previewView.setCaptureBtnEnAbility(true);
                        previewView.setCaptureBtnBackgroundResource(R.drawable.still_capture_btn);
                        previewView.setRemainCaptureCount(String.valueOf(cameraProperties.getRemainImageNum()));
                        MyToast.show(activity, R.string.capture_completed);
                    }
                    break;
                case SDKEvent.EVENT_FILE_ADDED:
                    AppLog.i(TAG, "EVENT_FILE_ADDED");
                    break;
                case SDKEvent.EVENT_TIME_LAPSE_STOP:
                    AppLog.i(TAG, "receive EVENT_TIME_LAPSE_STOP:curAppStateMode=" + curAppStateMode);
                    if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
                        stopVideoCaptureButtomChangeTimer();
                        stopRecordingLapseTimeTimer();
                        previewView.setRemainCaptureCount(new Integer(cameraProperties.getRemainImageNum()).toString());
                        curAppStateMode = APP_STATE_TIMELAPSE_VIDEO_PREVIEW;
                    } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
                        stopRecordingLapseTimeTimer();
                        previewView.setRemainCaptureCount(new Integer(cameraProperties.getRemainImageNum()).toString());
                        curAppStateMode = PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW;
                    }
                    break;
                case SDKEvent.EVENT_VIDEO_RECORDING_TIME:
                    AppLog.i(TAG, "receive EVENT_VIDEO_RECORDING_TIME");
                    startRecordingLapseTimeTimer(0);
                    break;
                case SDKEvent.EVENT_FILE_DOWNLOAD:
                    AppLog.i(TAG, "receive EVENT_FILE_DOWNLOAD");
                    AppLog.d(TAG, "receive EVENT_FILE_DOWNLOAD  msg.arg1 =" + msg.arg1);
                    if (AppInfo.autoDownloadAllow == false) {
                        AppLog.d(TAG, "GlobalInfo.autoDownload == false");
                        return;
                    }
                    final String path = StorageUtil.getRootPath(activity) + AppInfo.AUTO_DOWNLOAD_PATH;
                    File directory = new File(path);
                    if (FileTools.getFileSize(directory) / 1024 >= AppInfo.autoDownloadSizeLimit * 1024 * 1024) {
                        AppLog.d(TAG, "can not download because size limit");
                        return;
                    }
                    final ICatchFile file = (ICatchFile) msg.obj;
                    FileOper.createDirectory(path);
                    new Thread() {
                        @Override
                        public void run() {
                            AppLog.d(TAG, "receive downloadFile file =" + file);
                            AppLog.d(TAG, "receive downloadFile path =" + path);
                            boolean retValue = fileOperation.downloadFile(file, path + file.getFileName());
                            if (retValue == true) {
                                previewHandler.post(new Runnable() {
                                    @Override
                                    public void run() {
                                        String path1 = path + file.getFileName();
                                        Bitmap bitmap = BitmapTools.getImageByPath(path1, 150, 150);
                                        previewView.setAutoDownloadBitmap(bitmap);
                                    }
                                });
                            }
                            AppLog.d(TAG, "receive downloadFile retvalue =" + retValue);
                        }
                    }.start();
                    break;
                case AppMessage.SETTING_OPTION_AUTO_DOWNLOAD:
                    AppLog.d(TAG, "receive SETTING_OPTION_AUTO_DOWNLOAD");
                    Boolean switcher = (Boolean) msg.obj;
                    if (switcher == true) {
                        AppInfo.autoDownloadAllow = true;
                        previewView.setAutoDownloadVisibility(View.VISIBLE);
                    } else {
                        AppInfo.autoDownloadAllow = false;
                        previewView.setAutoDownloadVisibility(View.GONE);
                    }
                    break;
                case SDKEvent.EVENT_SDCARD_INSERT:
                    AppLog.i(TAG, "receive EVENT_SDCARD_INSERT");
                    AppDialog.showDialogWarn(activity, R.string.dialog_card_inserted);
                    break;
                default:
                    super.handleMessage(msg);
                    break;
            }
        }
    }

    public void addEvent() {
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_FULL);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_BATTERY_LEVEL_CHANGED);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_OFF);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_ON);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_START);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_COMPLETE);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_ADDED);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_TIMELAPSE_STOP);
        sdkEvent.addCustomizeEvent(0x5001);
        sdkEvent.addCustomizeEvent(0x400D);
        sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_DOWNLOAD);
        isDelEvent = false;
    }

    public synchronized void delEvent() {
        if (curCamera != null && curCamera.isConnected() && !isDelEvent) {
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_FULL);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_BATTERY_LEVEL_CHANGED);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_COMPLETE);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CAPTURE_START);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_OFF);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_ADDED);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_VIDEO_ON);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_TIMELAPSE_STOP);
            sdkEvent.delCustomizeEventListener(0x5001);
            sdkEvent.delCustomizeEventListener(0x400D);
            sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FILE_DOWNLOAD);
            isDelEvent = true;
        }
    }

    public synchronized void loadSettingMenuList() {
        AppLog.i(TAG, "setupBtn is clicked:allowClickButtoms=" + allowClickButtoms);
        if (allowClickButtoms == false) {
            return;
        }
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        allowClickButtoms = false;
        if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
            previewView.setSetupMainMenuVisibility(View.VISIBLE);
            currentSettingMenuMode = UIDisplaySource.CAPTURE_SETTING_MENU;
            if (settingMenuList != null) {
                settingMenuList.clear();
            }
            if (settingListAdapter != null) {
                settingListAdapter.notifyDataSetChanged();
            }
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            new Thread(new Runnable() {
                @Override
                public void run() {
                    previewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            settingMenuList = UIDisplaySource.getInstance().getList(UIDisplaySource.CAPTURE_SETTING_MENU, curCamera);
                            settingListAdapter = new SettingListAdapter(activity, settingMenuList, previewHandler);
                            previewView.setSettingMenuListAdapter(settingListAdapter);
                            MyProgressDialog.closeProgressDialog();
                        }
                    }, 500);
                }
            }).start();
        } else if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
            previewView.setSetupMainMenuVisibility(View.VISIBLE);
            currentSettingMenuMode = UIDisplaySource.VIDEO_SETTING_MENU;
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            if (settingMenuList != null) {
                settingMenuList.clear();
            }
            if (settingListAdapter != null) {
                settingListAdapter.notifyDataSetChanged();
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    previewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            settingMenuList = UIDisplaySource.getInstance().getList(UIDisplaySource.VIDEO_SETTING_MENU, curCamera);
                            settingListAdapter = new SettingListAdapter(activity, settingMenuList, previewHandler);
                            previewView.setSettingMenuListAdapter(settingListAdapter);
                            MyProgressDialog.closeProgressDialog();
                        }
                    }, 500);
                }
            }).start();
        } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW || curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
            previewView.setSetupMainMenuVisibility(View.VISIBLE);
            currentSettingMenuMode = UIDisplaySource.TIMELAPSE_SETTING_MENU;
            MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            if (settingMenuList != null) {
                settingMenuList.clear();
            }
            if (settingListAdapter != null) {
                settingListAdapter.notifyDataSetChanged();
            }
            new Thread(new Runnable() {
                @Override
                public void run() {
                    previewHandler.postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            settingMenuList = UIDisplaySource.getInstance().getList(UIDisplaySource.TIMELAPSE_SETTING_MENU, curCamera);
                            settingListAdapter = new SettingListAdapter(activity, settingMenuList, previewHandler);
                            previewView.setSettingMenuListAdapter(settingListAdapter);
                            MyProgressDialog.closeProgressDialog();
                        }
                    }, 500);
                }
            }).start();
        }
        allowClickButtoms = true;
    }

    @Override
    public void isAppBackground() {
        super.isAppBackground();
    }

    @Override
    public void finishActivity() {
        Tristate ret = Tristate.NORMAL;
        if (previewView.getSetupMainMenuVisibility() == View.VISIBLE) {
            AppLog.i(TAG, "onKeyDown curAppStateMode==" + curAppStateMode);
            previewView.setSetupMainMenuVisibility(View.GONE);
            if (curAppStateMode == PreviewMode.APP_STATE_VIDEO_PREVIEW) {
                AppLog.i(TAG, "onKeyDown curAppStateMode == APP_STATE_VIDEO_PREVIEW");
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE);
            } else if (curAppStateMode == PreviewMode.APP_STATE_STILL_PREVIEW) {
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_STILL_PREVIEW_MODE);
            } else if (curAppStateMode == APP_STATE_TIMELAPSE_VIDEO_PREVIEW) {
                AppLog.i(TAG, "onKeyDown curAppStateMode == APP_STATE_TIMELAPSE_PREVIEW_VIDEO");
                curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_VIDEO_PREVIEW_MODE);
            } else if (curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_PREVIEW) {
                AppLog.i(TAG, "onKeyDown curAppStateMode == APP_STATE_TIMELAPSE_PREVIEW_STILL");
                curCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
                changeCameraMode(curAppStateMode, ICatchCamPreviewMode.ICH_CAM_TIMELAPSE_STILL_PREVIEW_MODE);
            } else {
//                startPreview();
                initSurface();
                createUIByMode(curAppStateMode);
            }
        } else {
            savePvThumbnail();
            destroyPreview();
            super.finishActivity();
        }
    }

    @Override
    public void redirectToAnotherActivity(final Context context, final Class<?> cls) {
        AppLog.i(TAG, "pbBtn is clicked curAppStateMode=" + curAppStateMode);
        if (allowClickButtoms == false) {
            AppLog.i(TAG, "do not allow to response button clicking");
            return;
        }
        if (!checkModeSwitch(curAppStateMode)) {
            int resId = getSwitchErrorResId(curAppStateMode);
            if (resId > 0) {
                MyToast.show(activity, resId);
            }
            return;
        }
        allowClickButtoms = false;
        Boolean isSDCardExist = cameraProperties.isSDCardExist();
        if (isSDCardExist == null){
            AppDialog.showDialogWarn(activity, R.string.dialog_connect_failed);
            allowClickButtoms = true;
            return;
        }
        if (Boolean.FALSE.equals(isSDCardExist)) {
            AppDialog.showDialogWarn(activity, R.string.dialog_card_lose);
            allowClickButtoms = true;
            return;
        }

        AppLog.i(TAG, "curAppStateMode =" + curAppStateMode);
        destroyPreview();
        delEvent();
        allowClickButtoms = true;
        MyProgressDialog.showProgressDialog(context, R.string.action_processing);
        previewHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                MyProgressDialog.closeProgressDialog();
                Intent intent = new Intent();
                AppLog.i(TAG, "intent:start PbMainActivity.class");
                intent.setClass(context, cls);
                context.startActivity(intent);
                AppLog.i(TAG, "intent:end start PbMainActivity.class");
            }
        }, 500);
        allowClickButtoms = true;
        AppLog.i(TAG, "end processing for responsing pbBtn clicking");
    }

    private boolean checkModeSwitch(int appStateMode) {
        if (appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE
                || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE
                || curAppStateMode == PreviewMode.APP_STATE_STILL_CAPTURE
                || curAppStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return false;
        } else {
            return true;
        }
    }

    private int getSwitchErrorResId(int appStateMode) {
        if (appStateMode == PreviewMode.APP_STATE_VIDEO_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_VIDEO_CAPTURE) {
            return R.string.stream_error_recording;
        } else if (appStateMode == PreviewMode.APP_STATE_STILL_CAPTURE || appStateMode == PreviewMode.APP_STATE_TIMELAPSE_STILL_CAPTURE) {
            return R.string.stream_error_capturing;
        } else {
            return -1;
        }
    }

    private class WifiSSReceiver extends BroadcastReceiver {
        private WifiManager wifi;

        public WifiSSReceiver() {
            super();
            wifi = (WifiManager) activity.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            changeWifiStatusIcon();
        }

        @Override
        public void onReceive(Context arg0, Intent arg1) {
            changeWifiStatusIcon();
        }

        private void changeWifiStatusIcon() {
            WifiInfo info = wifi.getConnectionInfo();
            if (info.getBSSID() != null) {
                int strength = WifiManager.calculateSignalLevel(info.getRssi(), 8);
                AppLog.d(TAG, "change Wifi Status：" + strength);
                switch (strength) {
                    case 0:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_0_bar_green_24dp);
                        break;
                    case 1:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_1_bar_green_24dp);
                        break;
                    case 2:
                    case 3:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_2_bar_green_24dp);
                        break;
                    case 4:
                    case 5:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_3_bar_green_24dp);
                        break;
                    case 6:
                    case 7:
                        previewView.setWifiIcon(R.drawable.ic_signal_wifi_4_bar_green_24dp);
                        break;
                    default:
                        break;
                }
            }
        }
    }

    public void startPreview() {
        AppLog.d(TAG, "start startPreview hasInitSurface=" + hasInitSurface);
        if (hasInitSurface == false) {
            return;
        }
        if (panoramaPreviewPlayback == null) {
            AppLog.d(TAG, "null point");
            return;
        }
        if (curCamera.isStreamReady) {
            return;
        }
        boolean isSupportPreview = cameraProperties.isSupportPreview();
        AppLog.d(TAG, "start startPreview isSupportPreview=" + isSupportPreview);
        if (!isSupportPreview) {
            previewHandler.post(new Runnable() {
                @Override
                public void run() {
                    previewView.setSupportPreviewTxvVisibility(View.VISIBLE);
                }
            });
            return;
        }
        if (AppInfo.enableDumpVideo) {
            String streamOutputPath = Environment.getExternalStorageDirectory().toString() + AppInfo.STREAM_OUTPUT_DIRECTORY_PATH;
            FileOper.createDirectory(streamOutputPath);
            try {
                ICatchPancamConfig.getInstance().enableDumpTransportStream(true, streamOutputPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        int cacheTime = cameraProperties.getPreviewCacheTime();
        cacheTime = 400;
        AppLog.d(TAG, "setPreviewCacheParam cacheTime:" + cacheTime);
        ICatchPancamConfig.getInstance().setPreviewCacheParam(cacheTime, 200);
        ICatchStreamParam iCatchStreamParam = getStreamParam();
        final Tristate retValue;
        if (AppInfo.enableRender) {
            if (PanoramaTools.isPanorama(iCatchStreamParam.getWidth(), iCatchStreamParam.getHeight())) {
                registerGyroscopeSensor();
            }
            retValue = panoramaPreviewPlayback.start(iCatchStreamParam, !AppInfo.disableAudio);
        } else {
            retValue = cameraStreaming.start(iCatchStreamParam, !AppInfo.disableAudio);
        }
        if (retValue == Tristate.NORMAL) {
            curCamera.isStreamReady = true;
        } else {
            curCamera.isStreamReady = false;
        }
        previewHandler.post(new Runnable() {
            @Override
            public void run() {
                if (retValue == Tristate.ABNORMAL) {
                    previewView.setSupportPreviewTxvVisibility(View.VISIBLE);
                } else if (retValue == Tristate.NORMAL) {
                    previewView.setSupportPreviewTxvVisibility(View.GONE);
                } else {
                    previewView.setSupportPreviewTxvVisibility(View.GONE);
                    MyToast.show(activity, R.string.open_preview_failed);
                }
            }
        });
        AppLog.d(TAG, "end startPreview retValue=" + retValue);
    }

    private ICatchStreamParam getStreamParam() {
        StreamInfo streamInfo = null;
        if (curCamera.getCameraType() == CameraType.USB_CAMERA) {
            streamInfo = new StreamInfo(curCodecType, curVideoWidth, curVideoHeight, 5000000, curVideoFps);
            AppLog.d(TAG, "start startPreview videoWidth=" + curVideoWidth + " videoHeight=" + curVideoHeight + " videoFps=" + curVideoFps + " curCodecType=" +
                    curCodecType);
        } else {
            String streamUrl = cameraProperties.getCurrentStreamInfo();
            AppLog.d(TAG, " start startStreamAndPreview streamUrl=[" + streamUrl + "]");
            if (streamUrl != null) {
                streamInfo = StreamInfoConvert.convertToStreamInfoBean(streamUrl);
            }
        }
        ICatchStreamParam iCatchStreamParam = null;
        if (streamInfo == null) {
            iCatchStreamParam = new ICatchH264StreamParam(1280, 720, 30);
        } else if (streamInfo.mediaCodecType.equals("MJPG")) {
            iCatchStreamParam = new ICatchJPEGStreamParam(streamInfo.width, streamInfo.height, streamInfo.fps, streamInfo.bitrate);
        } else if (streamInfo.mediaCodecType.equals("H264")) {
            iCatchStreamParam = new ICatchH264StreamParam(streamInfo.width, streamInfo.height, streamInfo.fps, streamInfo.bitrate);
        } else {
            iCatchStreamParam = new ICatchH264StreamParam(1280, 720, 30);
        }
        return iCatchStreamParam;
    }

    public void stopPreview() {
        if (AppInfo.enableDumpVideo) {
            ICatchPancamConfig.getInstance().disableDumpTransportStream(true);
        }
        if (AppInfo.enableRender) {
            removeGyroscopeListener();
            if (panoramaPreviewPlayback != null && curCamera.isStreamReady) {
                curCamera.isStreamReady = false;
                panoramaPreviewPlayback.stop();
            }
        } else {
            if (curCamera.isStreamReady) {
                curCamera.isStreamReady = false;
                cameraStreaming.stop();
            }
        }
    }

    public void locate(float progerss) {
        panoramaPreviewPlayback.locate(progerss);
    }

    public void savePvThumbnail() {
        if (curCamera != null && panoramaPreviewPlayback != null && curCamera.isStreamReady) {
            Bitmap bitmap = panoramaPreviewPlayback.getPvThumbnail();
            if (bitmap != null) {
                ByteArrayOutputStream output = new ByteArrayOutputStream();
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, output);
                byte[] result = output.toByteArray();
                try {
                    output.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                CameraSlotSQLite.getInstance().update(new CameraSlot(curCamera.getPosition(), true, curCamera.getCameraName(), curCamera.getCameraType(), result, true));
            }
        }
    }

    public void destroyPreview() {
        if (AppInfo.enableDumpVideo) {
            ICatchPancamConfig.getInstance().disableDumpTransportStream(true);
        }
        hasInitSurface = false;
        if (AppInfo.enableRender) {
            removeGyroscopeListener();
            if (panoramaPreviewPlayback != null && curCamera.isStreamReady) {
                if (iCatchSurfaceContext != null) {
                    AppLog.d(TAG, "destroyPreview.....");
                    panoramaPreviewPlayback.removeSurface(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE, iCatchSurfaceContext);
                }
                panoramaPreviewPlayback.stop();
                panoramaPreviewPlayback.release();
                curCamera.isStreamReady = false;
            }
        } else {
            if (curCamera.isStreamReady) {
                curCamera.isStreamReady = false;
                cameraStreaming.stop();
            }
        }
    }

    public void rotate(ICatchGLPoint prev, ICatchGLPoint next) {
        panoramaPreviewPlayback.rotate(prev, next);
    }

    public void rotateB(MotionEvent e, float prevX, float prevY) {
        ICatchGLPoint prev = new ICatchGLPoint(prevX, prevY);
        ICatchGLPoint curr = new ICatchGLPoint(e.getX(), e.getY());
        panoramaPreviewPlayback.rotate(prev, curr);
    }

    public void onSufaceViewTouchDown(MotionEvent event) {
        touchMode = TouchMode.DRAG;
        mPreviousY = event.getY();
        mPreviousX = event.getX();
        beforeLenght = 0;
        afterLenght = 0;
    }

    public void onSufaceViewPointerDown(MotionEvent event) {
        if (event.getPointerCount() == 2) {
            touchMode = TouchMode.ZOOM;
            beforeLenght = getDistance(event);
        }
    }

    public void onSufaceViewTouchMove(MotionEvent event) {
        if (touchMode == TouchMode.DRAG) {
            rotateB(event, mPreviousX, mPreviousY);
            mPreviousY = event.getY();
            mPreviousX = event.getX();
        } else if (touchMode == TouchMode.ZOOM) {
            afterLenght = getDistance(event);
            float gapLenght = afterLenght - beforeLenght;
            if (Math.abs(gapLenght) > 5f) {
                float scale_temp = afterLenght / beforeLenght;
                this.setScale(scale_temp);
                beforeLenght = afterLenght;
            }
        }
    }

    float getDistance(MotionEvent event) {
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) StrictMath.sqrt(x * x + y * y);
    }

    void setScale(float scale) {
        if ((currentZoomRate >= MAX_ZOOM && scale > 1) || (currentZoomRate <= MIN_ZOOM && scale < 1)) {
            return;
        }
        float temp = currentZoomRate * scale;
        if (scale > 1) {
            if (temp <= MAX_ZOOM) {
                currentZoomRate = currentZoomRate * scale;
                zoom(currentZoomRate);
            } else {
                currentZoomRate = MAX_ZOOM;
                zoom(currentZoomRate);
            }
        } else if (scale < 1) {
            if (temp >= MIN_ZOOM) {
                currentZoomRate = currentZoomRate * scale;
                zoom(currentZoomRate);
            } else {
                currentZoomRate = MIN_ZOOM;
                zoom(currentZoomRate);
            }
        }
    }

    private void zoom(float currentZoomRate) {
        locate(1 / currentZoomRate);
    }

    public void onSufaceViewTouchUp() {
        showZoomView();
        touchMode = TouchMode.NONE;
    }

    public void onSufaceViewTouchPointerUp() {
        touchMode = TouchMode.NONE;
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (event.sensor == null) {
            return;
        }
        if (event.sensor.getType() == Sensor.TYPE_GYROSCOPE) {
            float speedX = event.values[0];
            float speedY = event.values[1];
            float speedZ = event.values[2];
            if (Math.abs(speedY) < 0.05 && Math.abs(speedZ) < 0.05) {
                return;
            }
            rotate(speedX, speedY, speedZ, event.timestamp);
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
    }

    private void rotate(float speedX, float speedY, float speedZ, long timestamp) {
        int rotation = activity.getWindowManager().getDefaultDisplay().getRotation();
        panoramaPreviewPlayback.rotate(rotation, speedX, speedY, speedZ, timestamp);
    }

    private void registerGyroscopeSensor() {
        AppLog.d(TAG, "registerGyroscopeSensor");
        sensorManager = (SensorManager) activity.getSystemService(Context.SENSOR_SERVICE);
        gyroscopeSensor = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE);
        sensorManager.registerListener(this, gyroscopeSensor, SensorManager.SENSOR_DELAY_GAME);
    }

    protected void removeGyroscopeListener() {
        if (sensorManager != null) {
            sensorManager.unregisterListener(this);
        }
    }

    public void setDrawingArea(int width, int height) {
        if (panoramaPreviewPlayback != null && iCatchSurfaceContext != null) {
            AppLog.d(TAG, "start setDrawingArea width=" + width + " height=" + height);
            try {
                iCatchSurfaceContext.setViewPort(0, 0, width, height);
            } catch (IchGLSurfaceNotSetException e) {
                e.printStackTrace();
            }
            AppLog.d(TAG, "end setDrawingArea");
        }
    }

    public void initSurface(SurfaceHolder surfaceHolder) {
        hasInitSurface = false;
        AppLog.i(TAG, "begin initSurface");
        if (panoramaPreviewPlayback == null) {
            return;
        }
        if (AppInfo.enableRender) {
            iCatchSurfaceContext = new ICatchSurfaceContext(surfaceHolder.getSurface());
            ICatchStreamParam iCatchStreamParam = getStreamParam();
            AppLog.i(TAG, "iCatchStreamParam.getWidth() = " + iCatchStreamParam.getWidth());
            AppLog.i(TAG, "iCatchStreamParam.getHeight() = " + iCatchStreamParam.getHeight());
            if (iCatchStreamParam != null && PanoramaTools.isPanorama(iCatchStreamParam.getWidth(), iCatchStreamParam.getHeight())) {
                panoramaPreviewPlayback.enableGLRender();
                panoramaPreviewPlayback.init(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE);
                panoramaPreviewPlayback.setSurface(ICatchGLSurfaceType.ICH_GL_SURFACE_TYPE_SPHERE, iCatchSurfaceContext);
                previewView.setPanoramaTypeBtnVisibility(View.VISIBLE);
            } else {
                panoramaPreviewPlayback.enableCommonRender(iCatchSurfaceContext);
                previewView.setPanoramaTypeBtnVisibility(View.GONE);
            }
        } else {
            previewView.setPanoramaTypeBtnVisibility(View.GONE);
            cameraStreaming.disnableRender();
            int width = previewView.getSurfaceViewWidth();
            int heigth = previewView.getSurfaceViewHeight();
            AppLog.i(TAG, "SurfaceViewWidth=" + width + " SurfaceViewHeight=" + heigth);
            if (width <= 0 || heigth <= 0) {
                width = 1080;
                heigth = 1920;
            }
            cameraStreaming.setSurface(surfaceHolder);
            cameraStreaming.setViewParam(width, heigth);
        }
        hasInitSurface = true;
        AppLog.i(TAG, "end initSurface");
    }


    public void initSurface(PanoPlayerImpl panoPlayer, PanoPlayerSurfaceView surfaceHolder) {
        againCount = 0;
        mPanoPlayer = panoPlayer;
        mSurfaceHolder = surfaceHolder;
        hasInitSurface = false;
        AppLog.i(TAG, "begin initSurface");
        if (panoramaPreviewPlayback == null) {
            return;
        }


        playF4OrF4Pluslive(false, true);
        hasInitSurface = true;
        AppLog.i(TAG, "end initSurface");
    }

    public void initSurface() {
        if (!hasInitSurface) {
            return;
        }

        if (curCamera.isStreamReady) {
            return;
        }

        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
            }
        });

        hasInitSurface = false;
        AppLog.i(TAG, "begin initSurface");
        if (panoramaPreviewPlayback == null) {
            return;
        }


//        boolean isSupportPreview = cameraProperties.isSupportPreview();
//        AppLog.d(TAG, "start startPreview isSupportPreview=" + isSupportPreview);
//        if (!isSupportPreview) {
//            previewHandler.post(new Runnable() {
//                @Override
//                public void run() {
//                    previewView.setSupportPreviewTxvVisibility(View.VISIBLE);
//                }
//            });
//            return;
//        }


//        if (AppInfo.enableDumpVideo) {
//            String streamOutputPath = Environment.getExternalStorageDirectory().toString() + AppInfo.STREAM_OUTPUT_DIRECTORY_PATH;
//            FileOper.createDirectory(streamOutputPath);
//            try {
//                ICatchPancamConfig.getInstance().enableDumpTransportStream(true, streamOutputPath);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        int cacheTime = cameraProperties.getPreviewCacheTime();
//        cacheTime = 400;
//        AppLog.d(TAG, "setPreviewCacheParam cacheTime:" + cacheTime);
//        ICatchPancamConfig.getInstance().setPreviewCacheParam(cacheTime, 500);
//        ICatchStreamParam iCatchStreamParam = getStreamParam();
//        final Tristate retValue;
//        if (AppInfo.enableRender) {
//            retValue = panoramaPreviewPlayback.start(iCatchStreamParam, !AppInfo.disableAudio);
//        } else {
//            retValue = cameraStreaming.start(iCatchStreamParam, !AppInfo.disableAudio);
//        }
//        if (retValue == Tristate.NORMAL) {
//            curCamera.isStreamReady = true;
//        } else {
//            curCamera.isStreamReady = false;
//        }

//        ThreadUtils.runOnUiThread(new Runnable() {
//            @Override
//            public void run() {
//                panoramaPreviewPlayback.stop();
//                cameraStreaming.stop();
//                ICatchPancamConfig.getInstance().disableDumpTransportStream(true);
//                stopVideoCaptureButtomChangeTimer();
//                stopRecordingLapseTimeTimer();
//                Plugin plugin = mPanoPlayer.getPlugin();
//                if (plugin instanceof VideoPlugin) {
//                    ((VideoPlugin) plugin).pause();
//                    ((VideoPlugin) plugin).close();
//                }
//            }
//        });

//        playF4OrF4Pluslive(true, true);

        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                previewHandler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
//                        ICatchStreamParam iCatchStreamParam = getStreamParam();
//                        panoramaPreviewPlayback.start(iCatchStreamParam, !AppInfo.disableAudio);
//                        cameraStreaming.start(iCatchStreamParam, !AppInfo.disableAudio);
//                        MyProgressDialog.closeProgressDialog();

                        playF4OrF4Pluslive(false, true);
                        hasInitSurface = true;
                        AppLog.i(TAG, "end initSurface");
                    }
                }, 2000);
            }
        });
    }

    public RemoteMultiPbPresenter remoteMultiPbPresenter;
    private int againCount = 0;
    public void playF4OrF4Pluslive(boolean isDisConnect, boolean isDisMiss) {
        String PLAYER_CONFIG =
                "<DetuVr>\n" +
                        "   <settings init='pano1' initmode='default' enablevr='false' title=''/>\n" +
                        "   <scenes>\n" +
                        "       <scene name='pano1' title='' thumburl=''>\n" +
                        "           <preview url='' />\n" +
                        "           <image type='%s' url='%s' device='%d' biaoding='%s'/>\n" +
                        "           <view hlookat ='180' viewmode='default' isptselect ='true' zScale ='5'/>\n" +
                        "       </scene>\n" +
                        "   </scenes>\n" +
                        "</DetuVr>";

        String path = "";
        if (isDisConnect) {
            path = "rtsp://*******:554/H264?W=3840&H=1920&BR=4000000&FPS=15";
        } else {
            path = "rtsp://***********:554/H264?W=3840&H=1920&BR=4000000&FPS=15";
        }

        String biaoding = "";
//        String cali_str = FileIOUtils.readFile2String(new File(StorageUtil.getRootPath(PanoramaApp.getContext()) + AppInfo.DOWNLOAD_PATH_PHOTO, "zd.txt"));
        String cali_str = FileIOUtils.readFile2String(new File(PanoramaApp.getContext().getCacheDir().getAbsolutePath(), "zd.txt"));
        if (TextUtils.isEmpty(cali_str)) {
//            biaoding = "6_1398.000000_1545.000000_1574.000000_180.000000_0.000000_0.000000_1421.500000_4595.500000_1572.500000_-0.887014_-1.653790_0.828743_6144_3072_0.020517_-27.014400_0.000000_0.000000_0.000000_0.000000_0.158651_0.000000_0.000000_0.158260_203.000000_185.000000";
            if (remoteMultiPbPresenter == null) {
                remoteMultiPbPresenter = new RemoteMultiPbPresenter(activity);
            }
            if (remoteMultiPbPresenter.downloadManager == null){
                remoteMultiPbPresenter.downloadManager = new PbDownloadManager(activity);
            }
            remoteMultiPbPresenter.downloadManager.setOnHttpListenerListener(new PbDownloadManager.OnHttpListener() {
                @Override
                public void onResult(boolean isSuccess) {
                    if (isSuccess){
                        playF4OrF4Pluslive(isDisConnect,isDisMiss);
                    } else {
                        againCount = 0;
                        ToastUtils.showLong("标定参数异常，请重新再试");
                        finishActivity();
                    }
                    remoteMultiPbPresenter.downloadManager.removeOnHttpListener();
                }
            });
            if (againCount < 2) {
                againCount++;
                remoteMultiPbPresenter.downZdInfo(remoteMultiPbPresenter);
                playF4OrF4Pluslive(isDisConnect,isDisMiss);
            } else {
                againCount = 0;
                finishActivity();
                MyProgressDialog.closeProgressDialog();
                ToastUtils.showLong("标定参数异常，请重新再试");
            }
            return;
        } else {
            cali_str = cali_str.substring(5);
            biaoding = SzStitch.GetCaliStr(cali_str);
        }

        String url = String.format(PLAYER_CONFIG, "video", path, 2002, biaoding);


        PanoPlayerUrl panoplayerurl = new PanoPlayerUrl();
        panoplayerurl.setXmlContent(url);
        List<PanoPlayerOption> options = new ArrayList<PanoPlayerOption>(1);
        PanoPlayerOption optionCodec = new PanoPlayerOption(PanoPlayerOptionType.OPT_CATEGORY_CODEC, PanoOptionKey.DETU_HW_DECODER, String.valueOf(false));
        options.add(optionCodec);
        PanoPlayerOption optionTcp = new PanoPlayerOption(PanoPlayerOptionType.OPT_CATEGORY_FORMAT, PanoOptionKey.RTSP_TRANSPORT, "tcp");
        options.add(optionTcp);


        mPanoPlayer.playByXml(panoplayerurl, options);
        mPanoPlayer.setPanoPlayerListener(new IPanoPlayerListener() {
            @Override
            public void onPanoPlayerStatusChanged(PanoPlayerStatus panoPlayerStatus, String s) {
                switch (panoPlayerStatus) {
                    case LOADING:
                        ThreadUtils.runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
                            }
                        });

                        break;
                    case LOADED:
//                        ThreadUtils.runOnUiThread(new Runnable() {
//                            @Override
//                            public void run() {
//                                new Handler().postDelayed(new Runnable() {
//                                    @Override
//                                    public void run() {
//                                        CameraManager.getInstance().getCurCamera().disconnect();
//                                        disconnectCamera();
//                                    }
//                                },3000);
//                            }
//                        });
                        if (isDisMiss) {
                            previewHandler.postDelayed(new Runnable() {
                                @Override
                                public void run() {
                                    MyProgressDialog.closeProgressDialog();
                                }
                            }, 500);
                        }
                        break;
                    case ERROR:
                        previewHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                MyProgressDialog.closeProgressDialog();
                            }
                        }, 500);
                        break;
                    default:
                        previewHandler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                MyProgressDialog.closeProgressDialog();
                            }
                        }, 500);
                        break;
                }
            }

            @Override
            public void onPanoPlayerConfigLoaded(int i) {

            }
        });
    }

    public void showSharedUrlDialog(final Context context, final String shareUrl) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(context);
        View view = LayoutInflater.from(context).inflate(R.layout.live_shared_url, null);
        final EditText resetTxv = (EditText) view.findViewById(R.id.shared_url);
        final ImageView qrcodeImage = (ImageView) view.findViewById(R.id.shared_url_qrcode);
        Bitmap bitmap = QRCode.createQRCodeWithLogo(shareUrl, QRCode.WIDTH,
                BitmapFactory.decodeResource(context.getResources(), R.drawable.ic_panorama_green_500_48dp));
        qrcodeImage.setImageBitmap(bitmap);
        resetTxv.setText(shareUrl);
        builder.setTitle("Success, share url is:");
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        });
        builder.setView(view);
        builder.setCancelable(false);
        builder.create().show();
    }


    private int count = 0;

    public void setPanoramaType() {
        if (curPanoramaType == ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE) {
            panoramaPreviewPlayback.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID;
            previewView.setPanoramaTypeBtnSrc(R.drawable.asteroid);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        } else if (curPanoramaType == ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID) {
            panoramaPreviewPlayback.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_VIRTUAL_R);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_VIRTUAL_R;
            previewView.setPanoramaTypeBtnSrc(R.drawable.vr);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE);
        } else {
            panoramaPreviewPlayback.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE;
            previewView.setPanoramaTypeBtnSrc(R.drawable.panorama);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        }


//        if (count % 4 == 0) {
//            panoramaPreviewPlayback.changePanoramaType(0);
//        } else if (count % 4 == 1) {
//            panoramaPreviewPlayback.changePanoramaType(1);
//        } else if (count % 4 == 2) {
//            panoramaPreviewPlayback.changePanoramaType(4);
//        } else if (count % 4 == 3) {
//            panoramaPreviewPlayback.changePanoramaType(6);
//        }
//        count++;

    }
}

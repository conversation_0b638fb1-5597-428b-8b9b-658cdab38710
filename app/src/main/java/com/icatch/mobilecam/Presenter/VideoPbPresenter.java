package com.icatch.mobilecam.Presenter;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.View;

import com.icatch.mobilecam.Function.SDKEvent;
import com.icatch.mobilecam.Function.streaming.VideoStreaming;
import com.icatch.mobilecam.Listener.VideoFramePtsChangedListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.Interface.BasePresenter;
import com.icatch.mobilecam.SdkApi.FileOperation;
import com.icatch.mobilecam.SdkApi.PanoramaVideoPlayback;
import com.icatch.mobilecam.SdkApi.StreamStablization;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Message.AppMessage;
import com.icatch.mobilecam.data.Mode.TouchMode;
import com.icatch.mobilecam.data.Mode.VideoPbMode;
import com.icatch.mobilecam.data.SystemInfo.SystemInfo;
import com.icatch.mobilecam.data.entity.DownloadInfo;
import com.icatch.mobilecam.data.entity.MultiPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Interface.VideoPbView;
import com.icatch.mobilecam.ui.RemoteFileHelper;
import com.icatch.mobilecam.ui.activity.LocalMultiPbActivity;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.ui.appdialog.SingleDownloadDialog;
import com.icatch.mobilecam.utils.ConvertTools;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.PanoramaTools;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.fileutils.FileOper;
import com.icatch.mobilecam.utils.fileutils.FileTools;
import com.icatchtek.pancam.customer.exception.IchGLFormatNotSupportedException;
import com.icatchtek.pancam.customer.type.ICatchGLEventID;
import com.icatchtek.pancam.customer.type.ICatchGLPanoramaType;
import com.icatchtek.pancam.customer.type.ICatchGLPoint;
import com.icatchtek.reliant.customer.type.ICatchFile;
import com.ijoyer.mobilecam.R;

import java.io.File;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
public class VideoPbPresenter extends BasePresenter implements SensorEventListener {
    private String TAG = VideoPbPresenter.class.getSimpleName();
    private VideoPbView videoPbView;
    private Activity activity;
    private FileOperation fileOperation;
    private VideoPbMode videoPbMode = VideoPbMode.MODE_VIDEO_IDLE;
    private boolean needUpdateSeekBar = true;
    private ICatchFile curVideoFile;
    private VideoPbHandler handler = new VideoPbHandler();
    private boolean cacheFlag = false;
    private Boolean waitForCaching = false;
    private double currentTime = -1.0;
    private int videoDuration = 0;
    private int lastSeekBarPosition;
    private final static float MIN_ZOOM = 0.5f;
    private final static float MAX_ZOOM = 2.2f;
    private final static float FIXED_OUTSIDE_DISTANCE = 1 / MIN_ZOOM;
    private final static float FIXED_INSIDE_DISTANCE = 1 / MAX_ZOOM;
    private PanoramaVideoPlayback panoramaVideoPlayback;
    private TouchMode touchMode = TouchMode.NONE;
    private float mPreviousY;
    private float mPreviousX;
    private float beforeLenght;
    private float afterLenght;
    private float currentZoomRate = MAX_ZOOM;
    private SensorManager sensorManager;
    private Sensor gyroscopeSensor;
    private int curVideoPosition;
    private ExecutorService executor;
    protected Timer downloadProgressTimer;
    private List<MultiPbItemInfo> fileList;
    private SingleDownloadDialog singleDownloadDialog;
    private SDKEvent sdkEvent;
    private VideoStreaming videoStreaming;
    private boolean enableRender = AppInfo.enableRender;
    private int curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE;
    private FileType fileType;
    private boolean hasDeleted = false;
    public VideoPbPresenter(Activity activity) {
        super(activity);
        this.activity = activity;
        Intent intent = activity.getIntent();
        Bundle data = intent.getExtras();
        curVideoPosition = data.getInt("curFilePosition");
        int fileTypeInt = data.getInt("fileType");
        fileType = FileType.values()[fileTypeInt];
        fileList = RemoteFileHelper.getInstance().getLocalFileList(fileType);
        if (fileList != null && fileList.isEmpty() == false) {
            this.curVideoFile = fileList.get(curVideoPosition).iCatchFile;
        }
        AppLog.i(TAG, "cur video fileType=" + fileType + " position=" + curVideoPosition + " video name=" + curVideoFile.getFileName());
        initClint();
    }
    @Override
    public void isAppBackground() {
        stopVideoStream();
        super.isAppBackground();
    }
    public void updatePbSeekbar(double pts) {
        if (videoPbMode != VideoPbMode.MODE_VIDEO_PLAY || needUpdateSeekBar == false) {
            return;
        }
        currentTime = pts;
        int temp = new Double(currentTime * 100).intValue();
        videoPbView.setSeekBarProgress(temp);
    }
    public void setView(VideoPbView videoPbView) {
        this.videoPbView = videoPbView;
        initCfg();
        initView();
    }
    private void initView() {
        String fileName = curVideoFile.getFileName();
        int start = fileName.lastIndexOf("/");
        String videoName = fileName.substring(start + 1);
        videoPbView.setVideoNameTxv(videoName);
        if (enableRender && PanoramaTools.isPanorama(curVideoFile.getFileWidth(), curVideoFile.getFileHeight())) {
            videoPbView.setPanoramaTypeBtnVisibility(View.VISIBLE);
        } else {
            videoPbView.setPanoramaTypeBtnVisibility(View.GONE);
        }
    }
    public void initClint() {
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        panoramaVideoPlayback = camera.getPanoramaVideoPlayback();
        fileOperation = camera.getFileOperation();
        videoStreaming = new VideoStreaming(panoramaVideoPlayback);
    }
    public void addEventListener() {
        if (panoramaVideoPlayback == null) {
            return;
        }
        if (sdkEvent == null) {
            sdkEvent = new SDKEvent(handler);
        }
        sdkEvent.addPanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_STREAM_PLAYING_STATUS);
        sdkEvent.addPanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_STREAM_PLAYING_ENDED);
        sdkEvent.addPanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_PLAYBACK_CACHING_CHANGED);
        sdkEvent.addPanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_PLAYBACK_CACHING_PROGRESS);
        sdkEvent.addPanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_STREAM_NO_EIS_INFORMATION);
    }
    public void removeEventListener() {
        if (panoramaVideoPlayback == null) {
            return;
        }
        sdkEvent.removePanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_STREAM_PLAYING_STATUS);
        sdkEvent.removePanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_STREAM_PLAYING_ENDED);
        sdkEvent.removePanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_PLAYBACK_CACHING_CHANGED);
        sdkEvent.removePanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_PLAYBACK_CACHING_PROGRESS);
        sdkEvent.removePanoramaEventListener(ICatchGLEventID.ICH_GL_EVENT_VIDEO_STREAM_NO_EIS_INFORMATION);
    }
    public void play() {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_IDLE) {
            AppLog.i(TAG, "start play video");
            addEventListener();
            if (enableRender) {
                registerGyroscopeSensor();
            }
            if (!enableRender) {
                videoStreaming.setFramePtsChangedListener(new VideoFramePtsChangedListener() {
                    @Override
                    public void onFramePtsChanged(double pts) {
                        updatePbSeekbar(pts);
                    }
                });
            }
            boolean ret;
            try {
                ret = videoStreaming.play(curVideoFile, false, true);
            } catch (IchGLFormatNotSupportedException e) {
                e.printStackTrace();
                MyToast.show(activity, R.string.video_format_not_support);
                AppLog.e(TAG, "failed to startPlaybackStream");
                return;
            }
            if (ret == false) {
                MyToast.show(activity, R.string.dialog_failed);
                AppLog.e(TAG, "failed to startPlaybackStream");
                return;
            }
            videoPbMode = VideoPbMode.MODE_VIDEO_PLAY;
            videoPbView.showLoadingCircle(true);
            cacheFlag = true;
            waitForCaching = true;
            needUpdateSeekBar = true;
            AppLog.i(TAG, "seekBar.getProgress() =" + videoPbView.getSeekBarProgress());
            int tempDuration = panoramaVideoPlayback.getLength();
            videoDuration = tempDuration;
            AppLog.i(TAG, "end getLength = " + tempDuration);
            videoPbView.setPlayBtnSrc(R.drawable.ic_pause_white_36dp);
            videoPbView.setSeekbarEnabled(true);
            videoPbView.setTimeLapsedValue("00:00");
            videoPbView.setTimeDurationValue(ConvertTools.secondsToMinuteOrHours(tempDuration / 100));
            videoPbView.setSeekBarMaxValue(tempDuration);
            videoPbView.setDownloadBtnEnabled(false);
            AppLog.i(TAG, "has start the GetVideoFrameThread() to get play video");
        } else if (videoPbMode == VideoPbMode.MODE_VIDEO_PAUSE) {
            resumeVideoPb();
        } else if (videoPbMode == VideoPbMode.MODE_VIDEO_PLAY) {
            pauseVideoPb();
        }
    }
    private void resumeVideoPb() {
        AppLog.i(TAG, "mode == MODE_VIDEO_PAUSE");
        if (panoramaVideoPlayback.resumePlayback() == false) {
            MyToast.show(activity, R.string.dialog_failed);
            AppLog.i(TAG, "failed to resumePlayback");
            return;
        }
        videoPbView.setPlayBtnSrc(R.drawable.ic_pause_white_36dp);
        videoPbMode = VideoPbMode.MODE_VIDEO_PLAY;
        videoPbView.setDownloadBtnEnabled(false);
    }
    private void pauseVideoPb() {
        AppLog.i(TAG, "begin pause the playing");
        if (panoramaVideoPlayback.pausePlayback() == false) {
            MyToast.show(activity, R.string.dialog_failed);
            AppLog.i(TAG, "failed to pausePlayback");
            return;
        }
        videoPbView.setPlayBtnSrc(R.drawable.ic_play_arrow_white_36dp);
        videoPbMode = VideoPbMode.MODE_VIDEO_PAUSE;
        videoPbView.showLoadingCircle(false);
        videoPbView.setDownloadBtnEnabled(true);
        return;
    }
    public void seekBarOnStopTrackingTouch() {
        AppLog.d(TAG, "seekBarOnStopTrackingTouch lastSeekBarPosition=" + lastSeekBarPosition + " videoDuration=" + videoDuration);
        int curProgress = videoPbView.getSeekBarProgress();
        if (videoDuration - curProgress < 500) {
            videoPbView.setSeekbarEnabled(false);
        }
        if (videoDuration - curProgress < 100) {
            lastSeekBarPosition = videoDuration - 100;
            videoPbView.setSeekBarProgress(lastSeekBarPosition);
        } else {
            lastSeekBarPosition = curProgress;
        }
        videoPbView.setTimeLapsedValue(ConvertTools.secondsToMinuteOrHours(lastSeekBarPosition / 100));
        MyProgressDialog.showProgressDialog(activity, R.string.action_processing);
        new Thread(new Runnable() {
            @Override
            public void run() {
                if (panoramaVideoPlayback.videoSeek(lastSeekBarPosition / 100.0)) {
                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    panoramaVideoPlayback.resumePlayback();
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            videoPbView.setPlayBtnSrc(R.drawable.ic_pause_white_36dp);
                            videoPbView.setDownloadBtnEnabled(false);
                            videoPbView.showLoadingCircle(true);
                        }
                    });
                } else {
                    handler.post(new Runnable() {
                        @Override
                        public void run() {
                            MyProgressDialog.closeProgressDialog();
                            videoPbView.setSeekBarProgress(lastSeekBarPosition);
                            MyToast.show(activity, R.string.dialog_failed);
                        }
                    });
                }
                videoPbMode = VideoPbMode.MODE_VIDEO_PLAY;
                needUpdateSeekBar = true;
            }
        }).start();
    }
    public void seekBarOnStartTrackingTouch() {
        needUpdateSeekBar = false;
        lastSeekBarPosition = videoPbView.getSeekBarProgress();
        panoramaVideoPlayback.pausePlayback();
        videoPbView.showLoadingCircle(false);
        videoPbMode = VideoPbMode.MODE_VIDEO_PAUSE;
    }
    public void setTimeLapsedValue(int progress) {
        if (needUpdateSeekBar && videoDuration > 0 && (videoDuration - progress) < 500) {
            AppLog.i(TAG, "setTimeLapsedValue setSeekbarEnabled");
            videoPbView.setSeekbarEnabled(false);
        }
        videoPbView.setTimeLapsedValue(ConvertTools.secondsToMinuteOrHours(progress / 100));
    }
    public void initSurface(SurfaceHolder surfaceHolder) {
        AppLog.i(TAG, "begin initSurface");
        videoStreaming.initSurface(enableRender, surfaceHolder, curVideoFile.getFileWidth(), curVideoFile.getFileHeight());
        if (enableRender) {
            locate(FIXED_INSIDE_DISTANCE);
        }
        if (!enableRender) {
            int width = videoPbView.getSurfaceViewWidth();
            int heigth = videoPbView.getSurfaceViewHeight();
            AppLog.i(TAG, "SurfaceViewWidth=" + width + " SurfaceViewHeight=" + heigth);
            if (width <= 0 || heigth <= 0) {
                width = 1080;
                heigth = 1920;
            }
            videoStreaming.setViewParam(width, heigth);
        }
        AppLog.i(TAG, "end initSurface");
    }
    public void stopVideoStream() {
        AppLog.i(TAG, "Begin stopVideoStream");
        removeEventListener();
        if (enableRender) {
            removeGyroscopeListener();
        }
        videoPbView.setTimeLapsedValue("00:00");
        videoStreaming.stop();
        videoPbView.setPlayBtnSrc(R.drawable.ic_play_arrow_white_36dp);
        videoPbView.setSeekBarProgress(0);
        videoPbView.setSeekBarSecondProgress(0);
        videoPbView.setTopBarVisibility(View.VISIBLE);
        videoPbView.setBottomBarVisibility(View.VISIBLE);
        videoPbView.showLoadingCircle(false);
        videoPbView.setSeekbarEnabled(false);
        videoPbMode = VideoPbMode.MODE_VIDEO_IDLE;
        videoPbView.setDownloadBtnEnabled(true);
        AppLog.i(TAG, "End stopVideoStream");
    }
    public void locate(float progerss) {
        if (enableRender) {
            panoramaVideoPlayback.locate(progerss);
        }
    }
    public void destroyVideo(int iCatchSphereType) {
        removeEventListener();
        if (enableRender) {
            removeGyroscopeListener();
        }
        videoStreaming.removeSurface(iCatchSphereType);
        videoStreaming.stop();
        videoStreaming.release();
    }
    public void rotateB(MotionEvent e, float prevX, float prevY) {
        ICatchGLPoint prev = new ICatchGLPoint(prevX, prevY);
        ICatchGLPoint curr = new ICatchGLPoint(e.getX(), e.getY());
        panoramaVideoPlayback.rotate(prev, curr);
    }
    public void delete() {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_PLAY) {
            pauseVideoPb();
        }
        showDeleteEnsureDialog();
    }
    public void download() {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_PLAY) {
            pauseVideoPb();
        }
        showDownloadEnsureDialog();
    }
    public void back() {
        stopVideoStream();
        removeEventListener();
        Intent intent = new Intent();
        intent.putExtra("hasDeleted", hasDeleted);
        intent.putExtra("fileType", fileType.ordinal());
        activity.setResult(1000, intent);
        activity.finish();
    }
    private class VideoPbHandler extends Handler {
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case AppMessage.EVENT_CACHE_STATE_CHANGED:
                    if (cacheFlag == false) {
                        return;
                    }
                    if (msg.arg1 == 1) {
                        videoPbView.showLoadingCircle(true);
                        waitForCaching = true;
                    } else if (msg.arg1 == 2) {
                        videoPbView.showLoadingCircle(false);
                        waitForCaching = false;
                        needUpdateSeekBar = true;
                    }
                    break;
                case AppMessage.EVENT_CACHE_PROGRESS_NOTIFY:
                    if (cacheFlag == false) {
                        return;
                    }
                    if (videoPbMode == VideoPbMode.MODE_VIDEO_IDLE || videoPbMode == VideoPbMode.MODE_VIDEO_PAUSE) {
                        return;
                    }
                    if (waitForCaching) {
                        videoPbView.setLoadPercent(msg.arg1);
                    }
                    videoPbView.setSeekBarSecondProgress(msg.arg2);
                    break;
                case SDKEvent.EVENT_VIDEO_PLAY_PTS:
                    double temp = (double) msg.obj;
                    updatePbSeekbar(temp);
                    break;
                case SDKEvent.EVENT_VIDEO_PLAY_CLOSED:
                    AppLog.i(TAG, "receive EVENT_VIDEO_PLAY_CLOSED");
                    removeEventListener();
                    removeGyroscopeListener();
                    if (videoPbMode == VideoPbMode.MODE_VIDEO_PLAY || videoPbMode == VideoPbMode.MODE_VIDEO_PAUSE) {
                        cacheFlag = false;
                        stopVideoStream();
                        videoPbMode = VideoPbMode.MODE_VIDEO_IDLE;
                        videoPbView.setProgress(0);
                    }
                    break;
                case AppMessage.MESSAGE_CANCEL_VIDEO_DOWNLOAD:
                    AppLog.d(TAG, "receive CANCEL_VIDEO_DOWNLOAD_SUCCESS");
                    if (singleDownloadDialog != null) {
                        singleDownloadDialog.dismissDownloadDialog();
                    }
                    if (downloadProgressTimer != null) {
                        downloadProgressTimer.cancel();
                    }
                    if (fileOperation.cancelDownload() == false) {
                        MyToast.show(activity, R.string.dialog_cancel_downloading_failed);
                        break;
                    }
                    try {
                        Thread.currentThread().sleep(200);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    String filePath = StorageUtil.getRootPath(activity) + AppInfo.DOWNLOAD_PATH_VIDEO +
                            curVideoFile.getFileName();
                    File file = new File(filePath);
                    if (file.exists()) {
                        file.delete();
                    }
                    videoPbMode = VideoPbMode.MODE_VIDEO_IDLE;
                    MyToast.show(activity, R.string.dialog_cancel_downloading_succeeded);
                    break;
                case AppMessage.MESSAGE_VIDEO_STREAM_NO_EIS_INFORMATION:
                    enableEIS(false);
                    videoPbView.setEisSwitchChecked(false);
                    break;
            }
        }
    }
    public void showBar(boolean isShowBar) {
        if (isShowBar) {
            if (videoPbMode == VideoPbMode.MODE_VIDEO_PLAY) {
                videoPbView.setBottomBarVisibility(View.GONE);
                videoPbView.setTopBarVisibility(View.GONE);
            }
        } else {
            videoPbView.setBottomBarVisibility(View.VISIBLE);
            videoPbView.setTopBarVisibility(View.VISIBLE);
            if (videoPbMode != VideoPbMode.MODE_VIDEO_PLAY) {
            }
        }
    }
    private class DownloadThread implements Runnable {
        private String TAG = "DownloadThread";
        String fileType;
        private String targetPath;
        DownloadThread(String targetPath) {
            this.targetPath = targetPath;
        }
        @Override
        public void run() {
            AppLog.d(TAG, "begin DownloadThread");
            AppInfo.isDownloading = true;
            boolean temp = fileOperation.downloadFile(curVideoFile, targetPath);
            if (temp == false) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (singleDownloadDialog != null) {
                            singleDownloadDialog.dismissDownloadDialog();
                        }
                        if (downloadProgressTimer != null) {
                            downloadProgressTimer.cancel();
                        }
                        MyToast.show(activity, R.string.message_download_failed);
                    }
                });
                AppInfo.isDownloading = false;
                return;
            }
            if (targetPath.endsWith(".mov") || targetPath.endsWith(".MOV")) {
                fileType = "video/mov";
            } else {
                fileType = "video/mp4";
            }
//            MediaRefresh.addMediaToDB(activity, targetPath, fileType);
            MediaRefresh.scanFileAsync(activity,targetPath);
            AppLog.d(TAG, "end downloadFile temp =" + temp);
            AppInfo.isDownloading = false;
            final String message = activity.getResources().getString(R.string.message_download_to).replace("$1$", targetPath);
            handler.post(new Runnable() {
                @Override
                public void run() {
                    if (singleDownloadDialog != null) {
                        singleDownloadDialog.dismissDownloadDialog();
                    }
                    if (downloadProgressTimer != null) {
                        downloadProgressTimer.cancel();
                    }
                    MyToast.show(activity, message);
                    activity.finish();
                    Intent intent = new Intent();
                    intent.putExtra("CUR_POSITION", 1);
                    intent.setClass(activity, LocalMultiPbActivity.class);
                    activity.startActivity(intent);
                }
            });
            AppLog.d(TAG, "end DownloadThread");
        }
    }
    private class DeleteThread implements Runnable {
        @Override
        public void run() {
            Boolean retValue = false;
            retValue = fileOperation.deleteFile(curVideoFile);
            if (retValue == false) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                        MyToast.show(activity, R.string.dialog_delete_failed_single);
                    }
                });
            } else {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                        RemoteFileHelper.getInstance().remove(fileList.get(curVideoPosition), fileType);
                        hasDeleted = true;
                        Intent intent = new Intent();
                        intent.putExtra("hasDeleted", hasDeleted);
                        intent.putExtra("fileType", fileType.ordinal());
                        activity.setResult(1000, intent);
                        activity.finish();
                    }
                });
            }
            AppLog.d(TAG, "end DeleteThread");
        }
    }
    public void showDownloadEnsureDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity, R.style.MyAlertDialog);
        builder.setCancelable(false);
        builder.setTitle(R.string.dialog_downloading_single);
        long videoFileSize = 0;
        videoFileSize = fileList.get(curVideoPosition).getFileSizeInteger() / 1024 / 1024;
        AppLog.d(TAG, "video FileSize=" + videoFileSize);
        long minute = videoFileSize / 60;
        long seconds = videoFileSize % 60;
        CharSequence what = activity.getResources().getString(R.string.gallery_download_with_vid_msg).replace("$1$", "1").replace("$3$", String.valueOf
                (seconds)).replace("$2$", String.valueOf(minute));
        builder.setMessage(what);
        builder.setNegativeButton(R.string.gallery_download, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                AppLog.d(TAG, "showProgressDialog");
                if (videoPbMode == VideoPbMode.MODE_VIDEO_PAUSE) {
                    stopVideoStream();
                }
                if (SystemInfo.getSDFreeSize(activity) < curVideoFile.getFileSize()) {
                    dialog.dismiss();
                    MyToast.show(activity, R.string.text_sd_card_memory_shortage);
                } else {
                    singleDownloadDialog = new SingleDownloadDialog(activity, curVideoFile);
                    singleDownloadDialog.setBackBtnOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            handler.obtainMessage(AppMessage.MESSAGE_CANCEL_VIDEO_DOWNLOAD, 0, 0).sendToTarget();
                        }
                    });
                    singleDownloadDialog.showDownloadDialog();
                    executor = Executors.newSingleThreadExecutor();
                    String path = StorageUtil.getRootPath(activity) + AppInfo.DOWNLOAD_PATH_VIDEO;
                    String fileName = curVideoFile.getFileName();
                    AppLog.d(TAG, "------------fileName =" + fileName);
                    FileOper.createDirectory(path);
                    String downloadFilePath = FileTools.chooseUniqueFilename(path + fileName);
                    executor.submit(new DownloadThread(downloadFilePath), null);
                    downloadProgressTimer = new Timer();
                    downloadProgressTimer.schedule(new DownloadProcessTask(downloadFilePath), 0, 1000);
                }
            }
        });
        builder.setPositiveButton(R.string.gallery_cancel, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                if (videoPbMode == VideoPbMode.MODE_VIDEO_PAUSE) {
                    resumeVideoPb();
                }
                dialog.dismiss();
            }
        });
        builder.create().show();
    }
    public void showDeleteEnsureDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setCancelable(false);
        builder.setTitle(activity.getResources().getString(R.string.gallery_delete_des).replace("$1$", "1"));
        builder.setNegativeButton(R.string.gallery_delete, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                if (videoPbMode == VideoPbMode.MODE_VIDEO_PAUSE) {
                    stopVideoStream();
                }
                MyProgressDialog.showProgressDialog(activity, R.string.dialog_deleting);
                executor = Executors.newSingleThreadExecutor();
                executor.submit(new DeleteThread(), null);
            }
        });
        builder.setPositiveButton(R.string.gallery_cancel, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int whichButton) {
                if (videoPbMode == VideoPbMode.MODE_VIDEO_PAUSE) {
                    resumeVideoPb();
                }
                dialog.dismiss();
            }
        });
        builder.create().show();
    }
    class DownloadProcessTask extends TimerTask {
        int downloadProgress = 0;
        long fileSize;
        long curFileLength;
        String curDownloadFile;
        public DownloadProcessTask(String downloadFile) {
            curDownloadFile = downloadFile;
        }
        @Override
        public void run() {
            File file = new File(curDownloadFile);
            fileSize = curVideoFile.getFileSize();
            curFileLength = file.length();
            if (file != null) {
                if (curFileLength == fileSize) {
                    downloadProgress = 100;
                } else {
                    downloadProgress = (int) (file.length() * 100 / fileSize);
                }
            } else {
                downloadProgress = 0;
            }
            final DownloadInfo downloadInfo = new DownloadInfo(curVideoFile, fileSize, curFileLength, downloadProgress, false);
            handler.post(new Runnable() {
                @Override
                public void run() {
                    if (singleDownloadDialog != null) {
                        singleDownloadDialog.updateDownloadStatus(downloadInfo);
                    }
                    AppLog.d(TAG, "update Process downloadProgress=" + downloadProgress);
                }
            });
            AppLog.d(TAG, "end DownloadProcessTask");
        }
    }
    public void onSufaceViewTouchDown(MotionEvent event) {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_IDLE) {
            return;
        }
        touchMode = TouchMode.DRAG;
        mPreviousY = event.getY();
        mPreviousX = event.getX();
        beforeLenght = 0;
        afterLenght = 0;
    }
    public void onSufaceViewPointerDown(MotionEvent event) {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_IDLE) {
            return;
        }
        if (event.getPointerCount() == 2) {
            touchMode = TouchMode.ZOOM;
            beforeLenght = getDistance(event);
        }
    }
    public void onSufaceViewTouchMove(MotionEvent event) {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_IDLE) {
            return;
        }
        if (touchMode == TouchMode.DRAG) {
            rotateB(event, mPreviousX, mPreviousY);
            mPreviousY = event.getY();
            mPreviousX = event.getX();
        } else if (touchMode == TouchMode.ZOOM) {
            afterLenght = getDistance(event);
            float gapLenght = afterLenght - beforeLenght;
            if (Math.abs(gapLenght) > 5f) {
                float scale_temp = afterLenght / beforeLenght;
                this.setScale(scale_temp);
                beforeLenght = afterLenght;
            }
        }
    }
    float getDistance(MotionEvent event) {
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float) StrictMath.sqrt(x * x + y * y);
    }
    void setScale(float scale) {
        if ((currentZoomRate >= MAX_ZOOM && scale > 1) || (currentZoomRate <= MIN_ZOOM && scale < 1)) {
            return;
        }
        float temp = currentZoomRate * scale;
        if (scale > 1) {
            if (temp <= MAX_ZOOM) {
                currentZoomRate = currentZoomRate * scale;
                zoom(currentZoomRate);
            } else {
                currentZoomRate = MAX_ZOOM;
                zoom(currentZoomRate);
            }
        } else if (scale < 1) {
            if (temp >= MIN_ZOOM) {
                currentZoomRate = currentZoomRate * scale;
                zoom(currentZoomRate);
            } else {
                currentZoomRate = MIN_ZOOM;
                zoom(currentZoomRate);
            }
        }
    }
    private void zoom(float currentZoomRate) {
        locate(1 / currentZoomRate);
    }
    public void onSufaceViewTouchUp() {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_IDLE) {
            return;
        }
        touchMode = TouchMode.NONE;
    }
    public void onSufaceViewTouchPointerUp() {
        if (videoPbMode == VideoPbMode.MODE_VIDEO_IDLE) {
            return;
        }
        touchMode = TouchMode.NONE;
    }
    @Override
    public void onSensorChanged(SensorEvent event) {
        if (event.sensor == null) {
            return;
        }
        if (event.sensor.getType() == Sensor.TYPE_GYROSCOPE) {
            float speedX = event.values[0];
            float speedY = event.values[1];
            float speedZ = event.values[2];
            rotate(speedX, speedY, speedZ, event.timestamp);
        }
    }
    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
    }
    private void rotate(float speedX, float speedY, float speedZ, long timestamp) {
        int rotation = activity.getWindowManager().getDefaultDisplay().getRotation();
        panoramaVideoPlayback.rotate(rotation, speedX, speedY, speedZ, timestamp);
    }
    private void registerGyroscopeSensor() {
        sensorManager = (SensorManager) activity.getSystemService(Context.SENSOR_SERVICE);
        gyroscopeSensor = sensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE);
        sensorManager.registerListener(this, gyroscopeSensor, SensorManager.SENSOR_DELAY_GAME);
    }
    protected void removeGyroscopeListener() {
        if (sensorManager != null) {
            sensorManager.unregisterListener(this);
            sensorManager = null;
        }
    }
    public void setDrawingArea(int windowW, int windowH) {
        videoStreaming.setDrawingArea(windowW, windowH);
    }
    public void redrawSurface() {
        int width = videoPbView.getSurfaceViewWidth();
        int heigth = videoPbView.getSurfaceViewHeight();
        videoStreaming.setViewParam(width, heigth);
        videoStreaming.setSurfaceViewArea();
    }
    public void setPanoramaType() {
        if (curPanoramaType == ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE) {
            videoStreaming.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID;
            videoPbView.setPanoramaTypeImageResource(R.drawable.asteroid);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        } else if (curPanoramaType == ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_ASTEROID) {
            videoStreaming.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_VIRTUAL_R);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_VIRTUAL_R;
            videoPbView.setPanoramaTypeImageResource(R.drawable.vr);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE);
        } else {
            videoStreaming.changePanoramaType(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE);
            curPanoramaType = ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE;
            videoPbView.setPanoramaTypeImageResource(R.drawable.panorama);
            activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        }
    }
    public void showMoreSettingLayout(boolean isShowBar) {
        if (isShowBar) {
            videoPbView.setBottomBarVisibility(View.GONE);
            videoPbView.setTopBarVisibility(View.GONE);
            videoPbView.setMoreSettingLayoutVisibility(View.VISIBLE);
        } else {
            videoPbView.setBottomBarVisibility(View.VISIBLE);
            videoPbView.setTopBarVisibility(View.VISIBLE);
            videoPbView.setMoreSettingLayoutVisibility(View.GONE);
        }
    }
    public void enableEIS(boolean enable) {
        StreamStablization streamStablization = panoramaVideoPlayback.getStreamStablization();
        if (streamStablization == null) {
            return;
        }
        if (enable) {
            streamStablization.enableStablization();
        } else {
            streamStablization.disableStablization();
        }
    }
    public void setSdCardEventListener() {
        GlobalInfo.getInstance().setOnEventListener(new GlobalInfo.OnEventListener() {
            @Override
            public void eventListener(int sdkEventId) {
                switch (sdkEventId) {
                    case SDKEvent.EVENT_SDCARD_REMOVED:
                        videoStreaming.stopForSdRemove();
                        RemoteFileHelper.getInstance().clearAllFileList();
                        AppDialog.showDialogWarn(activity, R.string.dialog_card_removed_and_back, false, new AppDialog.OnDialogSureClickListener() {
                            @Override
                            public void onSure() {
                                back();
                            }
                        });
                        break;
                }
            }
        });
    }
}

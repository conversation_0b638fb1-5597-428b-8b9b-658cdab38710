package com.icatch.mobilecam.utils;

import android.content.Context;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;
import android.webkit.MimeTypeMap;

import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PathUtils;
import com.coremedia.iso.IsoFile;
import com.coremedia.iso.boxes.HandlerBox;
import com.coremedia.iso.boxes.MediaBox;
import com.coremedia.iso.boxes.MovieHeaderBox;
import com.coremedia.iso.boxes.SampleTableBox;
import com.coremedia.iso.boxes.TimeToSampleBox;
import com.coremedia.iso.boxes.TrackBox;
import com.coremedia.iso.boxes.TrackHeaderBox;
import com.daasuu.mp4compose.Rotation;
import com.daasuu.mp4compose.composer.Mp4Composer;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

import Jni.FFmpegCmd;
import VideoHandle.OnEditorListener;

/**
 * 视频工具
 * <p>
 * Author:huangwubin
 * <p>
 * changeLogs:
 * 2025/5/22: First created this class.
 */
public class VideoUtil {
    private static final String TAG = "VideoUtils";

    // 在类加载时或应用初始化时调用一次硬件检测
    static {
        HwAccelChecker.detectSupportedHwEncoder();
    }

    /**
     * 获取视频文件的分辨率
     *
     * @param context   上下文
     * @param videoPath 视频文件的路径或 Uri 字符串
     * @return 一个包含宽度和高度的整型数组 int[]{width, height}，如果获取失败则返回 null。
     */
    public static int[] getVideoResolution(Context context, String videoPath) {
        if (!isVideo(videoPath)) {
            LogUtils.e(TAG, "不是视频：" + videoPath);
            return null;
        }
        VideoMetadata metadata = getMetadata(context, videoPath);
        if (metadata == null){
            return  null;
        }else{
            return new int[]{metadata.width,metadata.height};
        }
    }

    /**
     * 判断文件是否视频
     *
     * @param filePath
     * @return
     */
    public static boolean isVideo(String filePath) {
        if (!FileUtils.isFileExists(filePath)) {
            LogUtils.e(TAG, "Video path is null or empty.");
            return false;
        }
        String fileExtension = FileUtils.getFileExtension(filePath);
        if (TextUtils.isEmpty(fileExtension)) {
            return false;
        }
        String mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(fileExtension.toLowerCase());
        return !TextUtils.isEmpty(mimeType) && mimeType.startsWith("video/");
    }

    //视频逆时针旋转90 度
    public static void rotateVideo90(Context context, String filePath) { // Added externalListener
        if (!FileUtils.isFileExists(filePath)) {
            LogUtils.eTag(TAG, "原始文件不存在: " + filePath);
            LogUtils.file(TAG,"A6Max旋转视频：原始文件不存在: " + filePath);
            return;
        }

        // 记录文件信息用于调试
        File inputFile = new File(filePath);
        LogUtils.dTag(TAG, "开始旋转视频，文件路径: " + filePath + ", 文件大小: " + inputFile.length());
        LogUtils.file(TAG,"A6Max旋转视频：开始旋转，文件路径: " + filePath + ", 文件大小: " + inputFile.length());

        VideoMetadata originalMetadata = getMetadata(context, filePath);

        if (originalMetadata == null || originalMetadata.width == 0 || originalMetadata.height == 0) {
            LogUtils.eTag(TAG, "无法获取原始视频元数据或分辨率无效: " + filePath);
            LogUtils.file(TAG,"A6Max旋转视频：无法获取原始视频元数据或分辨率无效: " + filePath);
            return;
        }

        String tempOutputPathInCache = new File(PathUtils.getInternalAppCachePath(),
                "temp_rotate_" + FileUtils.getFileName(filePath)).getAbsolutePath();
        File ffmpegOutputCacheFile = new File(tempOutputPathInCache);

        // --- FFmpeg Command Construction ---
        ArrayList<String> cmdList = new ArrayList<>();
        cmdList.add("ffmpeg"); // Your ffmpeg executable name/path if not in PATH
        cmdList.add("-y");     // Overwrite output file if it exists

        cmdList.add("-i");
        cmdList.add(filePath);

        // Video Filters
        // transpose=1: 90 degrees clockwise.
        // If epVideo.rotation(90,false) had a different meaning (e.g. related to specific landscape/portrait handling)
        // you might need transpose=2 (90 counter-clockwise) or other values.
        // For a standard 90-degree clockwise rotation:
        int newWidth = originalMetadata.height;  // Original height becomes new width
        int newHeight = originalMetadata.width; // Original width becomes new height

        String filterComplex = String.format(Locale.ROOT, "transpose=2,scale=%d:%d,setdar=%d/%d",
                newWidth, newHeight, newWidth, newHeight);
        cmdList.add("-filter_complex");
        cmdList.add(filterComplex);

        LogUtils.dTag(TAG, "FFmpeg滤镜参数: " + filterComplex);
        LogUtils.file(TAG,"A6Max旋转视频：FFmpeg滤镜参数: " + filterComplex);

        // Frame Rate
        float targetFps;
        if (originalMetadata.frameRate > 0) {
            targetFps = Math.min(originalMetadata.frameRate, 30.0f); // Cap at 30fps, or use original if lower
            LogUtils.dTag(TAG, "原始视频帧率: " + originalMetadata.frameRate + ". 设置目标帧率: " + Math.round(targetFps) + "fps");
        } else {
            targetFps = 30.0f; // Default 30fps if original unknown
            LogUtils.wTag(TAG, "无法获取原始帧率或帧率为0，设置默认帧率: 30fps");
        }
        cmdList.add("-r");
        cmdList.add(String.valueOf(Math.round(targetFps)));

        // Bitrate (单位: kbps for FFmpeg -b:v option)
        // 针对A6Max旋转视频优化：高码率视频降低到原来的一半以提高处理速度
        int targetBitrateKbps;
        if (originalMetadata.bitRate > 0) {
            int originalBitrateKbps = (int) (originalMetadata.bitRate / 1000);

            // 对于高码率视频，降低到原来的一半
            if (originalBitrateKbps > 10000) { // 10Mbps以上降低到一半
                targetBitrateKbps = originalBitrateKbps / 2;
                LogUtils.dTag(TAG, "高码率视频优化: 原始码率 " + originalBitrateKbps + "kbps 降低到一半 " + targetBitrateKbps + "kbps");
            } else {
                targetBitrateKbps = originalBitrateKbps; // 保持原码率
            }

            // Ensure bitrate is not excessively low
            targetBitrateKbps = Math.max(targetBitrateKbps, 2000); // 最低2Mbps保证画质
            LogUtils.dTag(TAG, "原始码率: " + originalMetadata.bitRate + " bps. 设置目标视频码率: " + targetBitrateKbps + " kbps");
        } else {
            LogUtils.wTag(TAG, "无法获取原始码率或码率为0，将根据分辨率估算码率.");
            int pixels = newWidth * newHeight; // Use new dimensions for estimation
            if (pixels <= (640 * 480)) { // SD
                targetBitrateKbps = 1000; // 1 Mbps
            } else if (pixels <= (1280 * 720)) { // HD
                targetBitrateKbps = 2500; // 2.5 Mbps
            } else if (pixels <= (1920 * 1080)) { // Full HD
                targetBitrateKbps = 4000; // 4 Mbps
            } else { // 2K/4K+
                targetBitrateKbps = 6000; // 6 Mbps (降低了默认值)
            }
            LogUtils.dTag(TAG, String.format(Locale.US, "根据新分辨率 %dx%d 估算码率: %d kbps", newWidth, newHeight, targetBitrateKbps));
        }
        cmdList.add("-b:v");
        cmdList.add(targetBitrateKbps + "k");

        // Preset for encoding speed/compression efficiency
        // "superfast" is very fast but less efficient.
        // "fast" or "medium" offer better balance. "medium" is default if not specified.
        cmdList.add("-preset");
        cmdList.add("fast");

        // Audio Codec: Copy audio stream to avoid re-encoding and quality loss, and save time
        // If you need to re-encode audio (e.g., change format or bitrate), use:
        // cmdList.add("-c:a"); cmdList.add("aac"); cmdList.add("-b:a"); cmdList.add("128k");
//        cmdList.add("-c:a");
//        cmdList.add("copy");
        cmdList.add("-c:a");
        cmdList.add("aac");
        cmdList.add("-b:a");
        cmdList.add("192k");

        // Output file
        cmdList.add(tempOutputPathInCache);

        String[] cmds = cmdList.toArray(new String[0]);
        // --- End FFmpeg Command Construction ---

        // 记录完整的 FFmpeg 命令用于调试
        StringBuilder cmdLog = new StringBuilder("FFmpeg命令: ");
        for (String cmd : cmds) {
            cmdLog.append(cmd).append(" ");
        }
        LogUtils.dTag(TAG, cmdLog.toString());
        LogUtils.file(TAG,"A6Max旋转视频：" + cmdLog.toString());

        final CountDownLatch latch = new CountDownLatch(1);
        final boolean[] overallSuccess = {false};
        try {
            FFmpegCmd.exec(cmds, 0, new OnEditorListener() {
                @Override
                public void onSuccess() {
                    LogUtils.dTag(TAG, "FFmpeg旋转成功, 输出至: " + tempOutputPathInCache);
                    LogUtils.file(TAG,"A6Max旋转视频：FFmpeg旋转成功, 输出至: " + tempOutputPathInCache);
                    File originalFileLocal = new File(filePath);
                    File parentDir = originalFileLocal.getParentFile();
                    if (parentDir == null) {
                        parentDir = new File(originalFileLocal.getAbsolutePath()).getParentFile();
                        if (parentDir == null) {
                            parentDir = new File(".");
                        }
                    }
                    String intermediateName = "intermediate_rotated_" + originalFileLocal.getName();
                    File intermediateFile = new File(parentDir, intermediateName);
                    boolean replacementSuccess = false;
                    try {
                        if (FileUtils.copy(ffmpegOutputCacheFile.getAbsolutePath(), intermediateFile.getAbsolutePath())) {
                            LogUtils.dTag(TAG, "已复制到中间文件: " + intermediateFile.getAbsolutePath());
                            LogUtils.file(TAG,"A6Max旋转视频：已复制到中间文件: " + intermediateFile.getAbsolutePath());
                            if (originalFileLocal.delete()) {
                                LogUtils.dTag(TAG, "已删除原始文件: " + filePath);
                                if (intermediateFile.renameTo(originalFileLocal)) {
                                    LogUtils.dTag(TAG, "视频旋转并替换成功: " + filePath);
                                    LogUtils.file(TAG,"A6Max旋转视频：视频旋转并替换成功: " + filePath);
                                    replacementSuccess = true;
                                } else {
                                    LogUtils.eTag(TAG, "重命名中间文件 " + intermediateFile.getName() + " 到 " + originalFileLocal.getName() + " 失败.");
                                    LogUtils.iTag(TAG, "尝试通过复制恢复: " + intermediateFile.getAbsolutePath() + " 至 " + filePath);
                                    if (FileUtils.copy(intermediateFile.getAbsolutePath(), filePath)) {
                                        LogUtils.dTag(TAG, "通过复制恢复成功: " + filePath);
                                        replacementSuccess = true;
                                        if (!intermediateFile.delete()) {
                                            LogUtils.wTag(TAG, "清理复制源中间文件失败: " + intermediateFile.getAbsolutePath());
                                            LogUtils.file(TAG,"A6Max旋转视频：清理复制源中间文件失败: " + intermediateFile.getAbsolutePath());
                                        }
                                    } else {
                                        LogUtils.eTag(TAG, "复制恢复也失败. 旋转后的文件保留在: " + intermediateFile.getAbsolutePath() + ". 原始文件已丢失!");
                                    }
                                }
                            } else {
                                LogUtils.eTag(TAG, "删除原始文件失败: " + filePath + ". 无法完成替换.");
                                LogUtils.file(TAG,"A6Max旋转视频：删除原始文件失败: " + filePath + ". 无法完成替换.");
                                if (intermediateFile.exists() && !intermediateFile.delete()) {
                                    LogUtils.wTag(TAG, "清理因删除原始文件失败而残留的中间文件失败: " + intermediateFile.getAbsolutePath());
                                }
                            }
                        } else {
                            LogUtils.eTag(TAG, "从缓存 " + ffmpegOutputCacheFile.getAbsolutePath() + " 复制到中间文件 " + intermediateFile.getAbsolutePath() + " 失败.");
                            LogUtils.file(TAG,"A6Max旋转视频：从缓存 " + ffmpegOutputCacheFile.getAbsolutePath() + " 复制到中间文件 " + intermediateFile.getAbsolutePath() + " 失败.");
                            if (intermediateFile.exists() && !intermediateFile.delete()) {
                                LogUtils.wTag(TAG, "清理因复制到中间文件失败而残留的中间文件失败: " + intermediateFile.getAbsolutePath());
                            }
                        }
                    } catch (Exception e) {
                        LogUtils.eTag(TAG, "替换视频过程中发生严重异常: " + e.getMessage(), e);
                        LogUtils.file(TAG,"A6Max旋转视频：替换视频过程中发生严重异常: " + e.getMessage());
                        // e.printStackTrace(); // LogUtils might do this
                        if (intermediateFile.exists() && !intermediateFile.delete()) {
                            LogUtils.wTag(TAG, "清理异常时中间文件失败: " + intermediateFile.getAbsolutePath());
                        }
                    } finally {
                        overallSuccess[0] = replacementSuccess;
                        if (ffmpegOutputCacheFile.exists()) {
                            LogUtils.dTag(TAG, "清理FFmpeg缓存文件: " + ffmpegOutputCacheFile.getAbsolutePath());
                            if (!ffmpegOutputCacheFile.delete()) {
                                LogUtils.wTag(TAG, "清理FFmpeg缓存文件失败: " + ffmpegOutputCacheFile.getAbsolutePath());
                            }
                        }
                        if (intermediateFile.exists() && (!replacementSuccess || !intermediateFile.getAbsolutePath().equals(originalFileLocal.getAbsolutePath()))) {
                            LogUtils.dTag(TAG, "清理残留的中间文件: " + intermediateFile.getAbsolutePath());
                            if (!intermediateFile.delete()) {
                                LogUtils.wTag(TAG, "清理残留中间文件失败: " + intermediateFile.getAbsolutePath());
                            }
                        }
                        latch.countDown();
                    }
                }

                @Override
                public void onFailure() {
                    LogUtils.eTag(TAG, "FFmpeg视频旋转命令执行失败: " + filePath);
                    LogUtils.file(TAG,"A6Max旋转视频：FFmpeg视频旋转命令执行失败: " + filePath);

                    // 清理当前缓存文件
                    if (ffmpegOutputCacheFile.exists()) {
                        ffmpegOutputCacheFile.delete();
                    }

                    // FFmpeg失败时，尝试备用方案
                    LogUtils.wTag(TAG, "FFmpeg失败，尝试备用方案");
                    LogUtils.file(TAG,"A6Max旋转视频：FFmpeg失败，尝试备用方案");

                    tryAlternativeRotationMethod(filePath, tempOutputPathInCache, ffmpegOutputCacheFile, latch, overallSuccess);
                }

                @Override
                public void onProgress(float progress) {
                }
            });

            latch.await(); // 无限等待，不设置超时
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            LogUtils.eTag(TAG, "视频旋转等待过程被中断: " + e.getMessage(), e);
            LogUtils.file(TAG,"A6Max旋转视频：视频旋转等待过程被中断: " + e.getMessage());
            if (ffmpegOutputCacheFile.exists() && !ffmpegOutputCacheFile.delete()) {
                LogUtils.wTag(TAG, "清理FFmpeg缓存文件失败 (中断): " + ffmpegOutputCacheFile.getAbsolutePath());
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "视频旋转主流程发生异常: " + e.getMessage(), e);
            LogUtils.file(TAG,"A6Max旋转视频：视频旋转主流程发生异常: " + e.getMessage());
            // e.printStackTrace(); // LogUtils might do this
            if (ffmpegOutputCacheFile.exists() && !ffmpegOutputCacheFile.delete()) {
                LogUtils.wTag(TAG, "清理FFmpeg缓存文件失败 (主流程异常): " + ffmpegOutputCacheFile.getAbsolutePath());
            }
        } finally {
            if (!overallSuccess[0]) {
                LogUtils.wTag(TAG, "视频旋转最终判定为失败 (在最外层finally检查): " + filePath);
                LogUtils.file(TAG,"A6Max旋转视频：视频旋转最终判定为失败 (在最外层finally检查): " + filePath);
                // Ensure cache is cleaned if not already
                if (ffmpegOutputCacheFile.exists()) {
                    if (ffmpegOutputCacheFile.delete()) {
                        LogUtils.dTag(TAG, "在最外层finally块中再次尝试并成功清理FFmpeg缓存文件。");
                    } else {
                        LogUtils.eTag(TAG, "在最外层finally块中再次尝试清理FFmpeg缓存文件失败。");
                    }
                }
            } else {
                LogUtils.dTag(TAG, "视频旋转最终判定为成功 (在最外层finally检查): " + filePath);
                LogUtils.file(TAG,"A6Max旋转视频：视频旋转最终判定为成功 (在最外层finally检查): " + filePath);
            }
        }
    }

    public static class VideoMetadata {
        public float frameRate = 0f; // 帧率，可能是小数
        public int bitRate = 0;   // 码率，单位 bps (bits per second)
        public int width = 0;
        public int height = 0;
        public long durationMs = 0; // 时长，单位毫秒

        @Override
        public String toString() {
            return "VideoMetadata{" +
                    "frameRate=" + frameRate +
                    ", bitRate=" + bitRate + " bps" +
                    ", width=" + width +
                    ", height=" + height +
                    ", durationMs=" + durationMs +
                    '}';
        }
    }



    /**
     * 【总入口】以稳健的方式获取视频文件的元数据。
     * 采用两层降级策略: MediaMetadataRetriever -> ExoPlayer 2.8.4
     *
     * @param context   上下文
     * @param videoPath 视频文件的路径
     * @return 包含视频信息的 VideoMetadata 对象，如果所有方法都失败则返回 null。
     */
    public static VideoMetadata getMetadata(Context context, String videoPath) {
        if (!FileUtils.isFileExists(videoPath)) {
            LogUtils.eTag(TAG, "getMetadata: File does not exist - " + videoPath);
            return null;
        }

        // --- 策略 1: MediaMetadataRetriever ---
        VideoMetadata metadata = getMetadataWithRetriever(context, videoPath);
        if (isValidMetadata(metadata)) {
            LogUtils.dTag(TAG, "策略 1 [MediaMetadataRetriever] 成功。");
            return metadata;
        }
        LogUtils.wTag(TAG, "策略 1 [MediaMetadataRetriever] 失败，降级至 mp4parser...");


        // --- 策略 2: mp4parser ---
        IsoFile isoFile = null;
        try {
            metadata = new VideoMetadata();
            isoFile = new IsoFile(videoPath);

            MovieHeaderBox mvhd = isoFile.getMovieBox().getMovieHeaderBox();
            if (mvhd == null) {
                LogUtils.eTag(TAG, "策略 2 [mp4parser] 失败: 文件不包含 MovieHeaderBox。");
                return null;
            }

            long duration = mvhd.getDuration();
            long timescale = mvhd.getTimescale();
            double durationInSeconds = 0;
            if (timescale > 0) {
                durationInSeconds = (double) duration / timescale;
                metadata.durationMs = (long) (durationInSeconds * 1000);
            }

            File file = new File(videoPath);
            long fileSizeInBits = file.length() * 8;
            if (durationInSeconds > 0) {
                metadata.bitRate = (int) (fileSizeInBits / durationInSeconds);
            }

            List<TrackBox> trackBoxes = isoFile.getMovieBox().getBoxes(TrackBox.class);
            for (TrackBox trackBox : trackBoxes) {
                HandlerBox hdlr = trackBox.getMediaBox().getHandlerBox();
                if (hdlr != null && "vide".equals(hdlr.getHandlerType())) {
                    TrackHeaderBox tkhd = trackBox.getTrackHeaderBox();
                    metadata.width = (int) tkhd.getWidth();
                    metadata.height = (int) tkhd.getHeight();

                    // 【修正】通过 getBoxes() 方法获取 SampleTableBox
                    MediaBox mdia = trackBox.getMediaBox();
                    List<SampleTableBox> stbls = mdia.getBoxes(SampleTableBox.class);
                    if (stbls != null && !stbls.isEmpty()) {
                        SampleTableBox stbl = stbls.get(0); // 通常只有一个
                        TimeToSampleBox stts = stbl.getTimeToSampleBox();
                        if (stts != null && durationInSeconds > 0) {
                            long numberOfFrames = 0;
                            for (TimeToSampleBox.Entry entry : stts.getEntries()) {
                                numberOfFrames += entry.getCount();
                            }
                            metadata.frameRate = (float) (numberOfFrames / durationInSeconds);
                        }
                    }
                    break;
                }
            }

            if (isValidMetadata(metadata)) {
                LogUtils.dTag(TAG, "策略 2 [mp4parser] 成功。");
                return metadata;
            }

        } catch (Exception e) {
            LogUtils.eTag(TAG, "策略 2 [mp4parser] 解析失败: " + e.getMessage());
        } finally {
            if (isoFile != null) {
                try {
                    isoFile.close();
                } catch (IOException e) {
                    LogUtils.eTag(TAG, "关闭 IsoFile 时出错。", e);
                }
            }
        }

        LogUtils.eTag(TAG, "所有方案均无法获取视频元数据: " + videoPath);
        return null;
    }

    private static boolean isValidMetadata(VideoMetadata metadata) {
        return metadata != null && metadata.width > 0 && metadata.height > 0;
    }

    /**
     * 方案1: 使用原生 MediaMetadataRetriever 获取信息
     */
    private static VideoMetadata getMetadataWithRetriever(Context context, String filePath) {
        // ... 此方法无需修改 ...
        LogUtils.dTag(TAG, "尝试使用 MediaMetadataRetriever...");
        MediaMetadataRetriever retriever = new MediaMetadataRetriever();
        VideoMetadata metadata = new VideoMetadata();

        try {
            if (filePath.startsWith("content://")) {
                retriever.setDataSource(context, Uri.parse(filePath));
            } else {
                retriever.setDataSource(filePath);
            }
            // ... (其余代码与之前版本相同) ...
            String widthStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
            String heightStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
            String bitRateStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_BITRATE);
            String durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            // 2.8.4 的 Android SDK level 较低，METADATA_KEY_CAPTURE_FRAMERATE 可能不存在，做好兼容
            String frameRateStr = null;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                frameRateStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_CAPTURE_FRAMERATE);
            }
            if (widthStr != null) metadata.width = Integer.parseInt(widthStr);
            if (heightStr != null) metadata.height = Integer.parseInt(heightStr);
            if (bitRateStr != null) metadata.bitRate = Integer.parseInt(bitRateStr);
            if (durationStr != null) metadata.durationMs = Long.parseLong(durationStr);
            if (frameRateStr != null) {
                try {
                    metadata.frameRate = Float.parseFloat(frameRateStr);
                } catch (NumberFormatException e) { /* ignore */ }
            }
            return metadata;
        } catch (Exception e) {
            LogUtils.eTag(TAG, "getMetadataWithRetriever 失败: " + e.getMessage());
            return null;
        } finally {
            try {
                retriever.release();
            } catch (IOException e) {
                LogUtils.eTag(TAG, "Error releasing MediaMetadataRetriever: " + e.getMessage());
            }
        }
    }

    /**
     * 尝试备用的视频旋转方案: 通过重编码实现真正的帧旋转。
     * <p>
     * 当FFmpeg失败或需要确保视频在所有平台都正确显示时使用。
     * 此方法非常消耗资源（CPU/GPU和电量），且耗时较长。
     *
     * @param filePath            输入文件路径
     * @param tempOutputPathInCache 临时输出文件路径 (由 VideoReEncoder 使用)
     * @param ffmpegOutputCacheFile 临时输出文件的File对象
     * @param latch               用于同步的CountDownLatch
     * @param overallSuccess      用于返回成功状态的布尔数组
     */
    private static void tryAlternativeRotationMethod(String filePath,
                                                     String tempOutputPathInCache, File ffmpegOutputCacheFile,
                                                     CountDownLatch latch, boolean[] overallSuccess) {
        LogUtils.dTag(TAG, "开始备用旋转方案: 通过重编码实现物理旋转");



        boolean isAlternativeSuccess = false; // 使用局部变量跟踪备用方法的成功状态
        try {
            Thread.sleep(500); // 等待500毫秒，防止前面的文件权柄没释放
            File inputFile = new File(filePath);
            if (!inputFile.exists() || !inputFile.canRead()) {
                LogUtils.eTag(TAG, "输入文件不可访问: " + filePath);
                return;
            }

            // 优先使用 Context 和 Uri 的构造函数（如果库支持），否则使用 filePath
            // Mp4Composer composer = new Mp4Composer(context, Uri.fromFile(inputFile), tempOutputPathInCache);
            Mp4Composer composer = new Mp4Composer(filePath, tempOutputPathInCache); // 假设使用文件路径更安全

            CountDownLatch innerLatch = new CountDownLatch(1);
            AtomicBoolean composerSucceeded = new AtomicBoolean(false);
            AtomicReference<Exception> errorRef = new AtomicReference<>(null);

            composer.rotation(Rotation.ROTATION_270);
            composer.listener(new Mp4Composer.Listener() {
                @Override
                public void onProgress(double progress) { /* ... */ }

                @Override
                public void onCurrentWrittenVideoTime(long timeUs) { /* ... */ }

                @Override
                public void onCompleted() {
                    LogUtils.dTag(TAG, "Mp4Composer 旋转成功");
                    composerSucceeded.set(true);
                    innerLatch.countDown();
                }

                @Override
                public void onCanceled() {
                    LogUtils.wTag(TAG, "Mp4Composer 旋转被取消");
                    innerLatch.countDown();
                }

                @Override
                public void onFailed(Exception exception) {
                    LogUtils.eTag(TAG, "Mp4Composer 旋转失败", exception);
                    errorRef.set(exception);
                    innerLatch.countDown();
                }
            });

            composer.start();

            innerLatch.await(); // 无限等待，不设置超时
            if (composerSucceeded.get()) {
                // 注意：这里不再调用 latch.countDown()
                // 将成功状态的判断和文件操作封装在 handleSuccessfulRotation 内部
                // 并让它返回一个布尔值
                isAlternativeSuccess = handleSuccessfulRotation(filePath, tempOutputPathInCache, ffmpegOutputCacheFile);
            } else {
                LogUtils.eTag(TAG, "Mp4Composer 处理失败");
                // isAlternativeSuccess 保持 false
            }

        } catch (Exception e) {
            LogUtils.eTag(TAG, "备用方案执行时发生严重异常", e);
            isAlternativeSuccess = false;
        } finally {
            // 无论成功、失败、异常，最终都会在这里设置总成功状态并释放外部latch
            overallSuccess[0] = isAlternativeSuccess;

            // 如果备用方案最终失败了，确保清理临时输出文件
            if (!isAlternativeSuccess && ffmpegOutputCacheFile != null && ffmpegOutputCacheFile.exists()) {
                LogUtils.dTag(TAG, "备用方案失败，清理临时文件: " + ffmpegOutputCacheFile.getAbsolutePath());
                ffmpegOutputCacheFile.delete();
            }

            LogUtils.dTag(TAG, "备用旋转方案结束，最终状态: " + (isAlternativeSuccess ? "成功" : "失败"));
            latch.countDown(); // *** 唯一释放外部latch的地方 ***
        }
    }

    /**
     * 处理旋转成功后的文件替换逻辑
     */
    /**
     * 处理旋转成功后的文件替换逻辑。
     * 这个方法会尝试将处理好的临时文件安全地替换掉原始文件。
     *
     * @param originalFilePath      原始视频文件的完整路径。
     * @param tempOutputPathInCache 经过处理后（例如旋转后）的视频文件，位于应用缓存中。
     * @param tempOutputFile        指向临时输出文件的 File 对象，用于操作。
     * @return true 如果文件被成功替换，否则返回 false。
     */
    private static boolean handleSuccessfulRotation(String originalFilePath, String tempOutputPathInCache, File tempOutputFile) {
        File originalFile = new File(originalFilePath);
        File parentDir = originalFile.getParentFile();

        // 如果无法获取父目录，这是一个异常情况，操作无法继续
        if (parentDir == null) {
            LogUtils.eTag(TAG, "无法获取原始文件的父目录: " + originalFilePath);
            return false;
        }

        // 创建一个中间文件，用于安全地进行重命名操作
        String intermediateName = "intermediate_rotated_" + originalFile.getName();
        File intermediateFile = new File(parentDir, intermediateName);
        boolean replacementSuccess = false;

        try {
            // 步骤 1: 将缓存中的处理结果复制到与原文件同目录的中间文件
            if (FileUtils.copy(tempOutputFile.getAbsolutePath(), intermediateFile.getAbsolutePath())) {
                LogUtils.dTag(TAG, "已复制到中间文件: " + intermediateFile.getAbsolutePath());

                // 步骤 2: 删除原始文件
                if (originalFile.delete()) {
                    LogUtils.dTag(TAG, "已删除原始文件: " + originalFilePath);

                    // 步骤 3: 将中间文件重命名为原始文件名，完成替换
                    if (intermediateFile.renameTo(originalFile)) {
                        LogUtils.dTag(TAG, "视频旋转并替换成功: " + originalFilePath);
                        replacementSuccess = true;
                    } else {
                        // 如果重命名失败（在某些系统上可能发生），尝试最后的恢复策略：复制
                        LogUtils.eTag(TAG, "重命名中间文件失败，尝试通过复制进行恢复。");
                        if (FileUtils.copy(intermediateFile.getAbsolutePath(), originalFilePath)) {
                            LogUtils.dTag(TAG, "通过复制恢复成功: " + originalFilePath);
                            replacementSuccess = true;
                        } else {
                            LogUtils.eTag(TAG, "复制恢复也失败！旋转后的文件保留在: " + intermediateFile.getAbsolutePath() + "，但原始文件已丢失！");
                            // 此时 replacementSuccess 保持 false
                        }
                    }
                } else {
                    LogUtils.eTag(TAG, "删除原始文件失败: " + originalFilePath + "，无法完成替换。");
                    // 此时 replacementSuccess 保持 false
                }
            } else {
                LogUtils.eTag(TAG, "从缓存复制到中间文件失败。源: " + tempOutputFile.getAbsolutePath() + ", 目标: " + intermediateFile.getAbsolutePath());
                // 此时 replacementSuccess 保持 false
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "在替换视频过程中发生严重异常: " + e.getMessage(), e);
            replacementSuccess = false;
        } finally {
            // 清理工作区

            // 1. 清理缓存中的临时文件，因为它已经被复制走了或不再需要
            if (tempOutputFile.exists()) {
                if (!tempOutputFile.delete()) {
                    LogUtils.wTag(TAG, "清理缓存中的临时文件失败: " + tempOutputFile.getAbsolutePath());
                }
            }

            // 2. 清理中间文件
            //    - 如果替换成功，intermediateFile 应该已经被重命名，所以 !exists()
            //    - 如果替换失败，intermediateFile 仍然存在，需要被删除
            if (intermediateFile.exists()) {
                if (!intermediateFile.delete()) {
                    LogUtils.wTag(TAG, "清理残留的中间文件失败: " + intermediateFile.getAbsolutePath());
                }
            }
        }

        return replacementSuccess;
    }
}

package com.icatch.mobilecam.utils.executor;

public class AppExecutors {

    public static void runOnIoThread(Runnable runnable) {
        ArchTaskExecutor.getInstance().executeOnDiskIO(runnable);
    }

    public static void postToMainThread(Runnable runnable) {
        ArchTaskExecutor.getInstance().postToMainThread(runnable);
    }

    public static void postToMainThreadDelay(Runnable runnable, long delayMillis) {
        ArchTaskExecutor.getInstance().postToMainThreadDelay(runnable, delayMillis);
    }

    public static boolean isMainThread() {
        return ArchTaskExecutor.getInstance().isMainThread();
    }

}

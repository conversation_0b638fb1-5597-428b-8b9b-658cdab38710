package com.icatch.mobilecam.utils;

import com.icatch.mobilecam.data.entity.MultiPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.ui.RemoteFileHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class HdrUtils {

    public static boolean checkIsHdr(){
        List<MultiPbItemInfo> localFileList = RemoteFileHelper.getInstance().getLocalFileList(FileType.FILE_PHOTO);
        List<String> filePathList = new ArrayList<>();
        for (int i = 0; i < localFileList.size(); i++) {
            if (localFileList.get(i).hrdFilePath != null && localFileList.get(i).hrdFilePath.size()>0){
                for (int j = 0; j < localFileList.get(i).hrdFilePath.size(); j++) {
                    filePathList.add(localFileList.get(i).hrdFilePath.get(j).fileName);
                }
            } else {
                filePathList.add(localFileList.get(i).getFileName());
            }
        }


        HashMap<String, ArrayList<String>> map = new HashMap<>();
        for (int i = 0; i < filePathList.size(); i++) {
            String fileName = filePathList.get(i).substring(filePathList.get(i).lastIndexOf("/") + 1);
            String groupName = fileName.substring(0, fileName.lastIndexOf("_"));
            ArrayList<String> checkList = map.get(groupName);
            if (checkList == null) {
                ArrayList<String> newList = new ArrayList<>();
                newList.add(filePathList.get(i));
                map.put(groupName, newList);
            } else {
                checkList.add(filePathList.get(i));
            }
        }

        boolean isHdr = false;
        Iterator<Map.Entry<String, ArrayList<String>>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, ArrayList<String>> entry = iterator.next();
            ArrayList<String> checkList = entry.getValue();
            if (checkList.size() == 12) {
                isHdr = true;
                break;
            }
        }

        return isHdr;
    }
}

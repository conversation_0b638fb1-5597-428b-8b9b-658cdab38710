package com.icatch.mobilecam.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaMetadataRetriever;

import com.icatch.mobilecam.Log.AppLog;
import com.ijoyer.camera.utils.LogUtil;

import java.io.File;

public class PanoramaTools {
    private static final String TAG = PanoramaTools.class.getSimpleName();
    public static boolean isPanorama(String imagePath){
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        Bitmap bitmap = BitmapFactory.decodeFile(imagePath, options); 
        AppLog.e(TAG, "Bitmap Height == " + options.outHeight);
        AppLog.e(TAG, "Bitmap Width == " + options.outWidth);
        if(options.outHeight *2 == options.outWidth){
            return true;
        }
        return false;
    }
    public static boolean isPanoramaForVideo(String videoPath){
        if(videoPath == null){
            return false;
        }
        File file = new File(videoPath);
        if(file == null || !file.exists()){
            return false;
        }
        MediaMetadataRetriever mmr = new MediaMetadataRetriever();
        boolean isPanorama = false;
        try {
            mmr.setDataSource(videoPath);
            String width = mmr.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
            String height = mmr.extractMetadata(android.media.MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
            AppLog.d(TAG, "isPanoramaForVideo w="+width+" h="+height);
            if(width == null || height == null){
                isPanorama = false;
            }else {
                int widthInt = Integer.parseInt(width) ;
                int heightInt = Integer.parseInt(height) ;
                if(widthInt == 0 || heightInt == 0){
                    isPanorama = false;
                }else if(heightInt *2 == widthInt){
                    isPanorama = true;
                }
            }
        } catch (Exception ex) {
            AppLog.e(TAG, "MediaMetadataRetriever exception " + ex);
        } finally {
            try {
                mmr.release();
            } catch (Exception e) {
                LogUtil.e(e.toString());
            }
        }
        return isPanorama;
    }
    public static boolean isPanorama(long width, long height){
        if(width== 0 || height == 0){
            return false;
        }
        if(width >= height *2){
            return true;
        }
        return false;
    }
}

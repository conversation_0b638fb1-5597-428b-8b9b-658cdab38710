package com.icatch.mobilecam.utils;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.blankj.utilcode.util.FileUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.resource.bitmap.CenterCrop;
import com.bumptech.glide.load.resource.bitmap.RoundedCorners;
import com.ijoyer.mobilecam.R;
import com.luck.picture.lib.engine.ImageEngine;
import com.luck.picture.lib.utils.ActivityCompatHelper;
import com.luck.picture.lib.widget.SquareRelativeLayout;
import com.tencent.mmkv.MMKV;

import java.util.HashMap;
import java.util.Objects;

/**
 * @author：luck
 * @date：2019-11-13 17:02
 * @describe：Glide加载引擎
 */
public class GlideEngine implements ImageEngine {
    //备注名缓存
    private final HashMap<String, String> remarkMap = new HashMap<>();

    /**
     * 加载图片
     *
     * @param context   上下文
     * @param url       资源url
     * @param imageView 图片承载控件
     */
    @Override
    public void loadImage(Context context, String url, ImageView imageView) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return;
        }
        Glide.with(context)
                .load(url)
                .into(imageView);
    }

    @Override
    public void loadImage(Context context, ImageView imageView, String url, int maxWidth, int maxHeight) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return;
        }
        Glide.with(context)
                .load(url)
                .override(maxWidth, maxHeight)
                .into(imageView);
    }

    /**
     * 加载相册目录封面
     *
     * @param context   上下文
     * @param url       图片路径
     * @param imageView 承载图片ImageView
     */
    @Override
    public void loadAlbumCover(Context context, String url, ImageView imageView) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return;
        }
        Glide.with(context)
                .asBitmap()
                .load(url)
                .override(180, 180)
                .sizeMultiplier(0.5f)
                .transform(new CenterCrop(), new RoundedCorners(8))
                .placeholder(R.drawable.ps_image_placeholder)
                .into(imageView);
    }


    /**
     * 加载图片列表图片
     *
     * @param context   上下文
     * @param url       图片路径
     * @param imageView 承载图片ImageView
     */
    @Override
    public void loadGridImage(Context context, String url, ImageView imageView) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return;
        }
        //偷偷加入一个备注名
        if (imageView.getParent() instanceof SquareRelativeLayout) {
            TextView tvRemark = ((SquareRelativeLayout) imageView.getParent()).findViewById(R.id.tv_media_remark);
            if (tvRemark == null) {
                Glide.with(context)
                        .load(url)
                        .override(200, 200)
                        .centerCrop()
                        .placeholder(R.drawable.ps_image_placeholder)
                        .into(imageView);
                return;
            }
            String group;
            String fullName = "";
            String realPath = ImageUtils.getRealPath(imageView.getContext(), url);
            fullName = FileUtils.getFileName(realPath);
            if (remarkMap.containsKey(url)) {
                group = remarkMap.get(url);
            } else {
                group = SPKey.getRemarkGroup(realPath);
                if (Objects.equals(group, realPath)) {
                    remarkMap.put(url, "");//不是目标文件，直接用空字符串
                    group = "";
                } else {
                    remarkMap.put(url, group);
                }
            }
            String remarkName = "";
            if (!TextUtils.isEmpty(group)) {
                remarkName = MMKV.defaultMMKV().getString(SPKey.REMARK_NAME_PREFIX + group, null);
            }
            if (!TextUtils.isEmpty(remarkName)) {
                if (TextUtils.isEmpty(fullName)) {
                    fullName = remarkName;
                } else {
                    if (!fullName.startsWith("[")) {
                        fullName = "[" + remarkName + "]" + fullName;
                    }
                }
            }

            if (!TextUtils.isEmpty(fullName)) {
                tvRemark.setText(fullName);
                tvRemark.setVisibility(View.VISIBLE);
            } else {
                tvRemark.setVisibility(View.GONE);
            }
        }
        Glide.with(context)
                .load(url)
                .override(200, 200)
                .centerCrop()
                .placeholder(R.drawable.ps_image_placeholder)
                .into(imageView);
    }

    @Override
    public void pauseRequests(Context context) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return;
        }
        Glide.with(context).pauseRequests();
    }

    @Override
    public void resumeRequests(Context context) {
        if (!ActivityCompatHelper.assertValidRequest(context)) {
            return;
        }
        Glide.with(context).resumeRequests();
    }

    private GlideEngine() {
    }

    private static final class InstanceHolder {
        static final GlideEngine instance = new GlideEngine();
    }

    public static GlideEngine createGlideEngine() {
        return InstanceHolder.instance;
    }
}
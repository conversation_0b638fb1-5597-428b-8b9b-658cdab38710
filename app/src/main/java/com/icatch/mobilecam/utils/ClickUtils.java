package com.icatch.mobilecam.utils;
import android.view.View;
import com.icatch.mobilecam.Log.AppLog;
public class ClickUtils {
    private static final long MIN_CLICK_DELAY_TIME = 500;
    private static long lastClickTime = 0;
    private static int mLastClickViewId;
    public static synchronized boolean isFastClick() {
        boolean flag = false;
        long curClickTime = System.currentTimeMillis();
        long timeInterval = Math.abs(curClickTime - lastClickTime);
        if (timeInterval < MIN_CLICK_DELAY_TIME) {
            AppLog.d("ClickUtils","isFastClick");
            return true;
        }else {
            lastClickTime = curClickTime;
            return false;
        }
    }
    public static synchronized boolean isFastDoubleClick() {
        long time = System.currentTimeMillis();
        long timeInterval = Math.abs(time - lastClickTime);
        if (timeInterval < MIN_CLICK_DELAY_TIME) {
            return true;
        } else {
            lastClickTime = time;
            return false;
        }
    }
    public static boolean isFastDoubleClick(View v) {
        int viewId = v.getId();
        long time = System.currentTimeMillis();
        long timeInterval = time - lastClickTime;
        if (timeInterval < MIN_CLICK_DELAY_TIME && viewId == mLastClickViewId) {
            AppLog.d("ClickUtils","isFastDoubleClick timeInterval:" + timeInterval+ " viewId:" + viewId + " mLastClickViewId:" +mLastClickViewId);
            return true;
        } else {
            lastClickTime = time;
            mLastClickViewId = viewId;
            return false;
        }
    }
    public static boolean isFastDoubleClick(int id) {
        int viewId = id;
        long time = System.currentTimeMillis();
        long timeInterval = time - lastClickTime;
        if (timeInterval < MIN_CLICK_DELAY_TIME && viewId == mLastClickViewId) {
            AppLog.d("ClickUtils","isFastDoubleClick timeInterval:" + timeInterval+ " viewId:" + viewId + " mLastClickViewId:" +mLastClickViewId);
            return true;
        } else {
            lastClickTime = time;
            mLastClickViewId = viewId;
            return false;
        }
    }
}

package com.icatch.mobilecam.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.media.MediaMetadataRetriever;

import com.blankj.utilcode.util.LogUtils;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.utils.fileutils.MFileTools;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VideoListUtil {


    public static List<LocalPbItemInfo> getPhotoInfoList(FileType fileType, Context mContext) {
        Map<String, Integer> sectionMap = new HashMap<String, Integer>();
        int section = 1;

        String fileDate;
        String rootPath = StorageUtil.getRootPath(mContext);
        final List<LocalPbItemInfo> photoList = new ArrayList<LocalPbItemInfo>();
        List<File> fileList;
        if (fileType == FileType.FILE_PHOTO) {
            String filePath = rootPath + AppInfo.DOWNLOAD_PATH_PHOTO;
            fileList = MFileTools.getPhotosOrderByDate(filePath);
        } else if (fileType == FileType.FILE_VR_VIDEO) {
            String filePath = rootPath + AppInfo.DOWNLOAD_PATH_VIDEO;
            fileList = MFileTools.getVideosForVrOrderByDate(filePath);
        } else {
            String filePath = rootPath + AppInfo.DOWNLOAD_PATH_VIDEO;
            fileList = MFileTools.getVideosOrderByDate(filePath);
        }
        if (fileList == null || fileList.size() <= 0) {
            return null;
        }
        for (int ii = 0; ii < fileList.size(); ii++) {
            long time = fileList.get(ii).lastModified();
            @SuppressLint("SimpleDateFormat") SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            fileDate = format.format(new Date(time));
            if (!sectionMap.containsKey(fileDate)) {
                sectionMap.put(fileDate, section);
                LocalPbItemInfo mGridItem =
                        new LocalPbItemInfo(fileList.get(ii), section, PanoramaTools.isPanorama(fileList.get(ii).getPath()), ii);
                photoList.add(mGridItem);
                section++;
            } else {
                LocalPbItemInfo mGridItem = new LocalPbItemInfo(fileList.get(ii), sectionMap.get(fileDate),
                        PanoramaTools.isPanorama(fileList.get(ii).getPath()), ii);
                photoList.add(mGridItem);
            }
        }
        if (fileType == FileType.FILE_PHOTO) {
            GlobalInfo.getInstance().setLocalPhotoList(photoList);
        } else {
            GlobalInfo.getInstance().setLocalVideoList(photoList);
        }
        return photoList;
    }

    /**
     * 获取视频分辨率
     * @deprecated 建议使用 {@link VideoUtil#getVideoResolution(Context, String)}
     * 该方法有更好的异常处理和降级策略，可以解决特定设备上的兼容性问题
     */
    @Deprecated
    public static int[] getVideoResolution(String filePath) {
        MediaMetadataRetriever retriever = null;
        try {

            retriever = new MediaMetadataRetriever();
            retriever.setDataSource(filePath);
            int width = Integer.parseInt(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH));
            int height = Integer.parseInt(retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT));
            return new int[]{width,height};
        } catch (Exception e) {
            LogUtils.e("获取视频分辨率失败：" + filePath, e);
        } finally {
            if (retriever != null) {
                try {
                    retriever.release();
                } catch (IOException e) {
                    LogUtils.e("获取视频分辨率释放资源失败：" + filePath, e);
                }

            }
        }
        return null;
    }

}

package com.icatch.mobilecam.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.blankj.utilcode.util.LogUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;

import java.lang.Thread.UncaughtExceptionHandler;
import java.lang.reflect.Field;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.Map;
public class CrashHandler implements UncaughtExceptionHandler {
    public static final String TAG = "CrashHandler";
    private UncaughtExceptionHandler mDefaultHandler;
    private Handler handler = new Handler();
    private static CrashHandler INSTANCE = new CrashHandler();
    private Context mContext;
    private Map<String, String> infos = new HashMap<String, String>();
    private DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
    private CrashHandler() {
    }
    public static CrashHandler getInstance() {
        return INSTANCE;
    }
    public void init(Context context) {
        mContext = context;
        mDefaultHandler = Thread.getDefaultUncaughtExceptionHandler();
        Thread.setDefaultUncaughtExceptionHandler(this);
    }
    @Override
    public void uncaughtException(Thread thread, Throwable ex) {
        if (!handleException(thread,ex) && mDefaultHandler != null) {
            AppLog.e(TAG, "....default process!");
            mDefaultHandler.uncaughtException(thread, ex);
        } else {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                AppLog.e(TAG, e.toString());
            }
            AppLog.e(TAG, "....show custom process!");
            handler.post(new Runnable() {
                @Override
                public void run() {
//                    Toast.makeText(PanoramaApp.getContext(), R.string.app_exception, Toast.LENGTH_LONG).show();
                }
            });
            android.os.Process.killProcess(android.os.Process.myPid());
            System.exit(1);
        }
    }
    private boolean handleException(Thread thread ,final Throwable ex) {
        if (ex == null) {
            return false;
        }
        new Thread() {
            @Override
            public void run() {
                Looper.prepare();
//                MyToast.show(mContext, mContext.getResources().getString(R.string.app_exception) + "\n" + ex.toString());
                Looper.loop();
            }
        }.start();
//        String localizedMessage = ex.getLocalizedMessage();
//        AppLog.e(TAG, "localizedMessage" + (localizedMessage==null ? "null" : localizedMessage));
//        String message = ex.getMessage();
//        AppLog.e(TAG, message==null ? "null" : message);
//        AppLog.e(TAG, ex.toString());
//        getStackTrace(ex);
//        getThreadStackTrace(thread);
//        collectDeviceInfo(mContext);
        ex.printStackTrace();
        Log.e(TAG,ex.getMessage());
        PanoramaApp.setIjoyerLogToFile(true);
        LogUtils.file(LogUtils.E,Log.getStackTraceString(ex));
        return true;
    }
    private void getThreadStackTrace(Thread thread){
        if(thread == null){
            return;
        }
        AppLog.e(TAG,"thread id:" + thread.getId() + " name:" + thread.getName());
        StackTraceElement[] traceElements = thread.getStackTrace();
        if(traceElements != null && traceElements.length > 0){
            for (StackTraceElement temp:traceElements
            ) {
                if(temp != null){
                    String stackString = temp.toString();
                    AppLog.e(TAG, stackString);
                }
            }
        }
    }
    private void getStackTrace(Throwable ex){
        StackTraceElement[] traceElements = ex.getStackTrace();
        if(traceElements != null && traceElements.length > 0){
            for (StackTraceElement temp:traceElements
                 ) {
                if(temp != null){
                    String stackString = temp.toString();
                    AppLog.e(TAG, stackString);
                }
            }
        }
    }
    public void collectDeviceInfo(Context ctx) {
        try {
            PackageManager pm = ctx.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(ctx.getPackageName(), PackageManager.GET_ACTIVITIES);
            if (pi != null) {
                String versionName = pi.versionName == null ? "null" : pi.versionName;
                String versionCode = pi.versionCode + "";
                infos.put("versionName", versionName);
                infos.put("versionCode", versionCode);
            }
        } catch (NameNotFoundException e) {
            AppLog.d(TAG, e.toString());
        }
        Field[] fields = Build.class.getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                infos.put(field.getName(), field.get(null).toString());
                AppLog.d(TAG, field.getName() + " : " + field.get(null));
            } catch (Exception e) {
                AppLog.e(TAG, e.toString());
            }
        }
    }


}

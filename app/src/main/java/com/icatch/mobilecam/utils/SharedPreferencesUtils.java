package com.icatch.mobilecam.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.icatch.mobilecam.Application.PanoramaApp;


/**
 * Created by LZH On 2019/3/21
 */
public class SharedPreferencesUtils {

    public static String getString(String name, String key) {
        SharedPreferences preferences = PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        return preferences.getString(key, "");
    }

    public static Long getLong(String name, String key) {
        SharedPreferences preferences = PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        return preferences.getLong(key, 0);
    }

    public static int getInt(String name, String key) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        return preferences.getInt(key, 0);
    }

    public static float getFloat(String name, String key) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        return preferences.getFloat(key, 0);
    }

    public static boolean getBoolean(String name, String key) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        return preferences.getBoolean(key, false);
    }

    public static boolean getBoolean(String name, String key, boolean defaultValue) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        return preferences.getBoolean(key, defaultValue);
    }

    public static void putString(String name, String key, String value) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(key, value);
        editor.apply();
        editor.commit();
    }

    public static void putInt(String name, String key, int value) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(key, value);
        editor.apply();
        editor.commit();
    }

    public static void putBoolean(String name, String key, boolean value) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putBoolean(key, value);
        editor.apply();
        editor.commit();
    }

    public static void putFloat(String name, String key, float value) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putFloat(key, value);
        editor.apply();
        editor.commit();
    }
    /*获取字符串*/
    public static String getCourseIntroduceString(String name, String key) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        return preferences.getString(key, "");
    }
    /*存字符串*/
    public static void putCourseIntroduceString(String name, String key, String value) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(key, value);
        editor.apply();
        editor.commit();
    }
    /*清除字符串*/
    public static String clearCourseIntroduceString(String name, String key) {
        SharedPreferences preferences =  PanoramaApp.getContext().getSharedPreferences(name, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.clear();
        editor.commit();
        return preferences.getString(key, "");
    }
}

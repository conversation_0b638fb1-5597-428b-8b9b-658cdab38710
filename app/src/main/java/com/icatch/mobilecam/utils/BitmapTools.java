package com.icatch.mobilecam.utils;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ThumbnailUtils;
import com.icatch.mobilecam.Log.AppLog;
public class BitmapTools {
    private static String TAG = BitmapTools.class.getSimpleName();
    public final static int THUMBNAIL_WIDTH = 100;
    public final static int THUMBNAIL_HEIGHT = 100;
    private final static long LIMITED_IMGAE_SIZE = 1024 * 1024 * 10;
    public static Bitmap getImageByPath(String imagePath, int width, int height) {
        AppLog.d(TAG, "Start getImageByPath imagePath=" + imagePath);
        if (imagePath == null) {
            return null;
        }
        Bitmap bitmap = null;
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(imagePath, options);
        options.inJustDecodeBounds = false; 
        options.inSampleSize = calculateInSampleSize(options, width, height);
        ;
        bitmap = BitmapFactory.decodeFile(imagePath, options);
        return zoomBitmap(bitmap, width, height);
    }
    public static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;
        while ((height / inSampleSize > reqHeight) || (width / inSampleSize > reqWidth)) {
            inSampleSize *= 2;
        }
        while (height * width / (inSampleSize * inSampleSize) * 4 > LIMITED_IMGAE_SIZE) {
            inSampleSize *= 2;
        }
        return inSampleSize;
    }
    public static Bitmap getVideoThumbnail(String videoPath, int width, int height) {
        AppLog.i(TAG, "start getVideoThumbnail videoPath=" + videoPath);
        if (videoPath == null) {
            return null;
        }
        Bitmap bitmap = null;
        bitmap = ThumbnailUtils.createVideoThumbnail(videoPath, ThumbnailUtils.OPTIONS_RECYCLE_INPUT);
        AppLog.i(TAG, "End getVideoThumbnail bitmap=" + bitmap);
        return zoomBitmap(bitmap, width, height);
    }
    public static Bitmap zoomBitmap(Bitmap bitmap, float width, float heigth) {
        if (bitmap == null) {
            return bitmap;
        }
        float zoomRate = 1.0f;
        if (bitmap.getWidth() >= width || bitmap.getHeight() >= heigth) {
        } else if (width * bitmap.getHeight() > heigth * bitmap.getWidth()) {
            zoomRate = heigth / bitmap.getHeight();
        } else if (width * bitmap.getHeight() <= heigth * bitmap.getWidth()) {
            zoomRate = width / bitmap.getWidth();
        }
        Matrix matrix = new Matrix();
        matrix.postScale(zoomRate, zoomRate); 
        try {
            bitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
        } catch (OutOfMemoryError e) {
            AppLog.e(TAG, "zoomBitmap OutOfMemoryError");
            bitmap.recycle();
            System.gc();
        }
        return bitmap;
    }
    public static Bitmap decodeByteArray(byte[] data) {
        AppLog.d(TAG, "start decodeByteArray");
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeByteArray(data, 0, data.length, options);
        int sampleSize = calculateInSampleSize(options, options.outWidth, options.outHeight);
        options.inJustDecodeBounds = false;
        options.inSampleSize = sampleSize;
        Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length, options);
        AppLog.d(TAG, "end decodeByteArray bitmap=" + bitmap);
        return bitmap;
    }
    public static Bitmap decodeByteArray(byte[] data, int reqWidth, int reqHeight) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeByteArray(data, 0, data.length, options);
        int sampleSize = calculateInSampleSize(options, reqWidth, reqHeight);
        options.inJustDecodeBounds = false;
        options.inSampleSize = sampleSize;
        Bitmap bitmap = BitmapFactory.decodeByteArray(data, 0, data.length, options);
        return zoomBitmap(bitmap, reqWidth, reqHeight);
    }
    public static int getImageWidth(String path) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        Bitmap bitmap = BitmapFactory.decodeFile(path, options); 
        return options.outWidth;
    }
    public static int getImageHeight(String path) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        Bitmap bitmap = BitmapFactory.decodeFile(path, options); 
        return options.outHeight;
    }
}

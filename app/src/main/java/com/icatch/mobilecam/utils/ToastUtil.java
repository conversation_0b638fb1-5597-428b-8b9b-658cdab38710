package com.icatch.mobilecam.utils;

import android.widget.Toast;

import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.utils.executor.AppExecutors;

/**
 * Android 9 以上禁用后台弹自定义toast，之前工具包的已经不能用了
 * 在某些手机，比如小米，需要通知权限，华为需要悬浮窗权限
 * <p>
 * Author:<PERSON><PERSON><PERSON><PERSON>
 * <p>
 * changeLogs:
 * 2024/6/13: First created this class.
 */
public class ToastUtil {
    public static void showShortToast(String msg) {
        AppExecutors.postToMainThread(() -> Toast.makeText(PanoramaApp.getContext(), msg, Toast.LENGTH_SHORT).show());
    }

    public static void showLongToast(String msg) {
        AppExecutors.postToMainThread(() -> Toast.makeText(PanoramaApp.getContext(), msg, Toast.LENGTH_LONG).show());
    }
}

package com.icatch.mobilecam.utils.executor;

import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.blankj.utilcode.util.LogUtils;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

public class DefaultTaskExecutor extends TaskExecutor {

    private final Object mLock = new Object();

    /*private final ExecutorService mDiskIO = Executors.newFixedThreadPool(2, new ThreadFactory() {
        private static final String THREAD_NAME_STEM = "arch_disk_io_%d";

        private final AtomicInteger mThreadId = new AtomicInteger(0);

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            t.setName(String.format(THREAD_NAME_STEM, mThreadId.getAndIncrement()));
            return t;
        }
    });*/

    private final int NUMBER_OF_CORES = Runtime.getRuntime().availableProcessors();
    //最大线程数量设置为cpu 核心数的两倍
    private final int MAXIMUM_POOL_SIZE = NUMBER_OF_CORES * 2;
    private final long KEEP_ALIVE_TIME = 10000L; // Keep alive time in milliseconds
    BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(128);
    private final ExecutorService mDiskIO = new ThreadPoolExecutor(
            NUMBER_OF_CORES,       // Initial pool size
            MAXIMUM_POOL_SIZE,     // Max pool size
            KEEP_ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            workQueue, // Use a bounded queue
            new ThreadFactory() {
                private static final String THREAD_NAME_STEM = "arch_disk_io_%d";
                private final AtomicInteger mThreadId = new AtomicInteger(0);

                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r);
                    t.setName(String.format(THREAD_NAME_STEM, mThreadId.getAndIncrement()));
                    return t;
                }
            },
            new MyRejectedExecutionHandler(workQueue) // A handler for rejected tasks that runs the rejected task directly in the calling thread of the execute method
    );

    DefaultTaskExecutor() {
        LogUtils.file("线程池最大线程数："+MAXIMUM_POOL_SIZE);
        // Make sure to shutdown the executor service when it's no longer needed
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            mDiskIO.shutdown();
            try {
                // Wait a while for existing tasks to terminate
                if (!mDiskIO.awaitTermination(60, TimeUnit.SECONDS)) {
                    mDiskIO.shutdownNow(); // Cancel currently executing tasks
                    // Wait a while for tasks to respond to being cancelled
                    if (!mDiskIO.awaitTermination(60, TimeUnit.SECONDS)) {
                        System.err.println("Pool did not terminate");
                    }
                }
            } catch (InterruptedException ie) {
                // (Re-)Cancel if current thread also interrupted
                mDiskIO.shutdownNow();
                // Preserve interrupt status
                Thread.currentThread().interrupt();
            }
        }));
    }

    @Nullable
    private volatile Handler mMainHandler;

    @Override
    public void executeOnDiskIO(Runnable runnable) {
        mDiskIO.execute(runnable);
    }

    @Override
    public void postToMainThread(Runnable runnable) {
        if (mMainHandler == null) {
            synchronized (mLock) {
                if (mMainHandler == null) {
                    mMainHandler = new Handler(Looper.getMainLooper());
                }
            }
        }
        //noinspection ConstantConditions
        mMainHandler.post(runnable);
    }

    @Override
    public void postToMainThreadDelay(@NonNull Runnable runnable, long delayMillis) {
        if (mMainHandler == null) {
            synchronized (mLock) {
                if (mMainHandler == null) {
                    mMainHandler = new Handler(Looper.getMainLooper());
                }
            }
        }
        //noinspection ConstantConditions
        mMainHandler.postDelayed(runnable, delayMillis);
    }

    @Override
    public boolean isMainThread() {
        return Looper.getMainLooper().getThread() == Thread.currentThread();
    }

    //自定义的拒绝策略
    public static class MyRejectedExecutionHandler implements RejectedExecutionHandler {

        private final BlockingQueue<Runnable> queue;

        public MyRejectedExecutionHandler(BlockingQueue<Runnable> queue) {
            this.queue = queue;
        }

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            try {
                // 尝试将任务重新加入队列
                this.queue.put(r);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtils.file("DefaultTaskExecutor", "线程池溢出");
                LogUtils.e("DefaultTaskExecutor", "线程池溢出", e);
            }
        }
    }
}
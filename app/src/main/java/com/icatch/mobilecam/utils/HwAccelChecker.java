package com.icatch.mobilecam.utils;

import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.os.Build;

import com.blankj.utilcode.util.LogUtils;

import java.util.Arrays;

/**
 * 硬件加速检查器
 * <p>
 * Author:huang<PERSON><PERSON>
 * Contacts:<EMAIL>
 * <p>
 * changeLogs:
 * 2025/5/26: First created this class.
 */
public class HwAccelChecker {

    private static final String TAG = "HwAccelChecker";
    private static String selectedHwEncoderName = null; // FFmpeg中对应的硬件编码器名称, 如 "h264_mediacodec"
    private static boolean initialized = false;

    // 支持的MIME类型和对应的FFmpeg硬件编码器名称
    private static final String MIME_TYPE_AVC = "video/avc";    // H.264
    private static final String FFMPEG_H264_MEDIACODEC = "h264_mediacodec";

    // 可以扩展以支持HEVC等
    // private static final String MIME_TYPE_HEVC = "video/hevc";  // H.265
    // private static final String FFMPEG_HEVC_MEDIACODEC = "hevc_mediacodec";


    /**
     * 检测可用的硬件视频编码器。建议在应用启动或首次需要时调用一次。
     */
    public static synchronized void detectSupportedHwEncoder() {
        if (initialized) {
            return;
        }

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            LogUtils.dTag(TAG, "MediaCodecList API requires API level 21+.");
            initialized = true;
            return;
        }

        try {
            MediaCodecList mcl = new MediaCodecList(MediaCodecList.ALL_CODECS);
            for (MediaCodecInfo codecInfo : mcl.getCodecInfos()) {
                if (!codecInfo.isEncoder()) {
                    continue; // 只关心编码器
                }

                // 判断是否为软件编码器，我们想要硬件的
                boolean isSoftwareOnly = false;
                String codecNameLower = codecInfo.getName().toLowerCase();

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    isSoftwareOnly = codecInfo.isSoftwareOnly();
                } else {
                    // 旧版本API的启发式判断 (常见的软件编码器名称)
                    if (codecNameLower.startsWith("omx.google.") ||
                            codecNameLower.startsWith("c2.android.google.") || // Codec2 软件编码器
                            codecNameLower.contains("sw-encoder") ||
                            codecNameLower.startsWith("omx.ffmpeg.")) {
                        isSoftwareOnly = true;
                    }
                }
                // 一些设备可能将硬件编码器错误地标记为非别名，但其名称明确表示硬件
                if (codecNameLower.contains(".hw") || codecNameLower.contains(".qcom.") || codecNameLower.contains(".mediatek.") || codecNameLower.contains(".exynos.") || codecNameLower.contains(".hisilicon.")) {
                    isSoftwareOnly = false; // 强制认为是硬件
                }


                if (isSoftwareOnly) {
                    // LogUtils.dTag(TAG, "Skipping software encoder: " + codecInfo.getName());
                    continue;
                }

                String[] supportedTypes = codecInfo.getSupportedTypes();
                for (String type : supportedTypes) {
                    if (type.equalsIgnoreCase(MIME_TYPE_AVC)) {
                        LogUtils.dTag(TAG, "Found HW H.264 Encoder: " + codecInfo.getName() + " (Types: " + Arrays.toString(supportedTypes) + ")");
                        selectedHwEncoderName = FFMPEG_H264_MEDIACODEC;
                        initialized = true;
                        return; // 找到了H.264硬件编码器，优先使用
                    }
                    // 若要支持HEVC，可在此添加逻辑
                    // if (type.equalsIgnoreCase(MIME_TYPE_HEVC)) {
                    //     LogUtils.dTag(TAG, "Found HW H.265 Encoder: " + codecInfo.getName());
                    //     selectedHwEncoderName = FFMPEG_HEVC_MEDIACODEC;
                    //     initialized = true;
                    //     return;
                    // }
                }
            }
        } catch (Exception e) {
            LogUtils.eTag(TAG, "Error detecting HW encoders: " + e.getMessage());
            e.printStackTrace();
        }

        LogUtils.dTag(TAG, "No suitable HW H.264 encoder found via MediaCodecList. Will use software encoding.");
        initialized = true;
    }

    /**
     * FFmpeg是否可以使用检测到的硬件编码器。
     * @return true 如果检测到并选择了硬件编码器名称。
     */
    public static boolean isHwAccelerationAvailable() {
        if (!initialized) {
            detectSupportedHwEncoder();
        }
        return selectedHwEncoderName != null;
    }

    /**
     * 获取在FFmpeg命令中应使用的硬件编码器名称。
     * @return 例如 "h264_mediacodec"，如果未检测到则为null。
     */
    public static String getHwEncoderFfmpegName() {
        if (!initialized) {
            detectSupportedHwEncoder();
        }
        return selectedHwEncoderName;
    }
}
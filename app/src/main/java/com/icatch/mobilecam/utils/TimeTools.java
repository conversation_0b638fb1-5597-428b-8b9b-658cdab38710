package com.icatch.mobilecam.utils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
public class TimeTools {
    private static long lastClickTime;
    public synchronized static boolean isFastClick() {
        long time = System.currentTimeMillis();
        if ( time - lastClickTime < 1000) {
            return true;
        }
        lastClickTime = time;
        return false;
    }
    public static long stringToLong(String strTime, String formatType) throws ParseException {
        Date date = stringToDate(strTime, formatType); 
        if (date == null) {
            return 0;
        } else {
            long currentTime = date.getTime(); 
            return currentTime;
        }
    }
    public static Date stringToDate(String strTime, String formatType)
            throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat(formatType);
        Date date = formatter.parse(strTime);
        return date;
    }
    public static String getDateToString(long time,String formatType) {
        Date d = new Date(time);
        SimpleDateFormat sf = new SimpleDateFormat(formatType);
        return sf.format(d);
    }
}

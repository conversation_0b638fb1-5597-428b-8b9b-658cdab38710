package com.icatch.mobilecam.utils;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.icatch.mobilecam.MyCamera.CameraManager;
import com.ijoyer.camera.activity.RecordPlayActivity;

public class CameraUtils {

    //双镜头HDR款
    public static boolean isA6S() {
        return isA6S(getCurCamera());
    }

    public static boolean isA6S(String cameraName) {
        return "A6S".equals(cameraName) || "A6P".equals(cameraName) || "A6MAX".equals(cameraName);
    }

    //双镜头HDR款
    public static boolean isA8() {
        return isA8(getCurCamera());
    }

    public static boolean isA8(String cameraName) {
        return "A8".equals(cameraName);
    }

    //双镜头HDR款
    public static boolean isA6SOrA8() {
        return isA6SOrA8(getCurCamera());
    }

    public static boolean isA6SOrA8(String cameraName) {
        return isA6S(cameraName) || isA8(cameraName);
    }

    public static boolean isA3S(String cameraName) {
        return "A3S".equals(cameraName);
    }

    public static boolean isA3S() {
        return isA3S(getCurCamera());
    }

    /**
     * 标定参数是否在相机的sd 卡里面
     */
    public static boolean isZdFileInCamera() {
        String cameraName = getCurCamera();
        return "A6P".equals(cameraName) || "A6MAX".equals(cameraName) || isA3S(cameraName);
    }

    @Nullable
    public static String getCurCamera() {
        if (CameraManager.getInstance().getCurCamera() != null) {
            if (CameraManager.getInstance().getCurCamera().getCameraFixedInfo() != null) {
                return CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraName();
            }
        }
        return null;
    }

    /**
     * 检查是否合法相机WiFi
     *
     * @return
     */
    public static boolean isIjoyerCamera(String wifiSsid) {
        if (TextUtils.isEmpty(wifiSsid)) {
            return false;
        }
        String ssid = wifiSsid.toLowerCase();
        return ssid.contains("ijoyer") || ssid.contains("fooww");
    }

    public static boolean isA6Max() {
        return isA6Max(getCurCamera());
    }

    public static boolean isA6Max(String cameraName) {
        return "A6MAX".equals(cameraName);
    }

    /**
     * A6Max预览视频的时候需要特殊处理，直接跳到内置的普通视频播放器
     *
     * @return 是否已经处理了
     */
    public static boolean handleA6MaxVideo(Context context, String filePath) {
        int[] videoResolution = VideoUtil.getVideoResolution(context, filePath);
        if (videoResolution == null || videoResolution.length != 2) {
            return false;
        }
        int w = videoResolution[0];
        int h = videoResolution[1];
        if (h <= 0 || w <= 0) {
            return false;
        }
        //判断分辨率，如果高宽比不是2:1 的话，就直接进入普通播放页面
        if (w / h == 2 || h / w == 2) {
            return false;
        } else {
            Intent intent = new Intent();
            intent.putExtra("path", filePath);
            intent.setClass(context, RecordPlayActivity.class);
            context.startActivity(intent);
            return true;
        }
    }
}

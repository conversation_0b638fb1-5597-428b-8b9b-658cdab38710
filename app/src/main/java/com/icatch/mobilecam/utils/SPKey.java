package com.icatch.mobilecam.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.text.TextUtils;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SPUtils;
import com.icatch.mobilecam.Application.PanoramaApp;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 存放sp 变量
 * <p>
 * Author:h<PERSON><PERSON><PERSON>
 * <p>
 * changeLogs:
 * 2024/6/9: First created this class.
 */
public class SPKey {
    ////备注名分组前缀正则。用于匹配 "YYYYMMDD_HHMMSS_" 格式的前缀
    private static final Pattern remarkNameGroupPrefixPattern = Pattern.compile("^\\d{8}_\\d{6}");
    /**
     * 是否使用多线程模式
     */
    public static final String USE_MULTI_THREADED_MODE = "use_multi_threaded_mode";
    /**
     * HDR 的时候是开启压缩
     */
    public static final String HDR_COMPRESSION_MODE = "hdr_compression_mode";

    /**
     * 拍摄时图片的备注名关联前缀，这个使用MMKV，可能会比较多
     */
    public static final String REMARK_NAME_PREFIX = "remark_name_";

    /**
     * 是否使用图像增强
     */
    public static final String USE_IMAGE_ENHANCEMENT = "use_image_enhancement";

    /**
     * 是否开启多线程模式
     * 会根据手机性能处理默认值
     */
    public static boolean isUseMultiThreadMode() {
        if (SPUtils.getInstance().contains(USE_MULTI_THREADED_MODE)) {
            return SPUtils.getInstance().getBoolean(USE_MULTI_THREADED_MODE);
        } else {
            return isPhoneQualifiedPerformance();
        }
    }

    /**
     * 是否使用HDR 压缩，使用会降低图片质量，增加稳定性
     * 会根据手机性能处理默认值
     */
    public static boolean isHdrCompressMode() {
        if (SPUtils.getInstance().contains(HDR_COMPRESSION_MODE)) {
            return SPUtils.getInstance().getBoolean(HDR_COMPRESSION_MODE);
        } else {
            return !isPhoneQualifiedPerformance();
        }
    }

    /**
     * 是否使用图像增强
     */
    public static boolean useImageEnhancement() {
        return SPUtils.getInstance().getBoolean(USE_IMAGE_ENHANCEMENT, true);
    }

    //cpu 数量
    private static Integer cpuNum;
    //总内存大小
    private static Long memorySize;

    //手机的性能是否合格
    private static boolean isPhoneQualifiedPerformance() {
        if (cpuNum == null) {
            cpuNum = Runtime.getRuntime().availableProcessors();
            LogUtils.file("CPU 数量：" + cpuNum);
        }
        if (memorySize == null) {
            ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
            ActivityManager am = (ActivityManager) PanoramaApp.getContext().getSystemService(Context.ACTIVITY_SERVICE);
            am.getMemoryInfo(memoryInfo);
            memorySize = memoryInfo.totalMem;
            LogUtils.file("总内存大小：" + memorySize);
            LogUtils.file("可用内存大小：" + memoryInfo.availMem);
        }

        //cpu 4 核、内存5G 以上——基本要6核6G
        return cpuNum > 4 && memorySize > 5L * 1024 * 1024 * 1024;
    }

    /**
     * 从文件名中获取备注组（日期时间前缀）。
     * 文件名已经是纯文件名，不包含路径。
     * 如果文件名以 "[...]" 开头，这部分将被忽略，然后在剩余部分查找日期时间前缀。
     *
     * @param fileName 纯文件名，例如 "20240711_220023_xxx.JPG" 或 "[info]20240711_220023_xxx.JPG"
     * @return 如果找到匹配的日期时间前缀，则返回该前缀；否则返回原始的 fileName。
     */
    public static String getRemarkGroup(String fileName) {
        // 1. 处理原始输入为空的情况
        if (TextUtils.isEmpty(fileName)) {
            return fileName; // 返回 null 或 ""
        }

        String stringToSearch = fileName; // 初始化待搜索字符串为输入的文件名

        // 2. 如果 fileName 以 "[...]" 开头，则忽略这部分
        //    例如："[info]20240711_220023_xxx.JPG" -> "20240711_220023_xxx.JPG"
        //    例如："[]20240711_220023_xxx.JPG" -> "20240711_220023_xxx.JPG"
        if (fileName.startsWith("[")) {
            int closingBracketIndex = fileName.indexOf(']');
            // 确保找到了 ']' 且它在 '[' 之后 (即 closingBracketIndex > 0)
            if (closingBracketIndex > 0) {
                // 获取 ']' 之后的部分作为待搜索字符串
                stringToSearch = fileName.substring(closingBracketIndex + 1);
            }
            // 如果没有找到 ']' (closingBracketIndex == -1),
            // 或者 ']' 是第一个字符 (closingBracketIndex == 0, 例如 "]filename"),
            // 或者文件名是 "[unclosedRemark",
            // stringToSearch 将保持为原始的 fileName。
            // 这种情况下，后续的正则表达式 ^\d{8}_\\d{6} 通常不会匹配，这是期望的行为。
        }

        // 3. 在处理后的字符串 (stringToSearch) 上应用正则表达式
        Matcher matcher = remarkNameGroupPrefixPattern.matcher(stringToSearch);
        if (matcher.find()) {
            // 如果找到匹配，返回匹配到的组（即 "YYYYMMDD_HHMMSS" 部分）
            return matcher.group();
        } else {
            // 如果没有找到匹配，返回原始的 fileName 输入
            return fileName;
        }
    }
}

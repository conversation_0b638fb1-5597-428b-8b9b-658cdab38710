package com.icatch.mobilecam.utils;

import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.text.TextUtils;

import androidx.core.app.ActivityCompat;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.PermissionUtils;
import com.ijoyer.camera.ui.PermissionPopup;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.R;
import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.InjectResourceSource;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;
import com.luck.picture.lib.utils.PictureFileUtils;

import java.util.List;
import java.util.Objects;

/**
 * 图片工具
 * <p>
 * Author:huangwu<PERSON>
 * <p>
 * changeLogs:
 * 2024/7/23: First created this class.
 */
public class ImageUtils {

    public static void select(Activity activity, int chooseMode,int maxCount, OnResult onResult) {
        boolean hasPermission = checkPermission(activity, () -> PictureSelector.create(activity)
                .openGallery(chooseMode)
                .setImageEngine(GlideEngine.createGlideEngine())
                .setMaxSelectNum(maxCount)
                .setImageSpanCount(3)
                .isDisplayCamera(false)
                .setInjectLayoutResourceListener((context, resourceSource) -> {
                    switch (resourceSource) {
                        case InjectResourceSource.MAIN_ITEM_IMAGE_LAYOUT_RESOURCE:
                            return R.layout.ps_custom_item_grid_image;
                        case InjectResourceSource.MAIN_ITEM_VIDEO_LAYOUT_RESOURCE:
                            return R.layout.ps_custom_item_grid_video;
                        default:
                            return 0;
                    }
                })
                .forResult(onResult));
        if (!hasPermission) {
            if (onResult != null) {
                onResult.onCancel();
            }
        }

    }
    /**
     * 选择图片
     */
    public static void selectImg(Activity activity, int maxCount, OnResult onResult) {
        select(activity,SelectMimeType.ofAll(),maxCount,onResult);
    }

    private static boolean checkPermission(Activity activity, Runnable runnable) {
        String[] permissions = new String[]{PermissionConstants.STORAGE};
        if (PermissionUtils.isGranted(permissions)) {
            if (runnable != null) {
                runnable.run();
            }
            return true;
        }
        PermissionPopup popup = new PermissionPopup(activity);
        popup.setReqDesc("为使用该功能，请授予存储权限用于选择图片、视频");
        popup.showPopupWindow();
        popup.tvRight.setOnClickListener(v -> {
            popup.dismiss();
            PermissionUtils.permission(permissions)
                    .rationale((a, shouldRequest) -> new Handler().postDelayed(() ->
                            shouldRequest.again(true), 100))
                    .callback(new PermissionUtils.FullCallback() {
                        @Override
                        public void onGranted(List<String> permissionsGranted) {
                            int count = 0;
                            for (int i = 0; i < permissionsGranted.size(); i++) {
                                String granted = permissionsGranted.get(i);
                                for (String permission : permissions) {
                                    if (Objects.equals(granted, permission)) {
                                        count++;
                                        break;
                                    }
                                }
                            }

                            //成功赋予权限
                            if (count >= permissions.length) {
                                if (runnable != null) {
                                    runnable.run();
                                }
                            }
                        }

                        @Override
                        public void onDenied(List<String> permissionsDeniedForever, List<String> permissionsDenied) {
                            if (!permissionsDeniedForever.isEmpty()) {
                                String[] systemRequestArray = permissionsDenied.toArray(new String[permissionsDenied.size()]);
                                ActivityCompat.requestPermissions(activity, systemRequestArray, PermissionTools.ALL_REQUEST_CODE);
                            }
                        }
                    }).request();
        });
        return false;
    }

    /**
     * 获取真实路径
     * @param context
     * @param media
     * @return
     */
    public static String getRealPath(Context context, LocalMedia media) {
        String path = "";
        if (!TextUtils.isEmpty(media.getCompressPath())) {
            path = media.getCompressPath();
        } else {
            if (!TextUtils.isEmpty(media.getPath()) && media.getPath().startsWith("content:")) {
                Uri uri = Uri.parse(media.getPath());
                path = PictureFileUtils.getPath(context, uri);
            } else {
                path = media.getRealPath();
            }
        }
        return path;
    }

    public static String getRealPath(Context context, String path) {
        if (!TextUtils.isEmpty(path) && path.startsWith("content:")) {
            Uri uri = Uri.parse(path);
            path = PictureFileUtils.getPath(context, uri);
            return path;
        } else {
            return path;
        }
    }

    public abstract static class OnResult implements OnResultCallbackListener<LocalMedia> {

        @Override
        public void onCancel() {
            LogUtil.w("取消选择文件");
        }
    }
}

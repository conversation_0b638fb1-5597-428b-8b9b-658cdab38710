package com.icatch.mobilecam.data.entity;

import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.utils.ConvertTools;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class LocalMp3Bean {
    public File file;
    public boolean isCheck;
    public String type = "";
    public String time = "";

    public String getFileDate() {
        long time = file.lastModified();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(new Date(time));
    }
}

package com.icatch.mobilecam.data;

public class ICatchFile {
    private int fileHandle;
    private String filePath;
    private String fileName;
    private String fileDate;
    private int fileType;
    private long fileSize;
    private int fileWidth;
    private int fileHeight;
    private double frameRate;
    private int fileProtection;
    private int fileDuration;

    public ICatchFile() {
        this.init(1, 16, "zd.info", "", 0L, "", 0.0D, 0, 0, 0, 0);
    }

    private void init(int fileHandle, int fileType, String filePath, String fileName, long fileSize, String fileDate, double frameRate, int fileWidth, int fileHeight, int fileProtection, int fileDuration) {
        this.fileHandle = fileHandle;
        this.fileType = fileType;
        this.filePath = filePath;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.fileDate = fileDate;
        this.frameRate = frameRate;
        this.fileWidth = fileWidth;
        this.fileHeight = fileHeight;
        this.fileProtection = fileProtection;
        this.fileDuration = fileDuration;
    }
}

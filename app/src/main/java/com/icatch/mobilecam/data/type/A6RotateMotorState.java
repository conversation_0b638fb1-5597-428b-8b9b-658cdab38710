package com.icatch.mobilecam.data.type;
public class A6RotateMotorState {
    public static final int ROTATE_OFF = 0;
    public static final int ROTATE_START = 1;
    public static final int ROTATE_END = 2;


    public static final int ROTATE_REBOOT = 7;

    public static final int ROTATE_RESET = 3;

    /**
     * 中断指令
     */
    public static final int ROTATE_INTERRUPT = 4;

    /**
     * 短录像模式（15 秒左右自动停）
     */
    public static final int RECORD_MODE_SHORT_VIDEO = 0;
    /**
     * 长录像模式
     */
    public static final int RECORD_MODE_LONG_VIDEO = 1;
}

package com.icatch.mobilecam.data.SystemInfo;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.StatFs;
import android.text.format.Formatter;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.utils.StorageUtil;

import java.io.File;
public class SystemInfo {
    private static final String TAG = "SystemInfo";
    private static SystemInfo instance;
    public static SystemInfo getInstance() {
        if (instance == null) {
            instance = new SystemInfo();
        }
        return instance;
    }
    public static DisplayMetrics getMetrics(Context context) {
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        return metrics;
    }
    public String getLocalMacAddress(Activity activity) {
        WifiManager wifi = (WifiManager) activity.getSystemService(Context.WIFI_SERVICE);
        WifiInfo info = wifi.getConnectionInfo();
        AppLog.i(TAG, "current Mac=" + info.getMacAddress().toLowerCase());
        return info.getMacAddress().toLowerCase();
    }
    public static long getSDFreeSize(Context context) {
        File path = StorageUtil.getStorageDirectory(context);
        StatFs stat = new StatFs(path.getPath());
        long blockSize;
        long totalBlocks;
        long availableBlocks;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            blockSize = stat.getBlockSizeLong();
            totalBlocks = stat.getBlockCountLong();
            availableBlocks = stat.getAvailableBlocksLong();
        } else {
            blockSize = stat.getBlockSize();
            totalBlocks = stat.getBlockCount();
            availableBlocks = stat.getAvailableBlocks();
        }
        AppLog.i(TAG, "getSDFreeSize=" + blockSize * availableBlocks);
        return (blockSize * availableBlocks);
    }
    private String formatSize(Context context, long size) {
        return Formatter.formatFileSize(context, size);
    }
    public static long getFreeMemory(Context mContext) {
        long freeMemorySize;
        ActivityManager am = (ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
        am.getMemoryInfo(mi);
        freeMemorySize = mi.availMem / 1024;
        AppLog.i(TAG, "current FreeMemory=" + freeMemorySize);
        return freeMemorySize;
    }

    public static long getTotalMemory(Context mContext) {
        long totalMemorySize;
        ActivityManager am = (ActivityManager) mContext.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo mi = new ActivityManager.MemoryInfo();
        am.getMemoryInfo(mi);
        totalMemorySize = mi.totalMem / 1024;
        AppLog.i(TAG, "TotalMemory=" + totalMemorySize);
        return totalMemorySize;
    }
    public static int getWindowVisibleCountMax(Context context, int row) {
        int visibleCountMax = 0;
        int height = getMetrics(context).heightPixels;
        int width = getMetrics(context).widthPixels;
        int itemWidth = width / row;
        visibleCountMax = (height / itemWidth) * row + row;
        AppLog.i(TAG, "end getWindowVisibleCountMax visibleCountMax=" + visibleCountMax);
        return visibleCountMax;
    }
    public static void hideInputMethod(Activity activity) {
        InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager.isActive()) {
            View view = activity.getCurrentFocus();
            if (view != null) {
                inputMethodManager.hideSoftInputFromWindow(view.getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        }
    }
}

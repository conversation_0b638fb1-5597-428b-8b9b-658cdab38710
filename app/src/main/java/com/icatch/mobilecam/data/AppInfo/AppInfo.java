package com.icatch.mobilecam.data.AppInfo;
import android.app.ActivityManager;
import android.content.Context;
import androidx.appcompat.app.AlertDialog;

import com.blankj.utilcode.util.PathUtils;
import com.icatch.mobilecam.Listener.MyOrientoinListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.data.entity.BluetoothAppDevice;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.utils.GpsUtil;
import com.icatchtek.bluetooth.customer.client.ICatchBluetoothClient;
import com.ijoyer.mobilecam.R;
import java.util.List;
public class AppInfo {
    public static final String APP_PATH = "/IJOYER/";
    public static final String APP_VERSION = "V1.2.0_beta34";
    public static final String SDK_VERSION = "V3.6.0.35";
    public static final String SDK_LOG_DIRECTORY_PATH = APP_PATH + "IJOYER_SDK_Log/";
    public static final String APP_LOG_DIRECTORY_PATH = APP_PATH + "IJOYER_APP_Log/";
    public static final String PROPERTY_CFG_FILE_NAME = "netconfig.properties";
    public static final String STREAM_OUTPUT_DIRECTORY_PATH = APP_PATH + "Resoure/Raw/";
    public static final String PROPERTY_CFG_DIRECTORY_PATH = APP_PATH + "Resoure/";
    public static final String DOWNLOAD_PATH = "/DCIM/IJOYER/";
//    public static final String TEMP_DOWNLOAD_PATH = "/DCIM/IJTEMP/";
    public static final String TEMP_DOWNLOAD_PATH = PathUtils.getInternalAppCachePath() + "/" ;
    public static final String DOWNLOAD_PATH_PHOTO = DOWNLOAD_PATH;
    public static final String DOWNLOAD_PATH_VIDEO = DOWNLOAD_PATH;
    public static final String AUTO_DOWNLOAD_PATH = DOWNLOAD_PATH;
    public static final String PHOTO_TEMP = "/IJTEMP/";
    public static final String FW_UPGRADE_FILENAME = "sphost.BRN";
    private static final String TAG = AppInfo.class.getSimpleName();
    public static final String FILE_GOOGLE_TOKEN = "file_googleToken.dat";
    public static final String EULA_VERSION = "1.3";
    public static boolean isSupportAutoReconnection = false;
    public static boolean isSupportBroadcast = false;
    public static boolean isSupportSetting = false;
    public static boolean saveSDKLog = false;
    public static boolean isSdCardExist = true;
    public static boolean disableAudio = false;
    public static boolean enableLive = false;
    public static boolean autoDownloadAllow = false;
    public static float autoDownloadSizeLimit = 1.0f;
    public static boolean isDownloading = false;
    public static PhotoWallLayoutType photoWallLayoutType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
    public static PhotoWallLayoutType remoteViewType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
    public static int currentViewpagerPosition = 0;
    public static int curVisibleItem = 0;
    public static boolean enableSoftwareDecoder = false;
    public static boolean isBLE = false;
    public static ICatchBluetoothClient iCatchBluetoothClient;
    public static BluetoothAppDevice curBtDevice;
    public static boolean isReleaseBTClient = true;
    public static int videoCacheNum = 0;
    public static int curFps = 30;
    public static double unsteadyTime = 0.1; 
    public static String inputIp = "***********";
    public static boolean isNeedReconnect = true;
    public static boolean enableDumpVideo = false;
    public static MyOrientoinListener.ScreenOrientation curScreenOrientation = MyOrientoinListener.ScreenOrientation.SCREEN_ORIENTATION_PORTRAIT;
    public static boolean enableRender = false;
    public static boolean isAppSentToBackground(final Context context) {
        ActivityManager activityManager = (ActivityManager) context.getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        if (appProcesses != null) {
            for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
                if (appProcess.processName.equals(context.getPackageName())) {
                    if (appProcess.importance != ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                        AppLog.d(TAG, "isAppSentToBackground: true");
                        return true;
                    } else {
                        AppLog.d(TAG, "isAppSentToBackground: false");
                        return false;
                    }
                }
            }
        }
        AppLog.d(TAG, "isAppSentToBackground: false");
        return false;
    }
    public static void checkLocationDialog(Context context) {
        if (!GpsUtil.checkGPSIsOpen(context)) {
            AlertDialog.Builder builder;
            builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
            builder.setIcon(R.drawable.warning);
            builder.setTitle(R.string.title_warning);
            builder.setMessage("请打开位置信息！\n\n定位服务基于网络信息（WLAN/蜂窝网络/蓝牙/IP）等来提供，开启位置信息才能正常连接和使用相机！");
            builder.setNegativeButton(android.R.string.cancel, null);
            builder.setPositiveButton(R.string.ok, (dialogInterface, i) -> GpsUtil.openGpsSettings(context));
            builder.show();
        }
    }
}

package com.icatch.mobilecam.data.entity;
import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.utils.ConvertTools;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class LocalPbItemInfo {
    public File file;
    public int section;
    public int position;
    public boolean isItemChecked = false;
    private boolean isPanorama = false;
    public String type = "";
    public String time = "";
    public boolean isCheck = false;
    public int selectPosition = 0;
    public boolean isDownload;


    public LocalPbItemInfo() {
    }

    public LocalPbItemInfo(File file, int section) {
        this.file = file;
        this.section = section;
        this.isItemChecked = false;
        this.initQuarter();
    }
    public LocalPbItemInfo(File file, int section, boolean isPanorama) {
        this.file = file;
        this.section = section;
        this.isPanorama = isPanorama;
        this.initQuarter();
    }
    public LocalPbItemInfo(File file, int section, boolean isPanorama, int position) {
        this(file, section, isPanorama);
        this.position = position;
    }
    public LocalPbItemInfo(File file) {
        super();
        this.file = file;
        this.isItemChecked = false;
        this.initQuarter();
    }
    public boolean isPanoramaQuarter;
    public int panoramaQuarterIndex = -1;
    public String panoramaQuarterFront;
    public String panoramaQuarterTail;
    public void initQuarter() {
        this.isPanoramaQuarter = this.getFileName().matches(StitchUtils.REGEX);
        if (this.isPanoramaQuarter) {
            this.panoramaQuarterFront = this.getFileName().substring(0, 15); 
            this.panoramaQuarterTail = this.getFileName().substring(17, 20); 
            char ch = this.getFileName().charAt(16);
            if (Character.isDigit(ch)) {
                int index = Integer.parseInt(String.valueOf(ch));
                if (index >= 0 && index <= 3) {
                    this.panoramaQuarterIndex = index;
                }
            }
        }
    }
    public boolean isQuarterAllExists() {
        final Pattern pattern = Pattern.compile("_[0-3]\\.");
        Matcher matcher = pattern.matcher(this.getFilePath());
        String file0 = matcher.replaceFirst("_0.");
        String file1 = matcher.replaceFirst("_1.");
        String file2 = matcher.replaceFirst("_2.");
        String file3 = matcher.replaceFirst("_3.");
        boolean isFile0Exist = new File(file0).exists();
        boolean isFile1Exist = new File(file1).exists();
        boolean isFile2Exist = new File(file2).exists();
        boolean isFile3Exist = new File(file3).exists();
        return isFile0Exist && isFile1Exist && isFile2Exist && isFile3Exist;
    }
    public boolean getQuarterIs0To3Sort() {
        final Pattern pattern = Pattern.compile("_[0-3]\\.");
        Matcher matcher = pattern.matcher(this.getFilePath());
        String file0 = matcher.replaceFirst("_0.");
        String file3 = matcher.replaceFirst("_3.");
        if ((new File(file0).lastModified() - new File(file3).lastModified()) > 0) {
            return true;
        } else {
            return false;
        }
    }
    public int[] getPanoramaQuarterPos0123() {
        if (this.isPanoramaQuarter && isQuarterAllExists()) {
            if (getQuarterIs0To3Sort()) {
                switch (this.panoramaQuarterIndex) {
                    case 0:
                        return new int[]{position + 0, position + 1, position + 2, position + 3};
                    case 1:
                        return new int[]{position - 1, position + 0, position + 1, position + 2};
                    case 2:
                        return new int[]{position - 2, position - 1, position + 0, position + 1};
                    case 3:
                        return new int[]{position - 3, position - 2, position - 1, position + 0};
                }
            } else {
                switch (this.panoramaQuarterIndex) {
                    case 3:
                        return new int[]{position + 0, position + 1, position + 2, position + 3};
                    case 2:
                        return new int[]{position - 1, position + 0, position + 1, position + 2};
                    case 1:
                        return new int[]{position - 2, position - 1, position + 0, position + 1};
                    case 0:
                        return new int[]{position - 3, position - 2, position - 1, position + 0};
                }
            }
        }
        return new int[0];
    }
    public void setSection(int section) {
        this.section = section;
    }
    public String getFilePath() {
        return file.getPath();
    }
    public String getFileDate() {
        long time = file.lastModified();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(new Date(time));
    }
    public String getFileSize() {
        int size = (int) file.length();
        return ConvertTools.ByteConversionGBMBKB(size);
    }
    public String getFileName() {
        return file.getName();
    }
    public String getFileDateMMSS() {
        long time = file.lastModified();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(new Date(time));
    }
    public boolean isPanorama() {
        return isPanorama;
    }
    public void setPanorama(boolean panorama) {
        isPanorama = panorama;
    }
    public void getCreateTime() {
        String filePath = file.getPath();
        String strTime = null;
        try {
            Process p = Runtime.getRuntime().exec("cmd /C dir " + filePath + "/tc");
            InputStream is = p.getInputStream();
            BufferedReader br = new BufferedReader(new InputStreamReader(is));
            String line;
            while ((line = br.readLine()) != null) {
                if (line.endsWith(".txt")) {
                    strTime = line.substring(0, 17);
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("创建时间    " + strTime);
    }
}

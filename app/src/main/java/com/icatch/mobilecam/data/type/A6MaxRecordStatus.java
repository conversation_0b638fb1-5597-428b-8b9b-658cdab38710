package com.icatch.mobilecam.data.type;

/**
 * A6Max录像状态定义
 * 根据0xD763数值列表定义
 */
public class A6MaxRecordStatus {
    /**
     * 非视频模式
     */
    public static final int NON_VIDEO_MODE = 0;
    
    /**
     * 状态允许app执行录像功能
     */
    public static final int READY_TO_RECORD = 1;
    
    /**
     * 录像中，同时在移动中，允许执行停止录像操作
     */
    public static final int RECORDING_AND_MOVING = 2;
    
    /**
     * 录像停止，但在归位当中，不能允许执行录像操作，app应停止录像按钮显示
     */
    public static final int RECORDING_STOPPED_REPOSITIONING = 3;
    
    /**
     * 错误，录像卡满
     */
    public static final int ERROR_SD_CARD_FULL = 4;
    
    /**
     * 错误，录像卡被拔出
     */
    public static final int ERROR_SD_CARD_REMOVED = 5;
    
    /**
     * 错误，WiFi掉线
     */
    public static final int ERROR_WIFI_DISCONNECTED = 6;
    
    /**
     * 获取状态描述
     */
    public static String getStatusDescription(int status) {
        switch (status) {
            case NON_VIDEO_MODE:
                return "非视频模式";
            case READY_TO_RECORD:
                return "准备录像";
            case RECORDING_AND_MOVING:
                return "录像中（移动中）";
            case RECORDING_STOPPED_REPOSITIONING:
                return "相机归位中，请稍候";
            case ERROR_SD_CARD_FULL:
                return "错误：SD卡已满";
            case ERROR_SD_CARD_REMOVED:
                return "错误：SD卡被拔出";
            case ERROR_WIFI_DISCONNECTED:
                return "错误：WiFi掉线";
            default:
                return "未知状态：" + status;
        }
    }
    
    /**
     * 检查是否可以开始录像
     */
    public static boolean canStartRecord(int status) {
        return status == READY_TO_RECORD;
    }
    
    /**
     * 检查是否可以停止录像
     */
    public static boolean canStopRecord(int status) {
        return status == RECORDING_AND_MOVING;
    }
    
    /**
     * 检查是否为错误状态
     */
    public static boolean isErrorStatus(int status) {
        return status >= ERROR_SD_CARD_FULL && status <= ERROR_WIFI_DISCONNECTED;
    }
    
    /**
     * 检查是否需要禁用录像按钮
     */
    public static boolean shouldDisableRecordButton(int status) {
        return status == RECORDING_STOPPED_REPOSITIONING || isErrorStatus(status);
    }
}

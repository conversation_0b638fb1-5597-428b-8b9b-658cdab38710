package com.icatch.mobilecam.data.AppInfo;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
public class CfgProperty {
    private static Map<String, String> propertyMap = new HashMap<String, String>();
    private String fileName;
    public CfgProperty(String fileName) {
        this.fileName = fileName;
    }
    public String getProperty(String key) throws FileNotFoundException, IOException {
        String value = CfgProperty.propertyMap.get(key);
        Properties property = new Properties();
        FileInputStream inputFile = null;
        if (value == null) {
            inputFile = new FileInputStream(this.fileName);
            property.load(inputFile);
            value = property.getProperty(key);
            CfgProperty.propertyMap.put(key, value);
        }
        return value;
    }
    public Map<String, String> getProperty(List<String> propertyList) throws FileNotFoundException, IOException {
        Map<String, String> propertyMap = new HashMap<String, String>();
        Properties property = new Properties();
        FileInputStream inputFile = null;
        try {
            inputFile = new FileInputStream(this.fileName);
            property.load(inputFile);
            for (String name : propertyList) {
                String data = property.getProperty(name);
                propertyMap.put(name, data);
            }
        } finally {
            if (inputFile != null) {
                inputFile.close();
            }
        }
        return propertyMap;
    }
}

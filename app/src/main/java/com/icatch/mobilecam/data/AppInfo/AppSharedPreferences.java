package com.icatch.mobilecam.data.AppInfo;
import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
public class AppSharedPreferences {
    private final static String FILE_NAME = "storeInfo";
    public final static String OBJECT_NAME_LIVE_URL = "liveUrl";
    public final static String OBJECT_NAME_INPUT_IP = "inputIp";
    public static void writeDataByName(Context context, String name, String value) {
        SharedPreferences mySharedPreferences = context.getSharedPreferences( FILE_NAME, Activity.MODE_PRIVATE );
        SharedPreferences.Editor editor = mySharedPreferences.edit();
        editor.putString( name, value );
        editor.commit();
    }
    public static String readDataByName(Context context, String name) {
        SharedPreferences sharedPreferences = context.getSharedPreferences( FILE_NAME, Activity.MODE_PRIVATE );
        String value = sharedPreferences.getString( name, "***********" );
        return value;
    }
}

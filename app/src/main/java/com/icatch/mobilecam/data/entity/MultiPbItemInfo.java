package com.icatch.mobilecam.data.entity;

import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.bean.CheckSingleCameraPhotoBean;
import com.icatchtek.reliant.customer.type.ICatchFile;

import java.io.File;
import java.util.ArrayList;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MultiPbItemInfo {
    private static final String TAG = MultiPbItemInfo.class.getSimpleName();
    public ICatchFile iCatchFile;
    public int section;
    public boolean isItemChecked = false;
    private boolean isPanorama = false;
    public String fileSize;
    public String fileTime;
    public String fileDate;
    public String fileDuration;
    public boolean isDownload;
    public File fileLocal;

    public ArrayList<CheckSingleCameraPhotoBean> hrdFilePath;

    public MultiPbItemInfo(ICatchFile file) {
        super();
        this.iCatchFile = file;
        this.isItemChecked = false;
        initQuarter();
    }

    public MultiPbItemInfo(ICatchFile file, int section) {
        super();
        this.iCatchFile = file;
        this.section = section;
        this.isItemChecked = false;
        initQuarter();
    }

    public MultiPbItemInfo(ICatchFile iCatchFile, int section, boolean isPanorama, String fileSize, String fileTime, String fileDate, String fileDuration) {
        this.iCatchFile = iCatchFile;
        this.section = section;
        this.isPanorama = isPanorama;
        this.isItemChecked = false;
        this.fileSize = fileSize;
        this.fileTime = fileTime;
        this.fileDate = fileDate;
        this.fileDuration = fileDuration;
        initQuarter();
    }

    public void setPanorama(boolean panorama) {
        isPanorama = panorama;
    }

    public boolean isPanorama() {
        return this.isPanorama;
    }

    public void setSection(int section) {
        this.section = section;
    }

    public String getFilePath() {
        return iCatchFile.getFilePath();
    }

    public int getFileHandle() {
        return iCatchFile.getFileHandle();
    }

    public String getFileDate() {
        return fileDate;
    }

    public String getFileSize() {
        return fileSize;
    }

    public long getFileSizeInteger() {
        long fileSize = iCatchFile.getFileSize();
        return fileSize;
    }

    public String getFileDuration() {
        return fileDuration;
    }

    public String getFileName() {
        return iCatchFile.getFileName();
    }

    public String getFileDateMMSS() {
        return fileTime;
    }

    public boolean isPanoramaQuarter;
    public int panoramaQuarterIndex = -1;

    public void initQuarter() {
        this.isPanoramaQuarter = this.getFileName().matches(StitchUtils.REGEX);
        if (this.isPanoramaQuarter) {
//            MyCamera curCamera = CameraManager.getInstance().getCurCamera();
//            String cameraName = curCamera.getCameraFixedInfo().getCameraName();
            char ch;
//            if ("A8".equals(cameraName)) {
//                ch = this.getFileName().charAt(17);
//            } else {
//                ch = this.getFileName().charAt(16);
//            }

            ch = this.getFileName().charAt(16);
            if (Character.isDigit(ch)) {
                int index = Integer.parseInt(String.valueOf(ch));
                if (index >= 0 && index <= 3) {
                    this.panoramaQuarterIndex = index;
                }
            }
        }
    }

    public boolean isQuarter0123Exists() {
        final Pattern pattern = Pattern.compile("_[0-3]\\.");
        Matcher matcher = pattern.matcher(this.getFileName());
        String file0 = matcher.replaceFirst("_0.");
        String file1 = matcher.replaceFirst("_1.");
        String file2 = matcher.replaceFirst("_2.");
        String file3 = matcher.replaceFirst("_3.");
        return false;
    }

    public boolean getQuarterIs0To3Sort() {
        final Pattern pattern = Pattern.compile("_[0-3]\\.");
        Matcher matcher = pattern.matcher(this.getFilePath());
        String file0 = matcher.replaceFirst("_0.");
        String file3 = matcher.replaceFirst("_3.");
        if ((new File(file0).lastModified() - new File(file3).lastModified()) > 0) {
            return true;
        } else {
            return false;
        }
    }
}

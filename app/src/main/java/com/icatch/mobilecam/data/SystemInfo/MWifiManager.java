package com.icatch.mobilecam.data.SystemInfo;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.net.wifi.WifiConfiguration;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.text.TextUtils;

import com.blankj.utilcode.util.LogUtils;
import com.icatch.mobilecam.Log.AppLog;

import java.util.List;

public class MWifiManager {
    private static String TAG = "MWifiManager";
    private static String WIFI_SSID_UNKNOWN = "unknown";

    //缓存起来的ssid
    private static String cacheSsid;

    /// 获取缓存的ssid，规避合规问题
    public static String getCacheSsid(Context context){
        if (TextUtils.isEmpty(cacheSsid)){
            return  getSsid(context);
        }else{
            return cacheSsid;
        }
    }

    public static String getSsid(Context context) {
        if (!isWifiEnabled(context)) {
            AppLog.e(TAG, "----------ssid is null=");
            cacheSsid = null;
            return null;
        }
        //android 8.0及以下
        String ssid = WIFI_SSID_UNKNOWN;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            WifiManager mWifi = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = mWifi.getConnectionInfo();
            if (wifiInfo != null) {
                ssid = wifiInfo.getSSID();
                if (ssid.contains("\"")) {
                    ssid = ssid.replace("\"", "");
                }
            } else {
                AppLog.i(TAG, "getSsid wifiInfo is null");
            }
        } else if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
            ConnectivityManager mConnectivityManager = (ConnectivityManager) context.getApplicationContext().getSystemService(Context.CONNECTIVITY_SERVICE);
            NetworkInfo wifiInfo2 = mConnectivityManager.getActiveNetworkInfo();
            AppLog.i(TAG, "getSsid wifiInfo2:" + wifiInfo2);
            if (wifiInfo2 != null) {
                AppLog.i(TAG, "getSsid wifiInfo2.getExtraInfo()" + wifiInfo2.getExtraInfo());
            }
            if (wifiInfo2 == null || wifiInfo2.getExtraInfo() == null) {
                ssid = getSsidByNetworkId(context);
            } else {
                String wifiName = wifiInfo2.getExtraInfo();
                ssid = wifiName.replaceAll("\"", "");
            }
        } else {
            WifiManager mWifi = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            WifiInfo wifiInfo = mWifi.getConnectionInfo();
            if (wifiInfo != null) {
                ssid = wifiInfo.getSSID();
                if (ssid.contains("\"")) {
                    ssid = ssid.replace("\"", "");
                }
            } else {
                AppLog.i(TAG, "getSsid wifiInfo is null");
            }
        }
        //android 8.0以后

        AppLog.i(TAG, "getSsid ssid:" + ssid);
        cacheSsid = ssid;
        return ssid;
    }

    public static boolean isWifiEnabled(Context context) {
        WifiManager mWifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        return mWifiManager.isWifiEnabled();
    }

    private static String getSsidByNetworkId(Context context) {
        AppLog.d(TAG, "getSsidByNetworkId ");
        String ssid = null;
        WifiManager wifiManager = ((WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE));
        if (null != wifiManager) {
            WifiInfo wifiInfo = wifiManager.getConnectionInfo();
            int networkId = wifiInfo.getNetworkId();
            List<WifiConfiguration> configuredNetworks = wifiManager.getConfiguredNetworks();
            for (WifiConfiguration wifiConfiguration : configuredNetworks) {
                if (wifiConfiguration.networkId == networkId) {
                    ssid = wifiConfiguration.SSID;
                    break;
                }
            }
        }
        if (ssid != null && ssid.contains("\"")) {
            ssid = ssid.replace("\"", "");
        }
        AppLog.d(TAG, "getSsidByNetworkId ssid:" + ssid);
        return ssid;
    }

    @SuppressWarnings("deprecation") // 明确允许在旧版本上使用弃用的 API
    public static boolean isWifiConnected(Context context) {
        ConnectivityManager connMgr =
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connMgr == null) {
            LogUtils.file(TAG + "：无法获取 ConnectivityManager 服务");
            return false;
        }
        // 对于 Android 10 (API 29) 及以上版本，使用 NetworkCapabilities
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            Network[] networks = connMgr.getAllNetworks();
            for (Network network : networks) {
                NetworkCapabilities capabilities = connMgr.getNetworkCapabilities(network);
                if (capabilities != null && capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                    LogUtils.file(TAG + "：当前的NetworkCapabilities + " + capabilities.getTransportInfo());
                    return true; // 找到了一个 Wi-Fi 连接
                }
            }
            LogUtils.file(TAG,"无法获取到任何 Wi-Fi 连接");
            return false;
        } else {
            // 对于 Android 9 (API 28) 及以下版本，使用旧的 getActiveNetworkInfo()
            NetworkInfo activeNetworkInfo = connMgr.getActiveNetworkInfo();
            // 检查活动网络是否存在，是否是 Wi-Fi 类型，并且是否已连接
            return activeNetworkInfo != null
                    && activeNetworkInfo.isConnected()
                    && activeNetworkInfo.getType() == ConnectivityManager.TYPE_WIFI;
        }
    }

}

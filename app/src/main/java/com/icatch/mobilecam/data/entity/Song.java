package com.icatch.mobilecam.data.entity;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;

public class Song {
 
    public String song;//歌曲名
    public String singer;//歌手
    public long size;//歌曲所占空间大小
    public int duration;//歌曲时间长度
    public String path;//歌曲地址
    public File file;
    public boolean isCheck;
    public String type = "";
    public String time = "";
    public boolean isPlay = false;
    public float playPosition;


    public String getFileDate() {
        long time = file.lastModified();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(new Date(time));
    }

}
package com.icatch.mobilecam.Function.CameraAction;

import static com.icatch.mobilecam.Application.PanoramaApp.getContext;
import static org.opencv.photo.Photo.createCalibrateDebevec;
import static org.opencv.photo.Photo.createMergeMertens;
import static razerdp.basepopup.BasePopupSDK.getApplication;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.media.ExifInterface;
import android.os.AsyncTask;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.alibaba.fastjson.JSON;
import com.blankj.utilcode.util.FileIOUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ToastUtils;
import com.detu.libszstitch.SzStitch;
import com.detu.remux.DeviceId;
import com.detu.remux.IVideoStitchListener;
import com.detu.remux.ReVideoStitch;
import com.detu.remux.RetCode;
import com.detu.remux.StitchParam;
import com.detu.szStitch.StitchUtils;
import com.icatch.mobilecam.Application.Const;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.Presenter.RemoteMultiPbPresenter;
import com.icatch.mobilecam.SdkApi.CameraProperties;
import com.icatch.mobilecam.SdkApi.FileOperation;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.Message.AppMessage;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.data.SystemInfo.SystemInfo;
import com.icatch.mobilecam.data.entity.DownloadInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.activity.LocalMultiPbActivity;
import com.icatch.mobilecam.ui.adapter.DownloadManagerAdapter;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.ui.appdialog.AppToast;
import com.icatch.mobilecam.ui.appdialog.CustomDownloadDialog;
import com.icatch.mobilecam.ui.popupwindow.HdrWindow;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.MediaRefresh;
import com.icatch.mobilecam.utils.SPKey;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.VersionUtils;
import com.icatch.mobilecam.utils.VideoListUtil;
import com.icatch.mobilecam.utils.VideoUtil;
import com.icatch.mobilecam.utils.fileutils.FileTools;
import com.icatchtek.reliant.customer.type.ICatchFile;
import com.icatchtek.reliant.customer.type.ICatchFileType;
import com.ijoyer.camera.bean.HdrBean;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.camera.widget.SettingRecordDialog;
import com.ijoyer.mobilecam.R;

import org.opencv.core.CvType;
import org.opencv.core.Mat;
import org.opencv.core.MatOfFloat;
import org.opencv.core.Size;
import org.opencv.imgcodecs.Imgcodecs;
import org.opencv.imgproc.Imgproc;
import org.opencv.photo.CalibrateDebevec;
import org.opencv.photo.MergeMertens;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Timer;
import java.util.TimerTask;
import java.util.Vector;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import VideoHandle.EpEditor;
import VideoHandle.OnEditorListener;
import io.reactivex.Observable;
import io.reactivex.ObservableEmitter;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.Observer;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;

public class PbDownloadManager {
    private static String TAG = "PbDownloadManager";
    private ExecutorService executor;
    public long downloadProgress;
    public FileOperation fileOperation;
    private LinkedList<ICatchFile> downloadTaskList;
    private LinkedList<ICatchFile> downloadChooseList;
    private ICatchFile curDownloadFile;
    private DownloadManagerAdapter downloadManagerAdapter;
    private AlertDialog.Builder builder;
    private Context context;
    private HashMap<Integer, DownloadInfo> downloadInfoMap = new HashMap<>();
    private ICatchFile currentDownloadFile;
    private Timer downloadProgressTimer;
    private int downloadFailed = 0;
    private int downloadSucceed = 0;
    private CustomDownloadDialog customDownloadDialog;
    private String curFilePath = "";
    private AlertDialog cancelDownloadDialog;
    public static boolean isA6Camera = false;
    private CameraProperties cameraProperties;
    public String cameraVersion;
    public static boolean isA3SCamera = false;
    public static boolean isA6SCamera = false;
    public static boolean isA6MaxCamera = false;
    public HdrWindow hdrWindow;
    public RemoteMultiPbPresenter remoteMultiPbPresenter;
    public Activity activity;


    public PbDownloadManager(Context context, LinkedList<ICatchFile> downloadList) {
        this.context = context;
        this.downloadTaskList = downloadList;
        this.fileOperation = CameraManager.getInstance().getCurCamera().getFileOperation();
        this.cameraProperties = CameraManager.getInstance().getCurCamera().getCameraProperties();
        this.isA6Camera = cameraProperties.hasFunction(PropertyId.A6_ROTATE_MOTOR_STATE) && cameraProperties.hasFunction(PropertyId.A6_ROTATE_SHOT_TIMES);
        this.cameraVersion = CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraVersion();
        downloadChooseList = new LinkedList<>();
        downloadChooseList.addAll(downloadTaskList);
        for (int ii = 0; ii < downloadChooseList.size(); ii++) {
            DownloadInfo downloadInfo = new DownloadInfo(downloadChooseList.get(ii), downloadChooseList.get(ii).getFileSize(), 0, 0, false);
            downloadInfoMap.put(downloadChooseList.get(ii).getFileHandle(), downloadInfo);
        }
        isA3SCamera = CameraUtils.isA3S();
        isA6SCamera = CameraUtils.isA6SOrA8();

        hdrWindow = new HdrWindow(context);
    }

    public PbDownloadManager(Context context, LinkedList<ICatchFile> downloadList, Activity activity) {
        this.activity = activity;
        this.context = context;
        this.downloadTaskList = downloadList;
        this.fileOperation = CameraManager.getInstance().getCurCamera().getFileOperation();
        this.cameraProperties = CameraManager.getInstance().getCurCamera().getCameraProperties();
        this.isA6Camera = cameraProperties.hasFunction(PropertyId.A6_ROTATE_MOTOR_STATE) && cameraProperties.hasFunction(PropertyId.A6_ROTATE_SHOT_TIMES);
        this.cameraVersion = CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraVersion();
        downloadChooseList = new LinkedList<>();
        downloadChooseList.addAll(downloadTaskList);
        for (int ii = 0; ii < downloadChooseList.size(); ii++) {
            DownloadInfo downloadInfo = new DownloadInfo(downloadChooseList.get(ii), downloadChooseList.get(ii).getFileSize(), 0, 0, false);
            downloadInfoMap.put(downloadChooseList.get(ii).getFileHandle(), downloadInfo);
        }
        String cameraName = CameraUtils.getCurCamera();
        isA3SCamera = CameraUtils.isA3S(cameraName);
        isA6SCamera = CameraUtils.isA6S(cameraName) || CameraUtils.isA8(cameraName);
        isA6MaxCamera = CameraUtils.isA6Max(cameraName);

        hdrWindow = new HdrWindow(context);
        if (remoteMultiPbPresenter == null) {
            remoteMultiPbPresenter = new RemoteMultiPbPresenter(activity);
        }

        if (remoteMultiPbPresenter.downloadManager == null) {
            remoteMultiPbPresenter.downloadManager = new PbDownloadManager(activity);
        }
    }

    public PbDownloadManager(Context context) {
        this.context = context;
    }

    private boolean isEx;
    private boolean isUseHdrThis;

    public void show() {
        isCancel = false;
        if (downloadTaskList.size() > 0) {
            //分组，看照片数组内  哪些是 4张一组  哪些是 12张一组
            HashMap<String, ArrayList<String>> map = new HashMap<>();
            for (int i = 0; i < downloadTaskList.size(); i++) {
                String fileName = downloadTaskList.get(i).getFileName().substring(downloadTaskList.get(i).getFileName().lastIndexOf("/") + 1);
                String groupName = fileName.substring(0, fileName.lastIndexOf("_"));
                ArrayList<String> checkList = map.get(groupName);
                if (checkList == null) {
                    ArrayList<String> newList = new ArrayList<>();
                    newList.add(downloadTaskList.get(i).getFilePath());
                    map.put(groupName, newList);
                } else {
                    checkList.add(downloadTaskList.get(i).getFilePath());
                }
            }

            if (downloadTaskList.size() == 12 && isA6SCamera && map.size() == 1) {
                if (SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 0) {
                    hdrWindow.showPopupWindow();
                    hdrWindow.tvCancel.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (hdrWindow.ivSelect.isActivated()) {
                                SPUtils.getInstance().put(Const.SP.HDR_DATA, 1);
                            } else {
                                SPUtils.getInstance().put(Const.SP.HDR_DATA, 0);
                            }
                            hdrWindow.dismiss();
                            to4Pic();
                        }
                    });

                    hdrWindow.tvSure.setOnClickListener(new OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (hdrWindow.ivSelect.isActivated()) {
                                SPUtils.getInstance().put(Const.SP.HDR_DATA, 2);
                            } else {
                                SPUtils.getInstance().put(Const.SP.HDR_DATA, 0);

                            }
                            hdrWindow.dismiss();

                            showDownloadManagerDialog();
                            executor = Executors.newSingleThreadExecutor();
                            currentDownloadFile = downloadTaskList.getFirst();
                            new DownloadAsyncTask(currentDownloadFile).execute();
                            downloadProgressTimer = new Timer();
                            downloadProgressTimer.schedule(new DownloadProgressTask(), 10, 10);
                        }
                    });
                } else if (SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 1) {
                    to4Pic();
                } else if (SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 2) {
                    showDownloadManagerDialog();
                    executor = Executors.newSingleThreadExecutor();
                    currentDownloadFile = downloadTaskList.getFirst();
                    new DownloadAsyncTask(currentDownloadFile).execute();
                    downloadProgressTimer = new Timer();
                    downloadProgressTimer.schedule(new DownloadProgressTask(), 10, 10);
                }
            } else {
                if (downloadTaskList.size() != 12 * map.size() || !isA6SCamera) {
                    showDownloadManagerDialog();
                    executor = Executors.newSingleThreadExecutor();
                    currentDownloadFile = downloadTaskList.getFirst();
                    new DownloadAsyncTask(currentDownloadFile).execute();
                    downloadProgressTimer = new Timer();
                    downloadProgressTimer.schedule(new DownloadProgressTask(), 10, 10);
                } else {
                    //假如超出2组图片时候  有hdr图片组的（12张）就不用全下了，只要A的图片即可
                    //不需要HDR12张选4张
                    try {
                        if (map.size() > 1 && (SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 0 || SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 1) == 1)) {
                            if (SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 0) {
                                hdrWindow.showPopupWindow();
                                hdrWindow.tvCancel.setOnClickListener(new OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        if (hdrWindow.ivSelect.isActivated()) {
                                            SPUtils.getInstance().put(Const.SP.HDR_DATA, 1);
                                        } else {
                                            SPUtils.getInstance().put(Const.SP.HDR_DATA, 0);
                                        }
                                        hdrWindow.dismiss();
                                        to4PicV2();
                                        isUseHdrThis = false;
                                    }
                                });

                                hdrWindow.tvSure.setOnClickListener(new OnClickListener() {
                                    @Override
                                    public void onClick(View v) {
                                        if (hdrWindow.ivSelect.isActivated()) {
                                            SPUtils.getInstance().put(Const.SP.HDR_DATA, 2);
                                        } else {
                                            SPUtils.getInstance().put(Const.SP.HDR_DATA, 0);
                                        }
                                        hdrWindow.dismiss();
                                        isUseHdrThis = true;

                                        isEx = false;
                                        showDownloadManagerDialog();
                                        executor = Executors.newSingleThreadExecutor();
                                        currentDownloadFile = downloadTaskList.getFirst();
                                        new DownloadAsyncTask(currentDownloadFile).execute();
                                        downloadProgressTimer = new Timer();
                                        downloadProgressTimer.schedule(new DownloadProgressTask(), 10, 10);
                                        isEx = true;
                                    }
                                });
                                isEx = true;
                            } else {
                                to4PicV2();
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        if (!isEx) {
                            showDownloadManagerDialog();
                            executor = Executors.newSingleThreadExecutor();
                            currentDownloadFile = downloadTaskList.getFirst();
                            new DownloadAsyncTask(currentDownloadFile).execute();
                            downloadProgressTimer = new Timer();
                            downloadProgressTimer.schedule(new DownloadProgressTask(), 10, 10);
                        }
                    }
                }

            }
        }
    }

    private void to4PicV2() {
        Iterator<ICatchFile> iterator = downloadTaskList.iterator();
        while (iterator.hasNext()) {
            ICatchFile value = iterator.next();
            if (value.getFileName().toUpperCase().contains("B") || value.getFileName().toUpperCase().contains("C")) {
                iterator.remove();
            }
        }
        Iterator<Map.Entry<Integer, DownloadInfo>> iterator2 = downloadInfoMap.entrySet().iterator();
        while (iterator2.hasNext()) {
            Map.Entry<Integer, DownloadInfo> entry = iterator2.next();
            DownloadInfo value = entry.getValue();
            if (value.file.getFileName().toUpperCase().contains("B") || value.file.getFileName().toUpperCase().contains("C")) {
                iterator2.remove();
            }
        }

        Iterator<ICatchFile> iterator3 = downloadChooseList.iterator();
        while (iterator3.hasNext()) {
            ICatchFile value = iterator3.next();
            if (value.getFileName().toUpperCase().contains("B") || value.getFileName().toUpperCase().contains("C")) {
                iterator3.remove();
            }
        }
        isEx = false;
        showDownloadManagerDialog();
        executor = Executors.newSingleThreadExecutor();
        currentDownloadFile = downloadTaskList.getFirst();
        new DownloadAsyncTask(currentDownloadFile).execute();
        downloadProgressTimer = new Timer();
        downloadProgressTimer.schedule(new DownloadProgressTask(), 10, 10);
        isEx = true;
    }

    private void to4Pic() {
        LinkedList<ICatchFile> iCatchFilesNew = new LinkedList<>();
        for (int i = 0; i < downloadTaskList.size(); i++) {
            if (downloadTaskList.get(i).getFileName().contains("A")) {
                iCatchFilesNew.add(downloadTaskList.get(i));
            }
        }
        downloadTaskList.clear();
        downloadTaskList.addAll(iCatchFilesNew);

        currentDownloadFile = downloadTaskList.getFirst();

        downloadInfoMap.clear();
        downloadChooseList.clear();
        for (int ii = 0; ii < iCatchFilesNew.size(); ii++) {
            DownloadInfo downloadInfo = new DownloadInfo(iCatchFilesNew.get(ii), iCatchFilesNew.get(ii).getFileSize(), 0, 0, false);
            downloadInfoMap.put(iCatchFilesNew.get(ii).getFileHandle(), downloadInfo);
        }
        downloadChooseList.addAll(downloadTaskList);

        showDownloadManagerDialog();
        executor = Executors.newSingleThreadExecutor();
        new DownloadAsyncTask(currentDownloadFile).execute();
        downloadProgressTimer = new Timer();
        downloadProgressTimer.schedule(new DownloadProgressTask(), 10, 10);
    }

    public void downloadZd(RemoteMultiPbPresenter remoteMultiPbPresenter) {
        AsyncTask<String, Integer, Boolean> execute = new DownloadAsyncTaskForZd(currentDownloadFile, remoteMultiPbPresenter).execute();
    }

    Handler downloadManagerHandler = new Handler(Looper.getMainLooper()) {
        @SuppressLint("HandlerLeak")
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case AppMessage.UPDATE_LOADING_PROGRESS:
                    ICatchFile icatchFile = ((DownloadInfo) msg.obj).file;
                    downloadInfoMap.put(icatchFile.getFileHandle(), (DownloadInfo) msg.obj);
                    downloadManagerAdapter.notifyDataSetChanged();
                    break;
                case AppMessage.CANCEL_DOWNLOAD_ALL:
                    AppLog.d(TAG, "receive CANCEL_DOWNLOAD_ALL");
                    alertForQuitDownload();
                    break;
                case AppMessage.MESSAGE_CANCEL_DOWNLOAD_SINGLE:
                    ICatchFile temp = (ICatchFile) msg.obj;
                    AppLog.d(TAG, "1122 receive MESSAGE_CANCEL_DOWNLOAD_SINGLE");
                    if (currentDownloadFile == temp) {
                        if (fileOperation.cancelDownload() == false) {
                            Toast.makeText(context, R.string.dialog_cancel_downloading_failed, Toast.LENGTH_SHORT).show();
                            break;
                        }
                        if (curFilePath != null) {
                            File file = new File(curFilePath);
                            if (file == null || !file.exists()) {
                                return;
                            }
                            if (file.delete()) {
                                AppLog.d("2222", "delete file success == " + curFilePath);
                            }
                        }
                    }
                    Toast.makeText(context, R.string.dialog_cancel_downloading_succeeded, Toast.LENGTH_SHORT).show();
                    downloadInfoMap.remove(temp.getFileHandle());
                    downloadChooseList.remove(temp);
                    downloadTaskList.remove(temp);
                    AppLog.d(TAG, "1122 receive MESSAGE_CANCEL_DOWNLOAD_SINGLE downloadChooseList size=" + downloadChooseList.size() + "downloadInfoMap size=" + downloadInfoMap.size());
                    downloadManagerAdapter.notifyDataSetChanged();
                    updateDownloadMessage();
                    if (downloadTaskList.size() <= 0) {
                        if (customDownloadDialog != null) {
                            customDownloadDialog.dismissDownloadDialog();
                        }
                    }
                    break;
                case AppMessage.DOWNLOAD_FAILURE:
                    AppLog.d(TAG, "receive DOWNLOAD_FAILURE downloadFailed=" + downloadFailed);
                    downloadFailed++;
                    updateDownloadMessage();
                    break;
                case AppMessage.DOWNLOAD_SUCCEED:
                    downloadSucceed++;
                    updateDownloadMessage();
                    break;
            }
        }
    };

    private boolean isCancel = false;

    public void showDownloadManagerDialog() {
        downloadManagerAdapter = new DownloadManagerAdapter(context, downloadInfoMap, downloadChooseList, downloadManagerHandler);
        downloadManagerAdapter.setOnCancelBtnClickListener(new DownloadManagerAdapter.OnCancelBtnClickListener() {
            @Override
            public void onClick(ICatchFile downloadFile) {
                cancelDownload(downloadFile);
            }
        });
        customDownloadDialog = new CustomDownloadDialog();
        customDownloadDialog.showDownloadDialog(context, downloadManagerAdapter);
        customDownloadDialog.setBackBtnOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View arg0) {
                alertForQuitDownload();
            }
        });
        updateDownloadMessage();
    }

    public void cancelDownload(ICatchFile downloadFile) {
        if (currentDownloadFile == downloadFile) {
            if (fileOperation.cancelDownload() == false) {
                Toast.makeText(context, R.string.dialog_cancel_downloading_failed, Toast.LENGTH_SHORT).show();
                return;
            }
            if (curFilePath != null) {
                File file = new File(curFilePath);
                if (file == null || !file.exists()) {
                    return;
                }
                if (file.delete()) {
                    AppLog.d("2222", "delete file success == " + curFilePath);
                }
            }
        }
        Toast.makeText(context, R.string.dialog_cancel_downloading_succeeded, Toast.LENGTH_SHORT).show();
        downloadInfoMap.remove(downloadFile.getFileHandle());
        downloadChooseList.remove(downloadFile);
        downloadTaskList.remove(downloadFile);
        downloadManagerAdapter.notifyDataSetChanged();
        updateDownloadMessage();
        if (downloadTaskList.size() <= 0) {
            if (customDownloadDialog != null) {
                customDownloadDialog.dismissDownloadDialog();
            }
        }
    }

    public void alertForQuitDownload() {
        if (builder != null) {
            return;
        }
        builder = new AlertDialog.Builder(context);
        builder.setIcon(R.drawable.warning).setMessage(R.string.download_cancel_all_tips);
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                isCancel = true;
                for (int i = 0; i < downloadChooseList.size(); i++) {
                    FileUtils.delete(StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_PHOTO + downloadChooseList.get(i).getFileName());
                    FileUtils.delete(StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_VIDEO + downloadChooseList.get(i).getFileName());
                    MediaRefresh.scanFileAsync(getContext(), StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_PHOTO + downloadChooseList.get(i).getFileName());
                    MediaRefresh.scanFileAsync(getContext(), StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_VIDEO + downloadChooseList.get(i).getFileName());
                }

                downloadTaskList.clear();
                if (curFilePath != null) {
                    File file = new File(curFilePath);
                    if (file == null || !file.exists()) {
                        return;
                    }
                    if (file.delete()) {
                        AppLog.d("2222", "alertForQuitDownload file success == " + curFilePath);
                    }
                }
                if (fileOperation.cancelDownload() == false) {
                    Toast.makeText(context, R.string.dialog_cancel_downloading_failed, Toast.LENGTH_SHORT).show();
                    return;
                } else {
                    customDownloadDialog.dismissDownloadDialog();
                    if (downloadProgressTimer != null) {
                        downloadProgressTimer.cancel();
                    }
                    Toast.makeText(context, R.string.dialog_cancel_downloading_succeeded, Toast.LENGTH_SHORT).show();
                }
                AppLog.d(TAG, "cancel download task and quit download manager");
            }
        });
        builder.setNegativeButton(R.string.gallery_cancel, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                builder = null;
            }
        });
        cancelDownloadDialog = builder.create();
        cancelDownloadDialog.setCancelable(false);
        cancelDownloadDialog.show();
    }

    public void singleDownloadComplete(boolean result, ICatchFile iCatchFile) {
        if (downloadInfoMap.containsKey(iCatchFile.getFileHandle())) {
            DownloadInfo downloadInfo = downloadInfoMap.get(iCatchFile.getFileHandle());
            downloadInfo.setDone(true);
            downloadInfo.progress = 100;
            downloadManagerAdapter.notifyDataSetChanged();
        }
    }

    private void downloadCompleted() {
        if (cancelDownloadDialog != null) {
            cancelDownloadDialog.dismiss();
            cancelDownloadDialog = null;
        }
        curFilePath = null;
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        builder.setTitle(context.getResources().getString(R.string.download_manager));
        String message = context.getResources().getString(R.string.download_complete_result).replace("$1$", String.valueOf(downloadSucceed))
                .replace("$2$", String.valueOf(downloadFailed));
        builder.setMessage(message);
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        AlertDialog dialog = builder.create();
        dialog.setCancelable(false);
        dialog.show();
    }

    private List<String> tempList = new ArrayList<>();
    private List<String> newTempList = new ArrayList<>();
    private List<String> newTempErrorList = new ArrayList<>();

    private int mp4forVideoCount = 0;

    private void downloadCompletedEx(ICatchFile iCatchFile) {
        if (isA3SCamera) {
            if (cancelDownloadDialog != null) {
                cancelDownloadDialog.dismiss();
                cancelDownloadDialog = null;
            }
            curFilePath = null;
            if (this.context != null) {
                MyProgressDialog.showProgressDialog(context, "正在进行拼接...");
                if (iCatchFile.getFileType() == ICatchFileType.ICH_FILE_TYPE_IMAGE) {
                    new Thread(new StitchThread(tempList, FileType.FILE_PHOTO, CameraUtils.getCurCamera())).start();
                } else if (iCatchFile.getFileType() == ICatchFileType.ICH_FILE_TYPE_VIDEO) {
                    new Thread(new StitchThread(tempList, FileType.FILE_VIDEO, CameraUtils.getCurCamera())).start();
                    return;

//                    mp4forVideoCount = 0;
//                    Observable.create(new ObservableOnSubscribe<Boolean>() {
//                        @Override
//                        public void subscribe(ObservableEmitter<Boolean> emitter) {
//                            mEmitterMp4 = emitter;
//                            newTempList.clear();
//                            newTempErrorList.clear();
//                            mp4ForYuv420();
//                        }
//                    })
//                            .subscribeOn(Schedulers.computation())
//                            .observeOn(AndroidSchedulers.mainThread())
//                            .subscribe(new Observer<Boolean>() {
//                                @Override
//                                public void onSubscribe(Disposable d) {
//
//                                }
//
//                                @Override
//                                public void onNext(Boolean success) {
//                                    if (newTempErrorList.size() + newTempList.size() < tempList.size()) {
//                                        mp4ForYuv420();
//                                    } else if (newTempErrorList.size() + newTempList.size() >= tempList.size()) {
//                                        for (String temp : tempList) {
//                                            File file = new File(temp);
//                                            file.delete();
//                                        }
//                                        List<String> data = new ArrayList<>();
//                                        data.addAll(newTempList);
//                                        data.addAll(newTempErrorList);
//                                        if (data.size() == 0){
//                                            MyProgressDialog.closeProgressDialog();
//                                            ToastUtils.showLong("转码失败,请重试");
//                                            return;
//                                        }
//                                        new Thread(new StitchThread(data, FileType.FILE_VIDEO, CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraName())).start();
//                                    }
//                                }
//
//                                @Override
//                                public void onError(Throwable e) {
//                                    e.printStackTrace();
//
//                                }
//
//                                @Override
//                                public void onComplete() {
//                                }
//                            });
                }
            }
            return;
        }
        //A3下载但不需要拼接，同时排除掉A6Max 的视频
        if (!isA6Camera || (isA6MaxCamera && !tempList.isEmpty() && tempList.get(0).endsWith(".MP4"))) {
            this.downloadCompleted();
            try {
                for (int i = 0; i < tempList.size(); i++) {
                    String path = tempList.get(i);
                    File file = new File(path);
                    String name = file.getName();
                    String[] names = name.split("\\.");
                    String cameraName = isA6MaxCamera? "A6Max":"A3";
                    String newImageName = names[0] + "_PANO_" + cameraName + "." + names[1];
                    FileUtils.copy(path, Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + newImageName);
                    MediaRefresh.scanFileAsync(PanoramaApp.getContext(), Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + newImageName);
                    FileUtils.delete(tempList.get(i));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            new Handler().postDelayed(() -> {
                ((AppCompatActivity) context).finish();
                Intent intent = new Intent();
                if (iCatchFile.getFileType() == ICatchFileType.ICH_FILE_TYPE_VIDEO) {
                    intent.putExtra("CUR_POSITION", 1);
                }
                intent.setClass(context, LocalMultiPbActivity.class);
                context.startActivity(intent);
            }, 500);
            return;
        }
        if (cancelDownloadDialog != null) {
            cancelDownloadDialog.dismiss();
            cancelDownloadDialog = null;
        }
        curFilePath = null;
        if (this.context != null) {
            HashMap<String, ArrayList<String>> map = new HashMap<>();
            for (int i = 0; i < tempList.size(); i++) {
                String fileName = tempList.get(i).substring(tempList.get(i).lastIndexOf("/") + 1);
                String groupName = fileName.substring(0, fileName.lastIndexOf("_"));
                ArrayList<String> checkList = map.get(groupName);
                if (checkList == null) {
                    ArrayList<String> newList = new ArrayList<>();
                    newList.add(tempList.get(i));
                    map.put(groupName, newList);
                } else {
                    checkList.add(tempList.get(i));
                }
            }
            Iterator<Map.Entry<String, ArrayList<String>>> iterator = map.entrySet().iterator();
            if (map.size() > 1 && (SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 0 || SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 1) && !isUseHdrThis) {
                MyProgressDialog.showProgressDialog(context, "正在进行拼接...");
            } else {
                if (tempList.size() != 12 * map.size() || !isA6SCamera) {
                    MyProgressDialog.showProgressDialog(context, "正在进行拼接...");
                } else {
                    MyProgressDialog.showProgressDialog(context, "正在进行HDR拼接...");
                }
            }
            new Thread(new StitchThread(tempList, FileType.FILE_PHOTO)).start();
        }
    }

    private void mp4ForYuv420() {
//        String outPath = context.getCacheDir().getAbsolutePath() + "/"+TimeUtil.getNowDateTime() + ".MP4";
        File file = new File(tempList.get(mp4forVideoCount));
        if (newTempList == null) {
            newTempList = new ArrayList<>();
        }
        if (newTempErrorList == null) {
            newTempErrorList = new ArrayList<>();
        }

        String filePath = tempList.get(mp4forVideoCount);
//        String outFilePath = filePath.substring(0, filePath.lastIndexOf("/") + 1) + TimeUtil.getNowDateTime() + ".MP4";
//        try {
//            new File(outFilePath).createNewFile();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
        String outFilePath = filePath.replace(".MP4", "_.MP4");

        String cmd = "-i " + file.getAbsolutePath() + " -pix_fmt yuv420p -acodec copy -vcodec copy -f mp4 " + outFilePath;
        EpEditor.execCmd(cmd, 500, new OnEditorListener() {
            @Override
            public void onSuccess() {
                LogUtil.e("转码成功");
                newTempList.add(outFilePath);
                mp4forVideoCount++;
                mEmitterMp4.onNext(true);
            }

            @Override
            public void onFailure() {
                LogUtil.e("转码失败");
                newTempErrorList.add(outFilePath);
                mp4forVideoCount++;
                mEmitterMp4.onNext(false);
            }

            @Override
            public void onProgress(float progress) {
            }
        });

        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    int stitchPhotoCount = 0;
    int stitchVideoCount = 0;

    private static String getNowDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd_HHmmss");
        return sdf.format(new Date());
    }

    private ObservableEmitter<Boolean> mEmitter;
    private ObservableEmitter<Boolean> mEmitterMp4;
    private final Pattern pattern = Pattern.compile("_[0-3]\\.");
    public final Pattern pattern2 = Pattern.compile("_[0-3]\\w{0,1}\\.");

    class StitchThread implements Runnable {
        private List<String> filePathList;
        private Handler handler;
        private FileType fileType;
        private String cameraName;

        public StitchThread(List<String> fileList, FileType fileType) {
            this.filePathList = fileList;
            this.handler = new Handler();
            this.fileType = fileType;
        }

        public StitchThread(List<String> fileList, FileType fileType, String cameraName) {
            this.filePathList = fileList;
            this.handler = new Handler();
            this.fileType = fileType;
            this.cameraName = cameraName;
        }


        @Override
        public void run() {
            stitchPhotoCount = 0;
            if ("A3S".equals(cameraName)) {
                if (fileType == FileType.FILE_PHOTO) {
                    for (int i = 0; i < filePathList.size(); i++) {
                        String path = filePathList.get(i);
                        Matcher matcher = pattern.matcher(path);
                        String filePathA = matcher.replaceFirst("_" + 0 + ".");
                        matcher = pattern.matcher(new File(path).getName());
                        String tmp = matcher.replaceFirst("_" + "PANO" + ".");
                        long start = System.currentTimeMillis();
                        int result = StitchUtils.stitchExecOne(context, filePathA, tmp);
                        handler.post(() -> {
                            long end = System.currentTimeMillis();
                            if (0 == result) {
                                AppToast.show(context, PanoramaApp.getContext().getString(R.string.the) + ++stitchPhotoCount +
                                        PanoramaApp.getContext().getString(R.string.photo) + tmp + PanoramaApp.getContext().getString(R.string.stitching_completed)
                                        + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                            } else {
                                ToastUtils.showLong("拼接失败，代码：" + result);
                            }
                        });
                    }

                    for (String temp : this.filePathList) {
                        File file = new File(temp);
                        file.delete();
                        MediaRefresh.scanFileAsync(PanoramaApp.getContext(),temp);
                    }


                    handler.postDelayed(() -> {
                        MyProgressDialog.closeProgressDialog();
                        ((AppCompatActivity) context).finish();
                        Intent intent = new Intent();
                        intent.setClass(context, LocalMultiPbActivity.class);
                        context.startActivity(intent);
                    }, 1000);

                } else if (fileType == FileType.FILE_VIDEO) {
                    stitchVideoCount = 0;
                    againCount = 0;
                    Observable.create((ObservableOnSubscribe<Boolean>) emitter -> {
                                mEmitter = emitter;
                                compositeVideo();
                            })
                            .subscribeOn(Schedulers.computation())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribe(new Observer<Boolean>() {
                                @Override
                                public void onSubscribe(Disposable d) {
                                }

                                @Override
                                public void onNext(Boolean success) {
                                    if (stitchVideoCount < filePathList.size()) {
                                        compositeVideo();
                                    }
                                }

                                @Override
                                public void onError(Throwable e) {
                                    e.printStackTrace();
                                }

                                @Override
                                public void onComplete() {
                                }
                            });
                }
                return;
            }
            if (isA6SCamera && fileType == FileType.FILE_PHOTO) {
                //分组，看照片数组内  哪些是 4张一组  哪些是 12张一组
                HashMap<String, ArrayList<String>> map = new HashMap<>();
                for (int i = 0; i < filePathList.size(); i++) {
                    String fileName = filePathList.get(i).substring(filePathList.get(i).lastIndexOf("/") + 1);
                    String groupName = fileName.substring(0, fileName.lastIndexOf("_"));
                    ArrayList<String> checkList = map.get(groupName);
                    if (checkList == null) {
                        ArrayList<String> newList = new ArrayList<>();
                        newList.add(filePathList.get(i));
                        map.put(groupName, newList);
                    } else {
                        checkList.add(filePathList.get(i));
                    }
                }
                Iterator<Map.Entry<String, ArrayList<String>>> iterator = map.entrySet().iterator();
                if (map.size() > 1 && (SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 0 || SPUtils.getInstance().getInt(Const.SP.HDR_DATA, 0) == 1) && !isUseHdrThis) {
                    //超过1组并选择不使用HDR
                    while (iterator.hasNext()) {
                        Map.Entry<String, ArrayList<String>> entry = iterator.next();
                        ArrayList<String> checkList = entry.getValue();
                        if (checkList.size() == 4) {
                            long start = System.currentTimeMillis();
                            String path = checkList.get(0);
                            Matcher matcher = pattern2.matcher(path);
                            String filePathA = matcher.replaceFirst("_" + 0 + ".");
                            String filePathB = matcher.replaceFirst("_" + 1 + ".");
                            String filePathC = matcher.replaceFirst("_" + 2 + ".");
                            String filePathD = matcher.replaceFirst("_" + 3 + ".");
                            if (path.substring(path.lastIndexOf("/") + 1).contains("A") || path.substring(path.lastIndexOf("/") + 1).contains("B") || path.substring(path.lastIndexOf("/") + 1).contains("C")) {
                                filePathA = matcher.replaceFirst("_" + 0 + "A.");
                                filePathB = matcher.replaceFirst("_" + 1 + "A.");
                                filePathC = matcher.replaceFirst("_" + 2 + "A.");
                                filePathD = matcher.replaceFirst("_" + 3 + "A.");
                            }
                            matcher = pattern2.matcher(new File(path).getName());
                            String tmp = matcher.replaceFirst("_" + "PANO" + ".");

                            AppLog.d(TAG, "new pano file name:" + tmp);
                            int result = StitchUtils.stitchExec(context, filePathA, filePathB, filePathC, filePathD, tmp);
                            handler.post(() -> {
                                long end = System.currentTimeMillis();
                                if (0 == result) {
                                    AppToast.show(context, PanoramaApp.getContext().getString(R.string.the) + ++stitchPhotoCount +
                                            PanoramaApp.getContext().getString(R.string.photo) + tmp + PanoramaApp.getContext().getString(R.string.stitching_completed)
                                            + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                                } else {
                                    AppDialog.showDialogWarn(context, "拼接失败，代码：" + result);
                                }
                            });
                            for (String temp : checkList) {
                                File file = new File(temp);
                                file.delete();
                                MediaRefresh.scanFileAsync(context,temp);
                            }
                        } else if (checkList.size() == 12) {
                            //因为超过1组图片 所以 只需要抽取4张进行拼接
                            ArrayList<String> fileList = new ArrayList<>();
                            for (int i = 0; i < checkList.size(); i++) {
                                if (checkList.get(i).contains("A")) {
                                    fileList.add(checkList.get(i));
                                }
                            }
                            long start = System.currentTimeMillis();
                            String path = fileList.get(0);
                            Matcher matcher = pattern2.matcher(path);
                            String filePathA = matcher.replaceFirst("_" + 0 + "A.");
                            String filePathB = matcher.replaceFirst("_" + 1 + "A.");
                            String filePathC = matcher.replaceFirst("_" + 2 + "A.");
                            String filePathD = matcher.replaceFirst("_" + 3 + "A.");

                            AppLog.d(TAG, "filePathA:" + filePathA);
                            AppLog.d(TAG, "filePathB:" + filePathB);
                            AppLog.d(TAG, "filePathC:" + filePathC);
                            AppLog.d(TAG, "filePathD:" + filePathD);
                            matcher = pattern2.matcher(new File(path).getName());
                            String tmp = matcher.replaceFirst("_" + "PANO" + ".");
                            AppLog.d(TAG, "new pano file name:" + tmp);
                            int result = StitchUtils.stitchExec(context, filePathA, filePathB, filePathC, filePathD, tmp);
                            String finalTmp = tmp;
                            handler.post(() -> {
                                long end = System.currentTimeMillis();
                                if (0 == result) {
                                    AppToast.show(context, PanoramaApp.getContext().getString(R.string.the) + ++stitchPhotoCount +
                                            PanoramaApp.getContext().getString(R.string.photo) + finalTmp + PanoramaApp.getContext().getString(R.string.stitching_completed)
                                            + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                                } else {
                                    AppDialog.showDialogWarn(context, "拼接失败，代码：" + result);
                                }
                            });

                            for (String temp : checkList) {
                                File file = new File(temp);
                                if (file.exists()) {
                                    file.delete();
                                    MediaRefresh.notifySystemToScan(file);
                                }
                            }
                            MediaRefresh.scanFileAsync(context, Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + tmp);
                        }
                    }
                } else {
                    while (iterator.hasNext()) {
                        Map.Entry<String, ArrayList<String>> entry = iterator.next();
                        ArrayList<String> checkList = entry.getValue();
                        if (checkList.size() == 4) {
                            long start = System.currentTimeMillis();
                            String path = checkList.get(0);
                            Matcher matcher = pattern2.matcher(path);
                            String filePathA = matcher.replaceFirst("_" + 0 + ".");
                            String filePathB = matcher.replaceFirst("_" + 1 + ".");
                            String filePathC = matcher.replaceFirst("_" + 2 + ".");
                            String filePathD = matcher.replaceFirst("_" + 3 + ".");
                            if (path.substring(path.lastIndexOf("/") + 1).contains("A") || path.substring(path.lastIndexOf("/") + 1).contains("B") || path.substring(path.lastIndexOf("/") + 1).contains("C")) {
                                filePathA = matcher.replaceFirst("_" + 0 + "A.");
                                filePathB = matcher.replaceFirst("_" + 1 + "A.");
                                filePathC = matcher.replaceFirst("_" + 2 + "A.");
                                filePathD = matcher.replaceFirst("_" + 3 + "A.");
                            }
                            AppLog.d(TAG, "filePathA:" + filePathA);
                            AppLog.d(TAG, "filePathB:" + filePathB);
                            AppLog.d(TAG, "filePathC:" + filePathC);
                            AppLog.d(TAG, "filePathD:" + filePathD);
                            matcher = pattern2.matcher(new File(path).getName());
                            String tmp = matcher.replaceFirst("_" + "PANO" + ".");

                            AppLog.d(TAG, "new pano file name:" + tmp);
//                        int result = StitchUtils.stitchExec(context, checkList.get(3), checkList.get(2), checkList.get(1), checkList.get(0), tmp);
                            int result = StitchUtils.stitchExec(context, filePathA, filePathB, filePathC, filePathD, tmp);
                            handler.post(() -> {
                                long end = System.currentTimeMillis();
                                if (0 == result) {
                                    AppToast.show(context, PanoramaApp.getContext().getString(R.string.the) + ++stitchPhotoCount +
                                            PanoramaApp.getContext().getString(R.string.photo) + tmp + PanoramaApp.getContext().getString(R.string.stitching_completed)
                                            + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                                } else {
                                    AppDialog.showDialogWarn(context, "拼接失败，代码：" + result);
                                }
                            });
                            for (String temp : checkList) {
                                File file = new File(temp);
                                file.delete();
                                MediaRefresh.scanFileAsync(context,temp);
                            }

                            MediaRefresh.scanFileAsync(context, Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + tmp);


                        } else if (checkList.size() == 12) {
                            //先执行HDR
                            ArrayList<HdrBean> hdrBeans = startHdr(checkList);
                            if (hdrBeans.size() < 4) {
                                LogUtil.e("HDR合成 失败，请尝试普通拼接");
                                return;
                            }

                            long start = System.currentTimeMillis();
                            String path = hdrBeans.get(0).mainPicPath;
                            Matcher matcher = pattern2.matcher(path);
                            String filePathA = hdrBeans.get(3).mainPicPath;
                            String filePathB = hdrBeans.get(2).mainPicPath;
                            String filePathC = hdrBeans.get(1).mainPicPath;
                            String filePathD = hdrBeans.get(0).mainPicPath;
                            String filePathCaliText = checkList.get(checkList.size() - 3);
//                            matcher = pattern2.matcher(new File(path).getName());
//                            String tmp = matcher.replaceFirst("_" + "HDR" + ".").replaceAll("fusion", "");
                            String initName = new File(path).getName();
                            String name = initName.replace("fusion", "");
                            String tmp;
                            if (!TextUtils.isEmpty(hdrBeans.get(0).nameId)) {
                                tmp = name.substring(0,name.indexOf('.')).replace(hdrBeans.get(0).nameId, "");
                            } else {
                                int index = name.lastIndexOf("_");
                                tmp = name.substring(0, index);
                            }
                            tmp += "_HDR.JPG";

                            AppLog.d(TAG, "new pano file name:" + tmp);
                            int result = StitchUtils.stitchExecHdr(context, filePathA, filePathB, filePathC, filePathD, filePathCaliText, tmp);
                            String finalTmp = tmp;
                            handler.post(() -> {
                                long end = System.currentTimeMillis();
                                if (0 == result) {
                                    AppToast.show(context, PanoramaApp.getContext().getString(R.string.the) + ++stitchPhotoCount +
                                            PanoramaApp.getContext().getString(R.string.photo) + finalTmp + PanoramaApp.getContext().getString(R.string.stitching_completed)
                                            + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                                } else {
                                    AppDialog.showDialogWarn(context, "拼接失败，代码：" + result);
                                }
                            });

                            for (String temp : checkList) {
                                File file = new File(temp);
                                if (file.exists()) {
                                    file.delete();
                                    MediaRefresh.scanFileAsync(context,temp);
                                }
                            }
                            MediaRefresh.scanFileAsync(context, Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + tmp);

                            for (HdrBean temp : hdrBeans) {
                                for (String tempPath : temp.deletePath) {
                                    File file = new File(tempPath);
                                    if (file.exists()) {
                                        file.delete();
                                        MediaRefresh.notifySystemToScan(file);
                                    }
                                }
                            }
                        }
                    }
                }
                handler.postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                        ((AppCompatActivity) context).finish();
                        Intent intent = new Intent();
                        intent.setClass(context, LocalMultiPbActivity.class);
                        context.startActivity(intent);
                    }
                }, 1000);
                return;
            }


            HashSet<String> hashSet = new HashSet<>();
            for (String temp : this.filePathList) {
                if (new File(temp).getName().matches(StitchUtils.REGEX)) {
                    Matcher matcher = pattern.matcher(temp);
                    String curFilePath = matcher.replaceFirst("_0.");
                    hashSet.add(curFilePath);
                }
            }
            Iterator iterator = hashSet.iterator();

            String version = "";
            if (cameraVersion != null) {
                if (cameraVersion.contains(":")) {
                    version = cameraVersion.split(":")[1].trim();
                } else {
                    version = "02.00.01";
                }
            }

            if (cameraVersion != null && (VersionUtils.compareVersion(version, "02.00.00") == 0 || VersionUtils.compareVersion(version, "02.00.00") == 1)) {
                while (iterator.hasNext()) {
                    long start = System.currentTimeMillis();
                    String path = (String) iterator.next();
                    Matcher matcher = pattern.matcher(path);
                    String filePathA = matcher.replaceFirst("_" + 0 + ".");
                    String filePathB = matcher.replaceFirst("_" + 1 + ".");
                    String filePathC = matcher.replaceFirst("_" + 2 + ".");
                    String filePathD = matcher.replaceFirst("_" + 3 + ".");
                    AppLog.d(TAG, "filePathA:" + filePathA);
                    AppLog.d(TAG, "filePathB:" + filePathB);
                    AppLog.d(TAG, "filePathC:" + filePathC);
                    AppLog.d(TAG, "filePathD:" + filePathD);
                    matcher = pattern.matcher(new File(path).getName());
                    String tmp = matcher.replaceFirst("_" + "PANO" + ".");
                    AppLog.d(TAG, "new pano file name:" + tmp);
                    int result = StitchUtils.stitchExec(context, filePathA, filePathB, filePathC, filePathD, tmp);
                    handler.post(() -> {
                        long end = System.currentTimeMillis();
                        if (0 == result) {
                            AppToast.show(context, PanoramaApp.getContext().getString(R.string.the) + ++stitchPhotoCount +
                                    PanoramaApp.getContext().getString(R.string.photo) + tmp + PanoramaApp.getContext().getString(R.string.stitching_completed)
                                    + PanoramaApp.getContext().getString(R.string.time_consuming) + (end - start) + "ms", Toast.LENGTH_LONG);
                        } else {
                            AppDialog.showDialogWarn(context, "拼接失败，代码：" + result);
                        }
                    });
                }
            } else {
                while (iterator.hasNext()) {
                    long start = System.currentTimeMillis();
                    String path = (String) iterator.next();
                    Matcher matcher = pattern.matcher(path);
                    String filePathA = matcher.replaceFirst("_" + 3 + ".");
                    String filePathB = matcher.replaceFirst("_" + 2 + ".");
                    String filePathC = matcher.replaceFirst("_" + 1 + ".");
                    String filePathD = matcher.replaceFirst("_" + 0 + ".");
                    AppLog.d(TAG, "filePathA:" + filePathA);
                    AppLog.d(TAG, "filePathB:" + filePathB);
                    AppLog.d(TAG, "filePathC:" + filePathC);
                    AppLog.d(TAG, "filePathD:" + filePathD);
                    matcher = pattern.matcher(new File(path).getName());
                    String tmp = matcher.replaceFirst("_" + "PANO" + ".");
                    AppLog.d(TAG, "new pano file name:" + tmp);
                    int result = StitchUtils.stitchExec(context, filePathA, filePathB, filePathC, filePathD, tmp);
                    handler.post(() -> {
                        long end = System.currentTimeMillis();
                        if (0 == result) {
                            AppToast.show(context, PanoramaApp.getContext().getString(R.string.the)
                                    + ++stitchPhotoCount + PanoramaApp.getContext().getString(R.string.time_consuming)
                                    + tmp + PanoramaApp.getContext().getString(R.string.stitching_completed) + "耗时" + (end - start) + "ms", Toast.LENGTH_LONG);
                        } else {
                            AppDialog.showDialogWarn(context, "拼接失败，代码：" + result);
                        }
                    });
                }
            }
            for (String temp : this.filePathList) {
                File file = new File(temp);
                file.delete();
                MediaRefresh.notifySystemToScan(file);
            }
            handler.post(() -> {
                MyProgressDialog.closeProgressDialog();
                ((AppCompatActivity) context).finish();
                Intent intent = new Intent();
                intent.setClass(context, LocalMultiPbActivity.class);
                context.startActivity(intent);
            });
        }


        private int againCount = 0;

        private void compositeVideo() {
            try {
                String PATH_TEST_PATH = context.getCacheDir().getAbsolutePath() + "/";
                final String pathToSave = PATH_TEST_PATH + "cache.mp4";
                String path = filePathList.get(stitchVideoCount);
                Matcher matcher = pattern.matcher(path);
                String filePathA = matcher.replaceFirst("_" + 0 + ".");
                matcher = pattern.matcher(new File(path).getName());
                String tmp = matcher.replaceFirst("_" + "PANO" + ".");
                long start = System.currentTimeMillis();

                String fileNameA = new File(filePathA).getName();
                String cacheDir = context.getCacheDir().getAbsolutePath();
                String cacheFilePathA = new File(cacheDir, fileNameA).getAbsolutePath();
                InputStream isA = new FileInputStream(filePathA);
                copyFile(new File(cacheFilePathA), isA);//缓存

//                String cali_str = FileIOUtils.readFile2String(new File(StorageUtil.getRootPath(PanoramaApp.getContext()) + AppInfo.DOWNLOAD_PATH_PHOTO, "zd.txt"));
                String cali_str = FileIOUtils.readFile2String(new File(PanoramaApp.getContext().getCacheDir().getAbsolutePath(), "zd.txt"));
                if (!TextUtils.isEmpty(cali_str)) {
                    cali_str = cali_str.substring(5);
                }
                if (!new File(filePathA).exists()) {
                    Log.e("File not exists", filePathA);
                }

                int panoWidth = 3840;
                int panoHeight = 1920;

                int[] videoResolution = VideoUtil.getVideoResolution(context, cacheFilePathA);
                if (videoResolution != null && videoResolution.length == 2) {
                    panoWidth = Math.min(panoWidth, videoResolution[0]);
                    panoHeight = Math.min(panoHeight, videoResolution[1]);
                } else {
                    //从sp 中取
                    String strResolution = "";
                    String curCamera = CameraUtils.getCurCamera();
                    if (curCamera != null) {
                        strResolution = SPUtils.getInstance().getString(curCamera);
                    }
                    String[] xes = new String[0];
                    if (!TextUtils.isEmpty(strResolution)) {
                        xes = strResolution.split("x");
                    }
                    if (xes.length == 2) {
                        panoWidth = Integer.valueOf(xes[0]);
                        panoHeight = Integer.valueOf(xes[1]);
                    }
                }


                ReVideoStitch vs = new ReVideoStitch();
                StitchParam param = new StitchParam();
                param.path = filePathA;
                param.savePath = pathToSave;
                param.panoWidth = panoWidth;
                param.panoHeight = panoHeight;
                param.gopSize = 30;
                param.startTime = 0;
                param.endTime = 0;


                int type = SPUtils.getInstance().getInt(SettingRecordDialog.VIDEO_SETTING, 1);
                if (type == 0) {
                    param.bitrate = 8000 * 1024;
                } else if (type == 1) {
                    param.bitrate = 22000 * 1024;
                } else if (type == 2) {
                    param.bitrate = 45000 * 1024;
                } else if (type == 3) {
                    int bitrate = SPUtils.getInstance().getInt(SettingRecordDialog.VIDEO_SETTING_CUSTOM, 22000);
                    param.bitrate = bitrate * 1024;
                }

                param.deviceId = DeviceId.TWO_FISHEYE;
                LogUtil.e("TextUtils.isEmpty(cali_str):" + TextUtils.isEmpty(cali_str));
                if (!TextUtils.isEmpty(cali_str)) {
                    param.calibration = SzStitch.GetCaliStr(cali_str);// "6_725_760_750_-180_-0.148578_-0.863683_725_2280_760_-0.394842_-0.324398_0.208244_3040_1520_1.75537_11.4153_0_0_0_0_-0.0962039_0_0_-0.107876_192_187";
                } else {
//                    param.calibration = "6_1398.000000_1545.000000_1574.000000_180.000000_0.000000_0.000000_1421.500000_4595.500000_1572.500000_-0.887014_-1.653790_0.828743_6144_3072_0.020517_-27.014400_0.000000_0.000000_0.000000_0.000000_0.158651_0.000000_0.000000_0.158260_203.000000_185.000000";
                    remoteMultiPbPresenter.downloadManager.setOnHttpListenerListener(new PbDownloadManager.OnHttpListener() {
                        @Override
                        public void onResult(boolean isSuccess) {
                            if (isSuccess) {
                                compositeVideo();
                            } else {
                                againCount = 0;
                                for (String temp : filePathList) {
                                    File fileData = new File(temp);
                                    fileData.delete();
                                    MediaRefresh.notifySystemToScan(fileData);
                                }
                                MyProgressDialog.closeProgressDialog();
                                ToastUtils.showLong("标定参数异常，请重新再试");
                            }

                            remoteMultiPbPresenter.downloadManager.removeOnHttpListener();
                        }
                    });
                    if (againCount < 2) {
                        againCount++;
                        remoteMultiPbPresenter.downZdInfo(remoteMultiPbPresenter);
                    } else {
                        againCount = 0;
                        for (String temp : filePathList) {
                            File fileData = new File(temp);
                            fileData.delete();
                            MediaRefresh.notifySystemToScan(fileData);
                        }
                        MyProgressDialog.closeProgressDialog();
                        ToastUtils.showLong("标定参数异常，请重新再试");
                    }
                    return;
                }
                vs.setListener(new IVideoStitchListener() {
                    @Override
                    public void onVideoStitchProgressChanged(int i) {

                    }

                    @Override
                    public void onVideoStitchStateChanged(RetCode retCode) {
                        String text = "";
                        stitchVideoCount++;
                        switch (retCode) {
                            case OK:
                                text = "转码完成";
                                try {
                                    File file = new File(pathToSave);
                                    String newImageName = tmp.substring(0, tmp.indexOf(".")) + "_" + System.currentTimeMillis() / 1000 + tmp.substring(tmp.indexOf("."));
                                    File newImage = new File(Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + newImageName);
                                    file.createNewFile();
                                    FileInputStream fileInputStream = new FileInputStream(file);
                                    copyFile(newImage, fileInputStream);
                                    MediaRefresh.scanFileAsync(context, newImage.getAbsolutePath());
                                    try {
                                        Thread.sleep(200);
                                    } catch (InterruptedException e) {
                                        e.printStackTrace();
                                    }
                                } catch (IOException e) {
                                    e.printStackTrace();
                                }
                                break;
                            case ABORT:
                                text = "主动取消";
                                break;
                            case OPENGL_ERROR:
                                text = "Opengl相关错误";
                                break;
                            case OTHER_ERROR:
                                text = "其他错误";
                                break;
                            case STITCH_PARAM_ERROR:
                                text = "参数错误";
                                break;
                            case OUT_OF_MEMORY:
                                text = "内存溢出";
                                break;
                        }
                        mEmitter.onNext(true);
                        if (stitchVideoCount >= filePathList.size()) {
                            for (String temp : filePathList) {
                                File fileData = new File(temp);
                                fileData.delete();
                                MediaRefresh.notifySystemToScan(fileData);
                            }

                            final String tipText = text;
                            ToastUtils.showLong(tipText);
                            MyProgressDialog.closeProgressDialog();
                            ((AppCompatActivity) context).finish();
                            Intent intent = new Intent();
                            intent.setClass(context, LocalMultiPbActivity.class);
                            context.startActivity(intent);
                        }
                    }
                });

                vs.startStitchVideo(param);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 自定义线程工厂，用于创建低优先级的后台线程。
     * 这样可以确保 HDR 处理不会抢占主线程（UI 线程）的资源，
     * 避免造成应用卡顿。
     */
    private static class HdrThreadFactory implements ThreadFactory {
        private int counter = 0;

        @Override
        public Thread newThread(@NonNull Runnable r) {
            Thread t = new Thread(() -> {
                // 在新线程开始执行后，由它自己来设置正确的后台优先级。
                android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_BACKGROUND);
                r.run();
            }, "HdrProcessorThread-" + counter++);
            return t;
        }
    }

    /**
     * 启动 HDR 批处理任务的入口方法。
     * 它负责将图片分组，并使用低优先级的线程池来并发处理它们。
     *
     * @param filePathList 所有待处理图片的路径列表。
     * @return 返回一个包含所有成功生成的 HDR 图片信息的列表。
     */
    public static ArrayList<HdrBean> startHdr(List<String> filePathList) {
        // 1. 将图片路径按组名进行分类
        LinkedHashMap<String, ArrayList<String>> map = groupImagePaths(filePathList);
        String rootDir = getApplication().getCacheDir().getAbsolutePath();
        ArrayList<HdrBean> hdrList = new ArrayList<>();

        // 2. 判断是使用多线程还是单线程模式
        boolean isUseMultiModel = SPKey.isUseMultiThreadMode();
        LogUtils.file("开始HDR，当前模式：" + (isUseMultiModel ? "多线程模式" : "单线程模式"));

        if (isUseMultiModel) {
            // --- 多线程模式 ---
            int threadNum = calculateOptimalThreadCount();
            LogUtils.file("开始HDR，多线程模式，线程池数量：" + threadNum);

            ThreadFactory hdrThreadFactory = new HdrThreadFactory();
            ExecutorService executorService = Executors.newFixedThreadPool(threadNum, hdrThreadFactory);
            List<Future<Optional<HdrBean>>> futures = new ArrayList<>();

            // 提交所有 HDR 任务到线程池
            for (ArrayList<String> checkList : map.values()) {
                LogUtil.d("提交HDR:" + JSON.toJSONString(checkList));
                Future<Optional<HdrBean>> future = executorService.submit(() -> hdr(rootDir, checkList));
                futures.add(future);
            }

            // 等待并收集所有任务的结果
            try {
                for (Future<Optional<HdrBean>> future : futures) {
                    future.get(60, TimeUnit.SECONDS).ifPresent(hdrList::add);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                LogUtils.e("HDR 批处理被中断", e);
            } catch (TimeoutException e) {
                LogUtils.e("HDR 批处理超时", e);
            } catch (Exception e) {
                LogUtils.e("HDR 批处理发生未知错误", e);
                e.printStackTrace();
            } finally {
                executorService.shutdown();
            }

        } else {
            // --- 单线程模式 (旧代码逻辑作为备用) ---
            for (Map.Entry<String, ArrayList<String>> entry : map.entrySet()) {
                hdr(rootDir, entry.getValue()).ifPresent(hdrList::add);
            }
        }

        LogUtils.file("HDR 结束：hdrList.size=" + hdrList.size());
        return hdrList;
    }


    // =================================================================================
    // Section 2: 核心HDR处理单元 (Core HDR Processing Unit)
    // 这是我们最终加固后的版本，具备极高的健壮性。
    // =================================================================================

    @NonNull
    private static Optional<HdrBean> hdr(String dir, List<String> imagesPath) {
        // --- 1. 输入验证 ---
        if (imagesPath == null || imagesPath.isEmpty()) {
            LogUtil.e("HDR失败：输入的图片路径列表为空或为null。");
            return Optional.empty();
        }

        // --- 2. 输出目录验证 ---
        File outputDir = new File(dir);
        if (!outputDir.exists() && !outputDir.mkdirs()) {
            LogUtil.e("HDR失败：无法创建输出目录: " + dir);
            return Optional.empty();
        }

        LogUtil.d("开始HDR核心处理：" + JSON.toJSONString(imagesPath));

        // --- 3. 资源管理：使用 try-finally 保证释放 ---
        Vector<Mat> images = new Vector<>();
        Mat response = new Mat();
        Mat fusion = new Mat();
        MatOfFloat timeMat = null;

        try {
            // --- 图像加载 (已整合压缩逻辑) ---
            boolean hdrCompressMode = SPKey.isHdrCompressMode();
            if (hdrCompressMode) {
                LogUtil.d("HDR压缩模式已开启，加载并缩小图片。");
                Size size = new Size();
                for (String path : imagesPath) {
                    Mat img = Imgcodecs.imread(path);
                    if (img.empty()) {
                        LogUtils.e("需要HDR的文件不存在或无法读取：" + path);
                        return Optional.empty();
                    }
                    // 计算压缩后尺寸并执行 resize
                    size.width = img.cols() / 2.0;
                    size.height = img.rows() / 2.0;
                    Mat dst = new Mat();
                    Imgproc.resize(img, dst, size);
                    images.add(dst);
                    img.release(); // 释放已无用的原始大图
                }
            } else {
                LogUtil.d("HDR标准模式，加载原始图片。");
                for (String path : imagesPath) {
                    Mat img = Imgcodecs.imread(path);
                    if (img.empty()) {
                        LogUtils.e("需要HDR的文件不存在或无法读取：" + path);
                        return Optional.empty();
                    }
                    images.add(img);
                }
            }

            // --- HDR 处理 ---
            float[] picMatTimeList = getPicMatTimeList(imagesPath);
            timeMat = new MatOfFloat(picMatTimeList);

            CalibrateDebevec calibrate = createCalibrateDebevec();
            calibrate.process(images, response, timeMat);

            MergeMertens merge_mertens = createMergeMertens();
            merge_mertens.process(images, fusion);

            fusion.convertTo(fusion, CvType.CV_8UC3, 255.0);

            // --- 文件保存 ---
            String nameId = "_" + System.nanoTime() + "_" + Thread.currentThread().getId();
            String sameName = imagesPath.get(0).substring(imagesPath.get(0).lastIndexOf("/") + 1, imagesPath.get(0).lastIndexOf("_")) + nameId;
            String filename = "fusion" + sameName + ".jpg";
            File file1 = new File(dir, filename);

            if (Imgcodecs.imwrite(file1.toString(), fusion)) {
                HdrBean result = new HdrBean();
                result.mainPicPath = file1.toString();
                result.deletePath.add(file1.toString());
                result.nameId = nameId;
                return Optional.of(result);
            } else {
                LogUtil.e("HDR 结果写入文件失败: " + file1.toString());
                return Optional.empty();
            }

        } catch (Exception e) {
            LogUtils.e("HDR 核心处理过程中发生未知错误", e);
            e.printStackTrace();
            return Optional.empty();
        } finally {
            // --- 资源安全保障：无论如何都会执行 ---
            LogUtil.d("开始释放HDR Mat资源...");
            for (Mat mat : images) {
                if (mat != null) mat.release();
            }
            if (response != null) response.release();
            if (fusion != null) fusion.release();
            if (timeMat != null) timeMat.release();
            LogUtil.d("HDR Mat资源释放完毕。");
        }
    }

    // =================================================================================
    // Section 3: 辅助方法与模拟类 (Helper Methods & Mock Classes)
    // =================================================================================

    private static LinkedHashMap<String, ArrayList<String>> groupImagePaths(List<String> filePathList) {
        LinkedHashMap<String, ArrayList<String>> map = new LinkedHashMap<>();
        if (filePathList == null) return map;
        for (String path : filePathList) {
            String fileName = path.substring(path.lastIndexOf("/") + 1);
            String groupName = fileName.substring(0, fileName.lastIndexOf("_") + 2);
            map.computeIfAbsent(groupName, k -> new ArrayList<>()).add(path);
        }
        return map;
    }

    private static int calculateOptimalThreadCount() {
        long totalMemory = (long) (SystemInfo.getTotalMemory(getContext()) / 1024 / 1024.0);
        int cpuNum = Runtime.getRuntime().availableProcessors();
        int threadNum = Math.max(Math.min(Math.min((int) (totalMemory / 2.5), cpuNum), 3), 1);
        return threadNum;
    }

    private static float[] getPicMatTimeList(List<String> imagesPath) {
        float[] times = new float[imagesPath.size()];
        int i = 0;
        for (String path : imagesPath) {
            String FExposureTime = "";
            try {
                ExifInterface exifInterface = new ExifInterface(
                        path);
                FExposureTime = exifInterface
                        .getAttribute(ExifInterface.TAG_EXPOSURE_TIME);
                times[i] = Float.valueOf(FExposureTime);
                i++;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return times;
    }

    public static boolean copyFile(File fileCopyDest, InputStream inputStream) {
        long start = System.currentTimeMillis();
        if (inputStream == null) throw new AssertionError();
        if (fileCopyDest == null) throw new AssertionError();
        BufferedOutputStream bos = null;
        BufferedInputStream bis = new BufferedInputStream(inputStream);
        try {
            bos = new BufferedOutputStream(new FileOutputStream(fileCopyDest));
            int length = 0;
            byte[] buffer = new byte[10240];
            while ((length = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, length);
            }
            AppLog.i(TAG, "copyFile success ：" + fileCopyDest.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
            AppLog.i(TAG, "copyFile fail ：" + fileCopyDest.getAbsolutePath());
            return false;
        } finally {
            try {
                if (bos != null) {
                    bos.close();
                }
                if (bis != null) {
                    bis.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        long end = System.currentTimeMillis();
        AppLog.d(TAG, "copyFile take time : " + (end - start) + "ms");
        return true;
    }

    public static final String SP = "stitch_param";
    private static SPUtils spUtils = SPUtils.getInstance(SP);
    public static final String KEY_WIDTH = "dstwidth";
    public static final String KEY_HEIGHT = "dstheight";
    public static final int RESOLUTION_MAX = 8000;
    public static final int DEFAULT_WIDTH = RESOLUTION_MAX;
    public static final int DEFAULT_HEIGHT = RESOLUTION_MAX / 2;


    class DownloadProgressTask extends TimerTask {
        long lastTime = 0;

        @Override
        public void run() {
            if (curDownloadFile == null) {
                return;
            }
            String TAG = "DownloadProgressTask";
            final ICatchFile iCatchFile = curDownloadFile;
            // 添加空值检查，防止下载完成后Timer线程仍在运行时出现NullPointerException
            if (curFilePath == null) {
                AppLog.d(TAG, "curFilePath is null, download may be completed");
                return;
            }
            File file = new File(curFilePath);
            AppLog.d(TAG, "Filename:" + file + " iCatchFile name:" + iCatchFile.getFileName() + " fileHandle:" + iCatchFile.getFileHandle());
            if (downloadInfoMap.containsKey(iCatchFile.getFileHandle()) == false) {
                return;
            }
            final DownloadInfo downloadInfo = downloadInfoMap.get(iCatchFile.getFileHandle());
            AppLog.d(TAG, "downloadInfo isDone:" + downloadInfo.isDone());
            if (downloadInfo.isDone()) {
                return;
            }
            long fileLength = file.length();
            if (file != null && file.exists()) {
                if (fileLength >= iCatchFile.getFileSize()) {
                    downloadProgress = 100;
                    downloadInfo.setDone(true);
                } else {
                    // 防止除零和进度溢出
                    long fileSize = Math.max(1, iCatchFile.getFileSize());
                    downloadProgress = Math.min(100, (fileLength * 100) / fileSize);
                }
            } else {
                downloadProgress = 0;
            }
            downloadInfo.curFileLength = fileLength;
            downloadInfo.progress = (int) downloadProgress;
            AppLog.d(TAG, "downloadProgress = " + downloadProgress);
            downloadManagerHandler.post(new Runnable() {
                @Override
                public void run() {
                    downloadInfoMap.put(iCatchFile.getFileHandle(), downloadInfo);
                    downloadManagerAdapter.notifyDataSetChanged();
                }
            });
        }
    }

    class DownloadAsyncTask extends AsyncTask<String, Integer, Boolean> {
        private String TAG = "DownloadAsyncTask";
        ICatchFile downloadFile;
        private String fileName;
        private String fileType = null;
        private String filePath = null;

        public DownloadAsyncTask(ICatchFile iCatchFile) {
            super();
            downloadFile = iCatchFile;
            curDownloadFile = iCatchFile;
            if (downloadFile.getFileType() == ICatchFileType.ICH_FILE_TYPE_IMAGE) {
                filePath = StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_PHOTO;
            } else if (downloadFile.getFileType() == ICatchFileType.ICH_FILE_TYPE_VIDEO) {
                filePath = StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_VIDEO;
            }
        }

        @Override
        protected Boolean doInBackground(String... params) {
            boolean retvalue = false;
            fileName = downloadFile.getFileName();
            File file = new File(filePath);
            if (!file.exists()) {
                file.mkdirs();
            }
            AppLog.d(TAG, "start downloadFile=" + filePath + fileName);
            curFilePath = FileTools.chooseUniqueFilename(filePath + fileName);
            retvalue = fileOperation.downloadFile(downloadFile, curFilePath);
            AppLog.d(TAG, "end downloadFile retvalue =" + retvalue);
            LogUtils.file("视频下载结果："+retvalue+"，文件路径："+curFilePath);
            if (retvalue) {
                if (downloadFile.getFileType() == ICatchFileType.ICH_FILE_TYPE_VIDEO) {
                    AppLog.d(TAG, "fileName = " + fileName);
                    if (fileName.endsWith(".mov") || fileName.endsWith(".MOV")) {
                        fileType = "video/mov";
                    } else {
                        fileType = "video/mp4";
                    }

                    LogUtils.file("视频下载：isA6MaxCamera = "+isA6MaxCamera);
                    if (isA6MaxCamera){//做个旋转，逆时针旋转90度
                        activity.runOnUiThread(() ->
                                MyProgressDialog.showProgressDialog(context,"正在旋转视频，请稍候"));
                        // 修复：使用实际下载的文件路径，而不是原始文件名
                        LogUtils.file("A6Max旋转视频：使用文件路径 = " + curFilePath);
                        VideoUtil.rotateVideo90(activity, curFilePath);
                        activity.runOnUiThread(MyProgressDialog::closeProgressDialog);
                    }
//                    MediaRefresh.addMediaToDB(context, filePath + downloadFile.getFileName(), fileType);
                    MediaRefresh.scanFileAsync(context,filePath + downloadFile.getFileName());
                    tempList.add(filePath + downloadFile.getFileName());
                } else if (downloadFile.getFileType() == ICatchFileType.ICH_FILE_TYPE_IMAGE) {
                    MediaRefresh.scanFileAsync(context, filePath + downloadFile.getFileName());
                    tempList.add(filePath + downloadFile.getFileName());
                }
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return retvalue;
        }

        protected void onProgressUpdate(Integer... progress) {
        }

        protected void onPostExecute(Boolean result) {
            if (!result) {
                AppLog.d(TAG, "receive DOWNLOAD_FAILURE downloadFailed=" + downloadFailed);
                downloadFailed++;
                updateDownloadMessage();
            } else {
                downloadSucceed++;
                updateDownloadMessage();
            }
            singleDownloadComplete(result, downloadFile);
            downloadTaskList.remove(downloadFile);

            if (downloadTaskList.size() > 0) {
                if (isCancel) {
                    return;
                }

                currentDownloadFile = downloadTaskList.getFirst();
                new DownloadAsyncTask(currentDownloadFile).execute();
            } else {
                if (customDownloadDialog != null) {
                    customDownloadDialog.dismissDownloadDialog();
                }
                if (downloadProgressTimer != null) {
                    downloadProgressTimer.cancel();
                }

                if (isCancel) {
                    return;
                }

                if (tempList.size() != 0) {
                    downloadCompletedEx(downloadFile);
                }
            }
        }
    }

    class DownloadAsyncTaskForZd extends AsyncTask<String, Integer, Boolean> {
        private String TAG = "DownloadAsyncTask";
        ICatchFile downloadFile;
        private String fileName;
        private String fileType = null;
        private String filePath = null;
        private RemoteMultiPbPresenter mRemoteMultiPbPresenter;

        public DownloadAsyncTaskForZd(ICatchFile iCatchFile, RemoteMultiPbPresenter remoteMultiPbPresenter) {
            super();
            mRemoteMultiPbPresenter = remoteMultiPbPresenter;
        }

        @Override
        protected Boolean doInBackground(String... params) {
            boolean retvalue = false;
//            String filePath = StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_PHOTO;
//            File file = new File(StorageUtil.getRootPath(context) + AppInfo.DOWNLOAD_PATH_PHOTO, "zd.txt");
            String filePath = context.getCacheDir().getAbsolutePath();
            File file = new File(context.getCacheDir().getAbsolutePath(), "zd.txt");
            if (FileUtils.isFileExists(file)) {
                file.delete();
            }
            String curFilePath = FileTools.chooseUniqueFilename(filePath + "/zd.txt");
            ICatchFile iCatchFile = new ICatchFile(1, 1, "/zd.info", "zd.info", 0);

            if (CameraManager.getInstance().getCurCamera() == null) {
                return false;
            }

            FileOperation fileOperation = CameraManager.getInstance().getCurCamera().getFileOperation();
            retvalue = fileOperation.downloadFile(iCatchFile, curFilePath);
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            return retvalue;
        }

        protected void onProgressUpdate(Integer... progress) {
        }

        protected void onPostExecute(Boolean result) {
            if (mOnHttpListener != null) {
                mOnHttpListener.onResult(result);
            }
            MyProgressDialog.closeProgressDialog();
        }
    }

    private void updateDownloadMessage() {
        String message = context.getResources().getString(R.string.download_progress).replace("$1$", String.valueOf(downloadSucceed))
                .replace("$2$", String.valueOf(downloadChooseList.size() - downloadSucceed)).replace("$3$", String.valueOf(downloadFailed));
        customDownloadDialog.setMessage(message);
    }

    private OnHttpListener mOnHttpListener;

    public interface OnHttpListener {
        void onResult(boolean isSuccess);
    }

    public void setOnHttpListenerListener(OnHttpListener onHttpListener) {
        this.mOnHttpListener = onHttpListener;
    }

    public void removeOnHttpListener() {
        mOnHttpListener = null;
    }
}

package com.icatch.mobilecam.Function.CameraAction;

import android.content.Context;
import android.media.MediaPlayer;

import com.blankj.utilcode.util.LogUtils;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.Presenter.PreviewPresenter;
import com.icatch.mobilecam.SdkApi.CameraAction;
import com.icatch.mobilecam.SdkApi.CameraProperties;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.data.type.A6RotateMotorState;
import com.icatchtek.control.customer.type.ICatchCamProperty;
import com.ijoyer.mobilecam.R;

import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
public class PhotoCapture {
    private static final String TAG = PhotoCapture.class.getSimpleName();
    private MediaPlayer stillCaptureStartBeep;
    private MediaPlayer delayBeep;
    private MediaPlayer continuousCaptureBeep;
    private OnStartPreviewListener onStartPreviewListener;
    private OnStopPreviewListener onStopPreviewListener;
    private OnCaptureCompletedListener onCaptureCompletedListener;
    private static final int TYPE_BURST_CAPTURE = 1;
    private static final int TYPE_NORMAL_CAPTURE = 2;
    private CameraProperties cameraProperties;
    private CameraAction cameraAction;
    private Context context;
    public PhotoCapture() {
        this.context = GlobalInfo.getInstance().getCurrentApp();
        stillCaptureStartBeep = MediaPlayer.create(context, R.raw.captureshutter);
        delayBeep = MediaPlayer.create(context, R.raw.delay_beep);
        continuousCaptureBeep = MediaPlayer.create(context, R.raw.captureburst);
        this.cameraProperties = CameraManager.getInstance().getCurCamera().getCameraProperties();
        this.cameraAction = CameraManager.getInstance().getCurCamera().getCameraAction();
    }
    private ExecutorService executorService;
    public void startCapture() {
        if (PreviewPresenter.isA6Camera) {
            new CaptureThread().run();
        } else {
            executorService = Executors.newSingleThreadExecutor();
            executorService.submit(new CaptureThread(), null);
        }
    }
    private class RotateCaptureThread implements Runnable {
        @Override
        public void run() {
            AppLog.i(TAG, "start RotateCaptureThread");
            long start = System.currentTimeMillis();
            if (onStopPreviewListener != null) {
                onStopPreviewListener.onStop();
            }
            cameraProperties.setPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE, A6RotateMotorState.ROTATE_START);
            int motorState = A6RotateMotorState.ROTATE_OFF;
            for (; ; ) {
                motorState = cameraProperties.getCurrentPropertyValue(PropertyId.A6_ROTATE_MOTOR_STATE);
                if (motorState == A6RotateMotorState.ROTATE_END) {
                    break;
                }
            }
            cameraAction.triggerCapturePhoto();
            if (onCaptureCompletedListener != null) {
                onCaptureCompletedListener.onCompleted();
            }
            long end = System.currentTimeMillis();
            AppLog.i(TAG, "RotateCaptureThread run take time : " + (end - start) / 1000 + "s");
            AppLog.i(TAG, "end RotateCaptureThread");
        }
    }
    private class NoBeepCaptureThread implements Runnable {
        @Override
        public void run() {
            if (onStopPreviewListener != null) {
                onStopPreviewListener.onStop();
            }
            cameraAction.triggerCapturePhoto();
            if (onCaptureCompletedListener != null) {
                onCaptureCompletedListener.onCompleted();
            }
        }
    }
    private class CaptureThread implements Runnable {
        @Override
        public void run() {
            AppLog.i(TAG, "start CameraCaptureThread");
            int delayTime;
            if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_CAPTURE_DELAY)) {
                delayTime = cameraProperties.getCurrentCaptureDelay();
            } else {
                delayTime = 0;
            }
            delayTime = Math.max(delayTime, 0);//防止获取不到延时（-1）
            if (delayTime < 1000) {
                if (onStopPreviewListener != null) {
                    onStopPreviewListener.onStop();
                }
            } else if (cameraProperties.hasFunction(PropertyId.CAPTURE_DELAY_MODE)) {
                TimerTask task = new TimerTask() {
                    @Override
                    public void run() {
                        if (onStopPreviewListener != null) {
                            onStopPreviewListener.onStop();
                        }
                    }
                };
                Timer timer = new Timer(true);
                timer.schedule(task, delayTime - 500);
            } else {
                if (onStopPreviewListener != null) {
                    onStopPreviewListener.onStop();
                }
            }
            int needCaptureCount = 1;
            if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_BURST_NUMBER)) {
                needCaptureCount = cameraProperties.getCurrentAppBurstNum();
            }
            if (needCaptureCount == 1) {
                CaptureAudioTask captureAudioTask = new CaptureAudioTask(needCaptureCount, TYPE_NORMAL_CAPTURE);
                Timer captureAudioTimer = new Timer(true);
                captureAudioTimer.schedule(captureAudioTask, delayTime);
            } else {
                CaptureAudioTask captureAudioTask = new CaptureAudioTask(needCaptureCount, TYPE_BURST_CAPTURE);
                Timer captureAudioTimer = new Timer(true);
                captureAudioTimer.schedule(captureAudioTask, delayTime, 420);
            }
            int count = delayTime / 1000;
            int timerDelay = 0;
            if (delayTime >= 5000) {
                Timer delayTimer = new Timer(true);
                DelayTimerTask delayTimerTask = new DelayTimerTask(count / 2, delayTimer);
                delayTimer.schedule(delayTimerTask, 0, 1000);
                timerDelay = delayTime;
            } else {
                timerDelay = 0;
                count = delayTime / 500;
            }
            if (delayTime >= 3000) {
                Timer delayTimer1 = new Timer(true);
                DelayTimerTask delayTimerTask1 = new DelayTimerTask(count / 2, delayTimer1);
                delayTimer1.schedule(delayTimerTask1, timerDelay / 2, 500);
                timerDelay = delayTime;
            } else {
                timerDelay = 0;
                count = delayTime / 250;
            }
            Timer delayTimer2 = new Timer(true);
            DelayTimerTask delayTimerTask2 = new DelayTimerTask(count, delayTimer2);
            delayTimer2.schedule(delayTimerTask2, timerDelay - timerDelay / 4, 250);
            cameraAction.triggerCapturePhoto();
            LogUtils.file("拍摄：触发拍摄动作（triggerCapturePhoto）");
            if (onCaptureCompletedListener != null) {
                onCaptureCompletedListener.onCompleted();
            }
            AppLog.i(TAG, "delayTime = " + delayTime + " needCaptureCount=" + needCaptureCount);
            AppLog.i(TAG, "end CameraCaptureThread");
        }
    }
    public void addOnStopPreviewListener(OnStopPreviewListener onStopPreviewListener) {
        this.onStopPreviewListener = onStopPreviewListener;
    }
    public interface OnStopPreviewListener {
        void onStop();
    }
    public void addOnStartPreviewListener(OnStartPreviewListener onStartPreviewListener) {
        this.onStartPreviewListener = onStartPreviewListener;
    }
    public interface OnStartPreviewListener {
        void onStart();
    }
    public void setOnCaptureCompletedListener(OnCaptureCompletedListener onCaptureCompletedListener) {
        this.onCaptureCompletedListener = onCaptureCompletedListener;
    }
    public interface OnCaptureCompletedListener {
        void onCompleted();
    }
    private class CaptureAudioTask extends TimerTask {
        private int burstNumber;
        private int type = TYPE_NORMAL_CAPTURE;
        public CaptureAudioTask(int burstNumber, int type) {
            this.burstNumber = burstNumber;
            this.type = type;
        }
        @Override
        public void run() {
            if (type == TYPE_NORMAL_CAPTURE) {
                if (burstNumber > 0) {
                    AppLog.i(TAG, "CaptureAudioTask remainBurstNumer =" + burstNumber);
                    stillCaptureStartBeep.start();
                    burstNumber--;
                } else {
                    cancel();
                }
            } else {
                if (burstNumber > 0) {
                    AppLog.i(TAG, "CaptureAudioTask remainBurstNumer =" + burstNumber);
                    continuousCaptureBeep.start();
                    burstNumber--;
                } else {
                    cancel();
                }
            }
        }
    }
    private class DelayTimerTask extends TimerTask {
        private int count;
        private Timer timer;
        public DelayTimerTask(int count, Timer timer) {
            this.count = count;
            this.timer = timer;
        }
        @Override
        public void run() {
            if (count-- > 0) {
                delayBeep.start();
            } else {
                if (timer != null) {
                    timer.cancel();
                }
            }
        }
    }
}

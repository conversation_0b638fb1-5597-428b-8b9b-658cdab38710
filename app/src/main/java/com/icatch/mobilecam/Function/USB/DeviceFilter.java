package com.icatch.mobilecam.Function.USB;
import android.content.Context;
import android.content.res.Resources.NotFoundException;
import android.hardware.usb.UsbDevice;
import android.text.TextUtils;
import android.util.Log;
import com.icatch.mobilecam.Log.AppLog;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
public final class DeviceFilter {
    private static final String TAG = "DeviceFilter";
    public final int mVendorId;
    public final int mProductId;
    public final int mClass;
    public final int mSubclass;
    public final int mProtocol;
    public final String mManufacturerName;
    public final String mProductName;
    public final String mSerialNumber;
    public DeviceFilter(final int vid, final int pid, final int clasz, final int subclass,
                        final int protocol, final String manufacturer, final String product, final String serialnum) {
        mVendorId = vid;
        mProductId = pid;
        mClass = clasz;
        mSubclass = subclass;
        mProtocol = protocol;
        mManufacturerName = manufacturer;
        mProductName = product;
        mSerialNumber = serialnum;
    }
    public DeviceFilter(final UsbDevice device) {
        mVendorId = device.getVendorId();
        mProductId = device.getProductId();
        mClass = device.getDeviceClass();
        mSubclass = device.getDeviceSubclass();
        mProtocol = device.getDeviceProtocol();
        mManufacturerName = null;    
        mProductName = null;        
        mSerialNumber = null;        
    }
    public static List<DeviceFilter> getDeviceFilters(final Context context, final int deviceFilterXmlId) {
        final XmlPullParser parser = context.getResources().getXml(deviceFilterXmlId);
        final List<DeviceFilter> deviceFilters = new ArrayList<DeviceFilter>();
        try {
            int eventType = parser.getEventType();
            while (eventType != XmlPullParser.END_DOCUMENT) {
                if (eventType == XmlPullParser.START_TAG) {
                    final DeviceFilter deviceFilter = read(context, parser);
                    if (deviceFilter != null) {
                        deviceFilters.add(deviceFilter);
                    }
                }
                eventType = parser.next();
            }
        } catch (final XmlPullParserException e) {
            Log.d(TAG, "XmlPullParserException", e);
        } catch (final IOException e) {
            Log.d(TAG, "IOException", e);
        }
        return Collections.unmodifiableList(deviceFilters);
    }
    private static final int getAttributeInteger(final Context context, final XmlPullParser parser, final String namespace, final String name, final int
			defaultValue) {
        int result = defaultValue;
        try {
            String v = parser.getAttributeValue(namespace, name);
            if (!TextUtils.isEmpty(v) && v.startsWith("@")) {
                final String r = v.substring(1);
                final int resId = context.getResources().getIdentifier(r, null, context.getPackageName());
                if (resId > 0) {
                    result = context.getResources().getInteger(resId);
                }
            } else {
                int radix = 10;
                if (v != null && v.length() > 2 && v.charAt(0) == '0' &&
                        (v.charAt(1) == 'x' || v.charAt(1) == 'X')) {
                    radix = 16;
                    v = v.substring(2);
                }
                result = Integer.parseInt(v, radix);
            }
        } catch (final NotFoundException e) {
            result = defaultValue;
        } catch (final NumberFormatException e) {
            result = defaultValue;
        } catch (final NullPointerException e) {
            result = defaultValue;
        }
        return result;
    }
    private static final String getAttributeString(final Context context, final XmlPullParser parser, final String namespace, final String name, final String
			defaultValue) {
        String result = defaultValue;
        try {
            result = parser.getAttributeValue(namespace, name);
            if (result == null)
                result = defaultValue;
            if (!TextUtils.isEmpty(result) && result.startsWith("@")) {
                final String r = result.substring(1);
                final int resId = context.getResources().getIdentifier(r, null, context.getPackageName());
                if (resId > 0)
                    result = context.getResources().getString(resId);
            }
        } catch (final NotFoundException e) {
            result = defaultValue;
        } catch (final NumberFormatException e) {
            result = defaultValue;
        } catch (final NullPointerException e) {
            result = defaultValue;
        }
        return result;
    }
    public static DeviceFilter read(final Context context, final XmlPullParser parser)
            throws XmlPullParserException, IOException {
        int vendorId = -1;
        int productId = -1;
        int deviceClass = -1;
        int deviceSubclass = -1;
        int deviceProtocol = -1;
        String manufacturerName = null;
        String productName = null;
        String serialNumber = null;
        boolean hasValue = false;
        String tag;
        int eventType = parser.getEventType();
        while (eventType != XmlPullParser.END_DOCUMENT) {
            tag = parser.getName();
            if (!TextUtils.isEmpty(tag) && (tag.equalsIgnoreCase("usb-device"))) {
                if (eventType == XmlPullParser.START_TAG) {
                    hasValue = true;
                    vendorId = getAttributeInteger(context, parser, null, "vendor-id", -1);
                    if (vendorId == -1) {
                        vendorId = getAttributeInteger(context, parser, null, "vendorId", -1);
                        if (vendorId == -1)
                            vendorId = getAttributeInteger(context, parser, null, "venderId", -1);
                    }
                    productId = getAttributeInteger(context, parser, null, "product-id", -1);
                    if (productId == -1)
                        productId = getAttributeInteger(context, parser, null, "productId", -1);
                    deviceClass = getAttributeInteger(context, parser, null, "class", -1);
                    deviceSubclass = getAttributeInteger(context, parser, null, "subclass", -1);
                    deviceProtocol = getAttributeInteger(context, parser, null, "protocol", -1);
                    manufacturerName = getAttributeString(context, parser, null, "manufacturer-name", null);
                    if (TextUtils.isEmpty(manufacturerName))
                        manufacturerName = getAttributeString(context, parser, null, "manufacture", null);
                    productName = getAttributeString(context, parser, null, "product-name", null);
                    if (TextUtils.isEmpty(productName))
                        productName = getAttributeString(context, parser, null, "product", null);
                    serialNumber = getAttributeString(context, parser, null, "serial-number", null);
                    if (TextUtils.isEmpty(serialNumber))
                        serialNumber = getAttributeString(context, parser, null, "serial", null);
                } else if (eventType == XmlPullParser.END_TAG) {
                    if (hasValue) {
                        return new DeviceFilter(vendorId, productId, deviceClass,
                                deviceSubclass, deviceProtocol, manufacturerName, productName,
                                serialNumber);
                    }
                }
            }
            eventType = parser.next();
        }
        return null;
    }
    public boolean matches(final UsbDevice device) {
        AppLog.d(TAG, "mVendorId =" + mVendorId);
        if (mVendorId != -1 && device.getVendorId() == mVendorId) {
            return true;
        }
        return false;
    }
    @Override
    public boolean equals(final Object obj) {
        if (mVendorId == -1 || mProductId == -1 || mClass == -1
                || mSubclass == -1 || mProtocol == -1) {
            return false;
        }
        if (obj instanceof DeviceFilter) {
            final DeviceFilter filter = (DeviceFilter) obj;
            if (filter.mVendorId != mVendorId
                    || filter.mProductId != mProductId
                    || filter.mClass != mClass || filter.mSubclass != mSubclass
                    || filter.mProtocol != mProtocol) {
                return (false);
            }
            if ((filter.mManufacturerName != null && mManufacturerName == null)
                    || (filter.mManufacturerName == null && mManufacturerName != null)
                    || (filter.mProductName != null && mProductName == null)
                    || (filter.mProductName == null && mProductName != null)
                    || (filter.mSerialNumber != null && mSerialNumber == null)
                    || (filter.mSerialNumber == null && mSerialNumber != null)) {
                return (false);
            }
            if ((filter.mManufacturerName != null && mManufacturerName != null && !mManufacturerName
                    .equals(filter.mManufacturerName))
                    || (filter.mProductName != null && mProductName != null && !mProductName
                    .equals(filter.mProductName))
                    || (filter.mSerialNumber != null && mSerialNumber != null && !mSerialNumber
                    .equals(filter.mSerialNumber))) {
                return (false);
            }
            return (true);
        }
        if (obj instanceof UsbDevice) {
            final UsbDevice device = (UsbDevice) obj;
            if (device.getVendorId() != mVendorId
                    || device.getProductId() != mProductId
                    || device.getDeviceClass() != mClass
                    || device.getDeviceSubclass() != mSubclass
                    || device.getDeviceProtocol() != mProtocol) {
                return (false);
            }
            return true;
        }
        return false;
    }
    @Override
    public int hashCode() {
        return (((mVendorId << 16) | mProductId) ^ ((mClass << 16)
                | (mSubclass << 8) | mProtocol));
    }
    @Override
    public String toString() {
        return "DeviceFilter[mVendorId=" + mVendorId + ",mProductId="
                + mProductId + ",mClass=" + mClass + ",mSubclass=" + mSubclass
                + ",mProtocol=" + mProtocol
                + ",mManufacturerName=" + mManufacturerName
                + ",mProductName=" + mProductName
                + ",mSerialNumber=" + mSerialNumber
                + "]";
    }
}

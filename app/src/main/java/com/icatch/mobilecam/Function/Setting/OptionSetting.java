package com.icatch.mobilecam.Function.Setting;

import static android.content.Context.MODE_PRIVATE;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.ComponentName;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.wifi.WifiManager;
import android.os.Environment;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.Toast;

import com.blankj.utilcode.util.SPUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Function.BaseProrertys;
import com.icatch.mobilecam.Function.SDKEvent;
import com.icatch.mobilecam.Listener.OnSettingCompleteListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.PreviewPresenter;
import com.icatch.mobilecam.Presenter.PreviewPresenterV2;
import com.icatch.mobilecam.Presenter.PreviewPresenter_BackUp;
import com.icatch.mobilecam.SdkApi.CameraAction;
import com.icatch.mobilecam.SdkApi.CameraProperties;
import com.icatch.mobilecam.SdkApi.CameraState;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.ExitApp;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Message.AppMessage;
import com.icatch.mobilecam.data.Mode.PreviewMode;
import com.icatch.mobilecam.data.PropertyId.PropertyId;
import com.icatch.mobilecam.data.type.SlowMotion;
import com.icatch.mobilecam.data.type.TimeLapseInterval;
import com.icatch.mobilecam.data.type.TimeLapseMode;
import com.icatch.mobilecam.data.type.Upside;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.StorageUtil;
import com.icatch.mobilecam.utils.WifiAPUtil;
import com.icatch.mobilecam.utils.WifiCheck;
import com.icatch.mobilecam.utils.fileutils.FileTools;
import com.icatchtek.control.customer.type.ICatchCamBurstNumber;
import com.icatchtek.control.customer.type.ICatchCamDateStamp;
import com.icatchtek.control.customer.type.ICatchCamEventID;
import com.icatchtek.control.customer.type.ICatchCamLightFrequency;
import com.icatchtek.control.customer.type.ICatchCamMode;
import com.icatchtek.control.customer.type.ICatchCamPreviewMode;
import com.icatchtek.control.customer.type.ICatchCamProperty;
import com.icatchtek.control.customer.type.ICatchCamWhiteBalance;
import com.icatchtek.reliant.customer.type.ICatchImageSize;
import com.ijoyer.mobilecam.R;

import java.lang.reflect.Field;
import java.util.List;

public class OptionSetting {
    private final static String TAG = OptionSetting.class.getSimpleName();
    private OnSettingCompleteListener onSettingCompleteListener;
    private AlertDialog alertDialog;
    private final SettingHandler handler = new SettingHandler();
    private SDKEvent sdkEvent;
    private Context context;
    private Activity activity;
    private String wifiSSID, password;
    private MyCamera myCamera;
    private BaseProrertys baseProrertys;
    private CameraProperties cameraProperties;
    private CameraAction cameraAction;
    private CameraState cameraState;
    private PreviewPresenterV2 mPreviewPresenterV2;
    private PreviewPresenter_BackUp mPreviewPresenterV100;
    private PreviewPresenter previewPresenter;

    public OptionSetting() {
        myCamera = CameraManager.getInstance().getCurCamera();
        baseProrertys = myCamera.getBaseProrertys();
        cameraProperties = myCamera.getCameraProperties();
        cameraAction = myCamera.getCameraAction();
        cameraState = myCamera.getCameraState();
    }

    public OptionSetting(PreviewPresenter previewPresenter) {
        this.previewPresenter = previewPresenter;
        myCamera = CameraManager.getInstance().getCurCamera();
        baseProrertys = myCamera.getBaseProrertys();
        cameraProperties = myCamera.getCameraProperties();
        cameraAction = myCamera.getCameraAction();
        cameraState = myCamera.getCameraState();
    }

    public OptionSetting(PreviewPresenterV2 previewPresenterV2) {
        mPreviewPresenterV2 = previewPresenterV2;
        myCamera = CameraManager.getInstance().getCurCamera();
        baseProrertys = myCamera.getBaseProrertys();
        cameraProperties = myCamera.getCameraProperties();
        cameraAction = myCamera.getCameraAction();
        cameraState = myCamera.getCameraState();
    }

    public OptionSetting(PreviewPresenter_BackUp previewPresenterV100) {
        mPreviewPresenterV100 = previewPresenterV100;
        myCamera = CameraManager.getInstance().getCurCamera();
        baseProrertys = myCamera.getBaseProrertys();
        cameraProperties = myCamera.getCameraProperties();
        cameraAction = myCamera.getCameraAction();
        cameraState = myCamera.getCameraState();
    }

    public void addSettingCompleteListener(OnSettingCompleteListener onSettingCompleteListener) {
        this.onSettingCompleteListener = onSettingCompleteListener;
    }

    public void showSettingDialog(int nameId, Activity activity) {
        this.context = activity;
        this.activity = activity;
        if (nameId == R.string.A6_ROTATE_MOTOR_STATE) {
            Log.d(TAG, "A6_ROTATE_MOTOR_STATE");
            showA6RotateMotorState(context);
        } else if (nameId == R.string.A6_ROTATE_SHOT_TIMES) {
            Log.d(TAG, "A6_ROTATE_SHOT_TIMES");
            showA6RotateShotTimes(context);
        } else if (nameId == R.string.restore_factory) {
            Log.d(TAG, "restore_factory");
            showRestoreFactory(context);
        } else if (nameId == R.string.setting_image_size) {
            Log.d(TAG, "setting_image_size");
            showImageSizeOptionDialog(context);
        } else if (nameId == R.string.setting_video_size) {
            Log.d(TAG, "setting_video_size");
            showVideoSizeOptionDialog(context);
        } else if (nameId == R.string.setting_capture_delay) {
            Log.d(TAG, "setting_capture_delay");
            showDelayTimeOptionDialog(context);
        } else if (nameId == R.string.setting_capture_delay_v2) {
            showDelayTimeOptionDialog(context);
        } else if (nameId == R.string.title_burst) {
            showBurstOptionDialog(context);
        } else if (nameId == R.string.title_awb) {
            Log.d(TAG, "showWhiteBalanceOptionDialog =");
            showWhiteBalanceOptionDialog(context);
        } else if (nameId == R.string.setting_power_supply) {
            showElectricityFrequencyOptionDialog(context);
        } else if (nameId == R.string.setting_datestamp) {
            showDateStampOptionDialog(context);
        } else if (nameId == R.string.setting_formatted) {
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (Boolean.TRUE.equals(isSDCardExist)) {
                showFormatConfirmDialog(context);
            } else if (Boolean.FALSE.equals(isSDCardExist)){
                sdCardIsNotReadyAlert(context);
            }else {
                AppDialog.showDialogWarn(context, R.string.dialog_connect_failed);
            }
        } else if (nameId == R.string.setting_time_lapse_interval) {
            showTimeLapseIntervalDialog(context);
        } else if (nameId == R.string.setting_time_lapse_duration) {
            showTimeLapseDurationDialog(context);
        } else if (nameId == R.string.title_timeLapse_mode) {
            showTimeLapseModeDialog(context);
        } else if (nameId == R.string.slowmotion) {
            showSlowMotionDialog(context);
        } else if (nameId == R.string.upside) {
            showUpsideDialog(context);
        } else if (nameId == R.string.camera_wifi_configuration) {
            showCameraConfigurationDialog(context);
        } else if (nameId == R.string.setting_update_fw) {
            Boolean isSDCardExist = cameraProperties.isSDCardExist();
            if (Boolean.TRUE.equals(isSDCardExist)) {
                String fwPth = Environment.getExternalStorageDirectory().toString() + AppInfo.PROPERTY_CFG_DIRECTORY_PATH;
                ;
                String fwUpgradeName = AppInfo.FW_UPGRADE_FILENAME;
                if (!FileTools.checkFwUpgradeFile(fwPth, fwUpgradeName)) {
                    String msg = context.getString(R.string.setting_updatefw_upgrade_file_not_exist)
                            .replace("$1$", fwUpgradeName)
                            .replace("$2$", AppInfo.PROPERTY_CFG_DIRECTORY_PATH);
                    AppDialog.showDialogWarn(context, msg);
                    return;
                }
                showUpdateFWDialog(context);
            } else if (Boolean.FALSE.equals(isSDCardExist)){
                sdCardIsNotReadyAlert(context);
            }else {
                AppDialog.showDialogWarn(context, R.string.dialog_connect_failed);
            }
        } else if (nameId == R.string.setting_auto_download_size_limit) {
            showSetDownloadSizeLimitDialog(context);
        } else if (nameId == R.string.setting_enable_wifi_hotspot) {
            showEnableWifihotspotDialog();
        } else if (nameId == R.string.setting_title_exposure_compensation) {
            AppLog.d(TAG, "showExposureCompensationDialog");
            showExposureCompensationDialog(context);
        } else if (nameId == R.string.setting_title_video_file_length) {
            AppLog.d(TAG, "showVideoFileLengthDialog");
            showVideoFileLengthDialog(context);
        } else if (nameId == R.string.setting_title_video_file_length_v2) {
            AppLog.d(TAG, "showVideoFileLengthDialog");
            showVideoFileLengthDialog(context);
        } else if (nameId == R.string.setting_title_screen_saver) {
            AppLog.d(TAG, "showScreenSaverDialog");
            showScreenSaverDialog(context);
        } else if (nameId == R.string.setting_title_auto_power_off) {
            AppLog.d(TAG, "showAutoPowerOffDialog");
            showAutoPowerOffDialog(context);
        } else if (nameId == R.string.setting_title_fast_motion_movie) {
            AppLog.d(TAG, "showFastMotionMovieDialog");
            showFastMotionMovieDialog(context);
        } else if (nameId == R.string.setting_storage_location) {
            AppLog.d(TAG, "showStorageLocationDialog");
            showStorageLocationDialog(context);
        }
    }

    public void showStorageLocationDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_storage_location);
        boolean sdCardExist = StorageUtil.sdCardExist(context);
        final String[] storageLocationString;
        int curIdx = 0;
        if (sdCardExist) {
            storageLocationString = new String[2];
            storageLocationString[0] = context.getResources().getString(R.string.setting_internal_storage);
            storageLocationString[1] = context.getResources().getString(R.string.setting_sd_card_storage);
        } else {
            storageLocationString = new String[1];
            storageLocationString[0] = context.getResources().getString(R.string.setting_internal_storage);
        }
        SharedPreferences preferences = context.getSharedPreferences("appData", MODE_PRIVATE);
        String storageLocation = preferences.getString("storageLocation", "InternalStorage");
        if (storageLocation.equals("InternalStorage")) {
            curIdx = 0;
        } else {
            curIdx = 1;
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                if (arg1 == 0) {
                    SharedPreferences.Editor editor = context.getSharedPreferences("appData", MODE_PRIVATE).edit();
                    editor.putString("storageLocation", "InternalStorage");
                    editor.commit();
                } else {
                    SharedPreferences.Editor editor = context.getSharedPreferences("appData", MODE_PRIVATE).edit();
                    editor.putString("storageLocation", "SdCard");
                    editor.commit();
                }
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
                AppLog.d(TAG, "showStorageLocationDialog  storageLocation =" + arg1);
            }
        };
        showOptionDialog(title, storageLocationString, curIdx, listener, true);
    }

    public void showFastMotionMovieDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_title_fast_motion_movie);
        final String[] fastMotionMovieUIString = baseProrertys.getFastMotionMovie().getValueList();
        if (fastMotionMovieUIString == null) {
            AppLog.e(TAG, "fastMotionMovieUIString == null");
            return;
        }
        int length = fastMotionMovieUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getFastMotionMovie().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (fastMotionMovieUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getFastMotionMovie().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, fastMotionMovieUIString, curIdx, listener, true);
    }

    public void showAutoPowerOffDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_title_auto_power_off);
        final String[] autoPowerOffUIString = baseProrertys.getAutoPowerOff().getValueList();
        if (autoPowerOffUIString == null) {
            AppLog.e(TAG, "autoPowerOffUIString == null");
            return;
        }
        int length = autoPowerOffUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getAutoPowerOff().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (autoPowerOffUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getAutoPowerOff().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, autoPowerOffUIString, curIdx, listener, true);
    }

    public void showScreenSaverDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_title_screen_saver);
        final String[] screenSaverUIString = baseProrertys.getScreenSaver().getValueList();
        if (screenSaverUIString == null) {
            AppLog.e(TAG, "screenSaverUIString == null");
            return;
        }
        int length = screenSaverUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getScreenSaver().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (screenSaverUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getScreenSaver().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, screenSaverUIString, curIdx, listener, true);
    }

    public void showEnableWifihotspotDialog() {
        LayoutInflater factory = LayoutInflater.from(context);
        View textEntryView = factory.inflate(R.layout.setting_enable_wifi_hotspot, null);
        final EditText wifiName = (EditText) textEntryView.findViewById(R.id.wifi_ssid);
        final EditText cameraPassword = (EditText) textEntryView.findViewById(R.id.wifi_password);
        AlertDialog.Builder ad1 = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        ad1.setTitle(R.string.setting_enable_wifi_hotspot);
        ad1.setIcon(android.R.drawable.ic_dialog_info);
        ad1.setView(textEntryView);
        ad1.setCancelable(true);
        ad1.setOnKeyListener(new DialogInterface.OnKeyListener() {
            @Override
            public boolean onKey(DialogInterface dialog, int keyCode, KeyEvent event) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    Log.d(TAG, "KeyEvent.KEYCODE_BACK");
                    try {
                        Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                        field.setAccessible(true);
                        field.set(dialog, true);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    dialog.dismiss();
                }
                return false;
            }
        });
        ad1.setPositiveButton(R.string.camera_configuration_set, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int arg1) {
                wifiSSID = wifiName.getText().toString();
                password = cameraPassword.getText().toString();
                if (wifiSSID.isEmpty() || password.isEmpty()) {
                    MyToast.show(context, R.string.wifi_or_password_cannot_be_empty);
                    try {
                        Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                        field.setAccessible(true);
                        field.set(dialog, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return;
                }
                if (wifiSSID.length() > 20) {
                    MyToast.show(context, R.string.camera_name_limit);
                    try {
                        Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                        field.setAccessible(true);
                        field.set(dialog, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return;
                }
                if (password.length() > 20) {
                    MyToast.show(context, R.string.password_limit);
                    try {
                        Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                        field.setAccessible(true);
                        field.set(dialog, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return;
                }
                try {
                    Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                    field.setAccessible(true);
                    field.set(dialog, true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                MyProgressDialog.showProgressDialog(context, R.string.action_processing);
                WifiAPUtil.getInstance(context.getApplicationContext()).regitsterHandler(handler);
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        boolean ret = cameraProperties.setStringPropertyValue(PropertyId.STA_MODE_SSID, wifiSSID);
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        if (!ret) {
                            handler.obtainMessage(AppMessage.AP_MODE_TO_STA_MODE_FAILURE).sendToTarget();
                            return;
                        }
                        ret = cameraProperties.setStringPropertyValue(PropertyId.STA_MODE_PASSWORD, password);
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        if (!ret) {
                            handler.obtainMessage(AppMessage.AP_MODE_TO_STA_MODE_FAILURE).sendToTarget();
                            return;
                        }
                        ret = cameraProperties.setPropertyValue(PropertyId.AP_MODE_TO_STA_MODE, 1);
                        if (!ret) {
                            handler.obtainMessage(AppMessage.AP_MODE_TO_STA_MODE_FAILURE).sendToTarget();
                            return;
                        }
                        ret = WifiAPUtil.getInstance(context).turnOnWifiAp(wifiSSID, password, WifiAPUtil.WifiSecurityType.WIFICIPHER_WPA2);
                        if (!ret) {
                            handler.post(new Runnable() {
                                @Override
                                public void run() {
                                    MyProgressDialog.closeProgressDialog();
                                    AlertDialog.Builder builder4 = new AlertDialog.Builder(activity, R.style.MyAlertDialog);
                                    context.getResources().getString(R.string.download_progress).replace("$1$", wifiSSID).replace("$2$", password);
                                    String info = "Wifi热点开启失败，可能系统不支持，需要手动开启,并设置Wifi热点名称为：" + wifiSSID + " 密码为：" + password + " 设置完成后，请重新启动并连接。";
                                    builder4.setMessage(info);
                                    builder4.setNegativeButton(R.string.ok, new DialogInterface.OnClickListener() {
                                        @Override
                                        public void onClick(DialogInterface dialog, int which) {
                                            Intent intent = new Intent();
                                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                            ComponentName comp = new ComponentName("com.android.settings", "com.android.settings.Settings$TetherSettingsActivity");
                                            intent.setComponent(comp);
                                            activity.startActivity(intent);
                                        }
                                    });
                                    alertDialog = builder4.create();
                                    alertDialog.setCancelable(false);
                                    alertDialog.show();
                                }
                            });
                        } else {
                            handler.obtainMessage(AppMessage.AP_MODE_TO_STA_MODE_SUSSED).sendToTarget();
                        }
                    }
                }).start();
            }
        });
        ad1.show();
    }

    private void showUpdateFWDialog(final Context context) {
        AppLog.i(TAG, "showUpdateFWDialog");
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setMessage(R.string.setting_updateFW_prompt);
        builder.setNegativeButton(R.string.setting_no, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        });
        builder.setPositiveButton(R.string.setting_yes, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (sdkEvent == null) {
                    sdkEvent = new SDKEvent(handler);
                }
                sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_COMPLETED);
                sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_POWEROFF);
                sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHECK);
                sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHKSUMERR);
                sdkEvent.addEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_NG);
                MyProgressDialog.showProgressDialog(context, R.string.setting_update_fw);
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        String filePath = Environment.getExternalStorageDirectory().toString() + AppInfo.PROPERTY_CFG_DIRECTORY_PATH;
                        String fileName = filePath + AppInfo.FW_UPGRADE_FILENAME;
                        if (!cameraAction.updateFW(fileName)) {
                            handler.post(new Runnable() {
                                @Override
                                public void run() {
                                    MyProgressDialog.closeProgressDialog();
                                    AppDialog.showDialogWarn(activity, R.string.setting_updatefw_failedInfo);
                                }
                            });
                        }
                    }
                }).start();
            }
        });
        builder.create().show();
    }

    private void showCameraConfigurationDialog(final Context context) {
        LayoutInflater factory = LayoutInflater.from(context);
        View textEntryView = factory.inflate(R.layout.camera_name_password_set, null);
        final EditText cameraName = (EditText) textEntryView.findViewById(R.id.camera_name);
        final String name = cameraProperties.getCameraSsid();
        cameraName.setText(name);
        final EditText cameraPassword = (EditText) textEntryView.findViewById(R.id.wifi_password);
        final String password = cameraProperties.getCameraPassword();
        cameraPassword.setText(password);
        AlertDialog.Builder ad1 = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        ad1.setTitle(R.string.camera_wifi_configuration);
        ad1.setIcon(android.R.drawable.ic_dialog_info);
        ad1.setView(textEntryView);
        ad1.setCancelable(true);
        ad1.setNegativeButton(R.string.gallery_cancel, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                try {
                    Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                    field.setAccessible(true);
                    field.set(dialog, true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                dialog.dismiss();
            }
        });
        ad1.setPositiveButton(R.string.camera_configuration_set, new DialogInterface.OnClickListener() {
            public void onClick(DialogInterface dialog, int arg1) {
                String temp1 = cameraName.getText().toString();
                if (temp1.length() > 20 || temp1.length() < 1) {
                    Toast.makeText(context, R.string.camera_name_limit, Toast.LENGTH_LONG).show();
                    try {
                        Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                        field.setAccessible(true);
                        field.set(dialog, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return;
                }
                String temp = cameraPassword.getText().toString();
                if (temp.length() > 10 || temp.length() < 8) {
                    Toast.makeText(context, R.string.password_limit, Toast.LENGTH_LONG).show();
                    try {
                        Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                        field.setAccessible(true);
                        field.set(dialog, false);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return;
                }
                try {
                    Field field = dialog.getClass().getSuperclass().getDeclaredField("mShowing");
                    field.setAccessible(true);
                    field.set(dialog, true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                if (name.equals(cameraName.getText().toString()) == false) {
                    cameraProperties.setCameraSsid(cameraName.getText().toString());
                }
                if (password.equals(temp) == false) {
                    cameraProperties.setCameraPassword(cameraPassword.getText().toString());
                }
            }
        });
        ad1.show();
    }

    private void showUpsideDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.upside);
        final String[] upsideUIString = baseProrertys.getUpside().getValueList();
        if (upsideUIString == null) {
            AppLog.e(TAG, "upsideUIString == null");
            return;
        }
        int length = upsideUIString.length;
        int curIdx = 0;
        String curValue = baseProrertys.getUpside().getCurrentUiStringInSetting();
        for (int i = 0; i < length; i++) {
            if (upsideUIString[i].equals(curValue)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getUpside().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, upsideUIString, curIdx, listener, true);
    }

    private void showSlowMotionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.title_slow_motion);
        final String[] slowMotionUIString = baseProrertys.getSlowMotion().getValueList();
        if (slowMotionUIString == null) {
            AppLog.e(TAG, "slowMotionUIString == null");
            return;
        }
        int length = slowMotionUIString.length;
        int curIdx = 0;
        String curValue = baseProrertys.getSlowMotion().getCurrentUiStringInSetting();
        for (int i = 0; i < length; i++) {
            if (slowMotionUIString[i].equals(curValue)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getSlowMotion().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, slowMotionUIString, curIdx, listener, true);
    }

    private void showRestoreFactory(final Context context) {
        showRestoreFactoryConfirmDialog(context);
    }

    private void restoreFactory(final Context context) {
        if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_IMAGE_SIZE)) {
            baseProrertys.getImageSize().setValueByPosition(0);
        }
        if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_VIDEO_SIZE)) {
            baseProrertys.getVideoSize().setValueByPosition(0);
            onSettingCompleteListener.settingVideoSizeComplete();
        }
        if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_CAPTURE_DELAY)) {
            baseProrertys.getCaptureDelay().setValue(0);
        }
        if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_BURST_NUMBER)) {
            baseProrertys.getBurst().setValue(ICatchCamBurstNumber.ICH_CAM_BURST_NUMBER_OFF);
        }
        if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_WHITE_BALANCE)) {
            baseProrertys.getWhiteBalance().setValue(ICatchCamWhiteBalance.ICH_CAM_WB_AUTO);
        }
        if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_LIGHT_FREQUENCY)) {
            baseProrertys.getElectricityFrequency().setValue(ICatchCamLightFrequency.ICH_CAM_LIGHT_FREQUENCY_50HZ);
        }
        if (cameraProperties.hasFunction(ICatchCamProperty.ICH_CAM_CAP_DATE_STAMP)) {
            baseProrertys.getDateStamp().setValue(ICatchCamDateStamp.ICH_CAM_DATE_STAMP_OFF);
        }
        if (cameraState.isSupportImageAutoDownload()) {
        }
        if (cameraProperties.hasFunction(PropertyId.UP_SIDE)) {
            baseProrertys.getUpside().setValue(Upside.UPSIDE_OFF);
        }
        if (cameraProperties.hasFunction(PropertyId.CAMERA_ESSID)) {
        }
        if (cameraProperties.hasFunction(PropertyId.POWER_ON_AUTO_RECORD)) {
            cameraProperties.setPropertyValue(PropertyId.POWER_ON_AUTO_RECORD, 1);
        }
        if (cameraProperties.hasFunction(PropertyId.AUTO_POWER_OFF)) {
        }
        if (cameraProperties.hasFunction(PropertyId.EXPOSURE_COMPENSATION)) {
            baseProrertys.getExposureCompensation().setValue(0);
        }
        if (cameraProperties.hasFunction(PropertyId.STA_MODE_SSID)) {
        }
        if (cameraProperties.hasFunction(PropertyId.SLOW_MOTION)) {
            baseProrertys.getSlowMotion().setValue(SlowMotion.SLOW_MOTION_OFF);
        }
        if (cameraProperties.hasFunction(PropertyId.SCREEN_SAVER)) {
        }
        if (cameraProperties.hasFunction(PropertyId.IMAGE_STABILIZATION)) {
        }
        if (cameraProperties.hasFunction(PropertyId.VIDEO_FILE_LENGTH)) {
            baseProrertys.getVideoFileLength().setValue(0);
        }
        if (cameraProperties.hasFunction(PropertyId.FAST_MOTION_MOVIE)) {
        }
        if (cameraProperties.hasFunction(PropertyId.WIND_NOISE_REDUCTION)) {
            cameraProperties.setPropertyValue(PropertyId.WIND_NOISE_REDUCTION, 1);
        }
        if (cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE)) {
            if (myCamera.timeLapsePreviewMode == TimeLapseMode.TIME_LAPSE_MODE_STILL) {
                baseProrertys.gettimeLapseDuration().setValueByPosition(0);
                if (baseProrertys.getTimeLapseStillInterval().getValueStringInt() != null) {
                    baseProrertys.getTimeLapseStillInterval().setValueByPosition(0);
                }
                myCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
            } else {
                baseProrertys.gettimeLapseDuration().setValueByPosition(0);
                if (baseProrertys.getTimeLapseVideoInterval().getValueStringInt() != null) {
                    baseProrertys.getTimeLapseVideoInterval().setValueByPosition(0);
                }
                myCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
            }
            if (!cameraProperties.cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_TIMELAPSE_VIDEO_OFF)) {
                if (myCamera.timeLapsePreviewMode == TimeLapseMode.TIME_LAPSE_MODE_STILL) {
                    baseProrertys.gettimeLapseDuration().setValueByPosition(0);
                    if (baseProrertys.getTimeLapseStillInterval().getValueStringInt() != null) {
                        baseProrertys.getTimeLapseStillInterval().setValueByPosition(0);
                    }
                    myCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_VIDEO;
                } else {
                    baseProrertys.gettimeLapseDuration().setValueByPosition(0);
                    if (baseProrertys.getTimeLapseVideoInterval().getValueStringInt() != null) {
                        baseProrertys.getTimeLapseVideoInterval().setValueByPosition(0);
                    }
                    myCamera.timeLapsePreviewMode = TimeLapseMode.TIME_LAPSE_MODE_STILL;
                }
                if (myCamera.timeLapsePreviewMode != TimeLapseMode.TIME_LAPSE_MODE_STILL) {
                }
            }
        }
        if (cameraProperties.hasFunction(PropertyId.A6_ROTATE_MOTOR_STATE)) {
        }
    }

    private void showRestoreFactoryConfirmDialog(final Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
//        builder.setMessage("恢复出厂设置，你的所有设置都将恢复为默认。");
        builder.setMessage(PanoramaApp.getContext().getString(R.string.restore));
        builder.setNegativeButton(R.string.setting_no, (dialog, which) -> {
        });
        builder.setPositiveButton(R.string.setting_yes, (dialog, which) -> {
            dialog.dismiss();
            final Handler handler = new Handler();
            MyProgressDialog.showProgressDialog(context, R.string.action_processing);
            new Thread(() -> {
                final int messageId;
                restoreFactory(context);
                messageId = R.string.text_operation_success;
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                        onSettingCompleteListener.onOptionSettingComplete();
                        MyToast.show(context, messageId);
                    }
                });
            }).start();
        });
        builder.create().show();
    }

    private void showA6RotateMotorState(final Context context) {
        CharSequence title = context.getResources().getString(R.string.A6_ROTATE_MOTOR_STATE);
        final String[] a6RotateMotorStateUIString = baseProrertys.getA6RotateMotorState().getValueList();
        if (a6RotateMotorStateUIString == null) {
            AppLog.e(TAG, "a6RotateMotorStateUIString == null");
            return;
        }
        int length = a6RotateMotorStateUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getA6RotateMotorState().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (a6RotateMotorStateUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = (arg0, arg1) -> {
            baseProrertys.getA6RotateMotorState().setValueByPosition(arg1);
            arg0.dismiss();
            onSettingCompleteListener.onOptionSettingComplete();
        };
        showOptionDialog(title, a6RotateMotorStateUIString, curIdx, listener, true);
    }

    private void showA6RotateShotTimes(final Context context) {
        CharSequence title = context.getResources().getString(R.string.A6_ROTATE_SHOT_TIMES);
        final String[] a6RotateShotTimesUIString = baseProrertys.getA6RotateShotTimes().getValueList();
        if (a6RotateShotTimesUIString == null) {
            AppLog.e(TAG, "a6RotateShotTimesUIString == null");
            return;
        }
        int length = a6RotateShotTimesUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getA6RotateShotTimes().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (a6RotateShotTimesUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = (arg0, arg1) -> {
            baseProrertys.getA6RotateShotTimes().setValueByPosition(arg1);
            arg0.dismiss();
            onSettingCompleteListener.onOptionSettingComplete();
        };
        showOptionDialog(title, a6RotateShotTimesUIString, curIdx, listener, true);
    }

    private void showTimeLapseModeDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.title_timeLapse_mode);
        final String[] timeLapseModeString = baseProrertys.getTimeLapseMode().getValueList();
        if (timeLapseModeString == null) {
            AppLog.e(TAG, "timeLapseModeString == null");
            return;
        }
        int length = timeLapseModeString.length;
        int curIdx = 0;
        String curValue = baseProrertys.getTimeLapseMode().getCurrentUiStringInSetting();
        for (int i = 0; i < length; i++) {
            Log.d(TAG, "timeLapseModeString[i] =" + timeLapseModeString[i]);
            if (timeLapseModeString[i] != null && timeLapseModeString[i].equals(curValue)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                myCamera.timeLapsePreviewMode = arg1;
                arg0.dismiss();
                onSettingCompleteListener.onResetDoCaptureBtn(arg1);
                onSettingCompleteListener.settingTimeLapseModeComplete(arg1);
                onSettingCompleteListener.onOptionSettingComplete();
                Log.d(TAG, "showTimeLapseModeDialog  timeLapseMode =" + arg1);

                if (previewPresenter != null) {
                    previewPresenter.stopPreview();
                    previewPresenter.changeCameraMode(PreviewMode.APP_STATE_VIDEO_PREVIEW, ICatchCamPreviewMode.ICH_CAM_VIDEO_PREVIEW_MODE);
                }

                if (mPreviewPresenterV2 != null) {
                    mPreviewPresenterV2.initSurface();
                }
            }
        };
        showOptionDialog(title, timeLapseModeString, curIdx, listener, true);
    }

    private void showTimeLapseDurationDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_time_lapse_duration);
        final String[] videoTimeLapseDurationString = baseProrertys.gettimeLapseDuration().getValueStringList();
        if (videoTimeLapseDurationString == null) {
            AppLog.e(TAG, "videoTimeLapseDurationString == null");
            return;
        }
        int length = videoTimeLapseDurationString.length;
        int curIdx = 0;
        String temp = baseProrertys.gettimeLapseDuration().getCurrentValue();
        for (int i = 0; i < length; i++) {
            if (videoTimeLapseDurationString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.gettimeLapseDuration().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, videoTimeLapseDurationString, curIdx, listener, true);
    }

    private void showTimeLapseIntervalDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_time_lapse_interval);
        final TimeLapseInterval timeLapseInterval;
        AppLog.e(TAG, "showTimeLapseIntervalDialog timeLapsePreviewMode:" + myCamera.timeLapsePreviewMode);
        if (myCamera.timeLapsePreviewMode == TimeLapseMode.TIME_LAPSE_MODE_STILL) {
            timeLapseInterval = baseProrertys.getTimeLapseStillInterval();
        } else {
            timeLapseInterval = baseProrertys.getTimeLapseVideoInterval();
        }
        final String[] videoTimeLapseIntervalString = timeLapseInterval.getValueStringList();
        if (videoTimeLapseIntervalString == null) {
            AppLog.e(TAG, "videoTimeLapseIntervalString == null");
            return;
        }
        int length = videoTimeLapseIntervalString.length;
        int curIdx = 0;
        String temp = timeLapseInterval.getCurrentValue();
        for (int i = 0; i < length; i++) {
            if (videoTimeLapseIntervalString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                timeLapseInterval.setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, videoTimeLapseIntervalString, curIdx, listener, true);
    }

    private void showDelayTimeOptionDialog(final Context context, final OnSettingCompleteListener settingCompleteListener) {
        CharSequence title = context.getResources().getString(R.string.stream_set_timer);
        final String[] delayTimeUIString = baseProrertys.getCaptureDelay().getValueList();
        if (delayTimeUIString == null) {
            AppLog.e(TAG, "delayTimeUIString == null");
            return;
        }
        int length = delayTimeUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getCaptureDelay().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (delayTimeUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getCaptureDelay().setValueByPosition(arg1);
                arg0.dismiss();
                settingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, delayTimeUIString, curIdx, listener, true);
    }

    public void showImageSizeOptionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.stream_set_res_photo);
        final String[] imageSizeUIString = baseProrertys.getImageSize().getValueArrayString();
        if (imageSizeUIString == null) {
            AppLog.e(TAG, "imageSizeUIString == null");
            return;
        }
        int length = imageSizeUIString.length;
        int curIdx = 0;
        String curValue = baseProrertys.getImageSize().getCurrentUiStringInSetting();
        for (int ii = 0; ii < length; ii++) {
            if (imageSizeUIString[ii].equals(curValue)) {
                curIdx = ii;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getImageSize().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, imageSizeUIString, curIdx, listener, true);
    }

    public void showUSBImageSizeOptionDialog(final Context context) {
        final CharSequence title = context.getResources().getString(R.string.stream_set_res_photo);
        final Handler handler = new Handler();
        final MyCamera myCamera = CameraManager.getInstance().getCurCamera();
        MyProgressDialog.showProgressDialog(context, R.string.action_processing);
        new Thread(new Runnable() {
            @Override
            public void run() {
                final List<ICatchImageSize> list = myCamera.getPanoramaPreviewPlayback().getSupportedImageSize();
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        MyProgressDialog.closeProgressDialog();
                    }
                });
                if (list == null) {
                    AppLog.e(TAG, "list == null");
                    return;
                }
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        int curIdx = 0;
                        String[] imageSizeUIString = new String[list.size()];
                        ICatchImageSize curImagesize = myCamera.getPanoramaPreviewPlayback().getCurImageSize();
                        if (curImagesize == null) {
                            curIdx = 0;
                        } else {
                            for (int ii = 0; ii < list.size(); ii++) {
                                ICatchImageSize size = list.get(ii);
                                if (size.getImageH() == curImagesize.getImageH() && size.getImageW() == curImagesize.getImageW()) {
                                    curIdx = ii;
                                    break;
                                }
                            }
                        }
                        for (int ii = 0; ii < list.size(); ii++) {
                            ICatchImageSize size = list.get(ii);
                            imageSizeUIString[ii] = size.getImageW() + "x" + size.getImageH();
                        }
                        if (imageSizeUIString == null) {
                            AppLog.e(TAG, "imageSizeUIString == null");
                            return;
                        }
                        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface arg0, int arg1) {
                                myCamera.getPanoramaPreviewPlayback().setImageSize(list.get(arg1));
                                arg0.dismiss();
                                if (onSettingCompleteListener != null) {
                                    onSettingCompleteListener.onOptionSettingComplete();
                                }
                            }
                        };
                        showOptionDialog(title, imageSizeUIString, curIdx, listener, true);
                    }
                });
            }
        }).start();
    }

    private void showDelayTimeOptionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.stream_set_timer);
        final String[] delayTimeUIString = baseProrertys.getCaptureDelay().getValueList();
        if (delayTimeUIString == null) {
            AppLog.e(TAG, "delayTimeUIString == null");
            return;
        }
        int length = delayTimeUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getCaptureDelay().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (delayTimeUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getCaptureDelay().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, delayTimeUIString, curIdx, listener, true);
    }

    private void showVideoSizeOptionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.stream_set_res_vid);
        final String[] videoSizeUIString = baseProrertys.getVideoSize().getValueArrayString();
        final List<String> videoSizeValueString;
        videoSizeValueString = baseProrertys.getVideoSize().getValueList();
        if (videoSizeUIString == null) {
            AppLog.e(TAG, "videoSizeUIString == null");
            return;
        }
        int length = videoSizeUIString.length;
        int curIdx = 0;
        String curVideoSize = baseProrertys.getVideoSize().getCurrentUiStringInSetting();
        for (int i = 0; i < length; i++) {
            if (videoSizeUIString[i].equals(curVideoSize)) {
                curIdx = i;
            }
        }
        String[] strResolution = videoSizeValueString.get(curIdx).split(" ");
        String curCamera = CameraUtils.getCurCamera();
        if (curCamera != null) {//fixme 这里可能有问题，应该是设置为选择的分辨率。而且每一次设置完之后没有刷新实际拍摄效果
            SPUtils.getInstance().put(curCamera, strResolution[0]);
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                final String value = videoSizeValueString.get(arg1);
                baseProrertys.getVideoSize().setValue(value);
                arg0.dismiss();
                onSettingCompleteListener.settingVideoSizeComplete();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, videoSizeUIString, curIdx, listener, false);
    }

    private void showFormatConfirmDialog(final Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setMessage(R.string.setting_format_desc);
        builder.setNegativeButton(R.string.setting_no, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
            }
        });
        builder.setPositiveButton(R.string.setting_yes, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                final Handler handler = new Handler();
                MyProgressDialog.showProgressDialog(context, R.string.setting_format);
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        final int messageId;
                        if (cameraAction.formatStorage()) {
                            messageId = R.string.text_operation_success;
                        } else {
                            messageId = R.string.text_operation_failed;
                        }
                        handler.post(new Runnable() {
                            @Override
                            public void run() {
                                MyProgressDialog.closeProgressDialog();
                                MyToast.show(context, messageId);
                            }
                        });
                    }
                }).start();
            }
        });
        builder.create().show();
    }

    private void showDateStampOptionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_datestamp);
        final String[] dateStampUIString = baseProrertys.getDateStamp().getValueList();
        if (dateStampUIString == null) {
            AppLog.e(TAG, "dateStampUIString == null");
            return;
        }
        int length = dateStampUIString.length;
        int curIdx = 0;
        String curValue = baseProrertys.getDateStamp().getCurrentUiStringInSetting();
        for (int i = 0; i < length; i++) {
            if (dateStampUIString[i].equals(curValue)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getDateStamp().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, dateStampUIString, curIdx, listener, true);
    }

    private void showElectricityFrequencyOptionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_power_supply);
        final String[] eleFreUIString = baseProrertys.getElectricityFrequency().getValueList();
        if (eleFreUIString == null) {
            AppLog.e(TAG, "eleFreUIString == null");
            return;
        }
        int length = eleFreUIString.length;
        int curIdx = 0;
        String curValue = baseProrertys.getElectricityFrequency().getCurrentUiStringInSetting();
        for (int i = 0; i < length; i++) {
            if (eleFreUIString[i].equals(curValue)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getElectricityFrequency().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, eleFreUIString, curIdx, listener, true);
    }

    private void showWhiteBalanceOptionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.title_awb);
        final String[] whiteBalanceUIString = baseProrertys.getWhiteBalance().getValueList();
        if (whiteBalanceUIString == null) {
            AppLog.e(TAG, "whiteBalanceUIString == null");
            return;
        }
        int length = whiteBalanceUIString.length;
        String curValue = baseProrertys.getWhiteBalance().getCurrentUiStringInSetting();
        int curIdx = 0;
        for (int i = 0; i < length; i++) {
            if (whiteBalanceUIString[i].equals(curValue)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getWhiteBalance().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, whiteBalanceUIString, curIdx, listener, true);
    }

    private void showBurstOptionDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.title_burst);
        final String[] burstUIString = baseProrertys.getBurst().getValueList();
        if (burstUIString == null) {
            AppLog.e(TAG, "burstUIString == null");
            return;
        }
        int length = burstUIString.length;
        String curValue = baseProrertys.getBurst().getCurrentUiStringInSetting();
        int curIdx = 0;
        for (int i = 0; i < length; i++) {
            if (burstUIString[i].equals(curValue)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getBurst().setValueByPosition(arg1);
                arg0.dismiss();
                Log.d(TAG, "refresh optionListAdapter!");
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, burstUIString, curIdx, listener, true);
    }

    private void showOptionDialog(CharSequence title, CharSequence[] items, int checkedItem,
                                  DialogInterface.OnClickListener listener,
                                  boolean cancelable) {
        AlertDialog.Builder optionDialog = new AlertDialog.Builder(GlobalInfo.getInstance().getCurrentApp(), R.style.MyAlertDialog);
        optionDialog.setTitle(title).setSingleChoiceItems(items, checkedItem, listener).create();
        optionDialog.show();
        optionDialog.setCancelable(cancelable);
    }

    private void sdCardIsNotReadyAlert(Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setMessage(R.string.dialog_no_sd);
        builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        AlertDialog dialog = builder.create();
        dialog.setCancelable(true);
        dialog.show();
    }

    private class SettingHandler extends Handler {
        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case AppMessage.OPEN_WIFI_HOTSPOT_FAILED:
                    AppLog.d(TAG, "receive OPEN_WIFI_HOTSPOT_FAILED");
                    MyProgressDialog.closeProgressDialog();
                    AlertDialog.Builder builder4 = new AlertDialog.Builder(context, R.style.MyAlertDialog);
                    builder4.setMessage("Wifi热点开启失败，可能系统版本不支持，需要手动开启");
                    builder4.setNegativeButton(R.string.ok, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            Intent intent = new Intent();
                            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                            ComponentName comp = new ComponentName("com.android.settings", "com.android.settings.Settings$TetherSettingsActivity");
                            intent.setComponent(comp);
                            context.startActivity(intent);
                        }
                    });
                    alertDialog = builder4.create();
                    alertDialog.setCancelable(false);
                    alertDialog.show();
                    break;
                case SDKEvent.EVENT_FW_UPDATE_COMPLETED:
                    AppLog.d(TAG, "receive EVENT_FW_UPDATE_COMPLETED");
                    MyProgressDialog.closeProgressDialog();
                    AlertDialog.Builder builder2 = new AlertDialog.Builder(context, R.style.MyAlertDialog);
                    builder2.setMessage(R.string.setting_updatefw_closeAppInfo);
                    builder2.setNegativeButton(R.string.ok, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            Log.d(TAG, "update FW completed!");
                        }
                    });
                    alertDialog = builder2.create();
                    alertDialog.setCancelable(false);
                    alertDialog.show();
                    break;
                case SDKEvent.EVENT_FW_UPDATE_POWEROFF:
                    AppLog.d(TAG, "receive EVENT_FW_UPDATE_POWEROFF");
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_COMPLETED);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_POWEROFF);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHECK);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHKSUMERR);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_NG);
                    AlertDialog.Builder builder3 = new AlertDialog.Builder(context, R.style.MyAlertDialog);
                    builder3.setMessage(R.string.setting_updatefw_closeAppInfo);
                    builder3.setNegativeButton(R.string.dialog_btn_exit, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            Log.d(TAG, "App quit");
                            ExitApp.getInstance().exit();
                        }
                    });
                    alertDialog = builder3.create();
                    alertDialog.setCancelable(false);
                    alertDialog.show();
                    break;
                case SDKEvent.EVENT_FW_UPDATE_CHECK:
                    AppLog.d(TAG, "receive EVENT_FW_UPDATE_CHECK");
                    break;
                case SDKEvent.EVENT_FW_UPDATE_CHKSUMERR:
                    AppLog.d(TAG, "receive EVENT_FW_UPDATE_CHKSUMERR");
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_COMPLETED);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_POWEROFF);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHECK);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHKSUMERR);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_NG);
                    AlertDialog.Builder builder5 = new AlertDialog.Builder(context, R.style.MyAlertDialog);
                    builder5.setMessage(R.string.setting_updatefw_chec_sum_failed);
                    builder5.setNegativeButton(R.string.dialog_btn_exit, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            Log.d(TAG, "App FW updatefw chech sume failed");
                            dialog.dismiss();
                        }
                    });
                    alertDialog = builder5.create();
                    alertDialog.setCancelable(false);
                    alertDialog.show();
                    break;
                case SDKEvent.EVENT_FW_UPDATE_NG:
                    AppLog.d(TAG, "receive EVENT_FW_UPDATE_NG");
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_COMPLETED);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_POWEROFF);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHECK);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_CHKSUMERR);
                    sdkEvent.delEventListener(ICatchCamEventID.ICH_CAM_EVENT_FW_UPDATE_NG);
                    AlertDialog.Builder builder6 = new AlertDialog.Builder(context, R.style.MyAlertDialog);
                    builder6.setMessage(R.string.setting_updatefw_failed);
                    builder6.setNegativeButton(R.string.dialog_btn_exit, new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            Log.d(TAG, "App FW updatefw failed");
                            dialog.dismiss();
                        }
                    });
                    alertDialog = builder6.create();
                    alertDialog.setCancelable(false);
                    alertDialog.show();
                    break;
                case AppMessage.AP_MODE_TO_STA_MODE_FAILURE:
                    MyProgressDialog.closeProgressDialog();
                    MyToast.show(context, R.string.dialog_failed);
                    break;
                case WifiAPUtil.MESSAGE_AP_STATE_ENABLED:
                    MyProgressDialog.closeProgressDialog();
                    String ssid = WifiAPUtil.getInstance(context).getValidApSsid();
                    String pw = WifiAPUtil.getInstance(context).getValidPassword();
                    String hint = "wifi hotspot is open:" + "\n"
                            + "SSID = " + ssid + "\n"
                            + "Password = " + pw + "\n"
                            + "App will exit, please wait for the camera to connect to the wifi hotspot and click search to enter preview.";
                    AppDialog.showDialogQuit(context, hint);
                    AppInfo.isNeedReconnect = false;
                    WifiAPUtil.getInstance(context.getApplicationContext()).unregitsterHandler();
                    break;
                case WifiAPUtil.MESSAGE_AP_STATE_FAILED:
                    break;
            }
        }
    }

    public void showSetDownloadSizeLimitDialog(final Context context) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(context, R.style.MyAlertDialog);
        View contentView = View.inflate(context, R.layout.content_download_size_dialog, null);
        final EditText resetTxv = (EditText) contentView.findViewById(R.id.download_size);
        String value = AppInfo.autoDownloadSizeLimit + "";
        resetTxv.setHint(value);
        builder.setTitle(R.string.setting_auto_download_size_limit);
        builder.setView(contentView);
        builder.setCancelable(false);
        builder.setPositiveButton(context.getResources().getString(R.string.action_save)
                , new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (resetTxv.getText().toString().equals("")) {
                        } else {
                            float sizeLimit = Float.parseFloat(resetTxv.getText().toString());
                            AppInfo.autoDownloadSizeLimit = sizeLimit;
                            onSettingCompleteListener.onOptionSettingComplete();
                        }
                    }
                });
        builder.setNegativeButton(context.getResources().getString(R.string.gallery_cancel)
                , new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                    }
                });
        builder.create().show();
    }

    public void connectWifi(String ssid, String password) {
        WifiCheck wifiCheck = new WifiCheck(activity);
        wifiCheck.openWifi();
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        WifiManager wifiManager = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
        while (wifiManager.getWifiState() == WifiManager.WIFI_STATE_ENABLING) {
            try {
                Thread.sleep(200);
            } catch (InterruptedException ie) {
            }
        }
        wifiCheck.connectWifi(ssid, password, WifiCheck.WIFICIPHER_WAP);
    }

    public void showExposureCompensationDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_title_exposure_compensation);
        final String[] exposureCompensationUIString = baseProrertys.getExposureCompensation().getValueList();
        if (exposureCompensationUIString == null) {
            AppLog.e(TAG, "exposureCompensationUIString == null");
            return;
        }
        int length = exposureCompensationUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getExposureCompensation().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (exposureCompensationUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getExposureCompensation().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, exposureCompensationUIString, curIdx, listener, true);
    }

    public void showVideoFileLengthDialog(final Context context) {
        CharSequence title = context.getResources().getString(R.string.setting_title_video_file_length);
        String curCamera = CameraUtils.getCurCamera();
        if (("A3S".equals(curCamera) || "V50 360".equals(curCamera))) {
            title = context.getResources().getString(R.string.setting_title_video_file_length_v2);
        } else {
            title = context.getResources().getString(R.string.setting_title_video_file_length);
        }

        final String[] videoFileLengthUIString = baseProrertys.getVideoFileLength().getValueList();
        if (videoFileLengthUIString == null) {
            AppLog.e(TAG, "videoFileLengthUIString == null");
            return;
        }
        int length = videoFileLengthUIString.length;
        int curIdx = 0;
        String temp = baseProrertys.getVideoFileLength().getCurrentUiStringInPreview();
        for (int i = 0; i < length; i++) {
            if (videoFileLengthUIString[i].equals(temp)) {
                curIdx = i;
            }
        }
        DialogInterface.OnClickListener listener = new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface arg0, int arg1) {
                baseProrertys.getVideoFileLength().setValueByPosition(arg1);
                arg0.dismiss();
                onSettingCompleteListener.onOptionSettingComplete();
            }
        };
        showOptionDialog(title, videoFileLengthUIString, curIdx, listener, true);
    }
}

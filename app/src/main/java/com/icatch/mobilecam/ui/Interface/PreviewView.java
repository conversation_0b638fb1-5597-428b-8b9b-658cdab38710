package com.icatch.mobilecam.ui.Interface;
import android.graphics.Bitmap;
import com.icatch.mobilecam.ui.adapter.SettingListAdapter;
public interface PreviewView {
    void hideActionBar();
    void setWbStatusVisibility(int visibility);
    void setBurstStatusVisibility(int visibility);
    void setWifiStatusVisibility(int visibility);
    void setWifiIcon(int drawableId);
    void setBatteryStatusVisibility(int visibility);
    void setBatteryIcon(int drawableId);
    void setTimeLapseModeVisibility(int visibility);
    void setTimeLapseModeIcon(int drawableId);
    void setSlowMotionVisibility(int visibility);
    void setCarModeVisibility(int visibility);
    void setRecordingTimeVisibility(int visibility);
    void setAutoDownloadVisibility(int visibility);
    void setCaptureBtnBackgroundResource(int id);
    void setRecordingTime(String lapseTime);
    void setDelayCaptureLayoutVisibility(int visibility);
    void setDelayCaptureTextTime(String delayCaptureTime);
    void setImageSizeLayoutVisibility(int visibility);
    void setRemainCaptureCount(String remainCaptureCount);
    void setVideoSizeLayoutVisibility(int visibility);
    void setRemainRecordingTimeText(String remainRecordingTime);
    void setBurstStatusIcon(int drawableId);
    void setWbStatusIcon(int drawableId);
    void setUpsideVisibility(int visibility);
    void setCaptureBtnEnAbility(boolean enAbility);
    void setVideoSizeInfo(String sizeInfo);
    void setImageSizeInfo(String sizeInfo);
    void showZoomView();
    void hideZoomView();
    void setMaxZoomRate(float maxZoomRate);
    float getZoomViewProgress();
    float getZoomViewMaxZoomRate();
    void updateZoomViewProgress(float currentZoomRatio);
    void setSettingMenuListAdapter(SettingListAdapter settingListAdapter);
    int getSetupMainMenuVisibility();
    void setSetupMainMenuVisibility(int visibility);
    void setAutoDownloadBitmap(Bitmap bitmap);
    void setActionBarTitle(int resId);
    void setBackBtnVisibility(boolean isVisible);
    void setSupportPreviewTxvVisibility(int visibility);
    void setPvModeBtnBackgroundResource(int drawableId);
    void setPvModeBtnVisibility(int visibility);
    void showPopupWindow(int curMode);
    void setTimeLapseRadioBtnVisibility(int visibility);
    void setCaptureRadioBtnVisibility(int visibility);
    void setVideoRadioBtnVisibility(int visibility);
    void setTimeLapseRadioChecked(boolean checked);
    void setCaptureRadioBtnChecked(boolean checked);
    void setVideoRadioBtnChecked(boolean checked);
    void dismissPopupWindow();
    void setMinZoomRate(float minZoomRate);
    int getSurfaceViewWidth();
    int getSurfaceViewHeight();
    void setPanoramaTypeBtnSrc(int srcId);
    void setPanoramaTypeBtnVisibility(int visibility);

    /**
     * 是否A6max 长录像模式，null 表示不是A6Max 或获取失败
     * @param isA6MaxLongRecordMode
     */
    default void setA6MaxRecordMode(Boolean isA6MaxLongRecordMode){}
}

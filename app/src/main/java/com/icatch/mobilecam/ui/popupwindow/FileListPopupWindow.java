package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.media.MediaPlayer;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ScreenUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.icatch.mobilecam.data.entity.Song;
import com.icatchtek.reliant.customer.type.ICatchFile;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;

import java.util.ArrayList;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class FileListPopupWindow extends BasePopupWindow {

    public RecyclerView rvFile;
    public TextView tvSure;
    public BaseQuickAdapter<ICatchFile, BaseViewHolder> mAdapter;
    private Context mContext;

    public FileListPopupWindow(Context context) {
        super(context);
        setWidth((int) (ScreenUtils.getScreenWidth() * 0.8));
        setHeight((int) (ScreenUtils.getScreenHeight() * 0.7));
        mContext = context;
        setPopupGravity(Gravity.CENTER);
        rvFile = findViewById(R.id.rv_file);
        tvSure = findViewById(R.id.tv_sure);
        findViewById(R.id.tv_cancel).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        initRv();
    }






    private void initRv() {
        mAdapter = new BaseQuickAdapter<ICatchFile, BaseViewHolder>(R.layout.recycler_file_list) {
            @Override
            protected void convert(BaseViewHolder holder, ICatchFile item) {
                TextView tvName = holder.getView(R.id.tv_name);
                tvName.setText(item.getFileName());
            }
        };
        rvFile.setLayoutManager(new LinearLayoutManager(mContext));
        rvFile.setAdapter(mAdapter);
    }




    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_file_list);
    }


}

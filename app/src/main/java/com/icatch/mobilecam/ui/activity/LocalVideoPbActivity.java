package com.icatch.mobilecam.ui.activity;
import android.annotation.SuppressLint;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.Presenter.LocalVideoPbPresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.ui.ExtendComponent.ProgressWheel;
import com.icatch.mobilecam.ui.Interface.LocalVideoPbView;
import com.ijoyer.mobilecam.R;
import java.util.Timer;
import java.util.TimerTask;
public class LocalVideoPbActivity extends AppCompatActivity implements LocalVideoPbView {
    private String TAG = LocalVideoPbActivity.class.getSimpleName();
    private TextView timeLapsed;
    private TextView timeDuration;
    private SeekBar seekBar;
    private ImageButton play;
    private ImageButton back;
    private RelativeLayout topBar;
    private LinearLayout bottomBar;
    private TextView localVideoNameTxv;
    private SurfaceView mSurfaceViewImage;
    private boolean isShowBar = true;
    private ProgressWheel progressWheel;
    private LocalVideoPbPresenter localVideoPbPresenter;
    private String videoPath;
    private ImageButton panoramaTypeBtn;
    private LinearLayout moreSettingLayout;
    private ImageButton moreBtn;
    private ImageButton cancelBtn;
    private Switch eisSwitch;
    private TextView codecInfoTxv;
    private void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            View decorView = window.getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            window.setNavigationBarColor(Color.TRANSPARENT);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        actionBar.hide();
    }
    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (hasFocus) {
            View decorView = getWindow().getDecorView();
            decorView.setSystemUiVisibility(
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                            | View.SYSTEM_UI_FLAG_FULLSCREEN
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
        }
    }
    private void hideControlDelay() {
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                hideControlView();
            }
        }, 3000);
    }
    private void hideControlView() {
        if (topBar != null) topBar.setVisibility(View.INVISIBLE);
        if (bottomBar != null) bottomBar.setVisibility(View.INVISIBLE);
    }
    private void showControlView() {
        if (topBar != null) topBar.setVisibility(View.VISIBLE);
        if (bottomBar != null) bottomBar.setVisibility(View.VISIBLE);
    }
    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_panorama_local_video_pb);
        codecInfoTxv = findViewById(R.id.codec_info_txv);
        timeLapsed = (TextView) findViewById(R.id.local_pb_time_lapsed);
        timeDuration = (TextView) findViewById(R.id.local_pb_time_duration);
        seekBar = (SeekBar) findViewById(R.id.local_pb_seekBar);
        play = (ImageButton) findViewById(R.id.local_pb_play_btn);
        back = (ImageButton) findViewById(R.id.local_pb_back);
        topBar = (RelativeLayout) findViewById(R.id.local_pb_top_layout);
        bottomBar = (LinearLayout) findViewById(R.id.local_pb_bottom_layout);
        mSurfaceViewImage = (SurfaceView) findViewById(R.id.m_surfaceView);
        localVideoNameTxv = (TextView) findViewById(R.id.local_pb_video_name);
        progressWheel = (ProgressWheel) findViewById(R.id.local_pb_spinner);
        panoramaTypeBtn = (ImageButton) findViewById(R.id.panorama_type_btn);
        panoramaTypeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                localVideoPbPresenter.setPanoramaType();
            }
        });
        Intent intent = getIntent();
        Bundle data = intent.getExtras();
        videoPath = data.getString("curFilePath");
        AppLog.i(TAG, "videoPath=" + videoPath);
        localVideoPbPresenter = new LocalVideoPbPresenter(this, videoPath);
        localVideoPbPresenter.setView(this);
        localVideoPbPresenter.initZoomView();
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        mSurfaceViewImage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppLog.d(TAG, "mSurfaceViewImage ClickListener");
                localVideoPbPresenter.showBar(topBar.getVisibility() == View.VISIBLE ? false : true);
            }
        });
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localVideoPbPresenter.back();
            }
        });
        play.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localVideoPbPresenter.play();
            }
        });
        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                localVideoPbPresenter.setTimeLapsedValue(progress);
            }
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                localVideoPbPresenter.startSeekTouch();
            }
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                localVideoPbPresenter.completedSeekToPosition();
            }
        });
        AppLog.d(TAG, "mSurfaceView = " + mSurfaceViewImage);
        mSurfaceViewImage.getHolder().addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) {
                localVideoPbPresenter.initSurface(mSurfaceViewImage.getHolder());
                localVideoPbPresenter.play();
            }
            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
                localVideoPbPresenter.setDrawingArea(width, height);
            }
            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
                AppLog.d(TAG, " 12233 surfaceDestroyed");
                localVideoPbPresenter.destroyVideo();
            }
        });
        mSurfaceViewImage.setOnTouchListener((v, event) -> {
            switch (event.getAction() & MotionEvent.ACTION_MASK) {
                case MotionEvent.ACTION_DOWN:
                    localVideoPbPresenter.onSurfaceViewTouchDown(event);
                    break;
                case MotionEvent.ACTION_POINTER_DOWN:
                    localVideoPbPresenter.onSurfaceViewPointerDown(event);
                    break;
                case MotionEvent.ACTION_MOVE:
                    localVideoPbPresenter.onSurfaceViewTouchMove(event);
                    break;
                case MotionEvent.ACTION_UP:
                    localVideoPbPresenter.onSurfaceViewTouchUp();
                    break;
                case MotionEvent.ACTION_POINTER_UP:
                    localVideoPbPresenter.onSurfaceViewTouchPointerUp();
                    break;
            }
            if (event.getAction() == MotionEvent.ACTION_UP) {
                if (topBar != null) {
                    topBar.setVisibility(topBar.getVisibility() == View.VISIBLE ? View.INVISIBLE : View.VISIBLE);
                }
                if (bottomBar != null) {
                    bottomBar.setVisibility(bottomBar.getVisibility() == View.VISIBLE ? View.INVISIBLE : View.VISIBLE);
                }
            }
            return true;
        });
        moreSettingLayout = (LinearLayout) findViewById(R.id.more_setting_layout);
        moreBtn = (ImageButton) findViewById(R.id.more_btn);
        cancelBtn = (ImageButton) findViewById(R.id.cancel_btn);
        eisSwitch = (Switch) findViewById(R.id.eis_switch);
        moreBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                localVideoPbPresenter.showMoreSettingLayout(true);
            }
        });
        cancelBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                localVideoPbPresenter.showMoreSettingLayout(false);
            }
        });
        eisSwitch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                boolean isChecked = eisSwitch.isChecked();
                localVideoPbPresenter.enableEIS(isChecked);
            }
        });
        hideControlDelay();
    }
    @Override
    protected void onResume() {
        super.onResume();
        localVideoPbPresenter.submitAppInfo();
        AppInfo.checkLocationDialog(this);
    }
    @Override
    protected void onStop() {
        super.onStop();
        localVideoPbPresenter.isAppBackground();
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        localVideoPbPresenter.removeActivity();
    }
    @Override
    public void setTopBarVisibility(int visibility) {
        topBar.setVisibility(visibility);
    }
    @Override
    public void setBottomBarVisibility(int visibility) {
        bottomBar.setVisibility(visibility);
    }
    @Override
    public void setTimeLapsedValue(String value) {
        timeLapsed.setText(value);
    }
    @Override
    public void setTimeDurationValue(String value) {
        timeDuration.setText(value);
    }
    @Override
    public void setSeekBarProgress(int value) {
        seekBar.setProgress(value);
    }
    @Override
    public void setSeekBarMaxValue(int value) {
        seekBar.setMax(value);
    }
    @Override
    public int getSeekBarProgress() {
        return seekBar.getProgress();
    }
    @Override
    public void setSeekBarSecondProgress(int value) {
        seekBar.setSecondaryProgress(value);
    }
    @Override
    public void setPlayBtnSrc(int resid) {
        play.setImageResource(resid);
    }
    @Override
    public void showLoadingCircle(boolean isShow) {
        AppLog.d(TAG, "showLoadingCircle isShow=" + isShow);
        if (isShow) {
            progressWheel.setVisibility(View.VISIBLE);
            progressWheel.setText("0%");
            progressWheel.startSpinning();
        } else {
            progressWheel.stopSpinning();
            progressWheel.setVisibility(View.GONE);
        }
    }
    @Override
    public void setLoadPercent(int value) {
        if (value >= 0) {
            String temp = value + "%";
            progressWheel.setText(temp);
        }
    }
    @Override
    public void setVideoNameTxv(String value) {
        localVideoNameTxv.setText(value);
    }
    @Override
    public void setZoomMinValue(float minValue) {
    }
    @Override
    public void setZoomMaxValue(float maxValue) {
    }
    @Override
    public void updateZoomRateTV(float zoomRate) {
    }
    @Override
    public void setProgress(float progress) {
    }
    @Override
    public void showZoomView(int visibility) {
    }
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_HOME:
                Log.d("AppStart", "home");
                break;
            case KeyEvent.KEYCODE_BACK:
                Log.d("AppStart", "back");
                localVideoPbPresenter.back();
                break;
            default:
                return super.onKeyDown(keyCode, event);
        }
        return true;
    }
    @Override
    public int getSurfaceViewWidth() {
        View parentView = (View) mSurfaceViewImage.getParent();
        int width = parentView.getWidth();
        return width;
    }
    @Override
    public int getSurfaceViewHeight() {
        View parentView = (View) mSurfaceViewImage.getParent();
        int heigth = parentView.getHeight();
        return heigth;
    }
    @Override
    public void setPanoramaTypeImageResource(int resId) {
        panoramaTypeBtn.setImageResource(resId);
    }
    @Override
    public void setPanoramaTypeBtnVisibility(int visibility) {
        panoramaTypeBtn.setVisibility(visibility);
    }
    @Override
    public void setMoreSettingLayoutVisibility(int visibility) {
        moreSettingLayout.setVisibility(visibility);
    }
    @Override
    public void setEisSwitchChecked(boolean checked) {
        eisSwitch.setChecked(checked);
    }
    @Override
    public void setCodecInfoTxv(String info) {
        if (codecInfoTxv.getVisibility() != View.VISIBLE) {
            codecInfoTxv.setVisibility(View.VISIBLE);
        }
        codecInfoTxv.setText(info);
    }
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                localVideoPbPresenter.redrawSurface();
            }
        }, 50);
        AppLog.d(TAG, "onConfigurationChanged newConfig Orientation=" + newConfig.orientation);
    }
}

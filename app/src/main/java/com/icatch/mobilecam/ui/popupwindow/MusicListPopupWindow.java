package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.media.MediaPlayer;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.icatch.mobilecam.data.entity.Song;
import com.icatch.mobilecam.utils.MusicUtil;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class MusicListPopupWindow extends BasePopupWindow {

    public RecyclerView rvVideo;
    public TextView tvCancel;
    public TextView tvSure;
    public BaseQuickAdapter<Song, BaseViewHolder> mAdapter;
    private Context mContext;
    private List<Song> mp3List;
    private List<Song> newData = new ArrayList<>();
    public IndicatorSeekBar sbSingle;
    public MediaPlayer mediaPlayer;
    public Song song;
    public boolean isTouch;

    public MusicListPopupWindow(Context context) {
        super(context);
        setWidth((int) (ScreenUtils.getScreenWidth() * 0.85));
        setHeight((int) (ScreenUtils.getScreenHeight() * 0.7));
        mContext = context;
        setPopupGravity(Gravity.CENTER);
        rvVideo = findViewById(R.id.rv_video);
        tvSure = findViewById(R.id.tv_sure);
        tvCancel = findViewById(R.id.tv_cancel);
        sbSingle = findViewById(R.id.sb_single);
        initRv();

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        sbSingle.setOnSeekChangeListener(new OnSeekChangeListener() {
            @Override
            public void onSeeking(SeekParams seekParams) {
                if (seekParams.fromUser) {
                    if (song != null) {
                        LogUtil.e(seekParams.progress + "");
                        song.playPosition = seekParams.progress;
                        LogUtil.e(song.playPosition * song.duration / 100 + "=======");
                        if (mediaPlayer != null) {
                            mediaPlayer.seekTo((int) ((song.playPosition * song.duration / 100) * 1000));
                            mediaPlayer.start();
                        }
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
                isTouch = true;
                if (mediaPlayer != null)
                    mediaPlayer.pause();
            }

            @Override
            public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                isTouch = false;

            }
        });
    }

    @Override
    public void showPopupWindow() {
        super.showPopupWindow();
        mp3List = MusicUtil.getmusic(mContext);
        newData = new ArrayList<>();
        tidyList();
        mAdapter.setNewInstance(newData);
    }


    private void tidyList() {
        for (int i = 0; i < mp3List.size(); i++) {
            Song item = mp3List.get(i);
            if (newData.size() == 0) {
                Song title = new Song();
                title.type = "1";
                title.time = item.getFileDate();
                newData.add(title);
                item.type = "2";
                newData.add(item);
            } else {
                boolean isExist = false;
                for (int j = 0; j < newData.size(); j++) {
                    if (newData.get(j).time == null) newData.get(j).time = "";
                    if (newData.get(j).time.equals(item.getFileDate())) {
                        isExist = true;
                        break;
                    }
                }

                for (int k = 0; k < newData.size(); k++) {
                    if (TextUtils.isEmpty(item.type)) {
                        if (isExist) {
                            item.type = "2";
                            newData.add(item);
                        } else {
                            Song title = new Song();
                            title.type = "1";
                            title.time = item.getFileDate();
                            newData.add(title);
                            item.type = "2";
                            newData.add(item);
                        }
                    }
                }
            }
        }
    }


    private MediaPlayer oldMediaPlayer;
    private Timer timer = new Timer();

    private void initRv() {
        mAdapter = new BaseQuickAdapter<Song, BaseViewHolder>(R.layout.recycler_music_list) {
            @Override
            protected void convert(BaseViewHolder holder, Song item) {
                TextView tvTime = holder.getView(R.id.tv_time);
                ConstraintLayout clItem = holder.getView(R.id.cl_item);
                LinearLayout llRoot = holder.getView(R.id.ll_root);
                ImageView ivCheck = holder.getView(R.id.iv_check);
                TextView tvName = holder.getView(R.id.tv_name);
                TextView tvFileTime = holder.getView(R.id.tv_file_time);
                ImageView ivPlay = holder.getView(R.id.iv_play);

                ViewGroup.LayoutParams params = llRoot.getLayoutParams();
                if ("1".equals(item.type)) {
                    params.height = SizeUtils.dp2px(30);
                    llRoot.setLayoutParams(params);

                    tvTime.setVisibility(View.VISIBLE);
                    clItem.setVisibility(View.GONE);
                    holder.setText(R.id.tv_time, item.time);
                } else {
                    params.height = SizeUtils.dp2px(80);
                    llRoot.setLayoutParams(params);

                    tvTime.setVisibility(View.GONE);
                    clItem.setVisibility(View.VISIBLE);

                    tvName.setText(item.song);

                    int hour = item.duration / 60 / 60;
                    int minute = item.duration / 60;
                    int second = item.duration % 60;

                    tvFileTime.setText(item.size / 1024 / 1024 + "M  " + String.format("%02d", hour) + ":" + String.format("%02d", minute) + ":" + String.format("%02d", second));

                    if (item.isCheck) {
                        ivCheck.setBackgroundResource(R.drawable.ic_check_yes);
                    } else {
                        ivCheck.setBackgroundResource(R.drawable.ic_check_no);
                    }

                    if (item.isPlay) {
                        ivPlay.setImageDrawable(mContext.getResources().getDrawable(R.drawable.ic_pause_white));
                    } else {
                        ivPlay.setImageDrawable(mContext.getResources().getDrawable(R.drawable.ic_play_arrow_white));
                    }


                    ivPlay.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            try {
                                mediaPlayer = new MediaPlayer();
                                mediaPlayer.setDataSource(item.file.getPath());//指定音频文件路径
                                mediaPlayer.setLooping(false);//设置为循环播放
                                mediaPlayer.prepare();//初始化播放器MediaPlayer
                                if (oldMediaPlayer != null) {
                                    oldMediaPlayer.pause();
                                    oldMediaPlayer.reset();
                                    oldMediaPlayer.release();
                                    oldMediaPlayer = null;
                                }

                                if (item.isPlay) {
                                    item.isPlay = false;
                                } else {
                                    for (Song bean : mAdapter.getData()) {
                                        bean.isPlay = false;
                                    }
                                    item.isPlay = true;
                                }

                                for (Song song : mAdapter.getData()) {
                                    song.isCheck = false;
                                }
                                item.isCheck = true;


                                if (item.isPlay) {
                                    song = item;
                                    mediaPlayer.start();
                                    TimerTask timerTask = new TimerTask() {
                                        @Override
                                        public void run() {
                                            if (mediaPlayer != null && !isTouch) {
                                                long position = mediaPlayer.getCurrentPosition();
                                                if (position > 1000) {
                                                    sbSingle.setProgress((int) ((position / 1000 * 100) / song.duration));
                                                } else {
                                                    sbSingle.setProgress(0);
                                                }
                                            }
                                        }
                                    };
                                    timer.schedule(timerTask, 500, 500);

                                } else {
                                    timer.cancel();
                                    mediaPlayer.pause();
                                }

                                oldMediaPlayer = mediaPlayer;
                                notifyDataSetChanged();

                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });


                    ivCheck.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            if (item.isCheck) {
                                item.isCheck = false;
                            } else {
                                for (Song bean : mAdapter.getData()) {
                                    bean.isCheck = false;
                                }
                                item.isCheck = true;
                            }

                            mAdapter.notifyDataSetChanged();
                        }
                    });
                }
            }
        };
        rvVideo.setLayoutManager(new LinearLayoutManager(mContext));
        rvVideo.setAdapter(mAdapter);
    }


    @Override
    public void onDismiss() {
        super.onDismiss();
        if (oldMediaPlayer != null) {
            oldMediaPlayer.pause();
            oldMediaPlayer.reset();
            oldMediaPlayer.release();
            oldMediaPlayer = null;
        }

        timer.cancel();
    }


    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_mp3_list);
    }


}

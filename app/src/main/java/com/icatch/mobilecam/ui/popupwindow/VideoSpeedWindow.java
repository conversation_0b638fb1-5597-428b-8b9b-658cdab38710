package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.media.MediaPlayer;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.icatch.mobilecam.data.entity.Song;
import com.icatch.mobilecam.utils.MusicUtil;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class VideoSpeedWindow extends BasePopupWindow {

    public TextView tvCancel;
    public TextView tvSure;
    private Context mContext;
    public IndicatorSeekBar sbSingle;

    public VideoSpeedWindow(Context context) {
        super(context);
        setWidth((int) (ScreenUtils.getScreenWidth() * 0.85));
        setHeight((int) (ScreenUtils.getScreenHeight() * 0.25));
        mContext = context;
        setPopupGravity(Gravity.CENTER);
        tvSure = findViewById(R.id.tv_sure);
        tvCancel = findViewById(R.id.tv_cancel);
        sbSingle = findViewById(R.id.sb_single);

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

        sbSingle.setOnSeekChangeListener(new OnSeekChangeListener() {
            @Override
            public void onSeeking(SeekParams seekParams) {
                if (seekParams.fromUser) {
                }
            }

            @Override
            public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(IndicatorSeekBar seekBar) {

            }
        });
    }

    @Override
    public void showPopupWindow() {
        super.showPopupWindow();
    }







    @Override
    public void onDismiss() {
        super.onDismiss();
    }


    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_video_speed);
    }


}

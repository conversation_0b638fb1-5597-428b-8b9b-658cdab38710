package com.icatch.mobilecam.ui.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.SystemInfo.SystemInfo;
import com.icatch.mobilecam.data.entity.MultiPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.ui.RemoteFileHelper;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.SPKey;
import com.icatch.mobilecam.utils.imageloader.ImageLoaderUtil;
import com.icatch.mobilecam.utils.imageloader.TutkUriUtil;
import com.ijoyer.mobilecam.R;
import com.tencent.mmkv.MMKV;

import org.jetbrains.annotations.NotNull;

import java.util.LinkedList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MultiPbRecyclerViewAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    public static final int TYPE_GRID = 1;
    public static final int TYPE_LIST = 2;
    public static final int TYPE_QUICK_LIST = 3;
    private final int TYPE_FOOTER = 4;
    private final int TYPE_LINE_INFO = 5;
    public final int LOADING = 1;
    public final int LOADING_COMPLETE = 2;
    public final int LOADING_END = 3;
    private int curViewType = TYPE_GRID;
    private int loadState = 2;
    private List<MultiPbItemInfo> list;
    private OperationMode operationMode = OperationMode.MODE_BROWSE;
    private FileType fileType;
    private int width;
    private final boolean isSingleCamera;
    private final boolean isA6Max;

    public MultiPbRecyclerViewAdapter(Context context, List<MultiPbItemInfo> list, FileType fileType) {
        this.list = list;
        this.fileType = fileType;
        this.width = SystemInfo.getMetrics(context).widthPixels;
        String cameraName = CameraUtils.getCurCamera();
        if (fileType == FileType.FILE_VIDEO && CameraUtils.isA6Max(cameraName)){
            isSingleCamera = false;
        }else{
            isSingleCamera = "A6".equals(cameraName) || CameraUtils.isA6SOrA8(cameraName);
        }
        isA6Max = CameraUtils.isA6Max(cameraName);
    }

    public static boolean isLineByPosition(int position) {
        if (AppInfo.remoteViewType == PhotoWallLayoutType.PREVIEW_TYPE_GRID) {
            return ((position + 1) % 5 == 0);
        }
        return false;
    }

    public static int repairPosition(int position) {
        if (AppInfo.remoteViewType == PhotoWallLayoutType.PREVIEW_TYPE_GRID) {
            if (position > 4) {
                if ((position + 1) % 5 != 0) {
                    int tmp = (position + 1) % 5;
                    if (1 == tmp || 2 == tmp || 3 == tmp || 4 == tmp) {
                        position -= position / 5;
                    }
                }
            }
        }
        return position;
    }

    @Override
    public int getItemViewType(int position) {
        if (isLineByPosition(position) && isSingleCamera) {
            return TYPE_LINE_INFO;
        } else if (position + 1 == getItemCount()) {
            return TYPE_FOOTER;
        } else {
            return curViewType;
        }
    }

    public void setCurViewType(int curViewType) {
        switch (curViewType) {
            case TYPE_GRID:
                AppInfo.remoteViewType = PhotoWallLayoutType.PREVIEW_TYPE_GRID;
                break;
            case TYPE_LIST:
                AppInfo.remoteViewType = PhotoWallLayoutType.PREVIEW_TYPE_LIST;
                break;
            case TYPE_QUICK_LIST:
                AppInfo.remoteViewType = PhotoWallLayoutType.PREVIEW_TYPE_QUICK_LIST;
                break;
        }
        this.curViewType = curViewType;
    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NotNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_GRID) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_pb_recyclerview_grid, parent, false);
            return new RecyclerViewGridHolder(view);
        } else if (viewType == TYPE_LIST || viewType == TYPE_QUICK_LIST) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_pb_recyclerview_list, parent, false);
            return new RecyclerViewListHolder(view, viewType == TYPE_LIST);
        } else if (viewType == TYPE_LINE_INFO) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_line_info, parent, false);
            return new LineInfoViewHolder(view);
        } else if (viewType == TYPE_FOOTER) {
            View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_refresh_footer, parent, false);
            return new FootViewHolder(view);
        }
        return null;
    }

    private static final String TAG = "MultiPbRecyclerViewAdapter";

    @Override
    public void onBindViewHolder(RecyclerView.@NotNull ViewHolder holder, int position) {
        if (holder instanceof RecyclerViewGridHolder) {
            if (isSingleCamera) {
                position = repairPosition(position);
            }
            MultiPbItemInfo info = list.get(position);//fixme 报错
            RecyclerViewGridHolder gridHolder = (RecyclerViewGridHolder) holder;
            gridHolder.videoSignImageView.setVisibility(fileType == FileType.FILE_PHOTO ? View.GONE : View.VISIBLE);
            gridHolder.mIsPanoramaSign.setVisibility(info.isPanorama() ? View.VISIBLE : View.GONE);
            gridHolder.mCheckImageView.setVisibility(operationMode == OperationMode.MODE_EDIT ? View.VISIBLE : View.GONE);
            if (operationMode == OperationMode.MODE_EDIT) {
                gridHolder.mCheckImageView.setImageResource(info.isItemChecked ? R.drawable.ic_check_box_blue : R.drawable.ic_check_box_blank_grey);
            }
            ViewGroup.LayoutParams photoLayoutParams = gridHolder.mImageView.getLayoutParams();
            photoLayoutParams.width = (width - 3 * 1) / 4;
            photoLayoutParams.height = (width - 3 * 1) / 4;
            gridHolder.mImageView.setLayoutParams(photoLayoutParams);
            String uri = TutkUriUtil.getTutkThumbnailUri(info.iCatchFile);
            ImageLoaderUtil.loadImageView(uri, gridHolder.mImageView, R.drawable.pictures_no);
            gridHolder.llUnDownload.setVisibility(View.GONE);
            if (isSingleCamera && position % 4 == 0 && !info.isDownload) {
                gridHolder.llUnDownload.setVisibility(View.VISIBLE);
            }
        } else if (holder instanceof LineInfoViewHolder) {
            int x = position - (position + 1) / 5;
            LineInfoViewHolder lineInfoViewHolder = (LineInfoViewHolder) holder;
            final Pattern pattern = Pattern.compile("_[0-3](.JPEG|.jpeg|.JPG|.jpg)$");
            Matcher matcher = pattern.matcher(list.get(x).getFileName());
            String tmp = matcher.replaceFirst("");
            String group = SPKey.getRemarkGroup(tmp);//组名，用来查询备注名
            String remark = MMKV.defaultMMKV().getString(SPKey.REMARK_NAME_PREFIX + group, null);
            if (TextUtils.isEmpty(remark)) {
                lineInfoViewHolder.remark.setVisibility(View.GONE);
            } else {
                lineInfoViewHolder.remark.setVisibility(View.VISIBLE);
                lineInfoViewHolder.remark.setText(remark);
            }
            lineInfoViewHolder.name.setText(tmp);
            lineInfoViewHolder.time.setText(list.get(x).getFileDateMMSS());
            lineInfoViewHolder.size.setText(list.get(x).getFileSize() + "/张");
            //判断是否已经下载了：RemoteFileHelper.getInstance().getLocalFileList
//            if (checkIsDownload(repairPosition(position - 1))) {//这里需要的position 实际上是上一个item 的
//                lineInfoViewHolder.isDownloaded.setVisibility(View.VISIBLE);
//            } else {
//                lineInfoViewHolder.isDownloaded.setVisibility(View.GONE);
//            }
        } else if (holder instanceof RecyclerViewListHolder) {
            RecyclerViewListHolder listHolder = (RecyclerViewListHolder) holder;
            listHolder.imageNameTextView.setText(list.get(position).getFileName());
            listHolder.imageSizeTextView.setText(list.get(position).getFileSize());
            listHolder.imageDateTextView.setText(list.get(position).getFileDateMMSS());
            listHolder.videoSignImageView.setVisibility(fileType == FileType.FILE_PHOTO ? View.GONE : View.VISIBLE);
            MultiPbItemInfo curItemInfo = list.get(position);
            if (list.size() >= 4 && curItemInfo.isPanoramaQuarter && isHasQuarter0123(position)) {
                int index = curItemInfo.panoramaQuarterIndex;
                index = 3 - index;
                switch (index) {
                    case 0:
                        listHolder.quarter0.setVisibility(View.VISIBLE);
                        listHolder.quarter1.setVisibility(View.GONE);
                        listHolder.quarter2.setVisibility(View.GONE);
                        listHolder.quarter3.setVisibility(View.GONE);
                        listHolder.quarterNone.setVisibility(View.GONE);
                        listHolder.quarter0.setBackgroundResource(R.drawable.ic_panorama_symbol_top);
                        break;
                    case 1:
                        listHolder.quarter0.setVisibility(View.GONE);
                        listHolder.quarter1.setVisibility(View.VISIBLE);
                        listHolder.quarter2.setVisibility(View.GONE);
                        listHolder.quarter3.setVisibility(View.GONE);
                        listHolder.quarterNone.setVisibility(View.GONE);
                        listHolder.quarter1.setBackgroundResource(R.drawable.ic_panorama_symbol_center);
                    case 2:
                        listHolder.quarter0.setVisibility(View.GONE);
                        listHolder.quarter1.setVisibility(View.GONE);
                        listHolder.quarter2.setVisibility(View.VISIBLE);
                        listHolder.quarter3.setVisibility(View.GONE);
                        listHolder.quarterNone.setVisibility(View.GONE);
                        listHolder.quarter2.setBackgroundResource(R.drawable.ic_panorama_symbol_center);
                        break;
                    case 3:
                        listHolder.quarter0.setVisibility(View.GONE);
                        listHolder.quarter1.setVisibility(View.GONE);
                        listHolder.quarter2.setVisibility(View.GONE);
                        listHolder.quarter3.setVisibility(View.VISIBLE);
                        listHolder.quarterNone.setVisibility(View.GONE);
                        listHolder.quarter3.setBackgroundResource(R.drawable.ic_panorama_symbol_bottom);
                        break;
                }
            } else {
                listHolder.quarter0.setVisibility(View.GONE);
                listHolder.quarter1.setVisibility(View.GONE);
                listHolder.quarter2.setVisibility(View.GONE);
                listHolder.quarter3.setVisibility(View.GONE);
                if (list.get(position).isPanorama()) {
                    listHolder.quarterNone.setVisibility(View.VISIBLE);
                    listHolder.quarterNone.setBackgroundResource(R.drawable.ic_panorama_green_500_48dp);
                } else {
                    listHolder.quarterNone.setVisibility(View.GONE);
                }
            }
            listHolder.mIsPanoramaSign.setVisibility(list.get(position).isPanorama() ? View.VISIBLE : View.GONE);
            listHolder.mCheckImageView.setVisibility(operationMode == OperationMode.MODE_EDIT ? View.VISIBLE : View.GONE);
            if (operationMode == OperationMode.MODE_EDIT) {
                listHolder.mCheckImageView.setImageResource(list.get(position).isItemChecked ? R.drawable.ic_check_box_blue : R.drawable.ic_check_box_blank_grey);
            }
            listHolder.thumbnailLayout.setVisibility(listHolder.showThumbnail ? View.VISIBLE : View.GONE);
            if (listHolder.showThumbnail) {
                MultiPbItemInfo itemInfo = list.get(position);
                if (itemInfo != null) {
                    String uri = TutkUriUtil.getTutkThumbnailUri(itemInfo.iCatchFile);
                    ImageLoaderUtil.loadImageView(uri, listHolder.imageView, R.drawable.pictures_no);
                }
            }
            listHolder.llUnDownload.setVisibility(View.GONE);
            if (isA6Max && !list.get(position).isDownload) {
                listHolder.llUnDownload.setVisibility(View.VISIBLE);
            }
        } else if (holder instanceof FootViewHolder) {
            FootViewHolder footViewHolder = (FootViewHolder) holder;
            switch (loadState) {
                case LOADING:
                    footViewHolder.pbLoading.setVisibility(View.VISIBLE);
                    footViewHolder.tvLoading.setVisibility(View.VISIBLE);
                    footViewHolder.llEnd.setVisibility(View.GONE);
                    break;
                case LOADING_COMPLETE:
                    footViewHolder.pbLoading.setVisibility(View.INVISIBLE);
                    footViewHolder.tvLoading.setVisibility(View.INVISIBLE);
                    footViewHolder.llEnd.setVisibility(View.GONE);
                    break;
                case LOADING_END:
                    footViewHolder.pbLoading.setVisibility(View.GONE);
                    footViewHolder.tvLoading.setVisibility(View.GONE);
                    footViewHolder.llEnd.setVisibility(View.VISIBLE);
                    break;
                default:
                    break;
            }
        }
    }

    //判断文件是否已经下载了
    private boolean checkIsDownload(int position) {
        List<MultiPbItemInfo> localFileList = RemoteFileHelper.getInstance().getLocalFileList(fileType);
        if (localFileList != null && !localFileList.isEmpty()) {
            if (position >= 0 && position < localFileList.size()) {
                return localFileList.get(position).isDownload;
            }
        }
        return false;
    }

    @Override
    public int getItemCount() {
        if (AppInfo.remoteViewType == PhotoWallLayoutType.PREVIEW_TYPE_GRID && isSingleCamera) {
            return list.size() + 1 + list.size() / 4;
        } else {
            return list.size() + 1;
        }
    }

    @Override
    public void onAttachedToRecyclerView(@NotNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        RecyclerView.LayoutManager manager = recyclerView.getLayoutManager();
        if (manager instanceof GridLayoutManager) {
            final GridLayoutManager gridManager = ((GridLayoutManager) manager);
            gridManager.setSpanSizeLookup(new GridLayoutManager.SpanSizeLookup() {
                @Override
                public int getSpanSize(int position) {
                    if (getItemViewType(position) == TYPE_LINE_INFO) {
                        return gridManager.getSpanCount();
                    } else {
                        return getItemViewType(position) == TYPE_FOOTER ? 4 : 1;
                    }
                }
            });
        }
    }

    private static class RecyclerViewGridHolder extends RecyclerView.ViewHolder {
        ImageView mImageView;
        ImageView mCheckImageView;
        ImageView videoSignImageView;
        ImageView mIsPanoramaSign;
        LinearLayout llUnDownload;

        RecyclerViewGridHolder(View itemView) {
            super(itemView);
            mImageView = (ImageView) itemView.findViewById(R.id.local_photo_wall_grid_item);
            mCheckImageView = (ImageView) itemView.findViewById(R.id.local_photo_wall_grid_edit);
            videoSignImageView = (ImageView) itemView.findViewById(R.id.video_sign);
            mIsPanoramaSign = (ImageView) itemView.findViewById(R.id.is_panorama);
            llUnDownload = (LinearLayout) itemView.findViewById(R.id.llUnDownload);
        }
    }

    private static class RecyclerViewListHolder extends RecyclerView.ViewHolder {
        ImageView imageView;
        TextView imageNameTextView;
        TextView imageSizeTextView;
        TextView imageDateTextView;
        ImageView mCheckImageView;
        ImageView videoSignImageView;
        ImageView mIsPanoramaSign;
        FrameLayout thumbnailLayout;
        LinearLayout llUnDownload;
        boolean showThumbnail;
        TextView quarter0;
        TextView quarter1;
        TextView quarter2;
        TextView quarter3;
        ImageView quarterNone;

        RecyclerViewListHolder(View itemView, boolean showThumbnail) {
            super(itemView);
            this.showThumbnail = showThumbnail;
            imageView = (ImageView) itemView.findViewById(R.id.local_photo_thumbnail_list);
            imageNameTextView = (TextView) itemView.findViewById(R.id.local_photo_name);
            imageSizeTextView = (TextView) itemView.findViewById(R.id.local_photo_size);
            imageDateTextView = (TextView) itemView.findViewById(R.id.local_photo_date);
            mCheckImageView = (ImageView) itemView.findViewById(R.id.local_photo_wall_list_edit);
            videoSignImageView = (ImageView) itemView.findViewById(R.id.video_sign);
            mIsPanoramaSign = (ImageView) itemView.findViewById(R.id.is_panorama);
            thumbnailLayout = (FrameLayout) itemView.findViewById(R.id.thumbnail_layout);
            quarter0 = itemView.findViewById(R.id.quarter_0);
            quarter1 = itemView.findViewById(R.id.quarter_1);
            quarter2 = itemView.findViewById(R.id.quarter_2);
            quarter3 = itemView.findViewById(R.id.quarter_3);
            quarterNone = itemView.findViewById(R.id.quarter_none);
            llUnDownload = itemView.findViewById(R.id.llUnDownload);
        }
    }

    private static class FootViewHolder extends RecyclerView.ViewHolder {
        ProgressBar pbLoading;
        TextView tvLoading;
        LinearLayout llEnd;

        FootViewHolder(View itemView) {
            super(itemView);
            pbLoading = (ProgressBar) itemView.findViewById(R.id.pb_loading);
            tvLoading = (TextView) itemView.findViewById(R.id.tv_loading);
            llEnd = (LinearLayout) itemView.findViewById(R.id.ll_end);
        }
    }

    private static class LineInfoViewHolder extends RecyclerView.ViewHolder {
        TextView remark;
        TextView name;
        TextView time;
        TextView size;
        TextView isDownloaded;

        public LineInfoViewHolder(@NonNull @org.jetbrains.annotations.NotNull View itemView) {
            super(itemView);
            name = itemView.findViewById(R.id.tv_line_info_name);
            remark = itemView.findViewById(R.id.tv_line_info_remark);
            time = itemView.findViewById(R.id.tv_line_info_time);
            size = itemView.findViewById(R.id.tv_line_info_size);
            isDownloaded = itemView.findViewById(R.id.tv_line_info_is_download);
        }
    }

    public void setLoadState(int loadState) {
        this.loadState = loadState;
        notifyDataSetChanged();
    }

    public void setOperationMode(OperationMode operationMode) {
        this.operationMode = operationMode;
    }

    public void changeCheckBoxState(int position) {
        if (position < list.size()) {
            list.get(position).isItemChecked = list.get(position).isItemChecked == true ? false : true;
            this.notifyDataSetChanged();
        }
    }

    public boolean isHasQuarter0123(int position) {
        boolean isHas = true;
        return isHas;
    }

    public int[] getPanoramaQuarterPos0123(int position) {
        if (list.get(position).isPanoramaQuarter) {
            switch (list.get(position).panoramaQuarterIndex) {
                case 3:
                    return new int[]{position + 0, position + 1, position + 2, position + 3};
                case 2:
                    return new int[]{position - 1, position + 0, position + 1, position + 2};
                case 1:
                    return new int[]{position - 2, position - 1, position + 0, position + 1};
                case 0:
                    return new int[]{position - 3, position - 2, position - 1, position + 0};
            }
        }
        return new int[0];
    }

    public List<MultiPbItemInfo> getCheckedItemsList() {
        LinkedList<MultiPbItemInfo> checkedList = new LinkedList<MultiPbItemInfo>();
        for (int ii = 0; ii < list.size(); ii++) {
            if (list.get(ii).isItemChecked) {
                checkedList.add(list.get(ii));
            }
        }
        return checkedList;
    }

    public void quitEditMode() {
        this.operationMode = OperationMode.MODE_BROWSE;
        for (int ii = 0; ii < list.size(); ii++) {
            list.get(ii).isItemChecked = false;
        }
        this.notifyDataSetChanged();
    }

    public void selectAllItems() {
        for (int ii = 0; ii < list.size(); ii++) {
            list.get(ii).isItemChecked = true;
        }
        this.notifyDataSetChanged();
    }

    public void cancelAllSelections() {
        for (int ii = 0; ii < list.size(); ii++) {
            list.get(ii).isItemChecked = false;
        }
        this.notifyDataSetChanged();
    }

    public int getSelectedCount() {
        int checkedNum = 0;
        for (int ii = 0; ii < list.size(); ii++) {
            if (list.get(ii).isItemChecked) {
                checkedNum++;
            }
        }
        return checkedNum;
    }
}

package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.SizeUtils;
import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.utils.VideoListUtil;
import com.ijoyer.mobilecam.R;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class VideoListPopupWindow extends BasePopupWindow {

    public RecyclerView rvVideo;
    public TextView tvCancel;
    public TextView tvSure;
    public boolean mIsRecord;
    public BaseQuickAdapter<LocalPbItemInfo, BaseViewHolder> mAdapter;
    private Context mContext;
    private List<LocalPbItemInfo> photoInfoList;
    private List<LocalPbItemInfo> newData = new ArrayList<>();


    public VideoListPopupWindow(Context context,boolean isRecord) {
        super(context);
        setWidth((int) (ScreenUtils.getScreenWidth() * 0.85));
        setHeight((int) (ScreenUtils.getScreenHeight() * 0.7));
        mContext = context;
        mIsRecord = isRecord;
        setPopupGravity(Gravity.CENTER);
        rvVideo = findViewById(R.id.rv_video);
        tvSure = findViewById(R.id.tv_sure);
        tvCancel = findViewById(R.id.tv_cancel);
        initRv();

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });


    }

    @Override
    public void showPopupWindow() {
        super.showPopupWindow();
        if (!mIsRecord) {
            photoInfoList = VideoListUtil.getPhotoInfoList(FileType.FILE_VR_VIDEO, mContext);
        } else {
            if (photoInfoList == null) photoInfoList = new ArrayList<>();
            photoInfoList.clear();
            for (int i = 0;i<VideoListUtil.getPhotoInfoList(FileType.FILE_VIDEO, mContext).size();i++){
                if (VideoListUtil.getPhotoInfoList(FileType.FILE_VIDEO, mContext).get(i).file.getName().contains("RECORD")) {
                    photoInfoList.add(VideoListUtil.getPhotoInfoList(FileType.FILE_VIDEO, mContext).get(i));
                }
            }
        }

        newData = new ArrayList<>();
        tidyList();
        mAdapter.setNewInstance(newData);
    }


    private void tidyList() {
        for (int i = 0; i < photoInfoList.size(); i++) {
            LocalPbItemInfo item = photoInfoList.get(i);
            if (newData.size() == 0) {
                LocalPbItemInfo title = new LocalPbItemInfo();
                title.type = "1";
                title.time = item.getFileDate();
                newData.add(title);
                item.type = "2";
                newData.add(item);
            } else {
                boolean isExist = false;
                for (int j = 0; j < newData.size(); j++) {
                    if (newData.get(j).time == null) newData.get(j).time = "";
                    if (newData.get(j).time.equals(item.getFileDate())) {
                        isExist = true;
                        break;
                    }
                }

                for (int k = 0; k < newData.size(); k++) {
                    if (TextUtils.isEmpty(item.type)) {
                        if (isExist) {
                            item.type = "2";
                            newData.add(item);
                        } else {
                            LocalPbItemInfo title = new LocalPbItemInfo();
                            title.type = "1";
                            title.time = item.getFileDate();
                            newData.add(title);
                            item.type = "2";
                            newData.add(item);
                        }
                    }
                }
            }
        }
    }

    private void initRv() {
        mAdapter = new BaseQuickAdapter<LocalPbItemInfo, BaseViewHolder>(R.layout.recycler_video_list) {
            @Override
            protected void convert(BaseViewHolder holder, LocalPbItemInfo item) {
                TextView tvTime = holder.getView(R.id.tv_time);
                ConstraintLayout clItem = holder.getView(R.id.cl_item);
                LinearLayout llRoot = holder.getView(R.id.ll_root);
                TextView tvCheck = holder.getView(R.id.tv_check);
                TextView tvName = holder.getView(R.id.tv_name);
                TextView tvFileTime = holder.getView(R.id.tv_file_time);



                ViewGroup.LayoutParams params = llRoot.getLayoutParams();
                if ("1".equals(item.type)) {
                    params.height = SizeUtils.dp2px(30);
                    llRoot.setLayoutParams(params);

                    tvTime.setVisibility(View.VISIBLE);
                    clItem.setVisibility(View.GONE);
                    holder.setText(R.id.tv_time, item.time);
                } else {
                    params.height = SizeUtils.dp2px(70);
                    llRoot.setLayoutParams(params);

                    tvTime.setVisibility(View.GONE);
                    clItem.setVisibility(View.VISIBLE);

                    File file = item.file;
                    if (file != null) {
                        Glide.with(mContext).load(file).placeholder(R.drawable.pictures_no).frame(2000000).into((ImageView) holder.getView(R.id.iv_bg));
                    }

                    tvName.setText(file.getName());
                    tvFileTime.setText(item.getFileSize() + " "+item.getFileDateMMSS());

                    if (item.isCheck) {
                        tvCheck.setText(item.selectPosition + "");
                        tvCheck.setTextColor(mContext.getResources().getColor(R.color.colorPrimary));
                        tvCheck.setBackgroundResource(R.drawable.shape_video_bg_theme);
                    } else {
                        tvCheck.setText("");
                        tvCheck.setTextColor(mContext.getResources().getColor(R.color.text_line));
                        tvCheck.setBackgroundResource(R.drawable.shape_video_bg_ccc);
                    }

                    tvCheck.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            item.isCheck = !item.isCheck;
                            if (item.isCheck) {
                                int thisSelect = 0;
                                for (int i = 0; i < mAdapter.getData().size(); i++) {
                                    if (mAdapter.getData().get(i).isCheck) {
                                        thisSelect++;
                                    }
                                }
                                item.selectPosition = thisSelect;
                            } else {
                                for (int i = 0; i < mAdapter.getData().size(); i++) {
                                    if (holder.getAdapterPosition() <= i) {
                                        if (mAdapter.getData().get(i).isCheck) {
                                            mAdapter.getData().get(i).selectPosition = mAdapter.getData().get(i).selectPosition - 1;
                                        }
                                    }
                                }
                            }
                            mAdapter.notifyDataSetChanged();
                        }
                    });
                }
            }
        };
        rvVideo.setLayoutManager(new LinearLayoutManager(mContext));
        rvVideo.setAdapter(mAdapter);
    }


    @Override
    public void onDismiss() {
        super.onDismiss();
    }


    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_video_list);
    }
}

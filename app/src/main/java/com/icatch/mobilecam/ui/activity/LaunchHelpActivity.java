package com.icatch.mobilecam.ui.activity;
import android.os.Build;
import android.os.Bundle;
import android.view.Gravity;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import com.ijoyer.mobilecam.R;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
public class LaunchHelpActivity extends AppCompatActivity {
    private Unbinder unbinder;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_launch_help);
        this.unbinder = ButterKnife.bind(this);
        this.initWindow();
        this.initToolBar();
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        this.unbinder.unbind();
    }
    private void initWindow() {
        Window window = getWindow();
        window.setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
    }
    private AppCompatTextView titleView;
    private void initToolBar() {
        if (null == toolbar) {
            throw new NullPointerException("toolbar can not be null");
        } else {
            this.setSupportActionBar(toolbar);
            int childCount = toolbar.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View view = toolbar.getChildAt(i);
                if (view instanceof AppCompatImageButton) {
                    view.addOnLayoutChangeListener(new View.OnLayoutChangeListener() {
                        @Override
                        public void onLayoutChange(View view, int i, int i1, int i2, int i3, int i4, int i5, int i6, int i7) {
                            if (null != titleView) {
                                titleView.setPadding(0, 0, view.getWidth(), 0);
                            }
                        }
                    });
                }
                if (view instanceof AppCompatTextView) {
                    titleView = (AppCompatTextView) view;
                    titleView.setGravity(Gravity.CENTER);
                    Toolbar.LayoutParams params = new Toolbar.LayoutParams(Toolbar.LayoutParams.WRAP_CONTENT, Toolbar.LayoutParams.WRAP_CONTENT);
                    params.gravity = Gravity.CENTER;   
                    titleView.setLayoutParams(params); 
                    break;
                }
            }
            ActionBar actionBar = this.getSupportActionBar();
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setElevation(0);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                toolbar.setElevation(0);
            }
        }
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return super.onCreateOptionsMenu(menu);
    }
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}

package com.icatch.mobilecam.ui.activity;
import android.app.Fragment;
import android.app.FragmentTransaction;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;
import com.google.android.material.tabs.TabLayout;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.MyCamera.MyCamera;
import com.icatch.mobilecam.Presenter.RemoteMultiPbPresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Fragment.DialogFragmentFromBottom;
import com.icatch.mobilecam.ui.Interface.MultiPbView;
import com.icatch.mobilecam.ui.RemoteFileHelper;
import com.icatch.mobilecam.utils.FileFilter;
import com.icatch.mobilecam.utils.FixedSpeedScroller;
import com.ijoyer.mobilecam.R;
import java.lang.reflect.Field;
public class RemoteMultiPbActivity extends AppCompatActivity implements MultiPbView {
    private String TAG = "RemoteMultiPbActivity";
    private ViewPager viewPager;
    public RemoteMultiPbPresenter remoteMultiPbPresenter;
    MenuItem menuPhotoWallType;
    ImageButton selectBtn;
    ImageButton deleteBtn;
    ImageButton downloadBtn;
    TextView selectedNumTxv;
    LinearLayout multiPbEditLayout;
    TabLayout tabLayout;
    MenuItem filterItem;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppLog.d(TAG, "onCreate ");
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_remote_multi_pb);
        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
        viewPager = (ViewPager) findViewById(R.id.vPager);
        selectBtn = (ImageButton) findViewById(R.id.action_select);
        deleteBtn = (ImageButton) findViewById(R.id.action_delete);
        downloadBtn = (ImageButton) findViewById(R.id.action_download);
        selectedNumTxv = (TextView) findViewById(R.id.info_selected_num);
        multiPbEditLayout = (LinearLayout) findViewById(R.id.edit_layout);
        tabLayout = (TabLayout) findViewById(R.id.tabs);
        remoteMultiPbPresenter = new RemoteMultiPbPresenter(this,multiPbEditLayout);
        remoteMultiPbPresenter.setView(this);
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                remoteMultiPbPresenter.updateViewpagerStatus(position);
            }
            @Override
            public void onPageScrollStateChanged(int state) {
            }
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }
        });
        selectBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                remoteMultiPbPresenter.selectOrCancel();
            }
        });
        deleteBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                remoteMultiPbPresenter.delete();
            }
        });
        downloadBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                remoteMultiPbPresenter.download();
            }
        });
        remoteMultiPbPresenter.loadViewPager();
        tabLayout.setupWithViewPager(viewPager);
        try {
            Field field = ViewPager.class.getDeclaredField("mScroller");
            field.setAccessible(true);
            FixedSpeedScroller scroller = new FixedSpeedScroller(viewPager.getContext(),
                    new AccelerateInterpolator());
            field.set(viewPager, scroller);
            scroller.setmDuration(280);
        } catch (Exception e) {
            AppLog.e(TAG, "FixedSpeedScroller Exception");
        }
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        if (camera != null) {
            camera.setLoadThumbnail(true);
        }
    }
    @Override
    protected void onStart() {
        super.onStart();
    }
    @Override
    protected void onStop() {
        super.onStop();
        AppLog.d(TAG, "onStop()");
        remoteMultiPbPresenter.isAppBackground();
    }
    @Override
    protected void onResume() {
        super.onResume();
        remoteMultiPbPresenter.submitAppInfo();
        remoteMultiPbPresenter.setSdCardEventListener();
        AppLog.d(TAG, "onResume()");
        AppInfo.checkLocationDialog(this);
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        MyCamera camera = CameraManager.getInstance().getCurCamera();
        if (camera != null) {
            camera.setLoadThumbnail(false);
        }
        remoteMultiPbPresenter.reset();
        remoteMultiPbPresenter.removeActivity();
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_multi_pb, menu);
        filterItem = menu.findItem(R.id.menu_multi_pb_filter);
        setFilterItemVisibiliy(RemoteFileHelper.getInstance().isSupportSegmentedLoading());
        return true;
    }
    @Override
    public void setFilterItemVisibiliy(boolean visibility) {
        if (filterItem != null) {
            filterItem.setVisible(visibility);
        }
    }
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.grid) {
            remoteMultiPbPresenter.changePreviewType(PhotoWallLayoutType.PREVIEW_TYPE_GRID);
        } else if (id == R.id.quick_liner) {
            remoteMultiPbPresenter.changePreviewType(PhotoWallLayoutType.PREVIEW_TYPE_QUICK_LIST);
        } else if (id == R.id.liner) {
            remoteMultiPbPresenter.changePreviewType(PhotoWallLayoutType.PREVIEW_TYPE_LIST);
        } else if (id == android.R.id.home) {
            remoteMultiPbPresenter.reBack();
            return true;
        } else if (id == R.id.menu_multi_pb_filter) {
            showFilterDialog();
        } else if (R.id.phone_album == id) {
            if (remoteMultiPbPresenter.curOperationMode == OperationMode.MODE_EDIT) {
                MyToast.show(this, "当前处于批量处理状态!");
                return true;
            }
            Intent intent = new Intent();
            intent.setClass(this, LocalMultiPbActivity.class);
            startActivity(intent);
        }
        return super.onOptionsItemSelected(item);
    }
    public void showFilterDialog() {
        FragmentTransaction ft = getFragmentManager().beginTransaction();
        Fragment prev = getFragmentManager().findFragmentByTag("dialog");
        if (prev != null) {
            ft.remove(prev);
        }
        ft.addToBackStack(null);
        DialogFragmentFromBottom newFragment = new DialogFragmentFromBottom();
        newFragment.setOutCancel(true);
        newFragment.setLastFilter(RemoteFileHelper.getInstance().getFileFilter());
        newFragment.setOnSureClickListener(new DialogFragmentFromBottom.OnSureClickListener() {
            @Override
            public void onSureClick(FileFilter fileFilter) {
                AppLog.d(TAG, "onSureClick fileFilter:" + fileFilter);
                if (fileFilter != null) {
                    AppLog.d(TAG, "onSureClick startTime:" + fileFilter.getStringTimeString());
                    AppLog.d(TAG, "onSureClick endTime:" + fileFilter.getEndTimeString());
                }
                remoteMultiPbPresenter.setFileFilter(fileFilter);
            }
        });
        newFragment.show(getSupportFragmentManager(), "dialog");
    }
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_HOME:
                Log.d("AppStart", "home");
                break;
            case KeyEvent.KEYCODE_BACK:
                Log.d("AppStart", "back");
                remoteMultiPbPresenter.reBack();
                break;
            default:
                return super.onKeyDown(keyCode, event);
        }
        return true;
    }
    @Override
    public void setViewPageAdapter(FragmentPagerAdapter adapter) {
        viewPager.setAdapter(adapter);
    }
    @Override
    public void setViewPageCurrentItem(int item) {
        AppLog.d(TAG, "setViewPageCurrentItem item=" + item);
        viewPager.setCurrentItem(item);
    }
    @Override
    public void setMenuPhotoWallTypeIcon(int iconRes) {
    }
    @Override
    public void setViewPagerScanScroll(boolean isCanScroll) {
    }
    @Override
    public void setSelectNumText(String text) {
        selectedNumTxv.setText(text);
    }
    @Override
    public void setSelectBtnVisibility(int visibility) {
        selectBtn.setVisibility(visibility);
    }
    @Override
    public void setSelectBtnIcon(int icon) {
        selectBtn.setImageResource(icon);
    }
    @Override
    public void setSelectNumTextVisibility(int visibility) {
        selectedNumTxv.setVisibility(visibility);
    }
    @Override
    public void setTabLayoutClickable(boolean value) {
        AppLog.d(TAG, "setTabLayoutClickable value=" + value);
        tabLayout.setClickable(value);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            tabLayout.setContextClickable(value);
        }
        tabLayout.setFocusable(value);
        tabLayout.setLongClickable(value);
        tabLayout.setEnabled(value);
    }
    @Override
    public void setEditLayoutVisibility(int visibility) {
        multiPbEditLayout.setVisibility(visibility);
    }
    @Override
    public int getViewPageIndex() {
        return viewPager.getCurrentItem();
    }
}

package com.icatch.mobilecam.ui.Interface;
import android.graphics.Bitmap;
import com.icatch.mobilecam.ui.adapter.CameraSlotAdapter;
public interface  LaunchView {
    void loadDefaultLocalPhotoThumbnail();
    void loadDefaultLocalVideoThumbnail();
    void setNoPhotoFilesFoundVisibility(int visibility);
    void setNoVideoFilesFoundVisibility(int visibility);
    void setPhotoClickable(boolean clickable);
    void setVideoClickable(boolean clickable);
    void setListViewAdapter(CameraSlotAdapter cameraSlotAdapter);
    void setBackBtnVisibility(boolean visibility);
    void setNavigationTitle(int resId);
    void setNavigationTitle(String res);
    void setLaunchLayoutVisibility(int visibility);
    void setLaunchSettingFrameVisibility(int visibility);
    void setLocalPhotoThumbnail(String filePath);
    void setLocalVideoThumbnail(String filePath);
    void fragmentPopStackOfAll();
}

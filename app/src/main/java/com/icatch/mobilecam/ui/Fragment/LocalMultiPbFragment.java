package com.icatch.mobilecam.ui.Fragment;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.icatch.mobilecam.Listener.OnStatusChangedListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.Presenter.LocalMultiPbFragmentPresenter;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.evenbus.EventBusNotifyEntity;
import com.icatch.mobilecam.ui.Interface.LocalMultiPbFragmentView;
import com.icatch.mobilecam.ui.adapter.LocalMultiPbWallGridAdapter;
import com.icatch.mobilecam.ui.adapter.LocalMultiPbWallListAdapter;
import com.ijoyer.mobilecam.R;
import com.tonicartos.widget.stickygridheaders.StickyGridHeadersGridView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;
public class LocalMultiPbFragment extends Fragment implements LocalMultiPbFragmentView {
    private static final String TAG = LocalMultiPbFragment.class.getSimpleName();
    StickyGridHeadersGridView multiPbPhotoGridView;
    ListView listView;
    TextView headerView;
    TextView noContentTxv;
    FrameLayout multiPbPhotoListLayout;
    LocalMultiPbFragmentPresenter localMultiPbFragmentPresenter;
    private OnStatusChangedListener modeChangedListener;
    private boolean isCreated = false;
    private boolean isVisible = false;
    private FileType fileType = FileType.FILE_PHOTO;
    public LocalMultiPbFragment() {
    }
    public static LocalMultiPbFragment newInstance(int param1) {
        LocalMultiPbFragment fragment = new LocalMultiPbFragment();
        Bundle args = new Bundle();
        args.putInt("FILE_TYPE", param1);
        fragment.setArguments(args);
        return fragment;
    }
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        int fileTypeInt = 0;
        if (getArguments() != null) {
            fileTypeInt = getArguments().getInt("FILE_TYPE");
        }
        if (fileTypeInt == 0) {
            this.fileType = FileType.FILE_VIDEO;
        } else {
            this.fileType = FileType.FILE_PHOTO;
        }
        AppLog.d(TAG, "onCreate fileType=" + fileTypeInt);
    }
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        AppLog.d(TAG, "MultiPbPhotoFragment onCreateView");
        View view = inflater.inflate(R.layout.fragment_multi_pb_photo, container, false);
        multiPbPhotoGridView = (StickyGridHeadersGridView) view.findViewById(R.id.multi_pb_photo_grid_view);
        listView = (ListView) view.findViewById(R.id.multi_pb_photo_list_view);
        noContentTxv = (TextView) view.findViewById(R.id.no_content_txv);
        multiPbPhotoListLayout = (FrameLayout) view.findViewById(R.id.multi_pb_photo_list_layout);
        localMultiPbFragmentPresenter = new LocalMultiPbFragmentPresenter(getActivity(), fileType);
        localMultiPbFragmentPresenter.setView(this);
        listView.setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                if (isVisible) {
                    localMultiPbFragmentPresenter.listViewEnterEditMode(position);
                }
                return true;
            }
        });
        multiPbPhotoGridView.setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                localMultiPbFragmentPresenter.gridViewEnterEditMode(position);
                return true;
            }
        });
        listView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                Log.d(TAG, "listView.setOnItemClickListener");
                localMultiPbFragmentPresenter.listViewSelectOrCancelOnce(position);
            }
        });
        multiPbPhotoGridView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                Log.d(TAG, "multiPbPhotoGridView.setOnItemClickListener");
                localMultiPbFragmentPresenter.gridViewSelectOrCancelOnce(position);
            }
        });
        isCreated = true;
        return view;
    }
    @Override
    public void onResume() {
        super.onResume();
        AppLog.d(TAG, "start onResume() isVisible=" + isVisible + " presenter=" + localMultiPbFragmentPresenter);
        if (isVisible) {
            localMultiPbFragmentPresenter.loadPhotoWall();
        }
        refreshPhotoWall(); 
        AppLog.d(TAG, "end onResume");
    }
    @Override
    public void onStop() {
        AppLog.d(TAG, "start onStop()");
        super.onStop();
    }
    @Override
    public void onDestroy() {
        AppLog.d(TAG, "start onDestroy()");
        EventBus.getDefault().unregister(this);
        super.onDestroy();
    }
    public void changePreviewType() {
        AppLog.d(TAG, "start changePreviewType presenter=" + localMultiPbFragmentPresenter);
        if (localMultiPbFragmentPresenter != null) {
            localMultiPbFragmentPresenter.changePreviewType();
        }
    }
    public void quitEditMode() {
        localMultiPbFragmentPresenter.quitEditMode();
    }
    @Override
    public void setListViewVisibility(int visibility) {
        if (multiPbPhotoListLayout.getVisibility() != visibility) {
            multiPbPhotoListLayout.setVisibility(visibility);
        }
    }
    @Override
    public void setGridViewVisibility(int visibility) {
        if (multiPbPhotoGridView.getVisibility() != visibility) {
            multiPbPhotoGridView.setVisibility(visibility);
        }
    }
    @Override
    public void setListViewAdapter(LocalMultiPbWallListAdapter photoWallListAdapter) {
        listView.setAdapter(photoWallListAdapter);
    }
    @Override
    public void setGridViewAdapter(LocalMultiPbWallGridAdapter photoWallGridAdapter) {
        multiPbPhotoGridView.setAdapter(photoWallGridAdapter);
    }
    @Override
    public void setListViewSelection(int position) {
        listView.setSelection(position);
    }
    @Override
    public void setGridViewSelection(int position) {
        multiPbPhotoGridView.setSelection(position);
    }
    @Override
    public void setListViewHeaderText(String headerText) {
        headerView.setText(headerText);
    }
    @Override
    public View listViewFindViewWithTag(int tag) {
        return listView.findViewWithTag(tag);
    }
    @Override
    public View gridViewFindViewWithTag(int tag) {
        return multiPbPhotoGridView.findViewWithTag(tag);
    }
    @Override
    public void updateGridViewBitmaps(String tag, Bitmap bitmap) {
        ImageView imageView = (ImageView) multiPbPhotoGridView.findViewWithTag(tag);
        if (imageView != null) {
            imageView.setImageBitmap(bitmap);
        }
    }
    @Override
    public void notifyChangeMultiPbMode(OperationMode operationMode) {
        if (modeChangedListener != null) {
            modeChangedListener.onChangeOperationMode(operationMode);
        }
    }
    @Override
    public void setPhotoSelectNumText(int selectNum) {
        if (modeChangedListener != null) {
            modeChangedListener.onSelectedItemsCountChanged(selectNum);
        }
    }
    @Override
    public void setNoContentTxvVisibility(int visibility) {
        int v = noContentTxv.getVisibility();
        if (v != visibility) {
            noContentTxv.setVisibility(visibility);
        }
    }
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.i(TAG, "MultiPbPhotoFragment onConfigurationChanged");
        localMultiPbFragmentPresenter.refreshPhotoWall();
    }
    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        AppLog.d(TAG, "setUserVisibleHint isVisibleToUser=" + isVisibleToUser);
        AppLog.d(TAG, "setUserVisibleHint isCreated=" + isCreated);
        isVisible = isVisibleToUser;
        if (isCreated == false) {
            return;
        }
        if (isVisibleToUser == false) {
            localMultiPbFragmentPresenter.quitEditMode();
        } else {
            localMultiPbFragmentPresenter.loadPhotoWall();
        }
    }
    public void refreshPhotoWall() {
        localMultiPbFragmentPresenter.refreshPhotoWall();
    }
    public void setOperationListener(OnStatusChangedListener modeChangedListener) {
        this.modeChangedListener = modeChangedListener;
    }
    public void selectOrCancelAll(boolean isSelectAll) {
        localMultiPbFragmentPresenter.selectOrCancelAll(isSelectAll);
    }
    public List<LocalPbItemInfo> getSelectedList() {
        return localMultiPbFragmentPresenter.getSelectedList();
    }
    public void clealAsytaskList() {
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void notifyAdapter(EventBusNotifyEntity entity) {
        try {
            if (entity.tag.equals(EventBusNotifyEntity.EVENT_BUS_NOTIFY)) {
                refreshPhotoWall();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

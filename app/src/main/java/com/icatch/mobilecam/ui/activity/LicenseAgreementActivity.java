package com.icatch.mobilecam.ui.activity;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.appcompat.widget.Toolbar;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.ijoyer.camera.base.BaseSwipeBackActivity;
import com.ijoyer.camera.widget.SwipeBackLayout;
import com.ijoyer.mobilecam.R;
public class LicenseAgreementActivity extends BaseSwipeBackActivity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_license);
        Toolbar toolbar = (Toolbar) findViewById(R.id.toolbar);
        TextView tvMessage = (TextView) findViewById(R.id.tv_message);

        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        setDragEdge(SwipeBackLayout.DragEdge.LEFT);


        String type = getIntent().getStringExtra("type");
        if (!TextUtils.isEmpty(type)){
            if ("Agreement".equals(type)){
                setTitle(getString(R.string.title_privacy_policy1));
                tvMessage.setText(getString(R.string.app_license1));
            } else {
                setTitle(getString(R.string.title_privacy_policy2));
                tvMessage.setText(getString(R.string.app_license2));
            }
        }
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return super.onCreateOptionsMenu(menu);
    }
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    @Override
    protected void onResume() {
        super.onResume();
        AppInfo.checkLocationDialog(this);
    }
}

package com.icatch.mobilecam.ui.appdialog;

import android.content.Context;
import android.widget.Toast;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.ThreadUtils;
import com.icatch.mobilecam.Application.PanoramaApp;

public class AppToast {
    private static Toast toast;

    public static void show(Context context, CharSequence text) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (toast == null) {
                    toast = Toast.makeText(context, "默认的Toast", Toast.LENGTH_SHORT);
                }
                toast.setText(text);
                toast.setDuration(Toast.LENGTH_SHORT);
                toast.show();
            }
        });

    }

    public static void show(Context context, CharSequence text, int duration) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (toast == null) {
                    toast = Toast.makeText(context, "默认的Toast", Toast.LENGTH_SHORT);
                }
                toast.setText(text);
                toast.setDuration(duration);
                toast.show();
            }
        });
    }

    public static void show(Context context, int stringId, int duration) {
        ThreadUtils.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (toast == null) {
                    toast = Toast.makeText(context, "默认的Toast", Toast.LENGTH_SHORT);
                }
                toast.setText(stringId);
                toast.setDuration(duration);
                toast.show();
            }
        });
    }
}

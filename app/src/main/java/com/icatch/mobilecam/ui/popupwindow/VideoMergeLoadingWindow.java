package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import com.blankj.utilcode.util.ScreenUtils;
import com.daimajia.numberprogressbar.NumberProgressBar;
import com.ijoyer.mobilecam.R;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class VideoMergeLoadingWindow extends BasePopupWindow {

    public NumberProgressBar numberProgressBar;

    private Context mContext;


    public VideoMergeLoadingWindow(Context context) {
        super(context);
        setOutSideDismiss(false);
        setWidth((int) (ScreenUtils.getScreenWidth() * 0.8));
        setHeight((int) (ScreenUtils.getScreenHeight() * 0.1));
        mContext = context;
        setPopupGravity(Gravity.CENTER);
        numberProgressBar = findViewById(R.id.number_progress_bar);
        numberProgressBar.setMax(100);
    }


    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_video_cut_loading
        );
    }



}

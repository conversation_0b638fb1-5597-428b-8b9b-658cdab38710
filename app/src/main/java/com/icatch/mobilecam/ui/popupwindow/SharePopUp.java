package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.ScreenUtils;
import com.ijoyer.mobilecam.R;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class SharePopUp extends BasePopupWindow {


    private Context mContext;
    public TextView tvCancel;
    public LinearLayout llShareForQQ;
    public LinearLayout llShareForWX;
    public LinearLayout llShareForPyq;
    public LinearLayout llShareForWeibo;
    public LinearLayout llShareForQqZone;
    public LinearLayout llShareLink;


    public SharePopUp(Context context) {
        super(context);
        setWidth((int) (ScreenUtils.getScreenWidth()));
        mContext = context;
        setPopupGravity(Gravity.BOTTOM);
        tvCancel = findViewById(R.id.tv_cancel);
        llShareForQQ = findViewById(R.id.ll_share_for_qq);
        llShareForWX = findViewById(R.id.ll_share_for_wx);
        llShareForPyq = findViewById(R.id.ll_share_for_pyq);
        llShareForWeibo = findViewById(R.id.ll_share_for_wb);
        llShareForQqZone = findViewById(R.id.ll_share_for_qq_zone);
        llShareLink = findViewById(R.id.ll_share_link);

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }


    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_share
        );
    }



}

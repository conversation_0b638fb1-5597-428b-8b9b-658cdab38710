package com.icatch.mobilecam.ui.Fragment;

import android.content.Intent;
import android.content.res.Configuration;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.icatch.mobilecam.Listener.EndlessRecyclerOnScrollListener;
import com.icatch.mobilecam.Listener.OnRecyclerItemClickListener;
import com.icatch.mobilecam.Listener.OnStatusChangedListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.Presenter.MultiPbFragmentPresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.entity.MultiPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.data.type.PhotoWallLayoutType;
import com.icatch.mobilecam.evenbus.EventBusNotifyEntity;
import com.icatch.mobilecam.ui.Interface.MultiPbFragmentView;
import com.icatch.mobilecam.ui.RemoteFileHelper;
import com.icatch.mobilecam.ui.activity.RemoteMultiPbActivity;
import com.icatch.mobilecam.ui.adapter.MultiPbRecyclerViewAdapter;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.ClickUtils;
import com.ijoyer.camera.activity.PanoramaPlayerActivity;
import com.ijoyer.camera.activity.RecordPlayActivity;
import com.ijoyer.mobilecam.R;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.opencv.android.BaseLoaderCallback;
import org.opencv.android.LoaderCallbackInterface;
import org.opencv.android.OpenCVLoader;

import java.util.List;

public class RemoteMultiPbFragment extends BaseMultiPbFragment implements MultiPbFragmentView {
    private static final String TAG = "RemoteMultiPbFragment";
    RecyclerView recyclerView;
    MultiPbFragmentPresenter presenter;
    private OnStatusChangedListener modeChangedListener;
    private boolean isCreated = false;
    private boolean isVisible = false;
    private TextView noContentTxv;
    public FileType fileType;
    private boolean hasDeleted = false;
    private String cameraName;

    public RemoteMultiPbFragment() {
    }

    public static RemoteMultiPbFragment newInstance(int param1) {
        RemoteMultiPbFragment fragment = new RemoteMultiPbFragment();
        Bundle args = new Bundle();
        args.putInt("FILE_TYPE", param1);
        fragment.setArguments(args);
        return fragment;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void notifyAdapter(EventBusNotifyEntity entity) {
        try {
            if (entity.tag.equals(EventBusNotifyEntity.EVENT_BUS_NOTIFY)) {
                presenter.setAdapter();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        int fileTypeInt = 0;
        if (getArguments() != null) {
            fileTypeInt = getArguments().getInt("FILE_TYPE");
        }
        this.fileType = FileType.values()[fileTypeInt];
        AppLog.d(TAG, "onCreate fileType=" + fileType);
        if (CameraManager.getInstance().getCurCamera() == null ||
                CameraManager.getInstance().getCurCamera().getCameraFixedInfo() == null){
            if (getActivity() != null){
                getActivity().finish();
            }
            return;
        }
        cameraName = CameraUtils.getCurCamera();
    }

    private boolean isSingleCamera() {
        return "A6".equals(cameraName) || CameraUtils.isA6SOrA8(cameraName);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        if (cameraName == null){
            return null;
        }
        AppLog.d(TAG, "onCreateView fileType=" + fileType);
        View view = inflater.inflate(R.layout.fragment_multi_pb, container, false);
        recyclerView = (RecyclerView) view.findViewById(R.id.recycler_view);
        noContentTxv = (TextView) view.findViewById(R.id.no_content_txv);
        presenter = new MultiPbFragmentPresenter(getActivity(), fileType);
        presenter.setView(this);
        presenter.setFragment(this);
        recyclerView.addOnItemTouchListener(new OnRecyclerItemClickListener(recyclerView) {
            @Override
            public void onItemClick(int position, View view, RecyclerView.ViewHolder viewHolder) {
                if (!ClickUtils.isFastDoubleClick(R.id.recycler_view)) {
                    if (AppInfo.remoteViewType == PhotoWallLayoutType.PREVIEW_TYPE_GRID && isSingleCamera()) {
                        if (MultiPbRecyclerViewAdapter.isLineByPosition(position)) {
                            return;
                        }
                        position = MultiPbRecyclerViewAdapter.repairPosition(position);
                        if (position == presenter.getItemInfoSize()) {
                            return;
                        }
                        if (presenter.isCurBrowseMode()) {
                            List<MultiPbItemInfo> remotePhotoInfoList =   RemoteFileHelper.getInstance().getLocalFileList(fileType);
                            if (remotePhotoInfoList.get(position).isDownload) {
                                String filePath = null;
                                if (position >= 0) {
                                    filePath = remotePhotoInfoList.get(position).fileLocal.getAbsolutePath();
                                }
                                if (CameraUtils.handleA6MaxVideo(getActivity(),filePath)){
                                    LogUtils.d(TAG,"A6Max 视频");
                                    return;
                                }
                                Intent intent = new Intent();
                                if (fileType == FileType.FILE_VIDEO) {
                                    intent.putExtra("playTag", 2);
                                } else if (fileType == FileType.FILE_PHOTO){
                                    intent.putExtra("playTag", 1);
                                }
                                intent.putExtra("path", remotePhotoInfoList.get(position).fileLocal.getAbsolutePath());
                                intent.putExtra("curFilePosition", position);
                                intent.putExtra("isShowPhotoEdit", true);
                                intent.setClass(getActivity(), PanoramaPlayerActivity.class);
                                getActivity().startActivity(intent);
                                return;
                            }

                            presenter.enterEditMode(position);
                            ((RemoteMultiPbActivity) requireActivity()).remoteMultiPbPresenter.download();
                            presenter.quitEditMode();
                            return;
                        }
                    }
                    if (AppInfo.remoteViewType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
                        if (position == presenter.getItemInfoSize()) {
                            return;
                        }
                        if (presenter.isCurBrowseMode()) {
                            //下载过了
                            List<MultiPbItemInfo> remotePhotoInfoList =   RemoteFileHelper.getInstance().getLocalFileList(fileType);;
                            if (remotePhotoInfoList.get(position).isDownload) {
                                if (fileType == FileType.FILE_VIDEO) {
                                    String videoPath = FileUtils.isFileExists(remotePhotoInfoList.get(position).fileLocal) ?
                                            remotePhotoInfoList.get(position).fileLocal.getPath() : null;
                                    if (videoPath != null && (CameraUtils.handleA6MaxVideo(getActivity(), videoPath)
                                            || videoPath.contains("A6Max"))) {
                                        Intent intent = new Intent();
                                        intent.putExtra("path", videoPath);
                                        intent.setClass(getActivity(), RecordPlayActivity.class);
                                        return;
                                    }
                                }
                                Intent intent = new Intent();
                                if (fileType == FileType.FILE_VIDEO) {
                                    intent.putExtra("playTag", 2);
                                } else if (fileType == FileType.FILE_PHOTO){
                                    intent.putExtra("playTag", 1);
                                }
                                intent.putExtra("path", remotePhotoInfoList.get(position).fileLocal.getAbsolutePath());
                                intent.putExtra("curFilePosition", position);
                                intent.putExtra("isShowPhotoEdit", true);
                                intent.setClass(getActivity(), PanoramaPlayerActivity.class);
                                getActivity().startActivity(intent);
                                return;
                            }


                            // TODO: 2022/6/10
                            presenter.enterEditMode(position);
                            ((RemoteMultiPbActivity) requireActivity()).remoteMultiPbPresenter.download();
                            presenter.quitEditMode();
                            return;
                        }
                    }
                    presenter.itemClick(position);
                }
            }

            @Override
            public void onItemLongClick(int position, View view, RecyclerView.ViewHolder viewHolder) {
                if (AppInfo.remoteViewType == PhotoWallLayoutType.PREVIEW_TYPE_GRID && isSingleCamera()) {
                    if (MultiPbRecyclerViewAdapter.isLineByPosition(position) && position + 1 != presenter.getItemInfoSize()) {
                        return;
                    }
                    position = MultiPbRecyclerViewAdapter.repairPosition(position);
                    if (position == presenter.getItemInfoSize()) {
                        return;
                    }
                }
//                if (presenter.fileType == FileType.FILE_VIDEO && CameraManager.getInstance().getCurCamera() != null && "A3S".equals(CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraName())){
//                    return;
//                }

//                if (presenter.fileType == FileType.FILE_PHOTO && CameraManager.getInstance().getCurCamera() != null && "A6S".equals(CameraManager.getInstance().getCurCamera().getCameraFixedInfo().getCameraName())){
//                    return;
//                }

                if (AppInfo.remoteViewType == PhotoWallLayoutType.PREVIEW_TYPE_LIST) {
                    if (position == presenter.getItemInfoSize()) {
                        return;
                    }
                }
                if (isVisible) {
                    presenter.enterEditMode(position);
                }
            }
        });
        isCreated = true;
        recyclerView.addOnScrollListener(new EndlessRecyclerOnScrollListener() {
            @Override
            public void onLoadMore() {
                presenter.loadMoreFile();
            }
        });
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        AppLog.d(TAG, "start onResume() isVisible=" + isVisible + " fileType=" + fileType);
        if (isVisible) {
            if (hasDeleted && RemoteFileHelper.getInstance().isSupportSegmentedLoading()) {
                presenter.resetCurIndex();
                presenter.resetAdapter();
                RemoteFileHelper.getInstance().clearFileList(fileType);
            }
            if (presenter != null){
                presenter.loadPhotoWall();
            }
        }
        hasDeleted = false;

        if (!OpenCVLoader.initDebug()) {
            OpenCVLoader.initAsync(OpenCVLoader.OPENCV_VERSION, getContext(), mLoaderCallback);
        } else {
            mLoaderCallback.onManagerConnected(LoaderCallbackInterface.SUCCESS);
        }

        AppLog.d(TAG, "end onResume");
    }

    private BaseLoaderCallback mLoaderCallback = new BaseLoaderCallback(getContext()) {
        @Override
        public void onManagerConnected(int status) {
            switch (status) {
                case LoaderCallbackInterface.SUCCESS: {
                    Log.e("llf", "OpenCV loaded successfully");
                }
                break;
                default: {
                    super.onManagerConnected(status);
                }
                break;
            }
        }
    };
    @Override
    public void onStop() {
        AppLog.d(TAG, "start onStop()");
        super.onStop();
    }

    @Override
    public void onDestroy() {
        AppLog.d(TAG, "start onDestroy()");
        EventBus.getDefault().unregister(this);
        super.onDestroy();
        if (presenter != null){
            presenter.emptyFileList();
        }
    }

    public void changePreviewType(PhotoWallLayoutType layoutType) {
        AppLog.d(TAG, "start changePreviewType presenter=" + presenter);
        if (presenter != null) {
            presenter.changePreviewType(layoutType);
        }
    }

    public void quitEditMode() {
        presenter.quitEditMode();
    }

    @Override
    public void setRecyclerViewVisibility(int visibility) {
        recyclerView.setVisibility(visibility);
    }

    @Override
    public void setRecyclerViewAdapter(MultiPbRecyclerViewAdapter recyclerViewAdapter) {
        recyclerView.setAdapter(recyclerViewAdapter);
    }

    @Override
    public void setRecyclerViewLayoutManager(RecyclerView.LayoutManager layout) {
        recyclerView.setLayoutManager(layout);
    }

    @Override
    public void notifyChangeMultiPbMode(OperationMode operationMode) {
        if (modeChangedListener != null) {
            modeChangedListener.onChangeOperationMode(operationMode);
        }
    }

    @Override
    public void setPhotoSelectNumText(int selectNum) {
        if (modeChangedListener != null) {
            modeChangedListener.onSelectedItemsCountChanged(selectNum);
        }
    }

    @Override
    public void setNoContentTxvVisibility(int visibility) {
        int v = noContentTxv.getVisibility();
        if (v != visibility) {
            noContentTxv.setVisibility(visibility);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.i(TAG, "RemoteMultiPbPhotoFragment onConfigurationChanged");
        presenter.refreshPhotoWall();
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        AppLog.d(TAG, "setUserVisibleHint isVisibleToUser=" + isVisibleToUser + " fileType=" + fileType);
        AppLog.d(TAG, "setUserVisibleHint isCreated=" + isCreated);
        isVisible = isVisibleToUser;
        if (isCreated == false) {
            return;
        }
        if (isVisibleToUser == false) {
            presenter.quitEditMode();
        } else {
            presenter.loadPhotoWall();
        }
    }

    public void setOperationListener(OnStatusChangedListener modeChangedListener) {
        this.modeChangedListener = modeChangedListener;
    }

    public void selectOrCancelAll(boolean isSelectAll) {
        presenter.selectOrCancelAll(isSelectAll);
    }

    public List<MultiPbItemInfo> getSelectedList() {
        return presenter.getSelectedList();
    }

    public FileType getFileType() {
        return fileType;
    }

    public void deleteFile() {
        presenter.deleteFile();
    }

    public void loadPhotoWall() {
        if (isVisible) {
            presenter.loadPhotoWall();
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        AppLog.d(TAG, "onActivityResult requestCode=" + requestCode);
        AppLog.d(TAG, "onActivityResult data=" + data);
        AppLog.d(TAG, "onActivityResult curfileType=" + fileType.ordinal());
        if (data != null) {
            hasDeleted = data.getBooleanExtra("hasDeleted", false);
            int fileTypeInt = data.getIntExtra("fileType", -1);
            AppLog.d(TAG, "onActivityResult hasDeleted=" + hasDeleted + " fileType=" + fileTypeInt);
        }
    }
}

package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import com.blankj.utilcode.util.ScreenUtils;
import com.ijoyer.mobilecam.R;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class CutSuccessWindow extends BasePopupWindow {


    private Context mContext;
    public TextView tvSure;
    public TextView tvCancel;

    public CutSuccessWindow(Context context) {
        super(context);
        setWidth((int) (ScreenUtils.getScreenWidth() * 0.8));
        mContext = context;
        setPopupGravity(Gravity.CENTER);
        tvSure = findViewById(R.id.tv_sure);
        tvCancel = findViewById(R.id.tv_cancel);
    }


    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_cut_success
        );
    }



}

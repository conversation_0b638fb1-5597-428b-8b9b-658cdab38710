package com.icatch.mobilecam.ui.ExtendComponent;
import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import com.icatch.mobilecam.utils.ScaleTool;
public class DragImageView extends ImageView {
    private Activity mActivity;
    private int screen_W, screen_H;
    private int bitmap_W, bitmap_H;
    private int MAX_W, MAX_H, MIN_W, MIN_H;
    private int current_Top, current_Right, current_Bottom, current_Left;
    private int start_x, start_y, current_x, current_y;
    private float beforeLenght, afterLenght;
    private float scale_temp;
    private boolean firstTouch = true;
    private int maxZoom = 3;
    private int minZoom = 1;
    private int originalRight = 0;
    private int originalLeft = 0;
    private int originalTop = 0;
    private int originalBottom = 0;
    private Bitmap bm;
    private enum MODE {
        NONE, DRAG, ZOOM
    };
    private MODE mode = MODE.NONE;
    private boolean isControl_V = false;
    private boolean isControl_H = false;
    public DragImageView(Context context) {
        super(context);
    }
    public void setmActivity(Activity mActivity) {
        this.mActivity = mActivity;
    }
    public void setScreen_W(int screen_W) {
        this.screen_W = screen_W;
    }
    public void setScreen_H(int screen_H) {
        this.screen_H = screen_H;
    }
    public DragImageView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }
    public void setImageBitmap(Bitmap bm, int width, int heigth) {
        this.setImageBitmap(bm);
        this.bm = bm;
        if (bm == null) {
            return;
        }
        Rect drawFrameRect = ScaleTool.getScaledPosition(bm.getWidth(), bm.getHeight(), width, heigth);
        RelativeLayout.LayoutParams dragImageViewLayoutParams = (RelativeLayout.LayoutParams) this.getLayoutParams();
        dragImageViewLayoutParams.leftMargin = drawFrameRect.left;
        dragImageViewLayoutParams.rightMargin = drawFrameRect.left;
        dragImageViewLayoutParams.topMargin = drawFrameRect.top;
        dragImageViewLayoutParams.bottomMargin = drawFrameRect.top;
        this.setLayoutParams(dragImageViewLayoutParams);
        this.setScaleType(ScaleType.FIT_XY);
        screen_W = width;
        screen_H = heigth;
    }
    public void setMaxAndMin(){
        bitmap_W = this.getWidth();
        bitmap_H = this.getHeight();
        MAX_W = bitmap_W * maxZoom;
        MAX_H = bitmap_H * maxZoom;
        MIN_W = bitmap_W * minZoom;
        MIN_H = bitmap_H * minZoom;
    }
    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
    }
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        getParent().requestDisallowInterceptTouchEvent(true);
        if (firstTouch) {
            firstTouch = false;
            setMaxAndMin();
            originalLeft = DragImageView.this.getLeft();
            originalRight = DragImageView.this.getRight();
            originalTop = DragImageView.this.getTop();
            originalBottom = DragImageView.this.getBottom();
        }
        switch (event.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                onTouchDown(event);
                break;
            case MotionEvent.ACTION_POINTER_DOWN:
                onPointerDown(event);
                break;
            case MotionEvent.ACTION_MOVE:
                onTouchMove(event);
                break;
            case MotionEvent.ACTION_UP:
                mode = MODE.NONE;
                break;
            case MotionEvent.ACTION_POINTER_UP:
                mode = MODE.NONE;
                break;
        }
        return true;
    }
    void onTouchDown(MotionEvent event) {
        mode = MODE.DRAG;
        current_x = (int) event.getRawX();
        current_y = (int) event.getRawY();
        start_x = (int) event.getX();
        start_y = current_y - this.getTop();
    }
    void onPointerDown(MotionEvent event) {
        if (event.getPointerCount() == 2) {
            mode = MODE.ZOOM;
            beforeLenght = getDistance(event);
        }
    }
    void onTouchMove(MotionEvent event) {
        int left = 0, top = 0, right = 0, bottom = 0;
        if (mode == MODE.DRAG) {
            left = current_x - start_x;
            right = current_x + this.getWidth() - start_x;
            top = current_y - start_y;
            bottom = current_y - start_y + this.getHeight();
            if (isControl_H) {
                if (left >= 0) {
                    left = 0;
                    right = this.getWidth();
                }
                if (right <= screen_W) {
                    left = screen_W - this.getWidth();
                    right = screen_W;
                }
            } else {
                left = this.getLeft();
                right = this.getRight();
            }
            if (isControl_V) {
                if (top >= 0) {
                    top = 0;
                    bottom = this.getHeight();
                }
                if (bottom <= screen_H) {
                    top = screen_H - this.getHeight();
                    bottom = screen_H;
                }
            } else {
                top = this.getTop();
                bottom = this.getBottom();
            }
            if (isControl_H) {
                if ((this.getLeft() == 0 && (event.getX() - start_x) > 0) || (this.getRight() == screen_W && (event.getX() - start_x) < 0)) {
                    getParent().requestDisallowInterceptTouchEvent(false);
                }
            } else {
                if ((event.getX() - start_x) > 0 || (event.getX() - start_x) < 0) {
                    getParent().requestDisallowInterceptTouchEvent(false);
                }
            }
            if (isControl_H || isControl_V)
                this.setPosition(left, top, right, bottom);
            current_x = (int) event.getRawX();
            current_y = (int) event.getRawY();
        }
        else if (mode == MODE.ZOOM) {
            afterLenght = getDistance(event);
            float gapLenght = afterLenght - beforeLenght;
            if (Math.abs(gapLenght) > 5f) {
                scale_temp = afterLenght / beforeLenght;
                this.setScale(scale_temp);
                beforeLenght = afterLenght;
            }
        }
    }
    float getDistance(MotionEvent event) {
        float x = event.getX(0) - event.getX(1);
        float y = event.getY(0) - event.getY(1);
        return (float)Math.sqrt(x * x + y * y);
    }
    private void setPosition(int left, int top, int right, int bottom) {
        this.layout(left, top, right, bottom);
    }
    void setScale(float scale) {
        int disX = (int) (this.getWidth() * Math.abs(1 - scale)) / 4;
        int disY = (int) (this.getHeight() * Math.abs(1 - scale)) / 4;
        if (scale > 1 && this.getWidth() <= MAX_W) {
            current_Left = this.getLeft() - disX;
            current_Top = this.getTop() - disY;
            current_Right = this.getRight() + disX;
            current_Bottom = this.getBottom() + disY;
            this.setFrame(current_Left, current_Top, current_Right, current_Bottom);
            if (current_Top <= 0 && current_Bottom >= screen_H) {
                isControl_V = true;
            } else {
                isControl_V = false;
            }
            if (current_Left <= 0 && current_Right >= screen_W) {
                isControl_H = true;
            } else {
                isControl_H = false;
            }
        }
        else if (scale < 1 && this.getWidth() >= MIN_W) {
            current_Left = this.getLeft() + disX;
            current_Top = this.getTop() + disY;
            current_Right = this.getRight() - disX;
            current_Bottom = this.getBottom() - disY;
            if ((current_Right - current_Left) < MIN_W) {
                current_Left = originalLeft;
                current_Right = originalRight;
            }
            if ((current_Bottom - current_Top) < MIN_H) {
                current_Top = originalTop;
                current_Bottom = originalBottom;
            }
            if (isControl_V && current_Top > 0) {
                current_Top = 0;
                current_Bottom = this.getBottom() - 2 * disY;
                if (current_Bottom < screen_H) {
                    current_Bottom = screen_H;
                    isControl_V = false;
                }
            }
            if (isControl_V && current_Bottom < screen_H) {
                current_Bottom = screen_H;
                current_Top = this.getTop() + 2 * disY;
                if (current_Top > 0) {
                    current_Top = 0;
                    isControl_V = false;
                }
            }
            if (isControl_H && current_Left >= 0) {
                current_Left = 0;
                current_Right = this.getRight() - 2 * disX;
                if (current_Right <= screen_W) {
                    current_Right = screen_W;
                    isControl_H = false;
                }
            }
            if (isControl_H && current_Right <= screen_W) {
                current_Right = screen_W;
                current_Left = this.getLeft() + 2 * disX;
                if (current_Left >= 0) {
                    current_Left = 0;
                    isControl_H = false;
                }
            }
            if (isControl_H || isControl_V) {
                this.setFrame(current_Left, current_Top, current_Right, current_Bottom);
            } else {
                this.setFrame(current_Left, current_Top, current_Right, current_Bottom);
            }
        }
    }
    public void recyleBitmap() {
        if (bm != null) {
            bm.recycle();
            bm = null;
        }
    }
}

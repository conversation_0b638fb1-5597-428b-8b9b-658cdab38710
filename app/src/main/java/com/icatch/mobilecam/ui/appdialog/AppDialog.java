package com.icatch.mobilecam.ui.appdialog;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import com.blankj.utilcode.util.PermissionUtils;
import com.icatch.mobilecam.data.GlobalApp.ExitApp;
import com.icatch.mobilecam.Log.AppLog;
import com.ijoyer.mobilecam.R;
public class AppDialog {
    private final static String tag = "AppDialog";
    private static boolean needShown = true;
    private static AlertDialog dialog;
    public void showDialog(String title, String message, boolean cancelable) {
    }
    public static void showDialog(final Context context, String message) {
        if (dialog != null) {
            dialog.dismiss();
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setMessage(message);
        builder.setCancelable(true);
        builder.setPositiveButton(R.string.ok, (dialog, which) -> dialog.dismiss());
        dialog = builder.create();
        dialog.show();
    }
    public static void showDialog(final Context context, final int messageID) {
        if (dialog != null) {
            dialog.dismiss();
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setMessage(context.getResources().getString(messageID));
        builder.setCancelable(true);
        builder.setPositiveButton(R.string.ok, (dialog, which) -> dialog.dismiss());
        dialog = builder.create();
        dialog.show();
    }
    public static void showDialogQuit(final Context context, final int messageID) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(messageID);
        builder.setCancelable(false);
        builder.setPositiveButton(R.string.dialog_btn_exit, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                AppLog.i(tag, "ExitApp because of " + context.getResources().getString(messageID));
                ExitApp.getInstance().exit();
            }
        });
        builder.create().show();
    }
    public static void showDialogQuit(final Context context, final String message) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(message);
        builder.setCancelable(false);
        builder.setPositiveButton(R.string.dialog_btn_exit, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                AppLog.i(tag, "ExitApp because of " + message);
                ExitApp.getInstance().exit();
            }
        });
        builder.create().show();
    }
    public static void showDialogWarn(final Context context, String message) {
        try {
            if (dialog != null) {
                dialog.dismiss();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        // 检查 Activity 是否已经销毁
        if (context instanceof android.app.Activity) {
            android.app.Activity activity = (android.app.Activity) context;
            if (activity.isFinishing() || activity.isDestroyed()) {
                AppLog.e("AppDialog", "Activity is finishing or destroyed, cannot show dialog");
                return;
            }
        }

        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
            builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(message);
            builder.setCancelable(true);
            builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    dialog.dismiss();
                }
            });
            dialog = builder.create();
            dialog.show();
        } catch (Exception e) {
            AppLog.e("AppDialog", "Failed to show dialog: " + e.getMessage());
            e.printStackTrace();
        }
    }
    public static void showDialogWarn(final Context context, int messageID) {
        if (dialog != null) {
            dialog.dismiss();
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(messageID);
        builder.setCancelable(true);
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        dialog = builder.create();
        dialog.show();
    }


    public static void showDialogWarnV2(final Context context, int messageID) {
        if (dialog != null) {
            dialog.dismiss();
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(messageID);
        builder.setCancelable(true);
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        builder.setNegativeButton(R.string.go_to_permiss, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                PermissionUtils.launchAppDetailsSettings();
                dialog.dismiss();
            }
        });

        dialog = builder.create();
        dialog.show();
    }
    public static void showDialogWarn(final Context context, int messageID, boolean cancelable, final OnDialogSureClickListener listener) {
        if (dialog != null) {
            dialog.dismiss();
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(messageID);
        builder.setCancelable(cancelable);
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                if (listener != null) {
                    listener.onSure();
                }
            }
        });
        dialog = builder.create();
        dialog.show();
    }
    public static void showDialogWarn(final Context context, String message, boolean cancelable, final OnDialogSureClickListener listener) {
        if (dialog != null) {
            dialog.dismiss();
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(message);
        builder.setCancelable(cancelable);
        builder.setNegativeButton(android.R.string.cancel, (dialogInterface, i) -> {
            dialog.dismiss();
        });
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
                if (listener != null) {
                    listener.onSure();
                }
            }
        });
        dialog = builder.create();
        dialog.show();
    }
    public static void showConnectFailureWarning(final Context context) {
        if (needShown == false) {
            return;
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(R.string.dialog_timeout);
        builder.setPositiveButton(R.string.dialog_btn_exit, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                ExitApp.getInstance().exit();
            }
        });
        builder.setCancelable(false);
        builder.setNegativeButton(R.string.dialog_btn_reconnect, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                needShown = true;
            }
        });
        builder.create().show();
    }
    public static void showLowBatteryWarning(Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        builder.setIcon(R.drawable.warning).setTitle(R.string.title_warning).setMessage(R.string.low_battery);
        builder.setPositiveButton(R.string.ok, (dialog, which) -> dialog.dismiss());
        builder.create().show();
    }
    public static void showAPPVersionDialog(Context context) {
        AlertDialog.Builder builder = new AlertDialog.Builder(context, R.style.MyAlertDialog);
        PackageInfo packageInfo = null;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(context.getPackageName(), 0);
        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
        String appVersion = "";
        if (packageInfo != null) {
            appVersion = packageInfo.versionName;
        }
        builder.setTitle(R.string.setting_app_version).setMessage("App version : " + appVersion);
        builder.setPositiveButton(R.string.ok, new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        builder.create().show();
    }
    public interface OnDialogButtonClickListener {
        void onCancel();
        void onSure();
    }
    public interface OnDialogSureClickListener {
        void onSure();
    }
}

package com.icatch.mobilecam.ui.adapter;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.entity.LocalPbItemInfo;
import com.icatch.mobilecam.data.type.FileType;
import com.icatch.mobilecam.ui.appdialog.EditRemarkDialog;
import com.icatch.mobilecam.utils.SPKey;
import com.icatch.mobilecam.utils.ToastUtil;
import com.ijoyer.mobilecam.R;
import com.tencent.mmkv.MMKV;

import java.io.File;
import java.util.LinkedList;
import java.util.List;

public class LocalMultiPbWallListAdapter extends BaseAdapter {
    private String TAG = "LocalMultiPbWallListAdapter";
    private Context context;
    private List<LocalPbItemInfo> list;
    private OperationMode curMode = OperationMode.MODE_BROWSE;
    private FileType fileType;

    public LocalMultiPbWallListAdapter(Context context, List<LocalPbItemInfo> list, FileType fileType) {
        this.context = context;
        this.list = list;
        this.fileType = fileType;
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return list.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        String curFileDate = list.get(position).getFileDate();
        View view;
        if (convertView == null) {
            view = LayoutInflater.from(context).inflate(R.layout.item_local_photo_wall_list, null);
        } else {
            view = convertView;
        }
        ImageView imageView = (ImageView) view.findViewById(R.id.local_photo_thumbnail_list);
        TextView mTextView = (TextView) view.findViewById(R.id.photo_wall_header);
        RelativeLayout mLayout = (RelativeLayout) view.findViewById(R.id.local_photo_wall_header_layout);
        TextView imageNameTextView = (TextView) view.findViewById(R.id.local_photo_name);
        TextView imageSizeTextView = (TextView) view.findViewById(R.id.local_photo_size);
        TextView imageDateTextView = (TextView) view.findViewById(R.id.local_photo_date);
        ImageView mCheckImageView = (ImageView) view.findViewById(R.id.local_photo_wall_list_edit);
        ImageView mShareImageView = view.findViewById(R.id.local_photo_wall_list_share);
        ImageView videoSignImageView = (ImageView) view.findViewById(R.id.video_sign);
        ImageView mIsPanoramaSign = (ImageView) view.findViewById(R.id.is_panorama);
        String fileName = list.get(position).getFileName();
        imageNameTextView.setText(getWithoutRemark(fileName));
        imageSizeTextView.setText(list.get(position).getFileSize());
        imageDateTextView.setText(list.get(position).getFileDateMMSS());
        TextView quarter0 = view.findViewById(R.id.quarter_0);
        TextView quarter1 = view.findViewById(R.id.quarter_1);
        TextView quarter2 = view.findViewById(R.id.quarter_2);
        TextView quarter3 = view.findViewById(R.id.quarter_3);
        ImageView quarterNone = view.findViewById(R.id.quarter_none);
        TextView remarkTextView = view.findViewById(R.id.local_photo_remark);
        ImageView ivEditRemarkName = view.findViewById(R.id.local_photo_wall_list_edit_remark_name);
        LocalPbItemInfo curItemInfo = list.get(position);
        if ((getCount() >= 4) && curItemInfo.isPanoramaQuarter && curItemInfo.isQuarterAllExists()) {
            int index = curItemInfo.panoramaQuarterIndex;
            if (!curItemInfo.getQuarterIs0To3Sort()) {
                index = 3 - index;
            }
            switch (index) {
                case 0:
                    quarter0.setVisibility(View.VISIBLE);
                    quarter1.setVisibility(View.GONE);
                    quarter2.setVisibility(View.GONE);
                    quarter3.setVisibility(View.GONE);
                    quarterNone.setVisibility(View.GONE);
                    quarter0.setBackgroundResource(R.drawable.ic_panorama_symbol_top);
                    break;
                case 1:
                    quarter0.setVisibility(View.GONE);
                    quarter1.setVisibility(View.VISIBLE);
                    quarter2.setVisibility(View.GONE);
                    quarter3.setVisibility(View.GONE);
                    quarterNone.setVisibility(View.GONE);
                    quarter1.setBackgroundResource(R.drawable.ic_panorama_symbol_center);
                case 2:
                    quarter0.setVisibility(View.GONE);
                    quarter1.setVisibility(View.GONE);
                    quarter2.setVisibility(View.VISIBLE);
                    quarter3.setVisibility(View.GONE);
                    quarterNone.setVisibility(View.GONE);
                    quarter2.setBackgroundResource(R.drawable.ic_panorama_symbol_center);
                    break;
                case 3:
                    quarter0.setVisibility(View.GONE);
                    quarter1.setVisibility(View.GONE);
                    quarter2.setVisibility(View.GONE);
                    quarter3.setVisibility(View.VISIBLE);
                    quarterNone.setVisibility(View.GONE);
                    quarter3.setBackgroundResource(R.drawable.ic_panorama_symbol_bottom);
                    break;
            }
        } else {
            quarter0.setVisibility(View.GONE);
            quarter1.setVisibility(View.GONE);
            quarter2.setVisibility(View.GONE);
            quarter3.setVisibility(View.GONE);
            if (list.get(position).isPanorama()) {
                quarterNone.setVisibility(View.VISIBLE);
                quarterNone.setBackgroundResource(R.drawable.ic_panorama_green_500_48dp);
            } else {
                quarterNone.setVisibility(View.GONE);
            }
        }
        if (fileType == FileType.FILE_PHOTO) {
            videoSignImageView.setVisibility(View.GONE);
        } else {
            videoSignImageView.setVisibility(View.VISIBLE);
        }
        if (curMode == OperationMode.MODE_EDIT) {
            mCheckImageView.setVisibility(View.VISIBLE);
            mShareImageView.setVisibility(View.GONE);
            ivEditRemarkName.setVisibility(View.GONE);
            if (list.get(position).isItemChecked) {
                mCheckImageView.setImageResource(R.drawable.ic_check_box_blue);
            } else {
                mCheckImageView.setImageResource(R.drawable.ic_check_box_blank_grey);
            }
        } else {
            mCheckImageView.setVisibility(View.GONE);
            mShareImageView.setVisibility(View.VISIBLE);
            String remarkName = null;
            //正常的文件名应该是：20240711_220023_xxx.JPG，这里截取前面15 位时间
            String groupName = SPKey.getRemarkGroup(fileName);
            if (!TextUtils.isEmpty(groupName)) {
                remarkName = MMKV.defaultMMKV().getString(SPKey.REMARK_NAME_PREFIX + groupName, null);
            }
            if (TextUtils.isEmpty(remarkName)) {
                remarkTextView.setVisibility(View.GONE);
            } else {
                remarkTextView.setVisibility(View.VISIBLE);
                remarkTextView.setText(remarkName);
            }
        }
        ivEditRemarkName.setOnClickListener(v -> {
            editRemarkMark(fileName);
        });
        mShareImageView.setOnClickListener(v -> {
            Uri uri = Uri.parse("file://" + list.get(position).getFilePath());
            Intent intent = new Intent();
            intent.setAction(Intent.ACTION_SEND);
            intent.putExtra(Intent.EXTRA_STREAM, uri);
            if (fileType == FileType.FILE_PHOTO) {
                intent.setType("image/*");
            } else {
                intent.setType("video/*");
            }
            context.startActivity(Intent.createChooser(intent, "IJOYER选择分享应用"));
        });
        if (position == 0 || !list.get(position - 1).getFileDate().equals(curFileDate)) {
            mLayout.setVisibility(View.VISIBLE);
            mTextView.setText(list.get(position).getFileDate());
        } else {
            mLayout.setVisibility(View.GONE);
        }
        File file = list.get(position).file;
        if (file != null) {
            Glide.with(context)
                    .setDefaultRequestOptions(
                            new RequestOptions()
//                                    .frame(2000000)
                                    .centerCrop()
                                    .placeholder(R.drawable.pictures_no)
                    )
                    .load(file)
                    .into(imageView);

//            Glide.with(context).load(file).placeholder(R.drawable.pictures_no).into(imageView);
        }
        return view;
    }

    //去掉备注名
    private String getWithoutRemark(String fileName) {
        if (TextUtils.isEmpty(fileName)){
            return fileName;
        }
        // 查找第一个 '[' 和最后一个 ']' 的位置
        int startIndex = fileName.indexOf('[');
        int endIndex = fileName.lastIndexOf(']');

        // 确保 '[' 在 ']' 之前，并且两者都存在
        if (startIndex != -1 && endIndex != -1 && startIndex < endIndex) {
            // 构建新的文件名，移除方括号及其内部的内容
            // substring(0, startIndex) 获取第一个 '['之前的部分
            // substring(endIndex + 1) 获取最后一个 ']'之后的部分
            // 去掉前后的空格
            String part1 = fileName.substring(0, startIndex).trim();
            String part2 = fileName.substring(endIndex + 1).trim();

            // 如果两部分之间原本有空格，或者其中一部分为空，则直接拼接
            // 否则，在两部分之间加一个空格（如果它们都不是空的）
            if (part1.isEmpty() || part2.isEmpty() || fileName.charAt(startIndex -1) == ' ' || fileName.charAt(endIndex + 1) == ' ') {
                return (part1 + part2).trim(); // 最后再 trim 一次，确保没有多余空格
            } else {
                return (part1 + " " + part2).trim(); // 最后再 trim 一次
            }
        } else {
            // 如果没有找到匹配的方括号，或者顺序不正确，返回原始文件名
            return fileName;
        }
    }

    private void editRemarkMark(String fileName) {
        String groupName = SPKey.getRemarkGroup(fileName);
        if (TextUtils.isEmpty(groupName)) {
            ToastUtil.showShortToast("文件名错误");
            return;
        }
        String remarkName = MMKV.defaultMMKV().getString(SPKey.REMARK_NAME_PREFIX + groupName, null);
        EditRemarkDialog.show(remarkName, inputStr -> {
            MMKV.defaultMMKV().putString(SPKey.REMARK_NAME_PREFIX + groupName, inputStr);
            notifyDataSetChanged();
        });
    }

    public void setOperationMode(OperationMode operationMode) {
        this.curMode = operationMode;
    }

    public void changeSelectionState(int position) {
        list.get(position).isItemChecked = list.get(position).isItemChecked == true ? false : true;
        this.notifyDataSetChanged();
    }

    public List<LocalPbItemInfo> getSelectedList() {
        LinkedList<LocalPbItemInfo> checkedList = new LinkedList<>();
        for (int ii = 0; ii < list.size(); ii++) {
            if (list.get(ii).isItemChecked) {
                checkedList.add(list.get(ii));
            }
        }
        return checkedList;
    }

    public void quitEditMode() {
        this.curMode = OperationMode.MODE_BROWSE;
        for (int ii = 0; ii < list.size(); ii++) {
            list.get(ii).isItemChecked = false;
        }
        this.notifyDataSetChanged();
    }

    public void selectAllItems() {
        for (int ii = 0; ii < list.size(); ii++) {
            list.get(ii).isItemChecked = true;
        }
        this.notifyDataSetChanged();
    }

    public void cancelAllSelections() {
        for (int ii = 0; ii < list.size(); ii++) {
            list.get(ii).isItemChecked = false;
        }
        this.notifyDataSetChanged();
    }

    public int getSelectedCount() {
        int checkedNum = 0;
        for (int ii = 0; ii < list.size(); ii++) {
            if (list.get(ii).isItemChecked) {
                checkedNum++;
            }
        }
        return checkedNum;
    }
}

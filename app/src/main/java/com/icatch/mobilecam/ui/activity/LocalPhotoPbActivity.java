package com.icatch.mobilecam.ui.activity;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.Window;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.viewpager.widget.PagerAdapter;
import androidx.viewpager.widget.ViewPager;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.Presenter.LocalPhotoPbPresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.ui.ExtendComponent.HackyViewPager;
import com.icatch.mobilecam.ui.Interface.LocalPhotoPbView;
import com.icatchtek.pancam.customer.type.ICatchGLPanoramaType;
import com.ijoyer.mobilecam.R;
public class LocalPhotoPbActivity extends AppCompatActivity implements LocalPhotoPbView {
    private static final String TAG = LocalPhotoPbActivity.class.getSimpleName();
    private HackyViewPager viewPager;
    private TextView indexInfoTxv;
    private SurfaceView mSurfaceView;
    private ImageButton shareBtn;
    private ImageButton deleteBtn;
    private ImageButton photoInfoBtn;
    private RelativeLayout topBar;
    private LinearLayout bottomBar;
    private ImageButton back;
    private LocalPhotoPbPresenter localPhotoPbPresenter;
    private ImageButton doPrevious;
    private ImageButton doNext;
    private TextView panoramaTypeTxv;
    private void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            View decorView = window.getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            window.setNavigationBarColor(Color.TRANSPARENT);
            window.setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        actionBar.hide();
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        AppLog.d(TAG, "onCreate");
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_local_photo_pb);
        hideActionBar();
        viewPager = (HackyViewPager) findViewById(R.id.viewpager);
        viewPager.setPageMargin(30);
        viewPager.setLocked(true);
        indexInfoTxv = (TextView) findViewById(R.id.pb_index_info);
        mSurfaceView = (SurfaceView) findViewById(R.id.m_surfaceView);
        shareBtn = (ImageButton) findViewById(R.id.local_photo_pb_share);
        deleteBtn = (ImageButton) findViewById(R.id.local_photo_pb_delete);
        photoInfoBtn = (ImageButton) findViewById(R.id.local_photo_pb_info);
        topBar = (RelativeLayout) findViewById(R.id.local_pb_top_layout);
        bottomBar = (LinearLayout) findViewById(R.id.local_pb_bottom_layout);
        back = (ImageButton) findViewById(R.id.local_pb_back);
        doPrevious = (ImageButton) findViewById(R.id.do_previous);
        doNext = (ImageButton) findViewById(R.id.do_next);
        panoramaTypeTxv = (TextView) findViewById(R.id.panorama_type_btn);
        localPhotoPbPresenter = new LocalPhotoPbPresenter(this);
        localPhotoPbPresenter.setView(this);
        shareBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localPhotoPbPresenter.share(LocalPhotoPbActivity.this);
            }
        });
        deleteBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localPhotoPbPresenter.delete();
            }
        });
        photoInfoBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localPhotoPbPresenter.info();
            }
        });
        panoramaTypeTxv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                localPhotoPbPresenter.setPanoramaType();
            }
        });
        back.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localPhotoPbPresenter.finish();
            }
        });
        doPrevious.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppLog.d(TAG, "....doPrevious");
                localPhotoPbPresenter.loadPreviousImage();
            }
        });
        doNext.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                AppLog.d(TAG, "....doNext");
                localPhotoPbPresenter.loadNextImage();
            }
        });
        mSurfaceView.getHolder().addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) {
                AppLog.d(TAG, "surfaceCreated");
                localPhotoPbPresenter.setShowArea(mSurfaceView.getHolder().getSurface());
                localPhotoPbPresenter.loadPanoramaImage();
            }
            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
                AppLog.d(TAG, "surfaceChanged........width=" + width);
                localPhotoPbPresenter.setDrawingArea(width, height);
            }
            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
                AppLog.d(TAG, "surfaceDestroyed");
                localPhotoPbPresenter.clearImage(ICatchGLPanoramaType.ICH_GL_PANORAMA_TYPE_SPHERE);
            }
        });
        mSurfaceView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction() & MotionEvent.ACTION_MASK) {
                    case MotionEvent.ACTION_DOWN:
                        localPhotoPbPresenter.onSurfaceViewTouchDown(event);
                        break;
                    case MotionEvent.ACTION_UP:
                        localPhotoPbPresenter.onSufaceViewTouchUp();
                        break;
                    case MotionEvent.ACTION_POINTER_DOWN:
                        localPhotoPbPresenter.onSurfaceViewPointerDown(event);
                        break;
                    case MotionEvent.ACTION_POINTER_UP:
                        localPhotoPbPresenter.onSufaceViewTouchPointerUp();
                        break;
                    case MotionEvent.ACTION_MOVE:
                        localPhotoPbPresenter.onSufaceViewTouchMove(event);
                        break;
                }
                return true;
            }
        });
        localPhotoPbPresenter.initPanorama();
    } 
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_HOME:
                Log.d("AppStart", "home");
                break;
            case KeyEvent.KEYCODE_BACK:
                Log.d("AppStart", "back");
                localPhotoPbPresenter.finish();
                break;
            default:
                return super.onKeyDown(keyCode, event);
        }
        return true;
    }
    @Override
    protected void onResume() {
        AppLog.d(TAG, "onResume");
        super.onResume();
        localPhotoPbPresenter.initView();
        localPhotoPbPresenter.submitAppInfo();
        localPhotoPbPresenter.registerGyroscopeSensor();
        AppInfo.checkLocationDialog(this);
    }
    @Override
    protected void onStart() {
        AppLog.d(TAG, "onStart");
        super.onStart();
    }
    @Override
    protected void onStop() {
        AppLog.d(TAG, "onStop");
        super.onStop();
        localPhotoPbPresenter.isAppBackground();
    }
    @Override
    protected void onDestroy() {
        AppLog.d(TAG, "onDestroy");
        super.onDestroy();
        localPhotoPbPresenter.removeActivity();
    }
    @Override
    protected void onPause() {
        localPhotoPbPresenter.removeGyroscopeListener();
        super.onPause();
    }
    @Override
    public void setViewPagerAdapter(PagerAdapter adapter) {
        if (adapter != null) {
            viewPager.setAdapter(adapter);
        }
    }
    @Override
    public void setIndexInfoTxv(String indexInfo) {
        indexInfoTxv.setText(indexInfo);
    }
    @Override
    public void setViewPagerCurrentItem(int position) {
        viewPager.setCurrentItem(position);
    }
    @Override
    public void setOnPageChangeListener(ViewPager.OnPageChangeListener listener) {
        viewPager.addOnPageChangeListener(listener);
    }
    @Override
    public int getViewPagerCurrentItem() {
        return viewPager.getCurrentItem();
    }
    @Override
    public int getTopBarVisibility() {
        return topBar.getVisibility();
    }
    @Override
    public void setTopBarVisibility(int visibility) {
        topBar.setVisibility(visibility);
    }
    @Override
    public void setBottomBarVisibility(int visibility) {
        bottomBar.setVisibility(visibility);
    }
    @Override
    public void setSurfaceviewVisibility(int visibility) {
        int curVisibility = mSurfaceView.getVisibility();
        if (curVisibility != visibility) {
            mSurfaceView.setVisibility(visibility);
        }
    }
    @Override
    public void setViewPagerVisibility(int visibility) {
        viewPager.setVisibility(visibility);
    }
    @Override
    public void setPanoramaTypeTxv(int resId) {
        panoramaTypeTxv.setText(resId);
    }
}

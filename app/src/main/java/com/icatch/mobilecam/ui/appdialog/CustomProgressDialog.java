package com.icatch.mobilecam.ui.appdialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;
import com.ijoyer.mobilecam.R;
public class CustomProgressDialog extends ProgressDialog {
    public CustomProgressDialog(Context context) {
        super(context);
    }
    private String text;
    public CustomProgressDialog(Context context, int theme) {
        super(context, theme);
    }
    public CustomProgressDialog(Context context, int theme, String text) {
        super(context, theme);
        this.text = text;
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init(getContext());
    }
    private void init(Context context) {
        setCancelable(false);
        setCanceledOnTouchOutside(false);
        setContentView(R.layout.load_dialog);
        TextView textview = findViewById(R.id.tv_load_dialog);
        if (textview != null && text != null) {
            textview.setText(text);
        } else {
            textview.setVisibility(View.GONE);
        }
        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.width = WindowManager.LayoutParams.WRAP_CONTENT;
        params.height = WindowManager.LayoutParams.WRAP_CONTENT;
        getWindow().setAttributes(params);
    }
    @Override
    public void show() {
        super.show();
    }
}

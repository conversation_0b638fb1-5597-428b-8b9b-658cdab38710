package com.icatch.mobilecam.ui.appdialog;

import android.graphics.Rect;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.ConvertUtils;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.google.android.flexbox.JustifyContent;
import com.icatch.mobilecam.ui.adapter.SingleViewHolder;
import com.ijoyer.mobilecam.R;
import com.kongzue.dialogx.dialogs.BottomDialog;
import com.kongzue.dialogx.interfaces.OnBindView;
import com.qmuiteam.qmui.layout.QMUITextView;

import java.util.Arrays;
import java.util.List;

/**
 * 备注对话框
 * <p>
 * Author:huangwubin
 * <p>
 * changeLogs:
 * 2024/7/26: First created this class.
 */
public class EditRemarkDialog {
    //默认备注
    private static String[] defaultArrary = new String[]{
            "入户", "卧室", "客厅", "厨房", "卫生间", "阳台", "过道", "楼梯", "车库", "餐厅", "阁楼", "洗衣房", "花园", "衣帽间", "其他"
    };

    public static void show(String curRemark, OnRemarkCallback listener) {
        BottomDialog.show(
                new OnBindView<BottomDialog>(R.layout.dialog_edit_remark) {
                    @Override
                    public void onBind(BottomDialog dialog, View v) {
                        EditText etRemark = v.findViewById(R.id.etRemark);
                        RecyclerView rv = v.findViewById(R.id.rv);
                        initRecyclerView(rv, etRemark::setText);
                        etRemark.setText(curRemark);
                        v.findViewById(R.id.tvFinish).setOnClickListener(v1 -> {
                            String remark = etRemark.getText().toString().trim();
                            if (listener != null) {
                                listener.onRemark(remark);
                            }
                            dialog.dismiss();
                        });
                    }
                });
    }

    private static void initRecyclerView(RecyclerView view, OnRemarkCallback callback) {
        List<String> list = Arrays.asList(defaultArrary);
        BaseQuickAdapter<String, SingleViewHolder<QMUITextView>> adapter = new BaseQuickAdapter<String, SingleViewHolder<QMUITextView>>(0, list) {
            @NonNull
            @Override
            protected SingleViewHolder<QMUITextView> onCreateDefViewHolder(@NonNull ViewGroup parent, int viewType) {
                QMUITextView view = new QMUITextView(parent.getContext());
                view.setBorderColor(0xff999999);
                view.setTextColor(0xff999999);
                view.setBorderWidth(1);
                view.setRadius(ConvertUtils.dp2px(5));
                view.setPadding(ConvertUtils.dp2px(10), ConvertUtils.dp2px(5), ConvertUtils.dp2px(10), ConvertUtils.dp2px(5));
                return new SingleViewHolder<>(view);
            }

            @Override
            protected void convert(@NonNull SingleViewHolder<QMUITextView> holder, String s) {
                holder.view.setText(s);
            }
        };

        FlexboxLayoutManager layoutManager = new FlexboxLayoutManager(view.getContext());
        layoutManager.setFlexDirection(FlexDirection.ROW);
        layoutManager.setJustifyContent(JustifyContent.FLEX_START);

        view.setLayoutManager(layoutManager);
        view.setAdapter(adapter);
        view.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                super.getItemOffsets(outRect, view, parent, state);
                outRect.bottom = outRect.top = outRect.left = outRect.right = ConvertUtils.dp2px(5);
            }
        });
        adapter.setOnItemClickListener((adapter1, view1, position) -> {
            callback.onRemark(list.get(position));
        });

    }

    public interface OnRemarkCallback {
        void onRemark(String remark);
    }
}

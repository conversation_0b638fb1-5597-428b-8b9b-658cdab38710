package com.icatch.mobilecam.ui.activity;

import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.StrictMode;
import android.util.Log;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.viewpager.widget.ViewPager;

import com.blankj.utilcode.util.FileUtils;
import com.google.android.material.tabs.TabLayout;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.Presenter.LocalMultiPbPresenter;
import com.icatch.mobilecam.bean.VideoBean;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.Mode.OperationMode;
import com.icatch.mobilecam.data.SystemInfo.MWifiManager;
import com.icatch.mobilecam.ui.ExtendComponent.MyToast;
import com.icatch.mobilecam.ui.Interface.LocalMultiPbView;
import com.icatch.mobilecam.ui.appdialog.AppDialog;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.FixedSpeedScroller;
import com.ijoyer.camera.activity.SettingsActivity;
import com.ijoyer.mobilecam.R;

import java.io.File;
import java.lang.reflect.Field;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.Unbinder;
public class LocalMultiPbActivity extends AppCompatActivity implements LocalMultiPbView {
    private String TAG = LocalMultiPbActivity.class.getSimpleName();
    @BindView(R.id.vPager)
    ViewPager viewPager;
    @BindView(R.id.action_select)
    ImageButton selectBtn;
    @BindView(R.id.action_delete)
    ImageButton deleteBtn;
    @BindView(R.id.action_share)
    ImageButton shareBtn;
    @BindView(R.id.info_selected_num)
    TextView selectedNumTxv;
    @BindView(R.id.edit_layout)
    LinearLayout multiPbEditLayout;
    @BindView(R.id.tabs)
    TabLayout tabLayout;
    @BindView(R.id.toolbar)
    Toolbar toolbar;
    @BindView(R.id.stitch_batch)
    TextView stitchBatch;
    private LocalMultiPbPresenter localMultiPbPresenter;
    MenuItem menuPhotoWallType;
    private Unbinder unbinder;

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    public void otaAliPushOther(EventBusEntity entity) {
//        if ("MEGRA".equals(entity.tag)) {
//            MyProgressDialog.showProgressDialog(this, R.string.action_processing);
//            new Handler(Objects.requireNonNull(Looper.myLooper())).postDelayed(new Runnable() {
//                @Override
//                public void run() {
//                    mergeMp3AndMp4((VideoBean)entity.data);
//                }
//            },2000);
//        }
//    }

    private void mergeMp3AndMp4(VideoBean data) {
//        String outMp3AndMp4 = Environment.getExternalStorageDirectory() + AppInfo.DOWNLOAD_PATH_PHOTO + TimeUtil.getNowDateTime() + "_RECORD.MP4";
//        String cmd = "-i " + data.mp4 + " -i " + data.mp3 + " -c:v copy -c:a aac -strict experimental " + outMp3AndMp4;

//        EpEditor.execCmd(cmd, 0, new OnEditorListener() {
//            @Override
//            public void onSuccess() {
//                new File(data.mp4).delete();
//                new File(data.mp3).delete();
//                LogUtil.e("MP3合并MP4成功");
//                MediaRefresh.scanFileAsync(getApplication(), outMp3AndMp4);
//                MyProgressDialog.closeProgressDialog();
//            }
//
//            @Override
//            public void onFailure() {
//                new File(dataMp3).delete();
//                LogUtil.e("MP3合并MP4失败");
//                MediaRefresh.scanFileAsync(getApplication(), outMp3AndMp4);
//                MyProgressDialog.closeProgressDialog();
//            }
//
//            @Override
//            public void onProgress(float progress) {
//            }
//        });


    }



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

//        try {
//            EventBus.getDefault().register(this);
//        } catch (Exception e) {
//            // e.printStackTrace();
//        }

        FileUtils.createOrExistsDir(Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH);
        List<File> files = FileUtils.listFilesInDir(Environment.getExternalStorageDirectory() + AppInfo.TEMP_DOWNLOAD_PATH);
        for (int i = 0;i<files.size();i++){
            if (files.get(i).getName().endsWith("mp3")){
                files.get(i).delete();
            }
        }

        setContentView(R.layout.activity_local_multi_pb);
        unbinder = ButterKnife.bind(this);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setDisplayShowTitleEnabled(false);
        viewPager.setPageMargin(getResources().getDimensionPixelOffset(R.dimen.space_10));
        localMultiPbPresenter = new LocalMultiPbPresenter(this);
        localMultiPbPresenter.setView(this);
        viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageSelected(int position) {
                localMultiPbPresenter.updateViewpagerStatus(position);
            }
            @Override
            public void onPageScrollStateChanged(int state) {
            }
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }
        });
        selectBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localMultiPbPresenter.selectOrCancel();
            }
        });
        deleteBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                localMultiPbPresenter.delete();
            }
        });
        shareBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
            }
        });
        stitchBatch.setOnClickListener(view -> localMultiPbPresenter.stitchBatch());
        localMultiPbPresenter.loadViewPager();
        tabLayout.setupWithViewPager(viewPager);
        try {
            Field field = ViewPager.class.getDeclaredField("mScroller");
            field.setAccessible(true);
            FixedSpeedScroller scroller = new FixedSpeedScroller(viewPager.getContext(),
                    new AccelerateInterpolator());
            field.set(viewPager, scroller);
            scroller.setmDuration(280);
        } catch (Exception e) {
            AppLog.e(TAG, "FixedSpeedScroller Exception");
        }
        initPhotoError();
    }
    private void initPhotoError() {
        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());
        builder.detectFileUriExposure();
    }
    @Override
    protected void onResume() {
        super.onResume();
        AppLog.d(TAG, "onResume()");
        localMultiPbPresenter.submitAppInfo();
        AppInfo.checkLocationDialog(this);
    }
    @Override
    protected void onStop() {
        super.onStop();
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
        localMultiPbPresenter.reset();
        localMultiPbPresenter.removeActivity();
        unbinder.unbind();

//        try {
//            EventBus.getDefault().unregister(this);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_local_multi_pb, menu);
        return true;
    }
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        /*if (id == R.id.stitch_param) {
            new StitchParamDialog(this, R.layout.param_layout).show();
        } else if (id == R.id.logo_param) {
            new SettingLogoDialog(this, R.layout.set_logo_layout, new Handler() {
                @Override
                public void handleMessage(@NonNull Message msg) {
                    super.handleMessage(msg);
                    if (SettingsActivity.WHAT_REQUEST_ALBUM == msg.what) {
                        SettingsActivity.startAlbumForResult(LocalMultiPbActivity.this);
                    }
                }
            }).show();
        } */ if (id == android.R.id.home) {
            localMultiPbPresenter.reBack();
            return true;
        } else if (id == R.id.help) {
            AppDialog.showDialog(this, "说明：\n" +
                    "1. 全景相机拍摄的4幅图片（缺一不可）自动识别为一组，可进行拼接和播放预览。\n" +
                    "2. 长按可进行批量拼接，请至少选择一组。");
        } /*else if (R.id.record == id) {
            new SettingRecordDialog(this, R.layout.set_record_layout).show();
        } */else if (R.id.remote_album == id) {
            if (localMultiPbPresenter.curOperationMode == OperationMode.MODE_EDIT) {
                MyToast.show(this, "当前处于批量处理状态!");
                return true;
            }
//            if (!NetworkUtils.isWifiConnected()) {
            if (!MWifiManager.isWifiConnected(this)){
                MyToast.show(this, "没有连入IJOYER相机热点");
                return true;
            }
            String wifiSSID = MWifiManager.getSsid(this);
            if (!CameraUtils.isIjoyerCamera(wifiSSID)) {
                MyToast.show(this, getResources().getString(R.string.no_device_to_be_found));
            } else {
                Intent intent = new Intent();
                intent.setClass(this, RemoteMultiPbActivity.class);
                this.startActivity(intent);
            }
        }
        return super.onOptionsItemSelected(item);
    }
    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        SettingsActivity.handleActivityResult(this, requestCode, resultCode, data);
    }
    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_HOME:
                Log.d("AppStart", "home");
                break;
            case KeyEvent.KEYCODE_BACK:
                Log.d("AppStart", "back");
                localMultiPbPresenter.reBack();
                break;
            default:
                return super.onKeyDown(keyCode, event);
        }
        return true;
    }
    @Override
    public void setViewPageAdapter(FragmentPagerAdapter adapter) {
        viewPager.setAdapter(adapter);
    }
    @Override
    public void setViewPageCurrentItem(int item) {
        AppLog.d(TAG, "setViewPageCurrentItem item=" + item);
        viewPager.setCurrentItem(item);
    }
    @Override
    public void setMenuPhotoWallTypeIcon(int iconRes) {
        menuPhotoWallType.setIcon(iconRes);
    }
    @Override
    public void setViewPagerScanScroll(boolean isCanScroll) {
    }
    @Override
    public void setSelectNumText(String text) {
        selectedNumTxv.setText(text);
    }
    @Override
    public void setSelectBtnVisibility(int visibility) {
        selectBtn.setVisibility(visibility);
    }
    @Override
    public void setSelectBtnIcon(int icon) {
        selectBtn.setImageResource(icon);
    }
    @Override
    public void setSelectNumTextVisibility(int visibility) {
        selectedNumTxv.setVisibility(visibility);
    }
    @Override
    public void setTabLayoutClickable(boolean value) {
        AppLog.d(TAG, "setTabLayoutClickable value=" + value);
        tabLayout.setClickable(value);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            tabLayout.setContextClickable(value);
        }
        tabLayout.setFocusable(value);
        tabLayout.setLongClickable(value);
        tabLayout.setEnabled(value);
    }
    @Override
    public void setEditLayoutVisibility(int visibility) {
        multiPbEditLayout.setVisibility(visibility);
    }
}

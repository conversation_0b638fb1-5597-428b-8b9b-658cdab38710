package com.icatch.mobilecam.ui.activity;

import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.BitmapDrawable;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.SurfaceHolder;
import android.view.View;
import android.widget.AdapterView;
import android.widget.GridLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.PopupWindow;
import android.widget.RadioButton;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;

import com.detu.android_panoplayer.PanoPlayerImpl;
import com.detu.android_panoplayer.renderer.PanoPlayerSurfaceView;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraManager;
import com.icatch.mobilecam.Presenter.PreviewPresenterV2;
import com.icatch.mobilecam.Presenter.RemoteMultiPbPresenter;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.Mode.PreviewMode;
import com.icatch.mobilecam.ui.ExtendComponent.MyProgressDialog;
import com.icatch.mobilecam.ui.ExtendComponent.ZoomView;
import com.icatch.mobilecam.ui.Interface.PreviewView;
import com.icatch.mobilecam.ui.adapter.SettingListAdapter;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.ClickUtils;
import com.icatchtek.control.customer.type.ICatchCamEventID;
import com.icatchtek.control.customer.type.ICatchCamMode;
import com.icatchtek.pancam.customer.ICatchPancamConfig;
import com.ijoyer.camera.activity.MainActivity;
import com.ijoyer.mobilecam.R;

public class PreviewActivityV2 extends AppCompatActivity implements View.OnClickListener, PreviewView {
    private static final String TAG = PreviewActivityV2.class.getSimpleName();
    private PreviewPresenterV2 previewPresenter;
    private PanoPlayerSurfaceView mSurfaceView;
    private ImageButton pbBtn;
    private ImageButton captureBtn;
    private ImageView wbStatus;
    private ImageView burstStatus;
    private ImageView wifiStatus;
    private ImageView batteryStatus;
    private ImageView timeLapseMode;
    private ImageView slowMotion;
    private ImageView carMode;
    private TextView recordingTime;
    private ImageView autoDownloadImageView;
    private TextView delayCaptureText;
    private RelativeLayout delayCaptureLayout;
    private RelativeLayout imageSizeLayout;
    private RelativeLayout videoSizeLayout;
    private TextView remainRecordingTimeText;
    private TextView remainCaptureCountText;
    private TextView imageSizeTxv;
    //视频尺寸
    private TextView videoSizeTxv;
    private ZoomView zoomView;
    private RelativeLayout setupMainMenu;
    private ListView mainMenuList;
    private ActionBar actionBar;
    private TextView noSupportPreviewTxv;
    private PopupWindow pvModePopupWindow;
    private RadioButton captureRadioBtn;
    private RadioButton videoRadioBtn;
    private RadioButton timeLapseRadioBtn;
    private ImageButton pvModeBtn;
    private View contentView;
    private ImageButton panoramaTypeBtn;
    private ImageButton pvSettingBtn;
    private String cameraName;
    private PanoPlayerImpl panoPlayer;
    public RemoteMultiPbPresenter remoteMultiPbPresenter;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_preview_v2);

        remoteMultiPbPresenter = new RemoteMultiPbPresenter(this);
        remoteMultiPbPresenter.downZdInfo(remoteMultiPbPresenter);

        if (CameraManager.getInstance().getCurCamera() == null){
            finish();
            return;
        }

        cameraName = CameraUtils.getCurCamera();
        actionBar = getSupportActionBar();
        hideActionBar();
        pvSettingBtn = (ImageButton) findViewById(R.id.pv_setting);
        previewPresenter = new PreviewPresenterV2(PreviewActivityV2.this);
        previewPresenter.setView(this);
        mSurfaceView = (PanoPlayerSurfaceView) findViewById(R.id.preview);
        panoPlayer = mSurfaceView.getRender();
        mSurfaceView.setOnClickListener(this);
        pbBtn = (ImageButton) findViewById(R.id.multi_pb);
        pbBtn.setOnClickListener(this);
        captureBtn = (ImageButton) findViewById(R.id.doCapture);
        captureBtn.setOnClickListener(this);
        wbStatus = (ImageView) findViewById(R.id.wb_status);
        burstStatus = (ImageView) findViewById(R.id.burst_status);
        wifiStatus = (ImageView) findViewById(R.id.wifi_status);
        batteryStatus = (ImageView) findViewById(R.id.battery_status);
        timeLapseMode = (ImageView) findViewById(R.id.timelapse_mode);
        slowMotion = (ImageView) findViewById(R.id.slow_motion);
        carMode = (ImageView) findViewById(R.id.car_mode);
        recordingTime = (TextView) findViewById(R.id.recording_time);
        autoDownloadImageView = (ImageView) findViewById(R.id.auto_download_imageView);
        delayCaptureText = (TextView) findViewById(R.id.delay_capture_text);
        delayCaptureLayout = (RelativeLayout) findViewById(R.id.delay_capture_layout);
        imageSizeLayout = (RelativeLayout) findViewById(R.id.image_size_layout);
        imageSizeTxv = (TextView) findViewById(R.id.image_size_txv);
        remainCaptureCountText = (TextView) findViewById(R.id.remain_capture_count_text);
        videoSizeLayout = (RelativeLayout) findViewById(R.id.video_size_layout);
        videoSizeTxv = (TextView) findViewById(R.id.video_size_txv);
        remainRecordingTimeText = (TextView) findViewById(R.id.remain_recording_time_text);
        setupMainMenu = (RelativeLayout) findViewById(R.id.setupMainMenu);
        mainMenuList = (ListView) findViewById(R.id.setup_menu_listView);
        noSupportPreviewTxv = (TextView) findViewById(R.id.not_support_preview_txv);
        pvModeBtn = (ImageButton) findViewById(R.id.pv_mode);
        contentView = LayoutInflater.from(PreviewActivityV2.this).inflate(R.layout.camera_mode_switch_layout, null);
        pvModePopupWindow = new PopupWindow(contentView, GridLayout.LayoutParams.WRAP_CONTENT, GridLayout.LayoutParams.WRAP_CONTENT, true);
        pvModePopupWindow.setBackgroundDrawable(new BitmapDrawable());
        pvModePopupWindow.setFocusable(true);
        pvModePopupWindow.setOutsideTouchable(true);
        captureRadioBtn = (RadioButton) contentView.findViewById(R.id.capture_radio);
        videoRadioBtn = (RadioButton) contentView.findViewById(R.id.video_radio);
        timeLapseRadioBtn = (RadioButton) contentView.findViewById(R.id.timeLapse_radio);
        zoomView = (ZoomView) findViewById(R.id.zoom_view);
        zoomView.setZoomInOnclickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    previewPresenter.zoomIn();
                }
            }
        });
        zoomView.setZoomOutOnclickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    previewPresenter.zoomOut();
                }
            }
        });
        zoomView.setOnSeekBarChangeListener(new ZoomView.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(ZoomView zoomView, float progress, boolean fromUser) {
            }

            @Override
            public void onStartTrackingTouch(ZoomView zoomView) {
            }

            @Override
            public void onStopTrackingTouch(ZoomView zoomView) {
                previewPresenter.zoomBySeekBar();
            }
        });
        mainMenuList.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                if (!ClickUtils.isFastDoubleClick(mainMenuList)) {
                    previewPresenter.showSettingDialog(position);
                }
            }
        });
        pvSettingBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!ClickUtils.isFastDoubleClick(pvSettingBtn)) {
                    if (View.GONE == setupMainMenu.getVisibility()) {
                        previewPresenter.loadSettingMenuList();
                    } else {
                        setupMainMenu.setVisibility(View.GONE);
                    }
                }
            }
        });
        pvModeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    previewPresenter.showPvModePopupWindow();
                }
            }
        });
        captureRadioBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    setupMainMenu.setVisibility(View.GONE);
                    previewPresenter.changePreviewMode(PreviewMode.APP_STATE_STILL_MODE);
                }
            }
        });
        videoRadioBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    setupMainMenu.setVisibility(View.GONE);
                    previewPresenter.changePreviewMode(PreviewMode.APP_STATE_VIDEO_MODE);
                }
            }
        });
        timeLapseRadioBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!ClickUtils.isFastDoubleClick(v)) {
                    setupMainMenu.setVisibility(View.GONE);
                    previewPresenter.changePreviewMode(PreviewMode.APP_STATE_TIMELAPSE_MODE);
                }
            }
        });

        mSurfaceView.getHolder().addCallback(new SurfaceHolder.Callback() {
            @Override
            public void surfaceCreated(SurfaceHolder holder) {
                AppLog.d(TAG, "surfaceCreated!!!");
                previewPresenter.initSurface(panoPlayer,  mSurfaceView);
//                previewPresenter.startPreview();
            }

            @Override
            public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
                AppLog.d(TAG, "surfaceChanged!!!");
                previewPresenter.setDrawingArea(width, height);
            }

            @Override
            public void surfaceDestroyed(SurfaceHolder holder) {
//                previewPresenter.destroyPreview();
            }
        });
        mSurfaceView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                switch (event.getAction() & MotionEvent.ACTION_MASK) {
                    case MotionEvent.ACTION_DOWN:
                        previewPresenter.onSufaceViewTouchDown(event);
                        break;
                    case MotionEvent.ACTION_POINTER_DOWN:
                        previewPresenter.onSufaceViewPointerDown(event);
                        break;
                    case MotionEvent.ACTION_MOVE:
                        previewPresenter.onSufaceViewTouchMove(event);
                        break;
                    case MotionEvent.ACTION_UP:
                        previewPresenter.onSufaceViewTouchUp();
                        break;
                    case MotionEvent.ACTION_POINTER_UP:
                        previewPresenter.onSufaceViewTouchPointerUp();
                        break;
                }
                return true;
            }
        });
        GlobalInfo.getInstance().addEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_REMOVED);
        GlobalInfo.getInstance().addEventListener(ICatchCamEventID.ICH_CAM_EVENT_SDCARD_IN);
        GlobalInfo.getInstance().addEventListener(ICatchCamEventID.ICH_CAM_EVENT_CONNECTION_DISCONNECTED);
        panoramaTypeBtn = (ImageButton) findViewById(R.id.panorama_type_btn);
        panoramaTypeBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                if (!ClickUtils.isFastDoubleClick(panoramaTypeBtn)) {
                    previewPresenter.setPanoramaType();
                }
            }
        });
    }



    public void hideActionBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            View decorView = getWindow().getDecorView();
            int option = View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE;
            decorView.setSystemUiVisibility(option);
            getWindow().setNavigationBarColor(Color.TRANSPARENT);
            getWindow().setStatusBarColor(Color.TRANSPARENT);
        }
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.hide();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        AppLog.d(TAG, "onResume");
        previewPresenter.submitAppInfo();
        previewPresenter.initPreview();
        previewPresenter.initStatus();
        previewPresenter.addEvent();
        AppInfo.checkLocationDialog(this);

        panoPlayer.onGLSurfaceViewResume();
    }


    @Override
    protected void onPause() {
        super.onPause();
        panoPlayer.onGLSurfaceViewPause();
        panoPlayer.pauseAllHotMusic();
        panoPlayer.pauseAllBackgroundMusic();
        panoPlayer.onGLSurfaceViewPause();

        previewPresenter.playF4OrF4Pluslive(true,false);
        previewPresenter.panoramaPreviewPlayback.stop();
        previewPresenter.cameraStreaming.stop();
        ICatchPancamConfig.getInstance().disableDumpTransportStream(true);
    }


    @Override
    protected void onStop() {
        AppLog.d(TAG, "onStop");
        super.onStop();
        previewPresenter.isAppBackground();
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_HOME:
                AppLog.d("AppStart", "home");
                break;
            case KeyEvent.KEYCODE_BACK:
                MyProgressDialog.showProgressDialog(this, R.string.action_processing);
                AppLog.d("AppStart", "back");
//                previewPresenter.playF4OrF4Pluslive(true,false);
//                previewPresenter.panoramaPreviewPlayback.stop();
//                previewPresenter.cameraStreaming.stop();
//                ICatchPancamConfig.getInstance().disableDumpTransportStream(true);
//                new  Handler().postDelayed(new Runnable() {
//                    @Override
//                    public void run() {
//                        previewPresenter.finishActivity();
//                    }
//                },4000);

                remoteMultiPbPresenter.reBack5Second();
                break;
            default:
                return super.onKeyDown(keyCode, event);
        }
        return true;
    }


    @Override
    public void finish() {
        MainActivity.lastExitPreviewActivityTime = System.currentTimeMillis();//设置退出时间
        super.finish();
    }

    @Override
    protected void onDestroy() {
        AppLog.d(TAG, "onDestroy");
        super.onDestroy();
        if (previewPresenter!=null) {
            previewPresenter.removeActivity();
            previewPresenter.destroyPreview();
            previewPresenter.delEvent();
            previewPresenter.disconnectCamera();
            previewPresenter.delConnectFailureListener();
            previewPresenter.unregisterWifiSSReceiver();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_preview, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == android.R.id.home) {
            AppLog.e(TAG, "id == android.R.id.home");
            previewPresenter.finishActivity();
        } else if (id == R.id.action_setting) {
            if (!ClickUtils.isFastDoubleClick(id)) {
                previewPresenter.loadSettingMenuList();
            }
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onClick(View v) {
        AppLog.i(TAG, "click the v.getId() =" + v.getId());
        int id = v.getId();
        if (id == R.id.multi_pb) {
            AppLog.i(TAG, "click the multi_pb");
            if (!ClickUtils.isFastDoubleClick(R.id.multi_pb)) {
                previewPresenter.redirectToAnotherActivity(PreviewActivityV2.this, RemoteMultiPbActivity.class);
            }
        } else if (id == R.id.doCapture) {
            AppLog.i(TAG, "click the doCapture");
            if (!ClickUtils.isFastDoubleClick(R.id.doCapture)) {
                previewPresenter.startOrStopCapture();
            }
        }
    }

    @Override
    public void setWbStatusVisibility(int visibility) {
        wbStatus.setVisibility(visibility);
    }

    @Override
    public void setBurstStatusVisibility(int visibility) {
        burstStatus.setVisibility(visibility);
    }

    @Override
    public void setWifiStatusVisibility(int visibility) {
        wifiStatus.setVisibility(visibility);
    }

    @Override
    public void setWifiIcon(int drawableId) {
        wifiStatus.setBackgroundResource(drawableId);
    }

    @Override
    public void setBatteryStatusVisibility(int visibility) {
        batteryStatus.setVisibility(View.INVISIBLE);
    }

    @Override
    public void setBatteryIcon(int drawableId) {
        batteryStatus.setBackgroundResource(drawableId);
    }

    @Override
    public void setTimeLapseModeVisibility(int visibility) {
        timeLapseMode.setVisibility(visibility);
    }

    @Override
    public void setTimeLapseModeIcon(int drawableId) {
        timeLapseMode.setBackgroundResource(drawableId);
    }

    @Override
    public void setSlowMotionVisibility(int visibility) {
        slowMotion.setVisibility(visibility);
    }

    @Override
    public void setCarModeVisibility(int visibility) {
        carMode.setVisibility(visibility);
    }

    @Override
    public void setRecordingTimeVisibility(int visibility) {
        recordingTime.setVisibility(visibility);
    }

    @Override
    public void setAutoDownloadVisibility(int visibility) {
        autoDownloadImageView.setVisibility(visibility);
    }

    @Override
    public void setCaptureBtnBackgroundResource(int id) {
        captureBtn.setBackgroundResource(id);
    }

    @Override
    public void setRecordingTime(String lapseTime) {
        recordingTime.setText(lapseTime);
    }

    @Override
    public void setDelayCaptureLayoutVisibility(int visibility) {
        delayCaptureLayout.setVisibility(visibility);
    }

    @Override
    public void setDelayCaptureTextTime(String delayCaptureTime) {
        delayCaptureText.setText(delayCaptureTime);
    }

    @Override
    public void setImageSizeLayoutVisibility(int visibility) {
        imageSizeLayout.setVisibility(visibility);
    }

    @Override
    public void setRemainCaptureCount(String remainCaptureCount) {
        remainCaptureCountText.setText(remainCaptureCount);
    }

    @Override
    public void setVideoSizeLayoutVisibility(int visibility) {
        videoSizeLayout.setVisibility(visibility);
        remainRecordingTimeText.setVisibility(visibility);
    }

    @Override
    public void setRemainRecordingTimeText(String remainRecordingTime) {
        remainRecordingTimeText.setText(remainRecordingTime);
    }

    @Override
    public void setBurstStatusIcon(int drawableId) {
        burstStatus.setBackgroundResource(drawableId);
    }

    @Override
    public void setWbStatusIcon(int drawableId) {
        wbStatus.setBackgroundResource(drawableId);
    }

    @Override
    public void setUpsideVisibility(int visibility) {
        carMode.setVisibility(visibility);
    }

    @Override
    public void setCaptureBtnEnAbility(boolean enAbility) {
        captureBtn.setEnabled(enAbility);
    }

    @Override
    public void setVideoSizeInfo(String sizeInfo) {
        AppLog.i(TAG, "sizeInfo = " + sizeInfo);
        videoSizeTxv.setText(sizeInfo);
    }

    @Override
    public void setImageSizeInfo(String sizeInfo) {
        AppLog.i(TAG, "sizeInfo = " + sizeInfo);
        imageSizeTxv.setText(sizeInfo);
    }

    @Override
    public void showZoomView() {
        zoomView.startDisplay();
    }

    @Override
    public void hideZoomView() {
        zoomView.setHide();
    }

    @Override
    public void setMaxZoomRate(final float maxZoomRate) {
        zoomView.setMaxValue(maxZoomRate);
    }

    @Override
    public void setMinZoomRate(final float minZoomRate) {
        zoomView.setMinValue(minZoomRate);
    }

    @Override
    public float getZoomViewProgress() {
        return zoomView.getProgress();
    }

    @Override
    public float getZoomViewMaxZoomRate() {
        return ZoomView.MAX_VALUE;
    }

    @Override
    public void updateZoomViewProgress(float currentZoomRatio) {
        zoomView.updateZoomBarValue(currentZoomRatio);
        zoomView.setProgress(currentZoomRatio);
    }

    @Override
    public int getSetupMainMenuVisibility() {
        return setupMainMenu.getVisibility();
    }

    @Override
    public void setSetupMainMenuVisibility(int visibility) {
        setupMainMenu.setVisibility(visibility);
    }

    @Override
    public void setAutoDownloadBitmap(Bitmap bitmap) {
        if (bitmap != null) {
            autoDownloadImageView.setImageBitmap(bitmap);
        }
    }

    @Override
    public void setActionBarTitle(int resId) {
        actionBar.setTitle(resId);
    }

    @Override
    public void setBackBtnVisibility(boolean isVisible) {
        actionBar.setDisplayHomeAsUpEnabled(isVisible);
    }

    @Override
    public void setSettingMenuListAdapter(SettingListAdapter settingListAdapter) {
        mainMenuList.setAdapter(settingListAdapter);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                previewPresenter.redrawSurface();
            }
        }, 200);
        AppLog.d(TAG, "onConfigurationChanged newConfig Orientation=" + newConfig.orientation);
    }

    @Override
    public void setSupportPreviewTxvVisibility(int visibility) {
        noSupportPreviewTxv.setVisibility(visibility);
    }

    @Override
    public void setPvModeBtnBackgroundResource(int drawableId) {
        pvModeBtn.setBackgroundResource(drawableId);
    }

    @Override
    public void setPvModeBtnVisibility(int visibility) {
        pvModeBtn.setVisibility(visibility);
    }

    @Override
    public void setTimeLapseRadioBtnVisibility(int visibility) {
        timeLapseRadioBtn.setVisibility(visibility);
    }

    @Override
    public void setCaptureRadioBtnVisibility(int visibility) {
        captureRadioBtn.setVisibility(visibility);
    }

    @Override
    public void setVideoRadioBtnVisibility(int visibility) {
        videoRadioBtn.setVisibility(visibility);
    }

    @Override
    public void setTimeLapseRadioChecked(boolean checked) {
        timeLapseRadioBtn.setChecked(checked);
    }

    @Override
    public void setCaptureRadioBtnChecked(boolean checked) {
        captureRadioBtn.setChecked(checked);
    }

    @Override
    public void setVideoRadioBtnChecked(boolean checked) {
        videoRadioBtn.setChecked(checked);
    }

    @Override
    public void showPopupWindow(int curMode) {
        if (pvModePopupWindow != null) {
            int dp_10 = getResources().getDimensionPixelOffset(R.dimen.dp_10);
            int xOff = -dp_10, yOff;
            if (!CameraManager.getInstance().getCurCamera().getCameraProperties().cameraModeSupport(ICatchCamMode.ICH_CAM_MODE_VIDEO)) {
                yOff = -findViewById(R.id.bottomBar).getHeight() * 2 - dp_10;
            } else {
                yOff = -findViewById(R.id.bottomBar).getHeight() * 3 + pvModeBtn.getHeight() - dp_10;
            }
            pvModePopupWindow.showAsDropDown(pvModeBtn, xOff, yOff);
        }
    }

    @Override
    public void dismissPopupWindow() {
        if (pvModePopupWindow != null) {
            if (pvModePopupWindow.isShowing()) {
                pvModePopupWindow.dismiss();
            }
        }
    }

    @Override
    public int getSurfaceViewWidth() {
        View parentView = (View) mSurfaceView.getParent();
        int width = parentView.getWidth();
        return width;
    }

    @Override
    public int getSurfaceViewHeight() {
        View parentView = (View) mSurfaceView.getParent();
        int heigth = parentView.getHeight();
        return heigth;
    }

    @Override
    public void setPanoramaTypeBtnSrc(int srcId) {
        panoramaTypeBtn.setImageResource(srcId);
    }

    @Override
    public void setPanoramaTypeBtnVisibility(int visibility) {
        panoramaTypeBtn.setVisibility(visibility);
    }
}

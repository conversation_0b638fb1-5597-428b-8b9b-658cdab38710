package com.icatch.mobilecam.ui.popupwindow;

import android.content.Context;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.icatch.mobilecam.Application.Const;
import com.ijoyer.mobilecam.R;
import com.warkiz.widget.IndicatorSeekBar;
import com.warkiz.widget.OnSeekChangeListener;
import com.warkiz.widget.SeekParams;

import razerdp.basepopup.BasePopupWindow;

/**
 * 说明：
 * Created by jjs on 2019/1/4
 */
public class HdrWindow extends BasePopupWindow {

    public TextView tvCancel;
    public TextView tvSure;
    public TextView tvCancelNoTip;
    public ImageView ivSelect;
    private Context mContext;
    public LinearLayout llSelect;

    public HdrWindow(Context context) {
        super(context);
        setWidth((int) (ScreenUtils.getScreenWidth() * 0.85));
        mContext = context;
        setPopupGravity(Gravity.CENTER);
        tvSure = findViewById(R.id.tv_sure);
        tvCancel = findViewById(R.id.tv_cancel);
        tvCancelNoTip = findViewById(R.id.tv_cancel_no_tip);
        ivSelect = findViewById(R.id.iv_select);
        llSelect = findViewById(R.id.ll_select);


        if (SPUtils.getInstance().getInt(Const.SP.HDR_DATA,0) == 0){
            ivSelect.setActivated(false);
        } else {
            ivSelect.setActivated(true);
        }

        ivSelect.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (ivSelect.isActivated()){
                    ivSelect.setActivated(false);
                } else {
                    ivSelect.setActivated(true);
                }
            }
        });

        tvCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });

    }

    @Override
    public void showPopupWindow() {
        super.showPopupWindow();
    }







    @Override
    public void onDismiss() {
        super.onDismiss();
    }


    @Override
    public View onCreateContentView() {
        return createPopupById(R.layout.popup_hdr);
    }


}

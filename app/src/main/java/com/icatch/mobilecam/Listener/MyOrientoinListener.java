package com.icatch.mobilecam.Listener;
import android.app.Activity;
import android.content.Context;
import android.content.pm.ActivityInfo;
import android.view.OrientationEventListener;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.data.AppInfo.AppInfo;
public class MyOrientoinListener extends OrientationEventListener {
    private static final String TAG = "MyOrientoinListener";
    private Context context;
    private Activity activity;
    public MyOrientoinListener(Activity activity, Context context) {
        super(context);
        this.context = context;
        this.activity = activity;
    }
    public MyOrientoinListener(Activity activity, Context context, int rate) {
        super(context, rate);
        this.context = context;
        this.activity = activity;
    }
    @Override
    public void onOrientationChanged(int orientation) {
        int screenOrientation = context.getResources().getConfiguration().orientation;
        if (((orientation >= 0) && (orientation < 45)) || (orientation > 315)) {
            if (AppInfo.curScreenOrientation != ScreenOrientation.SCREEN_ORIENTATION_PORTRAIT) {
                AppLog.d(TAG, "设置竖屏");
                activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                AppInfo.curScreenOrientation = ScreenOrientation.SCREEN_ORIENTATION_PORTRAIT;
            }
        }
        else if (orientation > 135 && orientation < 225) {
            if (AppInfo.curScreenOrientation != ScreenOrientation.SCREEN_ORIENTATION_REVERSE_PORTRAIT) {
                AppLog.d(TAG, "设置反向竖屏");
                activity.setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_REVERSE_PORTRAIT);
                AppInfo.curScreenOrientation = ScreenOrientation.SCREEN_ORIENTATION_REVERSE_PORTRAIT;
            }
        }
    }
    public enum ScreenOrientation{
        SCREEN_ORIENTATION_PORTRAIT,
        SCREEN_ORIENTATION_REVERSE_PORTRAIT,
        SCREEN_ORIENTATION_LANDSCAPE,
        SCREEN_ORIENTATION_REVERSE_LANDSCAPE
    }
}

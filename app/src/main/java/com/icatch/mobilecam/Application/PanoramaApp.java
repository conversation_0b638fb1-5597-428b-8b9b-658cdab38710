package com.icatch.mobilecam.Application;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.RequiresApi;
import androidx.multidex.MultiDex;

import com.blankj.utilcode.constant.PermissionConstants;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.PathUtils;
import com.blankj.utilcode.util.PermissionUtils;
import com.blankj.utilcode.util.SPUtils;
import com.blankj.utilcode.util.Utils;
import com.icatch.mobilecam.utils.CrashHandler;
import com.icatch.mobilecam.utils.SharedPreferencesUtils;
import com.icatch.mobilecam.utils.imageloader.ImageLoaderConfig;
import com.ijoyer.camera.Presenter.LaunchPresenter;
import com.ijoyer.camera.http.net.rx.RetrofitBuilder;
import com.ijoyer.camera.utils.LogUtil;
import com.ijoyer.mobilecam.BuildConfig;
import com.tencent.bugly.crashreport.BuglyLog;
import com.tencent.bugly.crashreport.CrashReport;
import com.tencent.mmkv.MMKV;
import com.umeng.analytics.MobclickAgent;
import com.umeng.commonsdk.UMConfigure;
import com.umeng.socialize.PlatformConfig;

import org.json.JSONObject;

import java.io.File;
import java.io.FileWriter;
import java.util.UUID;

import xcrash.ICrashCallback;
import xcrash.TombstoneParser;
import xcrash.XCrash;

public class PanoramaApp extends Application {
    private static final String TAG = PanoramaApp.class.getSimpleName();
    private static Context instance;
    private static PanoramaApp mApp;
    private Intent mResultIntent = null;
    private int mResultCode = 0;
    private MediaProjectionManager mMpMgr;
    private LaunchPresenter launchPresenter;
    //微信id
    public static final String APP_ID = "wx4c84b24b71ce93ce";


    @RequiresApi(api = 30)
    @Override
    public void onCreate() {
        super.onCreate();
//        AppOpsManager systemService = (AppOpsManager)getSystemService(AppOpsManager.class);
//        systemService.setOnOpNotedCallback(new Executor() {
//            @Override
//            public void execute(Runnable command) {
//
//            }
//        }, new AppOpsManager.OnOpNotedCallback() {
//
//            public void logPrivateDataAccess(String opCode , String attributionTag ,String trace ){
//                LogUtil.e("opCode:" + opCode);
//                LogUtil.e("attributionTag:" + attributionTag);
//                LogUtil.e("trace:" + trace);
//            }
//
//            @Override
//            public void onNoted(@NonNull SyncNotedAppOp op) {
//                logPrivateDataAccess(op.getOp(), op.getAttributionTag(), new Throwable().getStackTrace().toString());
//            }
//
//            @Override
//            public void onSelfNoted(@NonNull SyncNotedAppOp op) {
//                logPrivateDataAccess(op.getOp(), op.getAttributionTag(), new Throwable().getStackTrace().toString());
//            }
//
//            @Override
//            public void onAsyncNoted(@NonNull AsyncNotedAppOp asyncOp) {
//                logPrivateDataAccess(asyncOp.getOp(), asyncOp.getAttributionTag(), new Throwable().getStackTrace().toString());
//            }
//        });
//        Bugtags.start("42c925545aacb539e96e54ab47563b7a", this, Bugtags.BTGInvocationEventNone);
        instance = getApplicationContext();
        ImageLoaderConfig.initImageLoader(getApplicationContext(), null);
        Utils.init(this);
        String rootDir = MMKV.initialize(this);
        LogUtil.d("MMKV rootDir：" + rootDir);
        mApp = this;

//        6247a7310059ce2bad1ab487
        if (SPUtils.getInstance().getBoolean("isFirst")) {
            UMConfigure.setLogEnabled(false);
            UMConfigure.preInit(this, "62a2b6f588ccdf4b7e900d62"
                    , "umeng");
            UMConfigure.init(getContext(), "62a2b6f588ccdf4b7e900d62"
                    , "umeng", UMConfigure.DEVICE_TYPE_PHONE, "");
            // 微信设置
            PlatformConfig.setWeixin("wx4c84b24b71ce93ce", "7c7c8988dcb770af02dc235d7bc56c6f");
            PlatformConfig.setWXFileProvider(AppUtils.getAppPackageName() + ".fileprovider");
            // QQ设置
            PlatformConfig.setQQZone("**********", "T8H27evxdjKYBORW");
            PlatformConfig.setQQFileProvider(AppUtils.getAppPackageName() + ".fileprovider");
            // 新浪微博设置
            PlatformConfig.setSinaWeibo("**********", "f18ce55760c615e43584614c3bf81766", "http://sns.whalecloud.com");
            PlatformConfig.setSinaFileProvider(AppUtils.getAppPackageName() + ".fileprovider");
            MobclickAgent.setDebugMode(false);

            CrashHandler.getInstance().init(this);
            setIjoyerLogToFile(true);

            initBugly();
        }

        RetrofitBuilder.getInstance().setBaseUrl("https://ijoyer.com");

        //放在最后，保证异常是被它捕获
        initxCrash();
    }

    public static void initBugly() {
        CrashReport.UserStrategy strategy = new CrashReport.UserStrategy(mApp);
        strategy.setDeviceModel(Build.MODEL);
        String uuid = SPUtils.getInstance().getString("uuid");
        if (TextUtils.isEmpty(uuid)) {
            uuid = UUID.randomUUID().toString();
            SPUtils.getInstance().put("uuid", uuid);
        }
        strategy.setDeviceID(uuid);
        CrashReport.initCrashReport(mApp, "7f7d006cfc", BuildConfig.DEBUG,strategy);
        CrashReport.setUserId(uuid);
    }

    private void initxCrash() {
        // callback for java crash, native crash and ANR
        ICrashCallback callback = (logPath, emergency) -> {
            if (PermissionUtils.isGranted(PermissionConstants.getPermissions(PermissionConstants.STORAGE))) {
                String dirPath = PathUtils.getExternalDocumentsPath() + "/IJOYER_Log";
                FileUtils.createOrExistsDir(dirPath);
                FileWriter writer = null;
                try {
                    File debug = new File(dirPath + "/xCrash_" + System.currentTimeMillis() + ".json");
                    FileUtils.createOrExistsFile(debug);
                    writer = new FileWriter(debug, false);
                    writer.write(new JSONObject(TombstoneParser.parse(logPath, emergency)).toString());
                } catch (Exception e) {
                    Log.d(TAG, "debug failed", e);
                } finally {
                    if (writer != null) {
                        try {
                            writer.close();
                        } catch (Exception ignored) {
                        }
                    }
                }
            }
        };

        xcrash.XCrash.init(this,
                new XCrash.InitParameters()
                        .setNativeCallback(callback)
                        .setJavaCallback(callback));
    }

    public static Context getContext() {
        return instance;
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        MultiDex.install(this);
    }

    public static PanoramaApp getInstance() {
        return mApp;
    }

    public Intent getResultIntent() {
        return mResultIntent;
    }

    public void setResultIntent(Intent mResultIntent) {
        this.mResultIntent = mResultIntent;
    }

    public int getResultCode() {
        return mResultCode;
    }

    public void setResultCode(int mResultCode) {
        this.mResultCode = mResultCode;
    }

    public MediaProjectionManager getMpMgr() {
        return mMpMgr;
    }

    public void setMpMgr(MediaProjectionManager mMpMgr) {
        this.mMpMgr = mMpMgr;
    }

    public LaunchPresenter getLaunchPresenter() {
        return launchPresenter;
    }

    public void setLaunchPresenter(LaunchPresenter launchPresenter) {
        this.launchPresenter = launchPresenter;
    }

    public static void setIjoyerLogToFile(boolean logSwitch) {
        //用这个文件不需要权限，保证可以打印日志
        String path = PathUtils.getExternalAppDataPath();
        if (!FileUtils.isFileExists(path)) {
            path = PathUtils.getInternalAppDataPath();
        }
        if (PermissionUtils.isGranted(PermissionConstants.getPermissions(PermissionConstants.STORAGE))) {
            //有权限的话就放在document 里面，方便找
            path = PathUtils.getExternalDocumentsPath();
        }
        LogUtils.d("日志文件存放目录：" + path + "/IJOYER_Log");
        LogUtils.getConfig().setLogSwitch(logSwitch)
                .setLog2FileSwitch(logSwitch)
                .setDir(new File(path + "/IJOYER_Log"))
                .setFilePrefix("IJOYER_" + System.currentTimeMillis())
                .setFileExtension(".log")
                .setOnFileOutputListener((s, s1) -> BuglyLog.d("logUtils.file", s1))//添加监听打印到bugly
                .setSaveDays(7);

        for (File file : LogUtils.getLogFiles()) {
            FileUtils.notifySystemToScan(file.getAbsolutePath());
        }
    }


    public static boolean getPlayGyro() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_GYRO, false);
    }

    public static void setPlayGyro(boolean gyro) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_GYRO, gyro);
    }

    public static boolean getPlayZoom() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_ZOOM, true);
    }

    public static void setPlayZoom(boolean zoom) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_ZOOM, zoom);
    }

    public static boolean getPlayGesture() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_GESTURE, true);
    }

    public static void setPlayGesture(boolean gesture) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_GESTURE, gesture);
    }

    public static boolean getPlayGyroModeShouldMove() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_GYRO_MODE_SHOULD_MOVE, false);
    }

    public static void setPlayGyroModeShouldMove(boolean gyroModeShouldMove) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_GYRO_MODE_SHOULD_MOVE, gyroModeShouldMove);
    }

    public static boolean getPlayAutoPlay() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_AUTO_PLAY, false);
    }

    public static void setPlayAutoPlay(boolean autoPlay) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_AUTO_PLAY, autoPlay);
    }

    public static boolean getReverse() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_REVERSE, false);
    }

    public static void setReverse(boolean reverse) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_REVERSE, reverse);
    }

    public static boolean getScenesBackgroundMusic() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_SCENES_BACKGROUND_MUSIC, true);
    }

    public static void setScenesBackgroundMusic(boolean scenesBackgroundMusic) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_SCENES_BACKGROUND_MUSIC, scenesBackgroundMusic);
    }

    public static boolean getPlayPointSelecting() {
        return SharedPreferencesUtils.getBoolean(Const.SP.USER_INFO, Const.SP.PLAY_POINT_SELECTING, false);
    }

    public static void setPlayPointSelecting(boolean playPointSelecting) {
        SharedPreferencesUtils.putBoolean(Const.SP.USER_INFO, Const.SP.PLAY_POINT_SELECTING, playPointSelecting);
    }

    public static int getPlayMode() {
        return SharedPreferencesUtils.getInt(Const.SP.USER_INFO, Const.SP.PLAY_MODE);
    }

    public static void setPlayMode(int playMode) {
        SharedPreferencesUtils.putInt(Const.SP.USER_INFO, Const.SP.PLAY_MODE, playMode);
    }

}

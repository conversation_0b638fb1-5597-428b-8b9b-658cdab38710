package com.icatch.mobilecam.evenbus;

/**
 * 说明：
 * Created by jjs on 2019/3/20
 */
public class EventBusEntity<T> {
    public String deviceId;
    public T data;
    public String tag;
    public boolean isSuccess;
    public String message;

    public EventBusEntity(String tag, T data) {
        this.data = data;
        this.tag = tag;
        this.isSuccess = true;
    }

    public EventBusEntity(String tag, T data, String deviceId) {
        this.data = data;
        this.tag = tag;
        this.isSuccess = true;
        this.deviceId=deviceId;
    }

    public EventBusEntity(String tag, String error) {
        this.message = error;
        this.tag = tag;
        this.isSuccess = false;
    }

}

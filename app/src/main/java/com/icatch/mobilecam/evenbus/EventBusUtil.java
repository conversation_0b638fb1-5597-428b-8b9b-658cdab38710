package com.icatch.mobilecam.evenbus;

import org.greenrobot.eventbus.EventBus;

/**
 * 注册 EventBus
 */
public class EventBusUtil {

    /**
     * 注册 EventBus
     *
     * @param object
     */
    public static void register(Object object) {
        if (object == null) {
            return;
        }
        if (!EventBus.getDefault().isRegistered(object)) {
            EventBus.getDefault().register(object);
        }
    }

    /**
     * 取消注册 EventBus
     *
     * @param object
     */
    public static void unRegister(Object object) {
        if (object == null) {
            return;
        }
        if (EventBus.getDefault().isRegistered(object)) {
            EventBus.getDefault().unregister(object);
        }
    }

    /**
     * 是否注册 EventBus
     *
     * @param object
     * @return
     */
    private static boolean isRegistered(Object object) {
        return EventBus.getDefault().isRegistered(object);
    }

}

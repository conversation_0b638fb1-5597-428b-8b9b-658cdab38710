package com.icatch.mobilecam.MyCamera;
import android.hardware.usb.UsbDevice;

import com.icatch.mobilecam.Log.AppLog;

public class CameraManager {
    private final String TAG = CameraManager.class.getSimpleName();
    private MyCamera curCamera;
    private static CameraManager instance;
    public static CameraManager getInstance() {
        if (instance == null) {
            synchronized (CameraManager.class) {
                if (instance == null) {
                    instance = new CameraManager();
                }
            }
        }
        return instance;
    }
    public MyCamera getCurCamera() {
        return curCamera;
    }
    public void setCurCamera(MyCamera curCamera) {
        this.curCamera = curCamera;
    }
    public synchronized MyCamera createCamera(int cameraType, String ssid, String ipAddress,int position, int mode) {
        this.curCamera = new MyCamera(cameraType, ssid, ipAddress,position, mode);
        return this.curCamera;
    }
    public synchronized MyCamera createUSBCamera(int cameraType, UsbDevice usbDevice, int  position) {
        this.curCamera = new MyCamera(cameraType, usbDevice, position);
        return this.curCamera;
    }

    /**
     * 强制清理所有相机资源，用于解决SDK状态卡住的问题
     */
    public synchronized void forceCleanup() {
        // 上报强制清理事件
        String cleanupInfo = "Camera: " + (curCamera != null ? curCamera.getCameraName() : "null") +
                            ", Connected: " + (curCamera != null && curCamera.isConnected());
        AppLog.w(TAG,"相机强制清理执行: " + cleanupInfo);

        if (curCamera != null) {
            try {
                curCamera.disconnect();
            } catch (Exception e) {
                // 忽略断开连接时的异常
                AppLog.w(TAG,"强制清理-断开连接异常: " + e.getMessage());
            }
            curCamera = null;
        }

        // 强制垃圾回收，帮助清理SDK内部状态
        System.gc();

        try {
            Thread.sleep(1000); // 等待1秒让系统完全清理
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}

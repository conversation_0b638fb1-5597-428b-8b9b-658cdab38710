package com.icatch.mobilecam.MyCamera;

import android.text.TextUtils;
import android.util.Log;

import com.blankj.utilcode.util.NetworkUtils;
import com.icatch.mobilecam.Application.PanoramaApp;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.utils.CameraUtils;
import com.icatch.mobilecam.utils.WifiAPUtil;
import com.icatchtek.control.customer.ICatchCameraSession;
import com.icatchtek.reliant.customer.exception.IchInvalidArgumentException;
import com.icatchtek.reliant.customer.exception.IchInvalidSessionException;
import com.icatchtek.reliant.customer.exception.IchTransportException;
import com.icatchtek.reliant.customer.transport.ICatchITransport;
import com.tencent.bugly.crashreport.CrashReport;

public class CommandSession {
    private static final String TAG = CommandSession.class.getSimpleName();
    private static int scanflag;
    private final static String tag = "CommandSession";
    private ICatchCameraSession session;
    private String ipAddress;
    private String uid;
    private String username;
    private String password;
    private boolean sessionPrepared = false;
    public CommandSession(String ipAddress, String uid, String username, String password) {
        this.ipAddress = ipAddress;
        this.username = username;
        this.password = password;
        this.uid = uid;
    }
    public CommandSession() {
    }
    public boolean prepareSession(ICatchITransport itrans) {
        try {
            ICatchCameraSession.getCameraConfig(itrans).enablePTPIP();
        } catch (IchInvalidArgumentException e) {
            e.printStackTrace();
        }
        sessionPrepared = true;
        session = ICatchCameraSession.createSession();
        boolean retValue = false;
        try {
            retValue = session.prepareSession(itrans);
        } catch (IchTransportException e) {
            AppLog.d(tag, "IchTransportException");
            e.printStackTrace();
        }
        sessionPrepared = retValue;
        AppLog.e(tag, "preparePanoramaSession =" + sessionPrepared);
        return retValue;
    }
    public boolean prepareSession(ICatchITransport itrans,boolean enablePTPIP) {
        AppLog.d(TAG, "start prepareSession itrans="+ itrans + " enablePTPIP=" +enablePTPIP);
        if (enablePTPIP) {
            try {
                ICatchCameraSession.getCameraConfig(itrans).enablePTPIP();
            } catch (IchInvalidArgumentException e) {
                AppLog.e(tag, "enablePTPIP IchInvalidArgumentException");
                e.printStackTrace();
            }
        } else {
            try {
                ICatchCameraSession.getCameraConfig(itrans).disablePTPIP();
            } catch (IchInvalidArgumentException e) {
                AppLog.e(tag, "disablePTPIP IchInvalidArgumentException");
                e.printStackTrace();
            }
        }
        sessionPrepared = true;
        AppLog.d(tag, "start createSession");
        session = ICatchCameraSession.createSession();
        boolean retValue = false;

        // 针对-348错误，需要完全重置SDK状态
        try {
            retValue = session.prepareSession(itrans);
        } catch (IchTransportException e) {
            AppLog.e(tag, "prepareSession failed with exception: " + e.getMessage());

            // 收集关键业务信息上报到Bugly
            String networkInfo = "SSID: " + NetworkUtils.getSSID() +
                    ", LocalIP: " + WifiAPUtil.getLocalIPAddressFromWifiInfo(PanoramaApp.getContext()) +
                    "，transport_type:" + (itrans != null ? itrans.getClass().getSimpleName() : "null");

            AppLog.w(tag, "建立会话失败，" + networkInfo);

            // 检查是否是-348错误
            if (e.getMessage() != null && e.getMessage().contains("-348")) {
                AppLog.w(tag, "发现-348错误，正在重置SDK状态...");

                // 完全销毁当前session
                try {
                    if (session != null) {
                        session.destroySession();
                    }
                } catch (Exception destroyEx) {
                    AppLog.e(tag, "发现-348错误，重置失败: " + destroyEx.getMessage());
                    CrashReport.postCatchedException(new Exception("SDK重置-销毁session失败: " + destroyEx.getMessage()));
                }

                // 重新创建session并尝试连接
                try {
                    Thread.sleep(2000); // 等待2秒让SDK完全重置
                    AppLog.d(tag, "发现-348错误,正在重试连接...");
                    session = ICatchCameraSession.createSession();
                    retValue = session.prepareSession(itrans);
                    AppLog.d(tag, "发现-348错误，重置后连接结果: " + retValue);

                    // 上报重置结果
                    String resetResult = retValue ? "成功" : "失败";
                    AppLog.d(tag,"SDK -348错误重置结果: " + resetResult + " | " + networkInfo);
                } catch (Exception retryEx) {
                    AppLog.e(tag, "Retry after SDK reset failed: " + retryEx.getMessage());
                    CrashReport.postCatchedException(new Exception("SDK重置-重新连接失败:"+retryEx.getMessage()));
                    retValue = false;
                }
            }
        }

        if (!retValue) {
            AppLog.e(tag, "failed to preparePanoramaSession");
            sessionPrepared = false;
            Log.v("1111", "CommandSession,preparePanoramaSession fail!");
            String ssid = NetworkUtils.getSSID();
            if (TextUtils.isEmpty(ssid)){
                CrashReport.postCatchedException(new Exception("连接相机失败：prepareSession：未链接WIFI"));
            }else if (CameraUtils.isIjoyerCamera(ssid)){
                CrashReport.postCatchedException(new Exception("连接相机失败：prepareSession：需要关注"));
            }else{
                CrashReport.postCatchedException(new Exception("连接相机失败：prepareSession：未链接到指定WIFI"));
            }
        }
        AppLog.d(tag, "end preparePanoramaSession ret=" + sessionPrepared);
        return sessionPrepared;
    }
    public ICatchCameraSession getSDKSession() {
        AppLog.d(TAG, "getSDKSession =" + session);
        return session;
    }
    public boolean checkWifiConnection() {
        AppLog.i(tag, "Start checkWifiConnection");
        boolean retValue = false;
        try {
            retValue = session.checkConnection();
        } catch (IchInvalidSessionException e) {
            e.printStackTrace();
        }
        AppLog.i(tag, "End checkWifiConnection,retValue=" + retValue);
        return retValue;
    }
    public boolean destroySession() {
        AppLog.i(tag, "Start destroyPanoramaSession");
        Boolean retValue = false;
        try {
            retValue = session.destroySession();
            AppLog.i(tag, "End  destroyPanoramaSession,retValue=" + retValue);
        } catch (IchInvalidSessionException e) {
            e.printStackTrace();
        }
        return retValue;
    }
    public static boolean startDeviceScan() {
        AppLog.i(tag, "Start startDeviceScan");
        boolean tempStartDeviceScanValue = false;
        AppLog.i(tag, "End startDeviceScan,tempStartDeviceScanValue=" + tempStartDeviceScanValue);
        if (tempStartDeviceScanValue) {
            scanflag = 1;
        }
        return tempStartDeviceScanValue;
    }
    public static void stopDeviceScan() {
        AppLog.i(tag, "Start stopDeviceScan");
        boolean tempStopDeviceScanValue = false;
        if (scanflag == 1) {
        } else {
            tempStopDeviceScanValue = true;
        }
        scanflag = 0;
        AppLog.i(tag, "End stopDeviceScan,tempStopDeviceScanValue=" + tempStopDeviceScanValue);
    }
}

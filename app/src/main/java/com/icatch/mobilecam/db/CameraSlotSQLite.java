package com.icatch.mobilecam.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;

import com.blankj.utilcode.util.AppUtils;
import com.icatch.mobilecam.Log.AppLog;
import com.icatch.mobilecam.MyCamera.CameraType;
import com.icatch.mobilecam.data.GlobalApp.GlobalInfo;
import com.icatch.mobilecam.data.SystemInfo.MWifiManager;
import com.icatch.mobilecam.data.entity.CameraSlot;

import java.util.ArrayList;
import java.util.Iterator;

public class CameraSlotSQLite {
    private SQLiteDatabase db;
    private String TAG = "CameraSlotSQLite";
    private static CameraSlotSQLite instance;
    private ArrayList<CameraSlot> camSlotList;
    private Context context;
    public static final int MAX = 10;

    public static CameraSlotSQLite getInstance() {
        if (instance == null) {
            instance = new CameraSlotSQLite();
        }
        return instance;
    }

    private CameraSlotSQLite() {
        creatTable(GlobalInfo.getInstance().getAppContext());
    }

    private void creatTable(Context context) {
        AppLog.i(TAG, "start creatTable");
        this.context = context;
        CameraSlotSQLiteHelper dbHelper = new CameraSlotSQLiteHelper(context);
        this.db = dbHelper.getWritableDatabase();
        AppLog.i(TAG, "end creatTable");
    }

    public boolean insert(CameraSlot camSlot) {
        AppLog.i(TAG, "start insert isOccupied=" + switchBoolToInt(camSlot.isOccupied));
        ContentValues cValue = new ContentValues();
        cValue.put("isOccupied", switchBoolToInt(camSlot.isOccupied));
        cValue.put("cameraName", camSlot.cameraName);
        cValue.put("imageBuffer", camSlot.cameraPhoto);
        cValue.put("cameraType", camSlot.cameraType);
        if (db.insert(CameraSlotSQLiteHelper.DATABASE_TABLE, null, cValue) == -1) {
            AppLog.i(TAG, "failed to insert!");
            return false;
        }
        ;
        AppLog.i(TAG, "end: insert success");
        return true;
    }

    public void update(CameraSlot camSlot) {
        AppLog.i(TAG, "start update slotPosition=" + camSlot.slotPosition);
        ContentValues values = new ContentValues();
        values.put("isOccupied", camSlot.isOccupied);
        if (camSlot.cameraPhoto != null && camSlot.cameraPhoto.length > 0) {
            values.put("imageBuffer", camSlot.cameraPhoto);
        }
        values.put("cameraName", camSlot.cameraName);
        values.put("cameraType", camSlot.cameraType);
        String whereClause = "_id=?";
        String[] whereArgs = {String.valueOf(camSlot.slotPosition + 1)};
        db.update(CameraSlotSQLiteHelper.DATABASE_TABLE, values, whereClause, whereArgs);
        AppLog.i(TAG, "end update");
    }

    public void deleteByPosition(int slotPosition) {
        AppLog.i(TAG, "start delete slotPosition=" + slotPosition);
        String whereClause = "_id=?";
        String[] whereArgs = {String.valueOf(slotPosition)};
        int delete = db.delete(CameraSlotSQLiteHelper.DATABASE_TABLE, whereClause, whereArgs);
//        update(new CameraSlot(slotPosition, false, null, CameraType.UNDEFIND_CAMERA, null, false));
        AppLog.i(TAG, "end delete");
    }

    public ArrayList<CameraSlot> getAllCameraSlotFormDb() {
        AppLog.i(TAG, "start getAllCameraSlotFormDb");
        camSlotList = new ArrayList<>();
        Cursor cursor = db.rawQuery("select * from " + CameraSlotSQLiteHelper.DATABASE_TABLE, null);
        AppLog.i(TAG, "end rawQuery =" + cursor.getCount());
        String wifiSSID = MWifiManager.getCacheSsid(context);
        while (cursor.moveToNext()) {
            AppLog.i(TAG, "cursor.getInt(cursor.getColumnIndex =" + cursor.getInt(cursor.getColumnIndex("_id")));
            int id = cursor.getInt(cursor.getColumnIndex("_id"));
            int isUsed = cursor.getInt(cursor.getColumnIndex("isOccupied"));
            String camName = cursor.getString(cursor.getColumnIndex("cameraName"));
            byte[] imageBuf = cursor.getBlob(cursor.getColumnIndex("imageBuffer"));
            int cameraType = cursor.getInt(cursor.getColumnIndex("cameraType"));
            boolean isReady = false;
            if (wifiSSID != null && wifiSSID.equals(camName)) {
                isReady = true;
            }
            camSlotList.add(new CameraSlot(id, switchIntToBool(isUsed), camName, cameraType, imageBuf, isReady));
            AppLog.i(TAG, " switchIntToBool(isUsed) =" + switchIntToBool(isUsed));
            AppLog.i(TAG, "_id=" + id + " isOccupied=" + isUsed + " camName=" + camName + " cameraType=" + cameraType);
        }
        AppLog.i(TAG, "end query all cameraSlot");
        cursor.close();

        if(camSlotList.size()>0 && AppUtils.getAppVersionCode()<=6){
            ArrayList<CameraSlot> checkList = new ArrayList<>();
            for(CameraSlot bean :camSlotList){
                if(!bean.isOccupied){
                    checkList.add(bean);
                }
            }
            if(checkList.size()>=2){
                Iterator<CameraSlot> iterator = camSlotList.iterator();
                while (iterator.hasNext()) {
                    CameraSlot bean = iterator.next();
                    if(!bean.isOccupied){
                        deleteByPosition(bean.slotPosition);
                        iterator.remove();
                    }
                }
            }
        }

        //永遠多一個
        if(camSlotList.isEmpty() || camSlotList.get(camSlotList.size()-1).isOccupied){
            if(camSlotList.isEmpty()){
                if (insert(new CameraSlot(1, false, null, null))) {
                    camSlotList.add(new CameraSlot(camSlotList.size()+1, false, null, CameraType.UNDEFIND_CAMERA, null, false));
                }
            }else{
                if (insert(new CameraSlot(camSlotList.get(camSlotList.size()-1).slotPosition+1, false, null, null))) {
                    camSlotList.add(new CameraSlot(camSlotList.size()+1, false, null, CameraType.UNDEFIND_CAMERA, null, false));
                }
            }
        }

        AppLog.i(TAG, "end getAllCameraSlotFormDb");
        return camSlotList;
    }

    public void updateImage(Bitmap bitmap) {
    }

    public void closeDB() {
        db.close();
    }

    private int switchBoolToInt(Boolean value) {
        if (value) {
            return 1;
        }
        return 0;
    }

    private Boolean switchIntToBool(int value) {
        if (value == 1) {
            return true;
        } else {
            return false;
        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.icatch.mobilecam">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.RESTART_PACKAGES" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.GET_TASKS" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.DOWNLOAD_WITHOUT_NOTIFICATION" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STAT" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIMAX_STATE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <!--    Android 13版本适配，细化存储权限-->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!--如果是安卓10.0，需要后台获取连接的wifi名称则添加进程获取位置信息权限 -->
    <!--    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />-->
    <uses-feature android:name="android.hardware.usb.host" />

    <queries>
        <package android:name="com.tencent.mm" />
<!--        // 指定微信包名-->
        <package android:name="com.tencent.mobileqq" />
<!--        //指定qq包名-->
        <package android:name="com.sina.weibo" />
<!--        //指定微博包名-->
        <package android:name="com.tencent.wework" />
<!--        //指定企业微信包名-->
        <package android:name="com.qzone" />
<!--        //指定QQ空间包名-->
        <package android:name="com.alibaba.android.rimet" />
<!--        // 指定钉钉包名-->
        <package android:name="com.eg.android.AlipayGphone" />
<!--        // 指定支付宝包名-->
        <package android:name="com.instagram.android" />
<!--        // 指定instagram包名-->

        <!--        Android 11 use camera，AndroidManifest.xm add the code：-->
        <intent>
            <action android:name="android.media.action.IMAGE_CAPTURE" />
        </intent>
        <intent>
            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
        </intent>
        <!--        Android 11 use camera，AndroidManifest.xm add the code：-->
    </queries>

    <!--查看应用是否已安装·Android 11 新增-->
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <application
        android:name=".Application.PanoramaApp"
        android:allowBackup="true"
        android:icon="@drawable/my_app_icon"
        android:label="@string/app_name"
        android:maxAspectRatio="2.4"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:supportsRtl="true"
        android:theme="@style/FullScreenTheme">

        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <!--适配小米（xiaomi）刘海屏-->
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

        <activity
            android:name=".ui.activity.LaunchActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>
            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/device_filter" />
        </activity>

        <activity
            android:name=".ui.activity.LaunchHelpActivity"
            android:label="@string/help"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/MyAppTheme.NoActionBar" />
        <activity
            android:name=".ui.activity.LicenseAgreementActivity"
            android:label="@string/title_privacy_policy2"
            android:launchMode="singleTask"
            android:theme="@style/MyAppTheme.NoActionBar" />

        <activity
            android:name=".ui.activity.PreviewActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_preview"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat" />

        <activity
            android:name=".ui.activity.LocalVideoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_pb_local_video"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat" />


        <activity
            android:name=".ui.activity.RemoteMultiPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_multi_pb"
            android:launchMode="singleTask"
            android:theme="@style/MyAppTheme.NoActionBar" />

        <activity
            android:name=".ui.activity.PhotoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_photo_pb"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat" />

        <activity
            android:name=".ui.activity.VideoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_panorama_video_pb"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat" />


        <activity
            android:name=".ui.activity.LocalPhotoPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_local_photo"
            android:launchMode="singleTask"
            android:theme="@style/Theme.AppCompat" />

        <activity
            android:name=".ui.activity.WifiApActivity"
            android:launchMode="singleTask" />

        <activity
            android:name=".ui.activity.PvParamSettingActivity"
            android:label="@string/title_activity_pv_param_setting"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait" />

        <activity
            android:name=".ui.activity.USBPreviewActivity"
            android:label="@string/title_activity_usbpreview"
            android:launchMode="singleTask"
            android:screenOrientation="sensorPortrait"
            android:theme="@style/FullScreenTheme.NoActionBar" />

        <activity
            android:name=".ui.activity.LocalMultiPbActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_local_pb"
            android:launchMode="singleTask"
            android:theme="@style/MyAppTheme.NoActionBar" />


        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.icatch.mobilecam.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_path" />
        </provider>

        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />


        <activity
            android:name="com.ijoyer.mobilecam.wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />


        <activity
            android:name="com.ijoyer.mobilecam.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop" />

        <queries>
            <package android:name="com.tencent.mm" />
            // 指定微信包名
            <package android:name="com.tencent.mobileqq" />
            //指定qq包名
            <package android:name="com.sina.weibo" />
            //指定微博包名
            <package android:name="com.tencent.wework" />
            //指定企业微信包名
            <package android:name="com.qzone" />
            //指定QQ空间包名
            <package android:name="com.alibaba.android.rimet" />
            // 指定钉钉包名
            <package android:name="com.eg.android.AlipayGphone" />
            // 指定支付宝包名
            <package android:name="com.instagram.android" />
            // 指定instagram包名
        </queries>
        <!--        <activity-->
        <!--            android:name="com.tencent.tauth.AuthActivity"-->
        <!--            android:launchMode="singleTask"-->
        <!--            android:noHistory="true">-->
        <!--&lt;!&ndash;            <intent-filter>&ndash;&gt;-->
        <!--&lt;!&ndash;                <actionandroid:name="android.intent.action.VIEW"/>&ndash;&gt;-->
        <!--&lt;!&ndash;                <categoryandroid:name="android.intent.category.DEFAULT"/>&ndash;&gt;-->
        <!--&lt;!&ndash;                <categoryandroid:name="android.intent.category.BROWSABLE"/>&ndash;&gt;-->
        <!--&lt;!&ndash;                <dataandroid:scheme="tencent100424468"/>&ndash;&gt;-->
        <!--&lt;!&ndash;            </intent-filter>&ndash;&gt;-->
        <!--        </activity>-->
        <!--        <activity-->
        <!--            android:name="com.tencent.connect.common.AssistActivity"-->
        <!--            android:theme="@android:style/Theme.Translucent.NoTitleBar"-->
        <!--            android:configChanges="orientation|keyboardHidden|screenSize"/>-->
        <!--        <provider-->
        <!--            android:name="androidx.core.content.FileProvider"-->
        <!--            android:authorities="com.ijoyer.mobilecam.fileprovider"-->
        <!--            android:exported="false"-->
        <!--            android:grantUriPermissions="true">-->
        <!--            <meta-data-->
        <!--                android:name="android.support.FILE_PROVIDER_PATHS"-->
        <!--                android:resource="@xml/filepaths"/>-->
        <!--        </provider>-->

        <!--        <activity-->
        <!--            android:name=".ddshare.DingCallBack"-->
        <!--            android:configChanges="keyboardHidden|orientation|screenSize"-->
        <!--            android:exported="true"-->
        <!--            android:screenOrientation="portrait"-->
        <!--            android:theme="@android:style/Theme.Translucent.NoTitleBar"/>-->


        <!--华为图像增强-->
        <!--        <meta-data-->
        <!--            android:name="com.huawei.hms.ml.DEPENDENCY"-->
        <!--            android:value= "imagesuperresolution"-->
        <!--            />-->

    </application>

</manifest>
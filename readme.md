相机控制流程：
## 连接相机
1.```com.ijoyer.camera.activity.MainActivity.goToNext()```：到WiFi 设置界面连接相机的wifi。 
2.```com.ijoyer.camera.Presenter.LaunchPresenter.launchCameraForMain()、launchCameraForMain(boolean isConnect, int cameraType)```：连接wifi 后，开始进入连接相机的流程。  
3.由于Android wifi 的限制，需要绑定网络，所以需要调用```com.ijoyer.camera.Presenter.LaunchPresenter.beginConnectCameraForMain()```，绑定网络后，调用```com.ijoyer.camera.Presenter.LaunchPresenter.beginConnectCameraForMainAfterBindNetwork()```连接相机。  
4.通过```com.ijoyer.camera.Presenter.LaunchPresenter.redirectToAnotherActivityByNav()```，连接相机后，根据不同的id，跳转到不同的界面（查看相册、拍摄预览）

## 预览```com.ijoyer.camera.activity.PreviewActivity```
页面采用MVP 的架构，Presenter 为```com.ijoyer.camera.Presenter.PreviewPresenter```，进入页面时，通过SurfaceView 绑定相机，并调用```com.ijoyer.camera.Presenter.PreviewPresenter.startPreview()```向相机发送指令开始预览。

## 拍摄
在预览页面，点击拍摄按钮，调用```com.ijoyer.camera.Presenter.PreviewPresenter.startPhotoCapture()```，开始进入拍摄流程。

1.停止预览，由于目前相机需要先等待复位完毕才能开始拍摄，所以起一个子线程向相机轮询。  
2.待相机复位完毕（0xD760=A6RotateMotorState.ROTATE_END || 0xD760=A6RotateMotorState.ROTATE_OFF），发送拍摄指令(photoCapture.startCapture()。同时启动计时器，如果拍摄超时，就停止拍摄。  
3.相机每拍摄成功一次，相机会发送指令```EVENT_CAPTURE_COMPLETED```，收到该指令后，等待相机准备完毕后开始下一次拍摄，直到完成整组拍摄（4次）。  
4.相机每生成一张照片，会发送```com.icatch.mobilecam.sdk.event.SDKEvent.EVENT_FILE_ADDED```，如果有开启自动下载功能，收到该指令后，开始下载文件。直到下载4组12 张图片，进入HDR-拼接流程（```com.icatch.mobilecam.Presenter.PreviewPresenter.executeHdr()```），拼接后续说明看后面。  
5.结束拍摄，向相机发送复位指令，同时重新开始预览。

注意 ：相机发送的指令会转发到```com.icatch.mobilecam.Presenter.PreviewPresenter.PreviewHandler```中进行实际处理。

## 相册、拼接
进入远程相册（RemoteMultiPbActivity）时，会调用相机sdk 读取相册内所有的照片，按12 张一组分组，对于未下载的图片，点击会弹出是否先HDR 的选项  
1.直接拼接：下载每组图片的第一张，成功后，会调用```com.detu.szStitch.StitchUtils.stitchExecOne()```、```SzStitch.CaliExecByContent()```进行拼接。  
2.先HDR再拼接：下载每组图片12 张，成功后，会调用```com.icatch.mobilecam.Presenter.PreviewPresenter.stitchExecHdr()```进行HDR，成功后调用```SzStitch.CaliExecByContent()```进行拼接。  
3.拼接成功后，会根据设置进行图像增强操作（```com.detu.szStitch.StitchUtils.imageEnhancement()```）。  

注意：执行HDR 和拼接都会消耗大量的系统资源（内存、CPU），这个过程比较容易导致系统卡顿。如果因为内存不足而崩溃了，请在设置页面开启单线程模式。  
注意：生成的日志，会记录在```/sdcard/Documents/ijoery```目录下，方便定位问题，因为系统权限问题，需要第二次进入app（冷启动） 才会将日志保存在这个文件夹中。  
